import { Injectable, CanActivate, ExecutionContext, Logger } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { GenericSessionService } from '../services';
import { AppException, ErrorCode } from '../../../common';

/**
 * Guard để kiểm tra session validity và user permissions
 */
@Injectable()
export class GenericSessionGuard implements CanActivate {
  private readonly logger = new Logger(GenericSessionGuard.name);

  constructor(
    private readonly reflector: Reflector,
    private readonly genericSessionService: GenericSessionService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    try {
      const request = context.switchToHttp().getRequest();
      const sessionId = request.params.sessionId || request.query.sessionId;

      if (!sessionId) {
        throw new AppException(
          ErrorCode.BAD_REQUEST,
          'Session ID is required'
        );
      }

      // Kiểm tra session có tồn tại và active không
      const isValid = await this.genericSessionService.isSessionActiveAndValid(sessionId);
      if (!isValid) {
        throw new AppException(
          ErrorCode.NOT_FOUND,
          'Session không tồn tại hoặc đã hết hạn'
        );
      }

      // Lấy session để kiểm tra ownership
      const session = await this.genericSessionService.getSessionBySessionId(sessionId);
      
      // Kiểm tra user permissions
      const user = request.user; // Từ authentication guard
      if (session.userId && user) {
        // Authenticated session - kiểm tra ownership
        if (session.userId !== user.id) {
          // Kiểm tra có phải admin không
          const isAdmin = this.checkAdminPermission(user);
          if (!isAdmin) {
            throw new AppException(
              ErrorCode.FORBIDDEN,
              'Bạn không có quyền truy cập session này'
            );
          }
        }
      }

      // Attach session to request for later use
      request.genericSession = session;
      
      return true;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Session guard error: ${error.message}`, error.stack);
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Lỗi khi kiểm tra session permissions'
      );
    }
  }

  /**
   * Kiểm tra user có quyền admin không
   */
  private checkAdminPermission(user: any): boolean {
    // Implement logic kiểm tra admin permission
    // Có thể check role, permissions, etc.
    return user.role === 'admin' || user.permissions?.includes('generic_admin');
  }
}

/**
 * Decorator để require session validation
 */
export const RequireSession = () => {
  return (target: any, propertyName: string, descriptor: PropertyDescriptor) => {
    // Có thể add metadata nếu cần
  };
};

/**
 * Decorator để require admin permissions
 */
export const RequireAdminSession = () => {
  return (target: any, propertyName: string, descriptor: PropertyDescriptor) => {
    // Có thể add metadata nếu cần
  };
};

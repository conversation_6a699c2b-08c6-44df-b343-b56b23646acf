import { ConfigService } from '@nestjs/config';
import { DocumentBuilder, SwaggerCustomOptions } from '@nestjs/swagger';
import { SWAGGER_API_TAGS } from './swagger.tags';

// Hàm tạo cấu hình <PERSON>wagger với thông tin từ biến môi trường
export const createSwaggerConfig = (configService: ConfigService) => {
  // Lấy môi trường hiện tại (có thể sử dụng cho logic phức tạp hơn trong tương lai)
  // const nodeEnv = configService.get<string>('NODE_ENV', 'development');

  // Lấy URL server từ biến môi trường dựa trên môi trường hiện tại
  const localUrl = configService.get<string>(
    'SWAGGER_LOCAL_URL',
    `http://localhost:${configService.get('PORT', 3003)}`,
  );
  const devUrl = configService.get<string>(
    'SWAGGER_DEV_URL',
    'http://localhost:3003',
  );
  const testUrl = configService.get<string>(
    'SWAGGER_TEST_URL',
    'http://*************:3003',
  );
  const stagingUrl = configService.get<string>(
    'SWAGGER_STAGING_URL',
    'https://api-staging.redai.vn',
  );
  const prodUrl = configService.get<string>(
    'SWAGGER_PROD_URL',
    'https://v2.redai.vn/api',
  );

  return (
    new DocumentBuilder()
      .setTitle('RedAI API')
      .setDescription(
        `
# RedAI API Documentation v1.0

Welcome to the RedAI API documentation. This API provides endpoints for managing tests, users, and blogs.

## API Version
Current version: v1

## Base URL
All API endpoints are prefixed with: \`/api/v1\`

## Authentication
Most endpoints require authentication. To authenticate, you need to:
1. Obtain a JWT token by logging in
2. Click the "Authorize" button at the top of this page
3. Enter your token in the format: \`Bearer your_token_here\`
4. Click "Authorize" and close the dialog

### Authentication for Protected Endpoints
Many endpoints require authentication. For these endpoints, you must provide a valid JWT token.

Public endpoints (like login, register, and those with 'public' in the path) do not require authentication.

## Rate Limiting
API calls are subject to rate limiting. Please refer to the response headers for rate limit information.

## Error Handling
The API uses standard HTTP status codes to indicate the success or failure of requests.
Common error codes:
- 400: Bad Request - The request was malformed or contains invalid parameters
- 401: Unauthorized - Authentication is required or has failed
- 403: Forbidden - The authenticated user does not have permission to access the requested resource
- 404: Not Found - The requested resource does not exist
- 429: Too Many Requests - Rate limit exceeded
- 500: Internal Server Error - An unexpected error occurred on the server
  `,
      )
      .setVersion('1.0')
      .setContact(
        'RedAI Support',
        'https://redai.com/support',
        '<EMAIL>',
      )
      .setLicense('MIT', 'https://opensource.org/licenses/MIT')
      .setExternalDoc('Additional Documentation', 'https://redai.com/docs')
      .addServer(localUrl, 'Local Server')
      .addServer(devUrl, 'Development Server')
      .addServer(testUrl, 'Test Server')
      .addServer(stagingUrl, 'Staging Server')
      .addServer(prodUrl, 'Production Server')
      .addBearerAuth(
        {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
          name: 'JWT',
          description: 'Enter JWT token',
          in: 'header',
        },
        'JWT-auth',
      )
      .addApiKey(
        {
          type: 'apiKey',
          name: 'x-api-key', // tên header chứa API Key
          in: 'header', // hoặc 'query' nếu bạn muốn nhận qua query param
        },
        'api-key', // tên này PHẢI khớp với @ApiSecurity('api-key')
      )
      .addTag(SWAGGER_API_TAGS.USERS, 'User endpoints')
      .addTag(SWAGGER_API_TAGS.COMMON, 'Common endpoints')
      .addTag(
        SWAGGER_API_TAGS.PUBLIC_CUSTOMER_PRODUCTS,
        'Public customer product endpoints (API Key required)',
      )
      .addTag(
        SWAGGER_API_TAGS.PUBLIC_ORDERS,
        'Public order endpoints (API Key required)',
      )
      .addTag(
        SWAGGER_API_TAGS.PAYMENT_OCB_INTEGRATION,
        'Payment - OCB Integration',
      )
      .addTag(
        SWAGGER_API_TAGS.PAYMENT_ACB_INTEGRATION,
        'Payment - ACB Integration',
      )
      .addTag(
        SWAGGER_API_TAGS.PAYMENT_KLB_INTEGRATION,
        'Payment - KLB Integration',
      )
      .addTag(SWAGGER_API_TAGS.ADMIN_USERS, 'Admin user management endpoints')
      .addTag(SWAGGER_API_TAGS.EMPLOYEES, 'Employee management endpoints')
      .addTag(
        SWAGGER_API_TAGS.PAYMENT_MB_INTEGRATION,
        'Payment - MB Integration',
      )
      .addTag(
        SWAGGER_API_TAGS.EMPLOYEE_ROLES,
        'Employee role management endpoints',
      )
      .addTag(SWAGGER_API_TAGS.TESTS, 'Test endpoints')
      .addTag(SWAGGER_API_TAGS.USER_SMS_TEMPLATE, 'User SMS template endpoints')
      .addTag(SWAGGER_API_TAGS.ADMIN_TESTS, 'Admin test management endpoints')
      .addTag(SWAGGER_API_TAGS.ADMIN_FILES, 'Admin file management endpoints')
      .addTag(
        SWAGGER_API_TAGS.ADMIN_SETTINGS,
        'Admin settings management endpoints',
      )
      .addTag(SWAGGER_API_TAGS.RESOURCES, 'Resource management endpoints')
      .addTag(SWAGGER_API_TAGS.BANKS, 'Bank management endpoints')
      .addTag(SWAGGER_API_TAGS.USER_BANKS, 'User bank management endpoints')
      // Add the blog-related tags here
      .addTag(SWAGGER_API_TAGS.BLOGS, 'Blog endpoints')
      .addTag(SWAGGER_API_TAGS.BLOG_COMMENTS, 'Blog comment endpoints')
      .addTag(SWAGGER_API_TAGS.BLOG_PURCHASES, 'Blog purchase endpoints')
      .addTag(SWAGGER_API_TAGS.ADMIN_BLOGS, 'Admin blog management endpoints')
      .addTag(
        SWAGGER_API_TAGS.USER_DEVICE_INFO,
        'Admin blog management endpoints',
      )
      .addTag(
        SWAGGER_API_TAGS.USER_AUTH_VERIFICATION_LOG,
        'User auth verification log endpoints',
      )
      .addTag(
        SWAGGER_API_TAGS.USER_SUBSCRIPTIONS,
        'User subscription endpoints',
      )
      .addTag(
        SWAGGER_API_TAGS.ADMIN_SUBSCRIPTION_PLAN,
        'Admin subscription plan management endpoints',
      )
      .addTag(
        SWAGGER_API_TAGS.ADMIN_SUBSCRIPTION_PLAN_PRICING,
        'Admin subscription plan pricing management endpoints',
      )
      .addTag(
        SWAGGER_API_TAGS.ADMIN_SUBSCRIPTION,
        'Admin subscription management endpoints',
      )
      .addTag(
        SWAGGER_API_TAGS.ADMIN_SUBSCRIPTION_ORDER,
        'Admin subscription order management endpoints',
      )
      .addTag(
        SWAGGER_API_TAGS.ADMIN_SUBSCRIPTION_PAYMENT,
        'Admin subscription payment management endpoints',
      )
      .addTag(SWAGGER_API_TAGS.DATA_STATISTICS, 'Data statistics endpoints')
      .addTag(
        SWAGGER_API_TAGS.USER_TEMPLATE_EMAIL,
        'User template email management endpoints',
      )
      .addTag(SWAGGER_API_TAGS.USER_STRATEGY, 'User strategy endpoints')
      .addTag(
        SWAGGER_API_TAGS.ADMIN_STRATEGY,
        'Admin strategy management endpoints',
      )
      .addTag(SWAGGER_API_TAGS.INTEGRATION, 'Integration endpoints')
      .addTag(
        SWAGGER_API_TAGS.INTEGRATION_ADMIN,
        'Admin integration management endpoints',
      )
      .addTag(
        SWAGGER_API_TAGS.USER_KNOWLEDGE_FILES,
        'User knowledge files endpoints',
      )
      .addTag(
        SWAGGER_API_TAGS.ADMIN_KNOWLEDGE_FILES,
        'Admin knowledge files management endpoints',
      )
      .addTag(SWAGGER_API_TAGS.USER_AUDIENCE, 'User audience endpoints')
      .addTag(SWAGGER_API_TAGS.USER_CAMPAIGN, 'User campaign endpoints')
      .addTag(
        SWAGGER_API_TAGS.USER_EMAIL_CAMPAIGN,
        'User email campaign endpoints',
      )
      .addTag(
        SWAGGER_API_TAGS.USER_TEMPLATE_EMAIL,
        'User template email endpoints',
      )
      .addTag(SWAGGER_API_TAGS.ZALO_VIDEO_UPLOAD, 'Zalo video upload endpoints')
      .addTag(SWAGGER_API_TAGS.ZALO_UPLOAD, 'Zalo file upload endpoints')
      .addTag(
        SWAGGER_API_TAGS.ADMIN_SYSTEM_CONFIGURATION,
        'Admin system configuration endpoints',
      )
      .addTag(
        SWAGGER_API_TAGS.USER_AFFILIATE_ORDER,
        'User affiliate order endpoints',
      )
      .addTag(SWAGGER_API_TAGS.USER_SEGMENT, 'User segment endpoints')
      .addTag(SWAGGER_API_TAGS.USER_TAG, 'User tag endpoints')
      .addTag(SWAGGER_API_TAGS.USER_SMS_CAMPAIGN, 'User SMS campaign endpoints')
      .addTag(SWAGGER_API_TAGS.USER_ACCOUNT, 'User account endpoints')
      .addTag(
        SWAGGER_API_TAGS.ADMIN_AFFILIATE_ACCOUNT,
        'Admin affiliate account management endpoints',
      )
      .addTag(
        SWAGGER_API_TAGS.USER_AFFILIATE_POINT_CONVERSION,
        'User affiliate point conversion endpoints',
      )
      .addTag(
        SWAGGER_API_TAGS.ADMIN_AFFILIATE_ORDER,
        'Admin affiliate order management endpoints',
      )
      .addTag(
        SWAGGER_API_TAGS.ADMIN_AFFILIATE_CUSTOMER,
        'Admin affiliate customer management endpoints',
      )
      .addTag(
        SWAGGER_API_TAGS.ADMIN_AFFILIATE_OVERVIEW,
        'Admin affiliate overview management endpoints',
      )
      .addTag(
        SWAGGER_API_TAGS.ADMIN_AFFILIATE_RANK,
        'Admin affiliate rank management endpoints',
      )
      .addTag(
        SWAGGER_API_TAGS.USER_MARKETING_STATISTICS,
        'User marketing statistics endpoints',
      )
      .addTag(
        SWAGGER_API_TAGS.ZALO_GROUP_MANAGEMENT,
        'Zalo group management endpoints',
      )
      .addTag(SWAGGER_API_TAGS.ADMIN_USER, 'Admin user endpoints')
      .addTag(
        SWAGGER_API_TAGS.ADMIN_SEGMENT,
        'Admin segment management endpoints',
      )
      .addTag(SWAGGER_API_TAGS.ADMIN_TAG, 'Admin tag management endpoints')
      .addTag(
        SWAGGER_API_TAGS.ADMIN_AUDIENCE,
        'Admin audience management endpoints',
      )
      .addTag(
        SWAGGER_API_TAGS.ADMIN_TEMPLATE_EMAIL,
        'Admin template email management endpoints',
      )
      .addTag(
        SWAGGER_API_TAGS.ADMIN_TEMPLATE_SMS,
        'Admin template SMS management endpoints',
      )
      .addTag(
        SWAGGER_API_TAGS.ADMIN_EMAIL_CAMPAIGN,
        'Admin email campaign management endpoints',
      )
      .addTag(
        SWAGGER_API_TAGS.ADMIN_CAMPAIGN,
        'Admin campaign management endpoints',
      )
      .addTag(
        SWAGGER_API_TAGS.ADMIN_RULE_CONTRACT,
        'Admin rule contract endpoints',
      )
      .addTag(
        SWAGGER_API_TAGS.USER_INTEGRATION_FPT_SMS_BRAND_NAME,
        'User integration FPT SMS Brand Name endpoints',
      )
      .addTag(
        SWAGGER_API_TAGS.USER_RULE_CONTRACT,
        'User rule contract endpoints',
      )
      .addTag(SWAGGER_API_TAGS.USER_TYPE_AGENT, 'User type agent endpoints')
      .addTag(
        SWAGGER_API_TAGS.ADMIN_TYPE_AGENT,
        'Admin type agent management endpoints',
      )
      .addTag(SWAGGER_API_TAGS.MODEL_TRAINING, 'Model training endpoints')
      .addTag(SWAGGER_API_TAGS.ADMIN_MEDIA, 'Admin media management endpoints')
      .addTag(SWAGGER_API_TAGS.ADMIN_URL, 'Admin URL management endpoints')
      .addTag(
        SWAGGER_API_TAGS.ADMIN_INTEGRATION_SMS_TEST,
        'Admin integration SMS test endpoints',
      )
      .addTag(SWAGGER_API_TAGS.R_POINT_ADMIN_COUPONS, 'R-Point - Admin Coupons')
      .addTag(SWAGGER_API_TAGS.R_POINT_ADMIN_POINTS, 'R-Point - Admin Points')
      .addTag(SWAGGER_API_TAGS.PAYMENT_R_POINT_USER, 'Payment - R-Point - User')
      .addTag(SWAGGER_API_TAGS.RPOINT_DASHBOARD, 'R-Point - Dashboard')
      .addTag(
        SWAGGER_API_TAGS.R_POINT_ADMIN_DASHBOARD,
        'R-Point - Admin Dashboard',
      )
      .addTag(
        SWAGGER_API_TAGS.ADMIN_RULE_CONTRACT,
        'Admin rule contract endpoints',
      )
      .addTag(SWAGGER_API_TAGS.USER_MEDIA, 'User media endpoints')
      .addTag(SWAGGER_API_TAGS.ADMIN_TASK, 'Admin task management endpoints')
      .addTag(SWAGGER_API_TAGS.USER_TASK, 'User task endpoints')
      .addTag(SWAGGER_API_TAGS.USER_BUSINESS, 'User business endpoints')
      .addTag(SWAGGER_API_TAGS.USER_ADDRESS, 'User address endpoints')
      .addTag(
        SWAGGER_API_TAGS.USER_MARKETING_CUSTOM_FIELD,
        'User marketing custom field endpoints',
      )
      .addTag(
        SWAGGER_API_TAGS.ADMIN_MARKETING_CUSTOM_FIELD,
        'Admin marketing custom field management endpoints',
      )
      .addTag(
        SWAGGER_API_TAGS.ADMIN_MARKETING_CUSTOM_FIELD,
        'Admin marketing custom field management endpoints',
      )
      .addTag(
        SWAGGER_API_TAGS.USER_INTEGRATION_TWILIO_SMS,
        'User integration Twilio SMS endpoints',
      )
      .addTag(
        SWAGGER_API_TAGS.ZALO_OAUTH_INTEGRATION,
        'Zalo OAuth Integration endpoints',
      )
      .addTag(SWAGGER_API_TAGS.ZALO_WEBHOOK, 'Zalo Webhook endpoints')
      .addTag(SWAGGER_API_TAGS.ZALO_TAG, 'Zalo Tag management endpoints')
      .addTag(SWAGGER_API_TAGS.ZALO_ZNS, 'Zalo ZNS management endpoints')
      .addTag(
        SWAGGER_API_TAGS.ZALO_OA_MESSAGE_CAMPAIGN,
        'Zalo OA Message Campaign endpoints',
      )
      .addTag(
        SWAGGER_API_TAGS.USER_MEMORIES,
        'User memories management endpoints',
      )
      .addTag(SWAGGER_API_TAGS.INTEGRATION_GMAIL, 'Integration Gmail endpoints')
      .addTag(SWAGGER_API_TAGS.MARKETING_GMAIL, 'Marketing Gmail endpoints')
      .addTag(
        SWAGGER_API_TAGS.USER_AGENT_MEMORIES,
        'User agent memories management endpoints',
      )
      .addTag(
        SWAGGER_API_TAGS.ZALO_PERSONAL_DIRECT,
        'Zalo Personal Direct API endpoints',
      )
      .addTag(
        SWAGGER_API_TAGS.ADMIN_AGENT_MEMORIES,
        'Admin agent memories management endpoints',
      )
      .addTag(
        SWAGGER_API_TAGS.ADMIN_USER_ANALYTICS,
        'Admin user analytics and statistics endpoints',
      )
      .addTag(SWAGGER_API_TAGS.ADDRESS, 'Address management endpoints')
      .addTag(SWAGGER_API_TAGS.GHN_ADDRESS, 'GHN address management endpoints')
      .addTag(SWAGGER_API_TAGS.ZALO_OA, 'Zalo OA endpoints')
      .addTag(
        SWAGGER_API_TAGS.ZALO_CONSULTATION,
        'Zalo consultation messaging endpoints',
      )
      .addTag(
        SWAGGER_API_TAGS.ZALO_CONVERSATION,
        'Zalo conversation messaging endpoints',
      )
      .addTag(
        SWAGGER_API_TAGS.ZALO_MESSAGE_SSE,
        'Zalo message SSE real-time endpoints',
      )
      .addTag(
        SWAGGER_API_TAGS.ZALO_CONTENT_MANAGEMENT,
        'Zalo content management endpoints',
      )
      .addTag(SWAGGER_API_TAGS.ZALO_CONTENT, 'Zalo content endpoints')
      .addTag(
        SWAGGER_API_TAGS.ZALO_PROMOTION,
        'Zalo promotion messaging endpoints',
      )
      .addTag(SWAGGER_API_TAGS.ZALO_TAG, 'Zalo tag management endpoints')
      .addTag(
        SWAGGER_API_TAGS.ZALO_TEMPLATE,
        'Zalo template management endpoints',
      )
      .addTag(
        SWAGGER_API_TAGS.ZALO_TRANSACTION,
        'Zalo transaction management endpoints',
      )
      .addTag(
        SWAGGER_API_TAGS.USER_EMAIL_TRACKING,
        'User email tracking endpoints',
      )
      .addTag(
        SWAGGER_API_TAGS.USER_MARKETING_CUSTOM_FIELD_DEFINITION,
        'User marketing custom field definition endpoints',
      )
      .addTag(
        SWAGGER_API_TAGS.ZALO_AUDIENCE_SYNC,
        'Zalo audience synchronization endpoints',
      )
      .addTag(SWAGGER_API_TAGS.ZALO_STATISTICS, 'Zalo statistics endpoints')
      .addTag(
        SWAGGER_API_TAGS.ZALO_GROUP_MESSAGE,
        'Zalo group message endpoints',
      )
      .addTag(
        SWAGGER_API_TAGS.ZALO_SEGMENTS,
        'Zalo segments management endpoints',
      )
      .addTag(
        SWAGGER_API_TAGS.ZALO_FOLLOWERS,
        'Zalo followers management endpoints',
      )
      .addTag(
        SWAGGER_API_TAGS.ZALO_ARTICLE,
        'Zalo article management endpoints',
      )
      .addTag(
        SWAGGER_API_TAGS.ZALO_BROADCAST,
        'Zalo broadcast message endpoints',
      )
      .addTag(
        SWAGGER_API_TAGS.ZALO_UNIFIED_MESSAGE,
        'Zalo unified message endpoints',
      )
      .addTag(
        SWAGGER_API_TAGS.GHN_SHIPMENT,
        'GHN shipment management endpoints',
      )
      .addTag(
        SWAGGER_API_TAGS.GHTK_SHIPMENT,
        'GHTK shipment management endpoints',
      )
      .addTag(
        SWAGGER_API_TAGS.USER_INVENTORY,
        'User inventory management endpoints',
      )
      .addTag(
        SWAGGER_API_TAGS.USER_ORDER_TRACKING,
        'User order tracking endpoints',
      )
      .addTag(
        SWAGGER_API_TAGS.USER_SHOP_ADDRESS,
        'User shop address management endpoints',
      )
      .addTag(
        SWAGGER_API_TAGS.USER_WAREHOUSE,
        'User warehouse management endpoints',
      )
      .addTag(
        SWAGGER_API_TAGS.USER_PHYSICAL_WAREHOUSE,
        'User physical warehouse management endpoints',
      )
      .addTag(SWAGGER_API_TAGS.USER_HELP_CENTER, 'User help center endpoints')
      .addTag(
        SWAGGER_API_TAGS.ADMIN_HELP_CENTER,
        'Admin help center management endpoints',
      )
      .addTag(
        SWAGGER_API_TAGS.DASHBOARD_PAGE,
        'Dashboard page management endpoints',
      )
      .addTag(
        SWAGGER_API_TAGS.DASHBOARD_WIDGET,
        'Dashboard widget management endpoints',
      )
      .addTag(
        SWAGGER_API_TAGS.ADMIN_MCP_SYSTEM,
        'Admin MCP system management endpoints',
      )
      .addTag(
        SWAGGER_API_TAGS.ADMIN_FINETUNING_JOB,
        'Admin fine-tuning job management endpoints',
      )
      .addTag(
        SWAGGER_API_TAGS.USER_WORKFLOW,
        'User workflow management endpoints',
      )
      .addTag(
        SWAGGER_API_TAGS.ADMIN_WORKFLOW,
        'Admin workflow management endpoints',
      )
      .addTag(
        SWAGGER_API_TAGS.ADMIN_SYSTEM_TEMPLATE_EMAIL,
        'Admin system template email management endpoints',
      )
      .addTag(
        SWAGGER_API_TAGS.USER_SYSTEM_TEMPLATE_EMAIL,
        'User system template email endpoints',
      )
      .addTag(
        SWAGGER_API_TAGS.ADMIN_WEBHOOK_EVENTS,
        'Admin webhook events management endpoints',
      )
      .addTag(
        SWAGGER_API_TAGS.USER_SIGNATURE,
        'User signature management endpoints',
      )
      .addTag(
        SWAGGER_API_TAGS.ADMIN_SIGNATURE,
        'Admin signature management endpoints',
      )
      .addTag(
        SWAGGER_API_TAGS.I18N_EXCEPTION_EXAMPLE,
        'I18n Exception Example endpoints for testing',
      )
      .addTag(
        SWAGGER_API_TAGS.MARKETPLACE_FLASH_SALE,
        'Marketplace flash sale endpoints',
      )
      .addTag(
        SWAGGER_API_TAGS.ADMIN_MARKETPLACE_FLASH_SALE,
        'Admin marketplace flash sale endpoints',
      )
      .addTag(
        SWAGGER_API_TAGS.MEDIA_TRACKING,
        'Media and file progress tracking SSE endpoints',
      )
      .addTag(
        SWAGGER_API_TAGS.ADMIN_MARKETPLACE_ANALYTICS,
        'Admin marketplace analytics endpoints',
      )
      .addTag(SWAGGER_API_TAGS.CALENDAR_EVENTS, 'Quản lý sự kiện lịch')
      .addTag(SWAGGER_API_TAGS.CALENDAR_TASKS, 'Quản lý nhiệm vụ lịch')
      .addTag(SWAGGER_API_TAGS.CALENDAR_REPORTS, 'Quản lý báo cáo lịch')
      .addTag(SWAGGER_API_TAGS.CALENDAR_REMINDERS, 'Quản lý nhắc nhở lịch')
      .addTag(SWAGGER_API_TAGS.CALENDAR_ATTENDEES, 'Quản lý người tham dự lịch')
      .addTag(SWAGGER_API_TAGS.CALENDAR_RESOURCES, 'Quản lý tài nguyên lịch')
      .addTag(SWAGGER_API_TAGS.CALENDAR_RECURRANCES, 'Quản lý lịch lặp lại')
      .addTag(
        SWAGGER_API_TAGS.BUSINESS_ANALYTICS_DASHBOARD,
        'Business analytics dashboard endpoints',
      )
      .addTag(
        SWAGGER_API_TAGS.ADMIN_ANALYTICS_DASHBOARD,
        'Admin analytics dashboard endpoints',
      )
      .addTag(
        SWAGGER_API_TAGS.BUSINESS_ANALYTICS_SALES,
        'Business analytics sales endpoints',
      )
      .addTag(
        SWAGGER_API_TAGS.ADMIN_ANALYTICS_SALES,
        'Admin analytics sales endpoints',
      )
      .addTag(SWAGGER_API_TAGS.BUSINESS_WEBHOOK, 'Business webhook endpoints')
      .build()
  );
};

export const swaggerCustomOptions: SwaggerCustomOptions = {
  swaggerOptions: {
    persistAuthorization: true,
    docExpansion: 'none',
    filter: true,
    tagsSorter: 'alpha',
    operationsSorter: 'alpha',
    defaultModelsExpandDepth: 1,
    defaultModelExpandDepth: 1,
    tryItOutEnabled: true, // Enable Try it out by default
    displayRequestDuration: true, // Show request duration
    showExtensions: true,
    showCommonExtensions: true,
  },
  customCss: `
  /* Thay nền topbar thành trắng, chữ và icon đỏ */
  .swagger-ui .topbar {
    background-color: #e53e3e;
    color: #ffffff;
  }
  .swagger-ui .topbar .download-url-wrapper .select-label select {
    border-color: #e53e3e;
    color: #e53e3e;
  }

  /* Tiêu đề API màu đỏ */
  .swagger-ui .info .title {
    color: #e53e3e;
  }

  /* Các opblock (GET, POST, PUT, DELETE, PATCH) chung nền trắng, viền đỏ nhạt */
  .swagger-ui .opblock {
    background: rgba(229, 62, 62, 0.1);
    border-color: #e53e3e;
  }
  .swagger-ui .opblock .opblock-summary-method {
    color: #ffffff;
  }

  /* Nút Execute màu đỏ tươi trên nền trắng */
  .swagger-ui .btn.execute {
    background-color: #e53e3e;
    color: #ffffff;
    border-color: #c53030;
  }
  .swagger-ui .btn.execute:hover {
    background-color: #c53030;
  }

  /* Các đường kẻ, nút mở rộng cũng về tông xám nhạt cho hài hòa */
  .swagger-ui .opblock .opblock-summary {
    border-bottom-color: #fde8e8;
  }
  .swagger-ui .parameter__name, .swagger-ui .response-col_status {
    color: #c53030;
  }
    `,
  customSiteTitle: 'RedAI API Documentation',
  customfavIcon: 'https://redai.com/favicon.ico',
};

import {
  <PERSON>umn,
  <PERSON>ti<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>To<PERSON>ne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { User } from '@modules/user/entities/user.entity';
import { OrderStatusEnum, ShippingStatusEnum } from '../enums';
import { BillInfo, PaymentInfo } from '../interfaces';

/**
 * Entity đại diện cho bảng user_orders trong cơ sở dữ liệu
 * Bảng quản lý đơn hàng của người dùng
 */
@Entity('user_orders')
export class UserOrder {
  /**
   * ID của đơn hàng
   */
  @PrimaryGeneratedColumn('uuid', { name: 'id' })
  id: string;

  /**
   * ID của người dùng sở hữu đơn hàng
   */
  @Column({
    name: 'user_id',
    type: 'integer',
    nullable: true,
    comment: 'Người dùng sở hữu đơn hàng',
  })
  userId: number;

  /**
   * Thông tin sản phẩm
   */
  @Column({
    name: 'product_info',
    type: 'jsonb',
    nullable: true,
    comment: 'Thông tin sản phẩm (JSON)',
  })
  productInfo: Record<string, unknown>;

  /**
   * Thông tin thanh toán
   */
  @Column({
    name: 'payment_info',
    type: 'jsonb',
    nullable: true,
    comment: 'Thông tin thanh toán (JSON)',
  })
  paymentInfo: PaymentInfo;

  /**
   * Thông tin hóa đơn
   */
  @Column({
    name: 'bill_info',
    type: 'jsonb',
    nullable: true,
    comment: 'Thông tin hóa đơn (JSON)',
  })
  billInfo: BillInfo;

  /**
   * Đơn hàng có yêu cầu vận chuyển hay không
   */
  @Column({
    name: 'has_shipping',
    type: 'boolean',
    default: true,
    nullable: false,
    comment: 'Đơn hàng có yêu cầu vận chuyển hay không',
  })
  hasShipping: boolean;

  /**
   * Trạng thái vận chuyển
   */
  @Column({
    name: 'shipping_status',
    type: 'enum',
    enum: ShippingStatusEnum,
    nullable: true,
    comment: 'Trạng thái vận chuyển (ví dụ: pending, shipped, delivered)',
  })
  shippingStatus: ShippingStatusEnum | null;

  /**
   * Thông tin vận chuyển chi tiết
   */
  @Column({
    name: 'logistic_info',
    type: 'jsonb',
    nullable: true,
    comment: 'Thông tin vận chuyển chi tiết (JSON)',
  })
  logisticInfo: Record<string, unknown>;

  /**
   * Thời gian tạo đơn hàng
   */
  @Column({
    name: 'created_at',
    type: 'bigint',
    nullable: false,
    default: () => '(EXTRACT(epoch FROM now()) * 1000)::bigint',
    comment: 'Thời gian tạo đơn hàng',
  })
  createdAt: number;

  /**
   * Thời gian cập nhật đơn hàng
   */
  @Column({
    name: 'updated_at',
    type: 'bigint',
    nullable: false,
    default: () => '(EXTRACT(epoch FROM now()) * 1000)::bigint',
    comment: 'Thời gian cập nhật đơn hàng',
  })
  updatedAt: number;

  /**
   * Nguồn đơn hàng
   */
  @Column({
    name: 'source',
    length: 45,
    nullable: true,
    comment: 'Nguồn đơn hàng',
  })
  source: string;

  /**
   * Trạng thái đơn hàng
   */
  @Column({
    name: 'order_status',
    type: 'enum',
    enum: OrderStatusEnum,
    nullable: true,
    comment:
      'Trạng thái đơn hàng (ENUM: pending, confirmed, processing, completed, cancelled)',
  })
  orderStatus: OrderStatusEnum;

  /**
   * Email khách hàng
   */
  @Column({
    name: 'convert_customer_email',
    length: 255,
    nullable: true,
    comment: 'Email khách hàng',
  })
  convertCustomerEmail: string;

  /**
   * Số điện thoại khách hàng
   */
  @Column({
    name: 'convert_customer_phone',
    length: 20,
    nullable: true,
    comment: 'Số điện thoại khách hàng',
  })
  convertCustomerPhone: string;

  /**
   * Mã quốc gia
   */
  @Column({
    name: 'country_code',
    type: 'smallint',
    nullable: true,
    comment: 'Mã quốc gia',
  })
  countryCode: number;

  /**
   * Tên khách hàng
   */
  @Column({
    name: 'convert_customer_name',
    length: 255,
    nullable: true,
    comment: 'Tên khách hàng',
  })
  convertCustomerName: string;

  /**
   * Quan hệ với User
   */
  @ManyToOne(() => User)
  @JoinColumn({ name: 'user_id' })
  user: User;
}

import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsString,
  IsOptional,
  IsDateString,
  IsBoolean,
  IsEnum,
  IsArray,
  ValidateNested,
  IsUUID,
  IsNumber,
  Min,
  Max,
  IsObject,
} from 'class-validator';
import { Type, Transform } from 'class-transformer';
import {
  CalendarActionType,
  CalendarPriority,
  CalendarEventStatus,
} from '../entities';

/**
 * DTO tạo sự kiện calendar
 */
export class CreateCalendarEventDto {
  @ApiProperty({
    description: 'Loại action calendar',
    enum: CalendarActionType,
  })
  @IsEnum(CalendarActionType)
  actionType: CalendarActionType;

  @ApiProperty({ description: 'Tiêu đề sự kiện', maxLength: 255 })
  @IsString()
  title: string;

  @ApiPropertyOptional({ description: 'Mô tả sự kiện' })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ description: 'Thời gian bắ<PERSON> đầ<PERSON> (ISO 8601)' })
  @IsDateString()
  startTime: string;

  @ApiPropertyOptional({ description: 'Thời gian kết thúc (ISO 8601)' })
  @IsOptional()
  @IsDateString()
  endTime?: string;

  @ApiPropertyOptional({ description: 'Múi giờ', default: 'Asia/Ho_Chi_Minh' })
  @IsOptional()
  @IsString()
  timeZone?: string;

  @ApiPropertyOptional({
    description: 'Mức độ ưu tiên',
    enum: CalendarPriority,
    default: CalendarPriority.MEDIUM,
  })
  @IsOptional()
  @IsEnum(CalendarPriority)
  priority?: CalendarPriority;

  @ApiProperty({
    description:
      'Cấu hình action cụ thể (TASK: {agentId, taskId, resources}, REMINDER: {channels, message, recipients}, REPORT: {reportType, config, recipients})',
    type: 'object',
    additionalProperties: true,
  })
  @IsObject()
  actionConfig: any;

  @ApiPropertyOptional({ description: 'Metadata bổ sung' })
  @IsOptional()
  metadata?: any;
}

/**
 * DTO cập nhật sự kiện calendar
 */
export class UpdateCalendarEventDto {
  @ApiPropertyOptional({ description: 'Tiêu đề sự kiện', maxLength: 255 })
  @IsOptional()
  @IsString()
  title?: string;

  @ApiPropertyOptional({ description: 'Mô tả sự kiện' })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({ description: 'Thời gian bắt đầu (ISO 8601)' })
  @IsOptional()
  @IsDateString()
  startTime?: string;

  @ApiPropertyOptional({ description: 'Thời gian kết thúc (ISO 8601)' })
  @IsOptional()
  @IsDateString()
  endTime?: string;

  @ApiPropertyOptional({ description: 'Múi giờ' })
  @IsOptional()
  @IsString()
  timeZone?: string;

  @ApiPropertyOptional({
    description: 'Trạng thái sự kiện',
    enum: CalendarEventStatus,
  })
  @IsOptional()
  @IsEnum(CalendarEventStatus)
  status?: CalendarEventStatus;

  @ApiPropertyOptional({
    description: 'Mức độ ưu tiên',
    enum: CalendarPriority,
  })
  @IsOptional()
  @IsEnum(CalendarPriority)
  priority?: CalendarPriority;

  @ApiPropertyOptional({
    description: 'Cấu hình action cụ thể',
    type: 'object',
    additionalProperties: true,
  })
  @IsOptional()
  @IsObject()
  actionConfig?: any;

  @ApiPropertyOptional({ description: 'Metadata bổ sung' })
  @IsOptional()
  metadata?: any;
}

/**
 * DTO thông tin người tham gia
 */
export class AttendeeDto {
  @ApiProperty({ description: 'Email người tham gia' })
  @IsString()
  email: string;

  @ApiPropertyOptional({ description: 'Tên người tham gia' })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiPropertyOptional({ description: 'Có bắt buộc tham gia', default: true })
  @IsOptional()
  @IsBoolean()
  isRequired?: boolean;

  @ApiPropertyOptional({ description: 'Có gửi thông báo', default: true })
  @IsOptional()
  @IsBoolean()
  sendNotifications?: boolean;
}

/**
 * DTO thông tin tài nguyên
 */
export class ResourceDto {
  @ApiProperty({ description: 'Tên tài nguyên' })
  @IsString()
  name: string;

  @ApiProperty({ description: 'Loại tài nguyên' })
  @IsString()
  resourceType: string;

  @ApiPropertyOptional({ description: 'Mô tả tài nguyên' })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({ description: 'ID media' })
  @IsOptional()
  @IsString()
  mediaId?: string;

  @ApiPropertyOptional({ description: 'ID URL' })
  @IsOptional()
  @IsString()
  urlId?: string;

  @ApiPropertyOptional({ description: 'URL truy cập' })
  @IsOptional()
  @IsString()
  url?: string;

  @ApiPropertyOptional({ description: 'Có bắt buộc', default: false })
  @IsOptional()
  @IsBoolean()
  isRequired?: boolean;
}

/**
 * DTO thông tin nhắc nhở
 */
export class ReminderDto {
  @ApiProperty({
    description: 'Thời gian nhắc nhở (phút trước sự kiện)',
    minimum: 0,
  })
  @IsNumber()
  @Min(0)
  reminderMinutes: number;

  @ApiProperty({ description: 'Kênh thông báo' })
  @IsString()
  channelType: string;

  @ApiPropertyOptional({ description: 'Cấu hình kênh' })
  @IsOptional()
  channelConfig?: any;

  @ApiPropertyOptional({ description: 'Tiêu đề thông báo tùy chỉnh' })
  @IsOptional()
  @IsString()
  customTitle?: string;

  @ApiPropertyOptional({ description: 'Nội dung thông báo tùy chỉnh' })
  @IsOptional()
  @IsString()
  customMessage?: string;
}

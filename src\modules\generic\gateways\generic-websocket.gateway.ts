import {
  WebSocketGateway,
  WebSocketServer,
  SubscribeMessage,
  MessageBody,
  ConnectedSocket,
  OnGatewayConnection,
  OnGatewayDisconnect,
  OnGatewayInit,
} from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { Logger } from '@nestjs/common';
import {
  GenericSessionService,
  GenericWidgetService,
  GenericLayoutService,
} from '../services';
import {
  ConnectEventDto,
  AddWidgetEventDto,
  RemoveWidgetEventDto,
  UpdateLayoutEventDto,
  SyncStateEventDto,
  WebSocketEventType,
} from '../dto/generic-websocket.dto';

/**
 * WebSocket Gateway cho Generic Page Dynamic Control
 * Xử lý real-time communication cho generic page widget management
 */
@WebSocketGateway({
  namespace: '/generic',
  cors: {
    origin: '*',
    credentials: true,
  },
})
export class GenericWebSocketGateway
  implements OnGatewayInit, OnGatewayConnection, OnGatewayDisconnect
{
  @WebSocketServer()
  server: Server;

  private readonly logger = new Logger(GenericWebSocketGateway.name);
  private readonly sessionConnections = new Map<string, Set<Socket>>(); // sessionId -> Set of sockets

  constructor(
    private readonly genericSessionService: GenericSessionService,
    private readonly genericWidgetService: GenericWidgetService,
    private readonly genericLayoutService: GenericLayoutService,
  ) {}

  afterInit(server: Server) {
    this.logger.log('🚀 Generic WebSocket Gateway initialized');
  }

  async handleConnection(client: Socket) {
    try {
      const sessionId = client.handshake.query.sessionId as string;

      if (!sessionId) {
        this.logger.warn(`Client ${client.id} connected without sessionId`);
        client.emit('error', {
          type: WebSocketEventType.ERROR,
          payload: {
            message: 'SessionId is required',
            code: 'MISSING_SESSION_ID',
          },
          sessionId: null,
          timestamp: new Date().toISOString(),
        });
        client.disconnect();
        return;
      }

      // Kiểm tra session có tồn tại và valid không
      const isValid =
        await this.genericSessionService.isSessionActiveAndValid(sessionId);
      if (!isValid) {
        this.logger.warn(
          `Client ${client.id} tried to connect with invalid session: ${sessionId}`,
        );
        client.emit('error', {
          type: WebSocketEventType.ERROR,
          payload: {
            message: 'Invalid or expired session',
            code: 'INVALID_SESSION',
          },
          sessionId,
          timestamp: new Date().toISOString(),
        });
        client.disconnect();
        return;
      }

      // Thêm client vào session connections
      if (!this.sessionConnections.has(sessionId)) {
        this.sessionConnections.set(sessionId, new Set());
      }
      this.sessionConnections.get(sessionId)!.add(client);

      // Store sessionId in client data
      client.data.sessionId = sessionId;
      client.data.userId = client.handshake.query.userId as string; // Optional user ID

      this.logger.log(`Client ${client.id} connected to session ${sessionId}`);

      // Gửi current state cho client
      await this.sendCurrentState(client, sessionId);

      // Broadcast user joined event nếu có userId
      if (client.data.userId) {
        this.broadcastToSession(sessionId, {
          type: 'user_joined',
          payload: {
            userId: client.data.userId,
            clientId: client.id,
          },
          sessionId,
          timestamp: new Date().toISOString(),
        });
      }
    } catch (error) {
      this.logger.error(
        `Error handling connection: ${error.message}`,
        error.stack,
      );
      client.emit('error', {
        type: WebSocketEventType.ERROR,
        payload: {
          message: 'Connection error',
          code: 'CONNECTION_ERROR',
        },
        sessionId: client.data.sessionId || null,
        timestamp: new Date().toISOString(),
      });
      client.disconnect();
    }
  }

  async handleDisconnect(client: Socket) {
    try {
      const sessionId = client.data.sessionId;
      const userId = client.data.userId;

      if (sessionId && this.sessionConnections.has(sessionId)) {
        this.sessionConnections.get(sessionId)!.delete(client);

        // Broadcast user left event nếu có userId
        if (userId) {
          this.broadcastToSession(sessionId, {
            type: 'user_left',
            payload: {
              userId,
              clientId: client.id,
            },
            sessionId,
            timestamp: new Date().toISOString(),
          });
        }

        // Xóa session khỏi map nếu không còn connections
        if (this.sessionConnections.get(sessionId)!.size === 0) {
          this.sessionConnections.delete(sessionId);
        }
      }

      this.logger.log(
        `Client ${client.id} disconnected from session ${sessionId}`,
      );
    } catch (error) {
      this.logger.error(
        `Error handling disconnect: ${error.message}`,
        error.stack,
      );
    }
  }

  /**
   * Handle add widget event
   */
  @SubscribeMessage('add_widget')
  async handleAddWidget(
    @MessageBody() data: AddWidgetEventDto,
    @ConnectedSocket() client: Socket,
  ) {
    try {
      const sessionId = client.data.sessionId;

      if (!sessionId) {
        throw new Error('No session ID found');
      }

      // Thêm widget vào session
      const widget = await this.genericWidgetService.addWidgetToSession(
        sessionId,
        data.payload.widget,
      );

      // Broadcast widget added event đến tất cả clients trong session
      this.broadcastToSession(sessionId, {
        type: WebSocketEventType.WIDGET_ADDED,
        payload: { widget },
        sessionId,
        timestamp: new Date().toISOString(),
      });

      this.logger.log(
        `Widget ${widget.widgetId} added to session ${sessionId}`,
      );
    } catch (error) {
      this.logger.error(`Error adding widget: ${error.message}`, error.stack);
      client.emit('error', {
        type: WebSocketEventType.ERROR,
        payload: {
          message: error.message,
          code: 'ADD_WIDGET_ERROR',
        },
        sessionId: client.data.sessionId,
        timestamp: new Date().toISOString(),
      });
    }
  }

  /**
   * Handle remove widget event
   */
  @SubscribeMessage('remove_widget')
  async handleRemoveWidget(
    @MessageBody() data: RemoveWidgetEventDto,
    @ConnectedSocket() client: Socket,
  ) {
    try {
      const sessionId = client.data.sessionId;

      if (!sessionId) {
        throw new Error('No session ID found');
      }

      // Xóa widget khỏi session
      await this.genericWidgetService.removeWidgetFromSession(
        sessionId,
        data.payload.widgetId,
      );

      // Broadcast widget removed event đến tất cả clients trong session
      this.broadcastToSession(sessionId, {
        type: WebSocketEventType.WIDGET_REMOVED,
        payload: { widgetId: data.payload.widgetId },
        sessionId,
        timestamp: new Date().toISOString(),
      });

      this.logger.log(
        `Widget ${data.payload.widgetId} removed from session ${sessionId}`,
      );
    } catch (error) {
      this.logger.error(`Error removing widget: ${error.message}`, error.stack);
      client.emit('error', {
        type: WebSocketEventType.ERROR,
        payload: {
          message: error.message,
          code: 'REMOVE_WIDGET_ERROR',
        },
        sessionId: client.data.sessionId,
        timestamp: new Date().toISOString(),
      });
    }
  }

  /**
   * Handle update layout event
   */
  @SubscribeMessage('update_layout')
  async handleUpdateLayout(
    @MessageBody() data: UpdateLayoutEventDto,
    @ConnectedSocket() client: Socket,
  ) {
    try {
      const sessionId = client.data.sessionId;

      if (!sessionId) {
        throw new Error('No session ID found');
      }

      // Cập nhật layout
      await this.genericLayoutService.updateLayoutData(
        sessionId,
        data.payload.layout,
      );

      // Broadcast layout updated event đến tất cả clients trong session
      this.broadcastToSession(sessionId, {
        type: WebSocketEventType.LAYOUT_UPDATED,
        payload: { layout: data.payload.layout },
        sessionId,
        timestamp: new Date().toISOString(),
      });

      this.logger.log(`Layout updated for session ${sessionId}`);
    } catch (error) {
      this.logger.error(`Error updating layout: ${error.message}`, error.stack);
      client.emit('error', {
        type: WebSocketEventType.ERROR,
        payload: {
          message: error.message,
          code: 'UPDATE_LAYOUT_ERROR',
        },
        sessionId: client.data.sessionId,
        timestamp: new Date().toISOString(),
      });
    }
  }

  /**
   * Handle sync state event
   */
  @SubscribeMessage('sync_state')
  async handleSyncState(
    @MessageBody() data: SyncStateEventDto,
    @ConnectedSocket() client: Socket,
  ) {
    try {
      const sessionId = client.data.sessionId;

      if (!sessionId) {
        throw new Error('No session ID found');
      }

      // Gửi current state cho client
      await this.sendCurrentState(client, sessionId);

      this.logger.log(`State synced for session ${sessionId}`);
    } catch (error) {
      this.logger.error(`Error syncing state: ${error.message}`, error.stack);
      client.emit('error', {
        type: WebSocketEventType.ERROR,
        payload: {
          message: error.message,
          code: 'SYNC_STATE_ERROR',
        },
        sessionId: client.data.sessionId,
        timestamp: new Date().toISOString(),
      });
    }
  }

  /**
   * Broadcast message đến tất cả clients trong session
   */
  public broadcastToSession(sessionId: string, message: any) {
    const sessionClients = this.sessionConnections.get(sessionId);
    if (sessionClients) {
      sessionClients.forEach((client) => {
        if (client.connected) {
          client.emit('message', message);
        }
      });
    }
  }

  /**
   * Broadcast message đến tất cả sessions
   */
  public broadcastToAll(message: any) {
    this.server.emit('message', message);
  }

  /**
   * Gửi current state cho client
   */
  private async sendCurrentState(client: Socket, sessionId: string) {
    try {
      // Lấy widgets và layout hiện tại
      const widgets =
        await this.genericWidgetService.getWidgetsBySessionId(sessionId);
      const layout =
        await this.genericLayoutService.getLayoutBySessionIdOptional(sessionId);

      // Gửi state synced event
      client.emit('message', {
        type: WebSocketEventType.STATE_SYNCED,
        payload: {
          widgets,
          layout: layout?.layoutData || [],
        },
        sessionId,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      this.logger.error(
        `Error sending current state: ${error.message}`,
        error.stack,
      );
      client.emit('error', {
        type: WebSocketEventType.ERROR,
        payload: {
          message: 'Failed to get current state',
          code: 'GET_STATE_ERROR',
        },
        sessionId,
        timestamp: new Date().toISOString(),
      });
    }
  }

  /**
   * Handle user activity event (collaboration)
   */
  @SubscribeMessage('user_activity')
  async handleUserActivity(
    @MessageBody()
    data: {
      activity: {
        type: 'cursor_move' | 'widget_select' | 'widget_edit' | 'layout_change';
        data: any;
      };
    },
    @ConnectedSocket() client: Socket,
  ) {
    try {
      const sessionId = client.data.sessionId;
      const userId = client.data.userId;

      if (!sessionId || !userId) {
        throw new Error('No session ID or user ID found');
      }

      // TODO: Implement collaboration service
      this.logger.debug(
        `User activity received: ${userId} - ${data.activity.type} in session ${sessionId}`,
      );

      this.logger.debug(
        `User activity broadcasted: ${userId} - ${data.activity.type} in session ${sessionId}`,
      );
    } catch (error) {
      this.logger.error(
        `Error handling user activity: ${error.message}`,
        error.stack,
      );
      client.emit('error', {
        type: WebSocketEventType.ERROR,
        payload: {
          message: error.message,
          code: 'USER_ACTIVITY_ERROR',
        },
        sessionId: client.data.sessionId,
        timestamp: new Date().toISOString(),
      });
    }
  }

  /**
   * Handle widget lock request
   */
  @SubscribeMessage('lock_widget')
  async handleLockWidget(
    @MessageBody() data: { widgetId: string; lockDuration?: number },
    @ConnectedSocket() client: Socket,
  ) {
    try {
      const sessionId = client.data.sessionId;
      const userId = client.data.userId;

      if (!sessionId || !userId) {
        throw new Error('No session ID or user ID found');
      }

      // TODO: Implement widget locking
      const result = {
        success: true,
        message: 'Widget locked successfully',
        widgetId: data.widgetId,
        lockedBy: parseInt(userId),
        lockDuration: data.lockDuration || 30,
      };

      // Send response to client
      client.emit('message', {
        type: 'widget_lock_response',
        payload: result,
        sessionId,
        timestamp: new Date().toISOString(),
      });

      this.logger.debug(
        `Widget lock request: ${data.widgetId} by user ${userId} - ${result.success ? 'success' : 'failed'}`,
      );
    } catch (error) {
      this.logger.error(
        `Error handling widget lock: ${error.message}`,
        error.stack,
      );
      client.emit('error', {
        type: WebSocketEventType.ERROR,
        payload: {
          message: error.message,
          code: 'WIDGET_LOCK_ERROR',
        },
        sessionId: client.data.sessionId,
        timestamp: new Date().toISOString(),
      });
    }
  }

  /**
   * Handle widget unlock request
   */
  @SubscribeMessage('unlock_widget')
  async handleUnlockWidget(
    @MessageBody() data: { widgetId: string },
    @ConnectedSocket() client: Socket,
  ) {
    try {
      const sessionId = client.data.sessionId;
      const userId = client.data.userId;

      if (!sessionId || !userId) {
        throw new Error('No session ID or user ID found');
      }

      // TODO: Implement widget unlocking
      const result = {
        success: true,
        message: 'Widget unlocked successfully',
        widgetId: data.widgetId,
        unlockedBy: parseInt(userId),
      };

      // Send response to client
      client.emit('message', {
        type: 'widget_unlock_response',
        payload: result,
        sessionId,
        timestamp: new Date().toISOString(),
      });

      this.logger.debug(
        `Widget unlock request: ${data.widgetId} by user ${userId} - ${result.success ? 'success' : 'failed'}`,
      );
    } catch (error) {
      this.logger.error(
        `Error handling widget unlock: ${error.message}`,
        error.stack,
      );
      client.emit('error', {
        type: WebSocketEventType.ERROR,
        payload: {
          message: error.message,
          code: 'WIDGET_UNLOCK_ERROR',
        },
        sessionId: client.data.sessionId,
        timestamp: new Date().toISOString(),
      });
    }
  }

  /**
   * Lấy số lượng connections hiện tại
   */
  public getConnectionCount(): { total: number; sessions: number } {
    let total = 0;
    this.sessionConnections.forEach((clients) => {
      total += clients.size;
    });

    return {
      total,
      sessions: this.sessionConnections.size,
    };
  }
}

import { HttpStatus } from '@nestjs/common';
import { ErrorCode } from '@common/exceptions';

/**
 * Mã lỗi cho module Marketing (20000-20999)
 */
export const MARKETING_ERROR_CODES = {
  // ===== SEEDING GROUP ERRORS (20000-20099) =====
  /**
   * Lỗi khi không tìm thấy seeding group
   */
  SEEDING_GROUP_NOT_FOUND: new ErrorCode(
    20000,
    'Không tìm thấy seeding group',
    HttpStatus.NOT_FOUND,
  ),

  /**
   * Lỗi khi tên seeding group đã tồn tại
   */
  SEEDING_GROUP_NAME_EXISTS: new ErrorCode(
    20001,
    'Tên seeding group đã tồn tại',
    HttpStatus.CONFLICT,
  ),

  /**
   * Lỗi khi nhóm Zalo đã được sử dụng
   */
  SEEDING_GROUP_ZALO_GROUP_IN_USE: new ErrorCode(
    20002,
    '<PERSON>hóm Zalo này đã được sử dụng cho seeding group khác',
    HttpStatus.CONFLICT,
  ),

  /**
   * Lỗi khi không thể xóa seeding group đang hoạt động
   */
  SEEDING_GROUP_CANNOT_DELETE_ACTIVE: new ErrorCode(
    20003,
    'Không thể xóa seeding group đang hoạt động',
    HttpStatus.BAD_REQUEST,
  ),

  /**
   * Lỗi khi seeding group đã đang hoạt động
   */
  SEEDING_GROUP_ALREADY_ACTIVE: new ErrorCode(
    20004,
    'Seeding group đã đang hoạt động',
    HttpStatus.BAD_REQUEST,
  ),

  /**
   * Lỗi khi chỉ có thể tạm dừng seeding group đang hoạt động
   */
  SEEDING_GROUP_CANNOT_PAUSE_INACTIVE: new ErrorCode(
    20005,
    'Chỉ có thể tạm dừng seeding group đang hoạt động',
    HttpStatus.BAD_REQUEST,
  ),

  /**
   * Lỗi khi chỉ có thể dừng seeding group đang hoạt động hoặc tạm dừng
   */
  SEEDING_GROUP_CANNOT_STOP_INVALID_STATUS: new ErrorCode(
    20006,
    'Chỉ có thể dừng seeding group đang hoạt động hoặc tạm dừng',
    HttpStatus.BAD_REQUEST,
  ),

  /**
   * Lỗi khi giờ bắt đầu phải nhỏ hơn giờ kết thúc
   */
  SEEDING_GROUP_INVALID_TIME_RANGE: new ErrorCode(
    20007,
    'Giờ bắt đầu phải nhỏ hơn giờ kết thúc',
    HttpStatus.BAD_REQUEST,
  ),

  /**
   * Lỗi khi phải có ít nhất 1 tài khoản
   */
  SEEDING_GROUP_NO_ACCOUNTS: new ErrorCode(
    20008,
    'Phải có ít nhất 1 tài khoản',
    HttpStatus.BAD_REQUEST,
  ),

  // ===== CAMPAIGN ERRORS (20100-20199) =====
  /**
   * Lỗi khi không tìm thấy campaign
   */
  CAMPAIGN_NOT_FOUND: new ErrorCode(
    20100,
    'Không tìm thấy campaign',
    HttpStatus.NOT_FOUND,
  ),

  /**
   * Lỗi khi campaign đã được kích hoạt
   */
  CAMPAIGN_ALREADY_ACTIVE: new ErrorCode(
    20101,
    'Campaign đã được kích hoạt',
    HttpStatus.BAD_REQUEST,
  ),

  // ===== AUDIENCE ERRORS (20200-20299) =====
  /**
   * Lỗi khi không tìm thấy audience
   */
  AUDIENCE_NOT_FOUND: new ErrorCode(
    20200,
    'Không tìm thấy audience',
    HttpStatus.NOT_FOUND,
  ),

  /**
   * Lỗi khi audience đã tồn tại
   */
  AUDIENCE_ALREADY_EXISTS: new ErrorCode(
    20201,
    'Audience đã tồn tại',
    HttpStatus.CONFLICT,
  ),

  // ===== SEGMENT ERRORS (20300-20399) =====
  /**
   * Lỗi khi không tìm thấy segment
   */
  SEGMENT_NOT_FOUND: new ErrorCode(
    20300,
    'Không tìm thấy segment',
    HttpStatus.NOT_FOUND,
  ),

  /**
   * Lỗi khi segment đã tồn tại
   */
  SEGMENT_ALREADY_EXISTS: new ErrorCode(
    20301,
    'Segment đã tồn tại',
    HttpStatus.CONFLICT,
  ),

  // ===== TAG ERRORS (20400-20499) =====
  /**
   * Lỗi khi không tìm thấy tag
   */
  TAG_NOT_FOUND: new ErrorCode(
    20400,
    'Không tìm thấy tag',
    HttpStatus.NOT_FOUND,
  ),

  /**
   * Lỗi khi tag đã tồn tại
   */
  TAG_ALREADY_EXISTS: new ErrorCode(
    20401,
    'Tag đã tồn tại',
    HttpStatus.CONFLICT,
  ),

  // ===== TEMPLATE ERRORS (20500-20599) =====
  /**
   * Lỗi khi không tìm thấy template
   */
  TEMPLATE_NOT_FOUND: new ErrorCode(
    20500,
    'Không tìm thấy template',
    HttpStatus.NOT_FOUND,
  ),

  /**
   * Lỗi khi template đã tồn tại
   */
  TEMPLATE_ALREADY_EXISTS: new ErrorCode(
    20501,
    'Template đã tồn tại',
    HttpStatus.CONFLICT,
  ),

  // ===== ZALO ERRORS (20600-20699) =====
  /**
   * Lỗi khi không tìm thấy Zalo account
   */
  ZALO_ACCOUNT_NOT_FOUND: new ErrorCode(
    20600,
    'Không tìm thấy tài khoản Zalo',
    HttpStatus.NOT_FOUND,
  ),

  /**
   * Lỗi khi Zalo account không hoạt động
   */
  ZALO_ACCOUNT_INACTIVE: new ErrorCode(
    20601,
    'Tài khoản Zalo không hoạt động',
    HttpStatus.BAD_REQUEST,
  ),

  /**
   * Lỗi khi không tìm thấy Zalo group
   */
  ZALO_GROUP_NOT_FOUND: new ErrorCode(
    20602,
    'Không tìm thấy nhóm Zalo',
    HttpStatus.NOT_FOUND,
  ),

  /**
   * Lỗi khi Zalo group không hoạt động
   */
  ZALO_GROUP_INACTIVE: new ErrorCode(
    20603,
    'Nhóm Zalo không hoạt động',
    HttpStatus.BAD_REQUEST,
  ),

  // ===== GENERAL MARKETING ERRORS (20900-20999) =====
  /**
   * Lỗi validation dữ liệu
   */
  VALIDATION_ERROR: new ErrorCode(
    20900,
    'Dữ liệu không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),

  /**
   * Lỗi khi không có quyền truy cập
   */
  UNAUTHORIZED_ACCESS: new ErrorCode(
    20901,
    'Không có quyền truy cập',
    HttpStatus.FORBIDDEN,
  ),

  /**
   * Lỗi khi tài nguyên đã tồn tại
   */
  RESOURCE_ALREADY_EXISTS: new ErrorCode(
    20902,
    'Tài nguyên đã tồn tại',
    HttpStatus.CONFLICT,
  ),

  /**
   * Lỗi khi tài nguyên không tìm thấy
   */
  RESOURCE_NOT_FOUND: new ErrorCode(
    20903,
    'Không tìm thấy tài nguyên',
    HttpStatus.NOT_FOUND,
  ),

  /**
   * Lỗi máy chủ nội bộ
   */
  INTERNAL_SERVER_ERROR: new ErrorCode(
    20999,
    'Lỗi máy chủ nội bộ',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
};

import { registerDecorator, ValidationOptions, ValidationArguments } from 'class-validator';

/**
 * Danh sách currency được hỗ trợ
 */
const SUPPORTED_CURRENCIES = [
  'VND',
  'USD',
  'EUR',
  'GBP',
  'JPY',
  'KRW',
  'CNY',
  'SGD',
  'THB',
  'MYR',
  'IDR',
  'PHP',
  'AUD',
  'CAD',
  'CHF',
  'RUB',
  'INR',
  'BRL',
  'MXN',
  'ZAR',
];

/**
 * Custom validator decorator để kiểm tra currency hợp lệ
 * @param validationOptions Tùy chọn validation
 * @returns Decorator function
 */
export function IsValidCurrency(validationOptions?: ValidationOptions) {
  return function (object: object, propertyName: string) {
    registerDecorator({
      name: 'isValidCurrency',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: {
        validate(value: any, args: ValidationArguments) {
          if (typeof value !== 'string') {
            return false;
          }
          return SUPPORTED_CURRENCIES.includes(value.toUpperCase());
        },
        defaultMessage(args: ValidationArguments) {
          return `${args.property} phải là một đơn vị tiền tệ hợp lệ. Các currency được hỗ trợ: ${SUPPORTED_CURRENCIES.join(', ')}`;
        },
      },
    });
  };
}

/**
 * Lấy danh sách currency được hỗ trợ
 * @returns Array của currency strings
 */
export function getSupportedCurrencies(): string[] {
  return [...SUPPORTED_CURRENCIES];
}

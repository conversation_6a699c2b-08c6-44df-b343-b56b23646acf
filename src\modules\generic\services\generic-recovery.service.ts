import { Injectable, Logger } from '@nestjs/common';
import { GenericSessionService, GenericWidgetService, GenericLayoutService, GenericQueueService } from './';
import { GenericWebSocketGateway } from '../gateways/generic-websocket.gateway';
import { AppException, ErrorCode } from '@/common';

/**
 * Service để handle session recovery, persistence và cleanup
 */
@Injectable()
export class GenericRecoveryService {
  private readonly logger = new Logger(GenericRecoveryService.name);

  constructor(
    private readonly genericSessionService: GenericSessionService,
    private readonly genericWidgetService: GenericWidgetService,
    private readonly genericLayoutService: GenericLayoutService,
    private readonly genericQueueService: GenericQueueService,
    private readonly genericWebSocketGateway: GenericWebSocketGateway,
  ) {}

  /**
   * Recover session state cho client reconnection
   * @param sessionId Session ID
   * @returns Full session state
   */
  async recoverSessionState(sessionId: string): Promise<{
    session: any;
    widgets: any[];
    layout: any;
    isRecovered: boolean;
  }> {
    try {
      this.logger.log(`Recovering session state: ${sessionId}`);

      // Lấy session info
      const session = await this.genericSessionService.getSessionBySessionId(sessionId);
      
      // Lấy widgets
      const widgets = await this.genericWidgetService.getWidgetsBySessionId(sessionId, false);
      
      // Lấy layout
      const layout = await this.genericLayoutService.getLayoutBySessionIdOptional(sessionId);

      const recoveredState = {
        session,
        widgets,
        layout: layout?.layoutData || [],
        isRecovered: true,
      };

      this.logger.log(`Session state recovered successfully: ${sessionId} - ${widgets.length} widgets`);
      
      return recoveredState;
    } catch (error) {
      this.logger.error(`Failed to recover session state: ${sessionId} - ${error.message}`, error.stack);
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        `Không thể khôi phục session ${sessionId}`,
      );
    }
  }

  /**
   * Backup session state to persistent storage
   * @param sessionId Session ID
   * @returns Backup success status
   */
  async backupSessionState(sessionId: string): Promise<{ success: boolean; backupId: string }> {
    try {
      this.logger.log(`Backing up session state: ${sessionId}`);

      // Lấy full state
      const state = await this.recoverSessionState(sessionId);
      
      // Generate backup ID
      const backupId = `backup-${sessionId}-${Date.now()}`;
      
      // Store backup (có thể lưu vào Redis, S3, hoặc database)
      // Ở đây tôi sẽ lưu vào session metadata
      await this.genericSessionService.updateSession(sessionId, {
        metadata: {
          ...state.session.metadata,
          lastBackup: {
            backupId,
            timestamp: new Date().toISOString(),
            widgetCount: state.widgets.length,
            hasLayout: !!state.layout,
          }
        }
      });

      this.logger.log(`Session state backed up successfully: ${sessionId} - ${backupId}`);
      
      return { success: true, backupId };
    } catch (error) {
      this.logger.error(`Failed to backup session state: ${sessionId} - ${error.message}`, error.stack);
      return { success: false, backupId: '' };
    }
  }

  /**
   * Auto-save session state periodically
   * @param sessionId Session ID
   */
  async autoSaveSession(sessionId: string): Promise<void> {
    try {
      // Kiểm tra session có active không
      const isValid = await this.genericSessionService.isSessionActiveAndValid(sessionId);
      if (!isValid) {
        return;
      }

      // Backup state
      await this.backupSessionState(sessionId);
      
      this.logger.debug(`Auto-saved session: ${sessionId}`);
    } catch (error) {
      this.logger.warn(`Auto-save failed for session: ${sessionId} - ${error.message}`);
    }
  }

  /**
   * Cleanup expired sessions (được gọi từ queue job)
   */
  async cleanupExpiredSessions(): Promise<void> {
    try {
      this.logger.log('Starting expired sessions cleanup...');

      // Trigger cleanup job
      await this.genericQueueService.cleanupExpiredSessionsJob({
        triggeredBy: 'queue-job',
      });

      this.logger.log('Expired sessions cleanup job triggered');
    } catch (error) {
      this.logger.error(`Cleanup job failed: ${error.message}`, error.stack);
    }
  }

  /**
   * Auto-save active sessions (được gọi từ queue job)
   */
  async autoSaveActiveSessions(): Promise<void> {
    try {
      this.logger.log('Starting auto-save for active sessions...');

      // Lấy tất cả active sessions
      const activeSessions = await this.genericSessionService.getAllActiveSessions();
      
      // Auto-save từng session
      const savePromises = activeSessions.map(session => 
        this.autoSaveSession(session.sessionId)
      );
      
      await Promise.allSettled(savePromises);
      
      this.logger.log(`Auto-save completed for ${activeSessions.length} active sessions`);
    } catch (error) {
      this.logger.error(`Auto-save job failed: ${error.message}`, error.stack);
    }
  }

  /**
   * Health check cho sessions và connections (được gọi từ queue job)
   */
  async healthCheckSessions(): Promise<void> {
    try {
      // Lấy connection stats
      const connectionStats = this.genericWebSocketGateway.getConnectionCount();
      
      // Lấy queue stats
      const queueStats = await this.genericQueueService.getQueueStats();
      
      // Log stats nếu có vấn đề
      if (queueStats.failed > 10) {
        this.logger.warn(`High number of failed queue jobs: ${queueStats.failed}`);
      }
      
      if (connectionStats.total === 0 && connectionStats.sessions > 0) {
        this.logger.warn('Sessions exist but no WebSocket connections');
      }

      this.logger.debug(`Health check - Connections: ${connectionStats.total}, Sessions: ${connectionStats.sessions}, Queue: ${JSON.stringify(queueStats)}`);
    } catch (error) {
      this.logger.error(`Health check failed: ${error.message}`, error.stack);
    }
  }

  /**
   * Force sync state cho tất cả active sessions
   */
  async forceSyncAllSessions(): Promise<{ syncedSessions: number; errors: number }> {
    try {
      this.logger.log('Force syncing all active sessions...');

      const activeSessions = await this.genericSessionService.getAllActiveSessions();
      let syncedSessions = 0;
      let errors = 0;

      for (const session of activeSessions) {
        try {
          await this.genericQueueService.syncStateJob(session.sessionId, {
            triggeredBy: 'force-sync',
          });
          syncedSessions++;
        } catch (error) {
          this.logger.warn(`Failed to sync session ${session.sessionId}: ${error.message}`);
          errors++;
        }
      }

      this.logger.log(`Force sync completed - Synced: ${syncedSessions}, Errors: ${errors}`);
      
      return { syncedSessions, errors };
    } catch (error) {
      this.logger.error(`Force sync failed: ${error.message}`, error.stack);
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Lỗi khi force sync sessions',
      );
    }
  }

  /**
   * Migrate session từ old format sang new format (nếu cần)
   */
  async migrateSession(sessionId: string, migrationOptions?: any): Promise<{ success: boolean; message: string }> {
    try {
      this.logger.log(`Migrating session: ${sessionId}`);

      // Implement migration logic nếu cần
      // Ví dụ: update schema, convert data format, etc.
      
      const session = await this.genericSessionService.getSessionBySessionId(sessionId);
      
      // Update metadata để mark là đã migrate
      await this.genericSessionService.updateSession(sessionId, {
        metadata: {
          ...session.metadata,
          migrated: true,
          migrationDate: new Date().toISOString(),
          migrationVersion: '1.0.0',
        }
      });

      this.logger.log(`Session migrated successfully: ${sessionId}`);
      
      return { success: true, message: 'Session migrated successfully' };
    } catch (error) {
      this.logger.error(`Session migration failed: ${sessionId} - ${error.message}`, error.stack);
      return { success: false, message: error.message };
    }
  }
}

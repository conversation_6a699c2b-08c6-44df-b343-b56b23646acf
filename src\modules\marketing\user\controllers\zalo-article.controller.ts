import {
  Body,
  Controller,
  Get,
  Logger,
  Param,
  ParseUUIDPipe,
  Post,
  Query,
  UseGuards,
  UseInterceptors,
  UploadedFile,
  Delete,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiBody,
  ApiConsumes,
  ApiQuery,
} from '@nestjs/swagger';
import { CurrentUser } from '@modules/auth/decorators/current-user.decorator';
import { JwtPayload } from '@/modules/auth/guards/jwt.util';
import { ApiResponseDto, PaginatedResult } from '@/common/response';
import { JwtUserGuard } from '@/modules/auth/guards';
import { SWAGGER_API_TAGS } from '@/common/swagger';
import { AppException, ErrorCode } from '@/common/exceptions';
import { ZaloArticleService } from '@/shared/services/zalo';
import { ZaloService } from '../services/zalo.service';
import { ZaloArticleManagementService } from '../services/zalo-article-management.service';
import { QueueService } from '@/shared/queue/queue.service';

import { ZaloArticleSchedulerJobData } from '@/shared/queue/queue.types';
import { MediaRepository } from '@/modules/data/media/repositories/media.repository';
import { CdnService } from '@/shared/services/cdn.service';
import { TimeIntervalEnum } from '@/shared/utils';
import { ZaloVideoTrackingJobData } from '@/shared/queue/queue.types';
import {
  ZaloArticleType,
  ZaloArticleStatus,
} from '../entities/zalo-article.entity';
import { CreateZaloArticleData } from '../services/zalo-article-management.service';
import {
  CreateZaloArticleNormalDto,
  CreateZaloArticleVideoDto,
  ZaloArticleResponseDto,
  ZaloArticleDraftResponseDto,
  CheckZaloArticleProcessDto,
  ZaloArticleProcessResponseDto,
  ZaloVideoUploadRequestDto,
  ZaloVideoUploadResponseDto,
  CheckZaloVideoStatusDto,
  ZaloVideoStatusResponseDto,
  VerifyZaloArticleDto,
  ZaloArticleVerifyResponseDto,
  GetZaloArticleDetailDto,
  ZaloArticleDetailNormalDto,
  ZaloArticleDetailVideoDto,
  GetZaloArticleListDto,
  QueryZaloArticleListDto,
  ZaloArticleListResponseDto,
  ZaloArticleListItemDto,
  RemoveZaloArticleDto,
  ZaloArticleRemoveResponseDto,
  UpdateZaloArticleNormalDto,
  UpdateZaloArticleVideoDto,
  ZaloArticleUpdateResponseDto,
  QueryZaloArticleManagementDto,
  ZaloArticleManagementResponseDto,
  CreateZaloArticleWithStatusDto,
  BulkDeleteZaloArticlesDto,
  BulkDeleteZaloArticlesResponseDto,
} from '../dto/zalo';

/**
 * Controller xử lý API tạo bài viết Zalo Official Account
 * Hỗ trợ tạo bài viết dạng normal (có nội dung phong phú) và video
 */
@ApiTags(SWAGGER_API_TAGS.ZALO_ARTICLE)
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtUserGuard)
@Controller('marketing/zalo')
export class ZaloArticleController {
  private readonly logger = new Logger(ZaloArticleController.name);

  constructor(
    private readonly zaloArticleService: ZaloArticleService,
    private readonly zaloService: ZaloService,
    private readonly queueService: QueueService,
    private readonly zaloArticleManagementService: ZaloArticleManagementService,
    private readonly mediaRepository: MediaRepository,
    private readonly cdnService: CdnService,
  ) {}

  /**
   * Tạo bài viết dạng normal (có nội dung phong phú)
   */
  @Post('articles/normal')
  @ApiOperation({
    summary: 'Tạo bài viết dạng normal',
    description: `Tạo bài viết dạng normal với nội dung phong phú:
    - Hỗ trợ text, image, video trong body
    - Có thể đặt lịch xuất bản hoặc xuất bản ngay
    - Tự động lưu vào database để tracking
    - Trả về token để kiểm tra tiến trình`,
  })
  @ApiBody({
    description: 'Thông tin bài viết cần tạo',
    type: CreateZaloArticleNormalDto,
    examples: {
      normal: {
        summary: 'Bài viết normal cơ bản',
        value: {
          id: '550e8400-e29b-41d4-a716-************',
          type: 'normal',
          title: 'Tiêu đề bài viết',
          description: 'Mô tả ngắn gọn về bài viết',
          author: 'Tác giả',
          cover: {
            cover_type: 'photo',
            photo_url: 'https://example.com/cover.jpg',
            status: 'show',
          },
          body: [
            {
              type: 'text',
              content: 'Nội dung văn bản của bài viết',
            },
            {
              type: 'image',
              url: 'https://example.com/image.jpg',
            },
          ],
          status: 'draft',
          comment: 'show',
        },
      },
    },
  })
  @ApiResponse({
    status: 201,
    description: 'Tạo bài viết thành công',
    type: ZaloArticleResponseDto,
    schema: {
      example: {
        success: true,
        message: 'Tạo bài viết thành công',
        data: {
          id: '550e8400-e29b-41d4-a716-************',
          token: 'token_123456789abcdef',
          status: 'draft',
          message: 'Bài viết đã được lưu dưới dạng draft',
        },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Dữ liệu không hợp lệ',
  })
  @ApiResponse({
    status: 404,
    description: 'Không tìm thấy Integration',
  })
  async createNormalArticle(
    @CurrentUser() user: JwtPayload,
    @Body() createDto: CreateZaloArticleNormalDto,
  ): Promise<ApiResponseDto<ZaloArticleResponseDto>> {
    try {
      this.logger.log(
        `User ${user.id} creating normal article: ${createDto.title}`,
      );

      // Kiểm tra Integration có tồn tại và thuộc về user không
      if (!createDto.id) {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          'Integration ID là bắt buộc',
        );
      }

      const oa = await this.zaloService.getOfficialAccountByIntegrationId(
        user.id,
        createDto.id,
      );

      // Xử lý cover nếu có media_id
      let processedCover = createDto.cover;
      if (createDto.cover?.media_id) {
        processedCover = await this.processCoverWithMediaId(
          createDto.cover,
          user.id,
        );
      }

      // Chuẩn bị request cho Zalo API
      const zaloRequest = {
        type: 'normal' as const,
        title: createDto.title,
        description: createDto.description,
        author: createDto.author,
        cover: processedCover,
        body: createDto.body,
        related_medias: createDto.related_medias || [],
        tracking_link: createDto.tracking_link,
        status:
          createDto.status === 'show' || createDto.status === 'hide'
            ? createDto.status
            : 'hide', // Mặc định là hide nếu không phải show/hide
        comment: createDto.comment || 'show',
      };

      let token = '';
      let status = ZaloArticleStatus.DRAFT;

      // Nếu status là 'show' hoặc 'hide', gọi API Zalo để tạo bài viết
      if (createDto.status === 'show' || createDto.status === 'hide') {
        const result = await this.zaloArticleService.createNormalArticle(
          oa.accessToken,
          zaloRequest,
          user.id,
        );
        token = result.token;
        status = ZaloArticleStatus.PENDING;
      }

      // Lưu vào database
      const articleData: CreateZaloArticleData = {
        userId: user.id,
        integrationId: createDto.id!,
        type: ZaloArticleType.NORMAL,
        title: createDto.title,
        description: createDto.description,
        content: JSON.stringify(createDto.body),
        author: createDto.author,
        coverPhotoUrl: processedCover?.photo_url,
        trackingLink: createDto.tracking_link,
        token: token,
        status: status,
        comment: createDto.comment,
        metadata: {
          originalRequest: createDto,
          zaloRequest: zaloRequest,
          cover: processedCover,
        },
      };

      const article =
        await this.zaloArticleManagementService.createArticle(articleData);

      // Nếu có token, tạo job tracking để theo dõi tiến trình
      if (token) {
        try {
          // TODO: Implement tracking job if needed
          this.logger.log(`Article created with token: ${token}`);
        } catch (error) {
          this.logger.error(
            `Error adding tracking job: ${error.message}`,
            error.stack,
          );
        }
      }

      // Tạo response
      const response: ZaloArticleResponseDto = {
        token: token || '',
      };

      this.logger.log(`Successfully created normal article: ${article.id}`);

      const message =
        createDto.status === 'show' || createDto.status === 'hide'
          ? status === ZaloArticleStatus.PENDING
            ? 'Bài viết đang được tạo trên Zalo'
            : 'Bài viết đã được lưu nhưng lỗi khi tạo trên Zalo'
          : 'Bài viết đã được lưu dưới dạng draft';

      return ApiResponseDto.success(response, message);
    } catch (error) {
      this.logger.error(
        `Error creating normal article: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Lỗi khi tạo bài viết',
      );
    }
  }

  /**
   * Helper method để xử lý cover với media_id
   * Nếu cover có media_id, sẽ lấy media từ database và chuyển thành URL
   */
  private async processCoverWithMediaId(
    cover: any,
    userId: number,
  ): Promise<any> {
    if (!cover || !cover.media_id) {
      return cover;
    }

    try {
      // Lấy media từ database
      const media = await this.mediaRepository.findOneBy({
        id: cover.media_id,
      });

      if (!media) {
        throw new AppException(
          ErrorCode.NOT_FOUND,
          `Media với ID ${cover.media_id} không tồn tại`,
        );
      }

      // Kiểm tra quyền sở hữu
      if (media.ownedBy !== userId) {
        throw new AppException(
          ErrorCode.FORBIDDEN,
          'Bạn không có quyền sử dụng media này',
        );
      }

      // Tạo URL từ storageKey
      let photoUrl = '';
      if (media.storageKey) {
        try {
          const downloadUrl = await this.cdnService.generateUrlView(
            media.storageKey,
            TimeIntervalEnum.ONE_HOUR,
          );
          if (downloadUrl) {
            photoUrl = downloadUrl;
          }
        } catch (error) {
          this.logger.warn(
            `Không thể tạo URL cho media ${cover.media_id}: ${error.message}`,
          );
        }
      }

      // Trả về cover với photo_url thay vì media_id
      const { media_id, ...coverWithoutMediaId } = cover;
      return {
        ...coverWithoutMediaId,
        photo_url: photoUrl,
      };
    } catch (error) {
      this.logger.error(
        `Lỗi xử lý media_id ${cover.media_id}: ${error.message}`,
      );
      throw error;
    }
  }

  /**
   * Kiểm tra tiến trình tạo bài viết
   */
  @Get('articles/process/:id')
  @ApiOperation({
    summary: 'Kiểm tra tiến trình tạo bài viết',
    description: `Kiểm tra tiến trình tạo bài viết bằng token:
    - Sử dụng token từ response tạo bài viết
    - Trạng thái: processing (đang xử lý), success (thành công), failed (thất bại)
    - Nếu thành công sẽ trả về article_id
    - Nếu thất bại sẽ có thông báo lỗi
    - Bao gồm thời gian tạo và cập nhật`,
  })
  @ApiParam({
    name: 'id',
    description: 'UUID của Zalo Integration',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy thông tin tiến trình thành công',
    type: ZaloArticleProcessResponseDto,
    schema: {
      example: {
        success: true,
        message: 'Lấy thông tin tiến trình thành công',
        data: {
          article_id: 'article_123456789',
          status: 'success',
          created_time: 1640995200,
          updated_time: 1640995260,
        },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Token không hợp lệ',
  })
  @ApiResponse({
    status: 404,
    description: 'Không tìm thấy Integration',
  })
  async checkArticleProcess(
    @CurrentUser() user: JwtPayload,
    @Param('id', ParseUUIDPipe) id: string,
    @Query() queryDto: CheckZaloArticleProcessDto,
  ): Promise<ApiResponseDto<ZaloArticleProcessResponseDto>> {
    // Kiểm tra Integration có tồn tại và thuộc về user không
    const oa = await this.zaloService.getOfficialAccountByIntegrationId(
      user.id,
      id,
    );

    // Kiểm tra tiến trình
    const result = await this.zaloArticleService.checkArticleProcess(
      oa.accessToken,
      queryDto.token,
    );

    return ApiResponseDto.success(
      result,
      'Lấy thông tin tiến trình thành công',
    );
  }

  /**
   * Upload video cho bài viết
   */
  @Post('articles/upload-video/:id')
  @UseInterceptors(FileInterceptor('file'))
  @ApiConsumes('multipart/form-data')
  @ApiOperation({
    summary: 'Upload video cho bài viết Zalo',
    description: `Upload video để sử dụng cho bài viết:
    - Hỗ trợ định dạng: MP4, AVI
    - Kích thước tối đa: 50MB
    - Có thể upload file trực tiếp hoặc sử dụng mediaId từ bảng media_data
    - Trả về token để kiểm tra trạng thái xử lý
    - Video sẽ được xử lý và chuyển đổi bởi Zalo
    - Sử dụng token để kiểm tra tiến trình xử lý`,
  })
  @ApiParam({
    name: 'id',
    description: 'UUID của Zalo Integration',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @ApiBody({
    description: 'Dữ liệu upload video - có thể là file hoặc mediaId',
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
          description:
            'File video (MP4/AVI, tối đa 50MB) - chỉ dùng khi không có mediaId',
        },
        mediaId: {
          type: 'string',
          format: 'uuid',
          description: 'ID của media từ bảng media_data (UUID)',
          example: '550e8400-e29b-41d4-a716-************',
        },
        description: {
          type: 'string',
          description: 'Mô tả video (tùy chọn)',
          example: 'Video giới thiệu sản phẩm',
        },
      },
    },
  })
  @ApiResponse({
    status: 201,
    description: 'Upload video thành công',
    type: ZaloVideoUploadResponseDto,
    schema: {
      example: {
        success: true,
        message: 'Upload video thành công',
        data: {
          token:
            '9xk090B1ANibkhyg9ke8SZgMg39Pz7bNJvJvKrpA821_XVbaI-Dr9ME5ma44nqGPOw3dKmR2SreHY_yaUiOcMrkWX0XknWH0IOQtEY_N1r9Ahu8xJT9QEIU9paOpwc0QDhhqSmU3So8WmlqcEvDEKb_euG5CbKHp9kJS432-35aKsOzSQva30L2cabKwcZqJNwkvQ63M7Mf7bQml9SK5BZJyZ4O8jmT16OwbFNdmA4iIdQPETiq8EtUkhM0AYMCHDkNmJWs9CLPkgFuBJ9Gm536kw1DQb34I9xF_4qE2BX4fk_uJIOKl6ZIYuH9LdpaMPAxi5mILRn5XgxmNLi8v56VywqLGnJaGDJ4mKWZXC0_G15',
          trackingId: 123,
          videoName: 'video.mp4',
          videoSize: 2979281,
          status: 3,
          statusMessage: 'Video đang được xử lý',
        },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'File không hợp lệ, vượt quá giới hạn hoặc thiếu dữ liệu',
  })
  @ApiResponse({
    status: 404,
    description: 'Không tìm thấy Integration hoặc Media',
  })
  async uploadVideo(
    @CurrentUser() user: JwtPayload,
    @Param('id', ParseUUIDPipe) id: string,
    @Body() uploadDto: ZaloVideoUploadRequestDto,
    @UploadedFile() file?: Express.Multer.File,
  ): Promise<ApiResponseDto<ZaloVideoUploadResponseDto>> {
    // Chuẩn hóa mediaId (loại bỏ chuỗi rỗng)
    const mediaId = uploadDto.mediaId?.trim() || undefined;

    // Validation: phải có ít nhất một trong hai - file hoặc mediaId
    if (!file && !mediaId) {
      throw new AppException(
        ErrorCode.VALIDATION_ERROR,
        'Phải cung cấp file hoặc mediaId',
      );
    }

    // Validation: không được cung cấp cả hai
    if (file && mediaId) {
      throw new AppException(
        ErrorCode.VALIDATION_ERROR,
        'Chỉ được cung cấp file hoặc mediaId, không được cả hai',
      );
    }

    // Kiểm tra Integration có tồn tại và thuộc về user không
    const oa = await this.zaloService.getOfficialAccountByIntegrationId(
      user.id,
      id,
    );

    // Upload video
    const result = await this.zaloArticleService.uploadVideoWithOptions(
      oa.accessToken,
      {
        file,
        mediaId: mediaId,
        description: uploadDto.description,
        userId: user.id,
        integrationId: id,
        oaId: oa.oaId, // Lấy oaId từ integration để tương thích với hệ thống cũ
      },
    );

    // Tạo queue job để tracking video status định kỳ
    try {
      const trackingJobData: ZaloVideoTrackingJobData = {
        token: result.token,
        accessToken: oa.accessToken,
        userId: user.id,
        integrationId: id,
        oaId: oa.oaId,
        timestamp: Date.now(),
        checkCount: 0,
        delayMs: 10000, // Delay 10 giây trước khi check lần đầu
      };

      // Debug log access token
      this.logger.debug(
        `[APP] Creating job with access token: ${oa.accessToken.substring(0, 50)}...`,
      );
      this.logger.debug(
        `[APP] Video token: ${result.token.substring(0, 50)}...`,
      );

      await this.queueService.addVideoTrackingJob(trackingJobData);

      this.logger.log(`Created video tracking job for token: ${result.token}`);
    } catch (error) {
      // Log lỗi nhưng không throw để không ảnh hưởng đến response upload
      this.logger.warn(`Failed to create video tracking job: ${error.message}`);
    }

    return ApiResponseDto.success(result, 'Upload video thành công');
  }

  /**
   * Kiểm tra trạng thái video đã upload
   */
  @Get('articles/video-status/:id')
  @ApiOperation({
    summary: 'Kiểm tra trạng thái video đã upload',
    description: `Kiểm tra trạng thái xử lý video:
    - Sử dụng token từ response upload video
    - Trạng thái 0: Không xác định
    - Trạng thái 1: Đã xử lý thành công, có thể sử dụng
    - Trạng thái 2: Đã bị khóa
    - Trạng thái 3: Đang xử lý
    - Trạng thái 4: Xử lý thất bại
    - Trạng thái 5: Đã bị xóa
    - Khi trạng thái = 1, sẽ có video_id để sử dụng`,
  })
  @ApiParam({
    name: 'id',
    description: 'UUID của Zalo Integration',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy trạng thái video thành công',
    type: ZaloVideoStatusResponseDto,
    schema: {
      example: {
        success: true,
        message: 'Lấy trạng thái video thành công',
        data: {
          status_message: 'Video is successfully uploaded',
          video_name: 'Android.mp4',
          video_size: 2979281,
          convert_percent: 100,
          convert_error_code: 0,
          video_id: 'f9377bde4e9ba7c5fe8',
          status: 1,
        },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Token không hợp lệ',
  })
  @ApiResponse({
    status: 404,
    description: 'Không tìm thấy Integration',
  })
  async checkVideoStatus(
    @CurrentUser() user: JwtPayload,
    @Param('id', ParseUUIDPipe) id: string,
    @Query() queryDto: CheckZaloVideoStatusDto,
  ): Promise<ApiResponseDto<ZaloVideoStatusResponseDto>> {
    // Kiểm tra Integration có tồn tại và thuộc về user không
    const oa = await this.zaloService.getOfficialAccountByIntegrationId(
      user.id,
      id,
    );

    // Debug log trước khi gọi API
    this.logger.debug(
      `[APP] Calling checkVideoStatus with access token: ${oa.accessToken.substring(0, 50)}...`,
    );
    this.logger.debug(
      `[APP] Video token: ${queryDto.token.substring(0, 50)}...`,
    );

    // Kiểm tra trạng thái video từ Zalo API
    const zaloResult = await this.zaloArticleService.checkVideoStatus(
      oa.accessToken,
      queryDto.token,
    );

    this.logger.debug('Zalo API result:', JSON.stringify(zaloResult, null, 2));

    // Lấy data từ response (bỏ wrapper error, message)
    const videoData = zaloResult;

    this.logger.debug(
      'Video data extracted:',
      JSON.stringify(videoData, null, 2),
    );

    // Cập nhật tracking information trong database với đầy đủ thông tin
    try {
      await this.zaloArticleService.updateVideoUploadStatus(
        queryDto.token,
        videoData,
      );
    } catch (error) {
      // Log lỗi nhưng không throw để không ảnh hưởng đến response chính
      this.logger.warn(
        `Failed to update video upload status: ${error.message}`,
      );
    }

    return ApiResponseDto.success(videoData, 'Lấy trạng thái video thành công');
  }

  /**
   * Verify bài viết và lấy ID bài viết
   */
  // @Post('verify/:id')
  // @ApiOperation({
  //   summary: 'Verify bài viết và lấy ID bài viết',
  //   description: `Verify bài viết bằng token và lấy ID bài viết:
  //   - Sử dụng token từ response tạo hoặc cập nhật bài viết
  //   - Trả về ID bài viết nếu đã tạo thành công
  //   - API này khác với checkArticleProcess ở chỗ chỉ trả về ID khi bài viết đã hoàn thành
  //   - Sử dụng để lấy ID cuối cùng của bài viết sau khi tạo thành công`
  // })
  // @ApiParam({
  //   name: 'id',
  //   description: 'UUID của Zalo Integration',
  //   example: '550e8400-e29b-41d4-a716-************'
  // })
  // @ApiResponse({
  //   status: 200,
  //   description: 'Verify bài viết thành công',
  //   type: ZaloArticleVerifyResponseDto,
  //   schema: {
  //     example: {
  //       success: true,
  //       message: 'Verify bài viết thành công',
  //       data: {
  //         id: 'fced9823yjans'
  //       }
  //     }
  //   }
  // })
  // @ApiResponse({
  //   status: 400,
  //   description: 'Token không hợp lệ'
  // })
  // @ApiResponse({
  //   status: 404,
  //   description: 'Không tìm thấy Integration'
  // })
  // async verifyArticle(
  //   @CurrentUser() user: JwtPayload,
  //   @Param('id', ParseUUIDPipe) id: string,
  //   @Body() verifyDto: VerifyZaloArticleDto,
  // ): Promise<ApiResponseDto<ZaloArticleVerifyResponseDto>> {
  //   // Kiểm tra Integration có tồn tại và thuộc về user không
  //   const oa = await this.zaloService.getOfficialAccountByIntegrationId(user.id, id);

  //   // Verify bài viết
  //   const result = await this.zaloArticleService.verifyArticle(
  //     oa.accessToken,
  //     verifyDto.token,
  //   );

  //   return ApiResponseDto.success(result, 'Verify bài viết thành công');
  // }

  /**
   * Lấy chi tiết bài viết
   */
  @Get('articles/detail/:id/:articleId')
  @ApiOperation({
    summary: 'Lấy chi tiết bài viết Zalo',
    description: `Lấy thông tin chi tiết của bài viết:
    - Hỗ trợ cả bài viết dạng normal và video
    - Bài viết normal: bao gồm title, author, cover, description, body, related_medias, cite
    - Bài viết video: bao gồm title, description, video_id, avatar
    - Trả về đầy đủ thông tin để hiển thị hoặc chỉnh sửa bài viết
    - Cần có ID bài viết từ API verify hoặc getslice`,
  })
  @ApiParam({
    name: 'id',
    description: 'UUID của Zalo Integration',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @ApiParam({
    name: 'articleId',
    description: 'ID của bài viết muốn lấy chi tiết',
    example: '39c07ccbe78e0ed0579f',
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy chi tiết bài viết thành công',
    schema: {
      oneOf: [
        {
          title: 'Normal Article',
          example: {
            success: true,
            message: 'Lấy chi tiết bài viết thành công',
            data: {
              id: '39c07ccbe78e0ed0579f',
              type: 'normal',
              title: 'Title',
              author: 'Author',
              cover: {
                cover_type: 'photo',
                photo_url:
                  'https://zalo-article-photo.zadn.vn/7ad578505104b85ae115#286621339',
                status: 'show',
              },
              description: 'Description',
              status: 'show',
              body: [
                {
                  type: 'text',
                  content: 'Content',
                },
              ],
              related_medias: [],
              comment: 'show',
              cite: {
                url: '',
                label: '',
              },
            },
          },
        },
        {
          title: 'Video Article',
          example: {
            success: true,
            message: 'Lấy chi tiết bài viết thành công',
            data: {
              id: '39c07ccbe78e0ed0579f',
              type: 'video',
              title: 'Video Title',
              description: 'Video description',
              video_id: 'f9377bde4e9ba7c5fe8',
              avatar: 'https://example.com/thumbnail.jpg',
              status: 'show',
              comment: 'show',
            },
          },
        },
      ],
    },
  })
  @ApiResponse({
    status: 404,
    description: 'Không tìm thấy Integration hoặc bài viết',
  })
  async getArticleDetail(
    @CurrentUser() user: JwtPayload,
    @Param('id', ParseUUIDPipe) id: string,
    @Param('articleId') articleId: string,
  ): Promise<
    ApiResponseDto<ZaloArticleDetailNormalDto | ZaloArticleDetailVideoDto>
  > {
    // Kiểm tra Integration có tồn tại và thuộc về user không
    const oa = await this.zaloService.getOfficialAccountByIntegrationId(
      user.id,
      id,
    );

    // Lấy chi tiết bài viết
    const result = await this.zaloArticleService.getArticleDetail(
      oa.accessToken,
      articleId,
    );

    return ApiResponseDto.success(result, 'Lấy chi tiết bài viết thành công');
  }

  /**
   * Lấy danh sách bài viết từ Zalo API
   */
  @Get('articles/list/:id')
  @ApiOperation({
    summary: 'Lấy danh sách bài viết Zalo từ API',
    description: `Lấy danh sách bài viết trực tiếp từ Zalo API với phân trang:
    - Hỗ trợ lọc theo loại: normal hoặc video
    - Phân trang với offset và limit
    - Limit tối đa 100 bài viết mỗi lần
    - Trả về thông tin tóm tắt: ID, title, status, view, share, dates, thumbnail
    - Dữ liệu realtime từ Zalo, không qua database
    - Có thể dùng ID để lấy chi tiết hoặc chỉnh sửa bài viết`,
  })
  @ApiParam({
    name: 'id',
    description: 'UUID của Zalo Integration',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy danh sách bài viết thành công',
    type: ZaloArticleListResponseDto,
    schema: {
      example: {
        success: true,
        message: 'Lấy danh sách bài viết thành công',
        data: {
          medias: [
            {
              id: '39c07ccbe78e0ed0579f',
              type: 'normal',
              title: 'Title',
              status: 'show',
              total_view: 1,
              total_share: 0,
              create_date: 1690260497732,
              update_date: 1690260504041,
              thumb:
                'https://zalo-article-photo.zadn.vn/7ad578505104b85ae115#286621339',
              link_view: 'https://example.com/article/view',
            },
          ],
          total: 2,
        },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Tham số không hợp lệ',
  })
  @ApiResponse({
    status: 404,
    description: 'Không tìm thấy Integration',
  })
  async getArticleList(
    @CurrentUser() user: JwtPayload,
    @Param('id', ParseUUIDPipe) id: string,
    @Query() queryDto: GetZaloArticleListDto,
  ): Promise<ApiResponseDto<ZaloArticleListResponseDto>> {
    // Kiểm tra Integration có tồn tại và thuộc về user không
    const oa = await this.zaloService.getOfficialAccountByIntegrationId(
      user.id,
      id,
    );

    // Lấy danh sách bài viết từ Zalo API
    const result = await this.zaloArticleService.getArticleList(
      oa.accessToken,
      queryDto,
    );

    // Kiểm tra lỗi từ Zalo API
    if (result.error !== 0) {
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        `Lỗi từ Zalo API: ${result.message}`,
      );
    }

    // Trả về phần data
    return ApiResponseDto.success(
      result.data || { medias: [], total: 0 },
      'Lấy danh sách bài viết thành công',
    );
  }

  /**
   * Cập nhật bài viết dạng normal
   */
  @Post('articles/update/normal/:id')
  @ApiOperation({
    summary: 'Cập nhật bài viết dạng normal trên Zalo Official Account',
    description: `Cập nhật bài viết dạng normal với nội dung phong phú:
    - Cần có ID bài viết từ API getslice hoặc verify
    - Hỗ trợ nhiều loại nội dung: text, image, video, product
    - Tiêu đề tối đa 150 ký tự
    - Tác giả tối đa 50 ký tự
    - Mô tả tối đa 300 ký tự
    - Cover có thể là ảnh hoặc video
    - Cover hỗ trợ cả photo_url và media_id:
      + photo_url: URL trực tiếp của ảnh
      + media_id: UUID của media từ bảng media_data (sẽ được chuyển thành URL)
    - Trả về token để kiểm tra tiến trình cập nhật
    - Trạng thái: show (hiển thị ngay) hoặc hide (ẩn để kiểm tra)`,
  })
  @ApiParam({
    name: 'id',
    description: 'UUID của Zalo Integration',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @ApiResponse({
    status: 200,
    description: 'Cập nhật bài viết thành công',
    type: ZaloArticleUpdateResponseDto,
    schema: {
      example: {
        success: true,
        message: 'Cập nhật bài viết dạng normal thành công',
        data: {
          token:
            '9xk090B1ANibkhyg9ke8SZgMg39Pz7bNJvJvKrpA821_XVbaI-Dr9ME5ma44nqGPOw3dKmR2SreHY_yaUiOcMrkWX0XknWH0IOQtEY_N1r9Ahu8xJT9QEIU9paOpwc0QDhhqSmU3So8WmlqcEvDEKb_euG5CbKHp9kJS432-35aKsOzSQva30L2cabKwcZqJNwkvQ63M7Mf7bQml9SK5BZJyZ4O8jmT16OwbFNdmA4iIdQPETiq8EtUkhM0AYMCHDkNmJWs9CLPkgFuBJ9Gm536kw1DQb34I9xF_4qE2BX4fk_uJIOKl6ZIYuH9LdpaMPAxi5mILRn5XgxmNLi8v56VywqLGnJaGDJ4mKWZXC0_G15',
        },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Dữ liệu đầu vào không hợp lệ',
  })
  @ApiResponse({
    status: 404,
    description: 'Không tìm thấy Integration hoặc bài viết',
  })
  async updateNormalArticle(
    @CurrentUser() user: JwtPayload,
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateDto: UpdateZaloArticleNormalDto,
  ): Promise<ApiResponseDto<ZaloArticleUpdateResponseDto>> {
    // Xử lý cover với media_id nếu có
    let processedCover = updateDto.cover;
    if (updateDto.cover?.media_id) {
      processedCover = await this.processCoverWithMediaId(
        updateDto.cover,
        user.id,
      );
    }

    // Tạo DTO đã được xử lý
    const processedDto = {
      ...updateDto,
      cover: processedCover,
    };

    // Kiểm tra Integration có tồn tại và thuộc về user không
    const oa = await this.zaloService.getOfficialAccountByIntegrationId(
      user.id,
      id,
    );

    // Cập nhật bài viết
    const result = await this.zaloArticleService.updateNormalArticle(
      oa.accessToken,
      processedDto,
      user.id,
    );

    return ApiResponseDto.success(
      result,
      'Cập nhật bài viết dạng normal thành công',
    );
  }

  /**
   * Cập nhật bài viết dạng video
   */
  @Post('articles/update/video/:id')
  @ApiOperation({
    summary: 'Cập nhật bài viết dạng video trên Zalo Official Account',
    description: `Cập nhật bài viết dạng video:
    - Cần có ID bài viết từ API getslice hoặc verify
    - Tiêu đề tối đa 150 ký tự
    - Mô tả tối đa 300 ký tự
    - Cần có video_id từ API upload video
    - Cần có URL ảnh thumbnail
    - Trả về token để kiểm tra tiến trình cập nhật
    - Trạng thái: show (hiển thị ngay) hoặc hide (ẩn để kiểm tra)`,
  })
  @ApiParam({
    name: 'id',
    description: 'UUID của Zalo Integration',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @ApiResponse({
    status: 200,
    description: 'Cập nhật bài viết video thành công',
    type: ZaloArticleUpdateResponseDto,
    schema: {
      example: {
        success: true,
        message: 'Cập nhật bài viết dạng video thành công',
        data: {
          token:
            '9xk090B1ANibkhyg9ke8SZgMg39Pz7bNJvJvKrpA821_XVbaI-Dr9ME5ma44nqGPOw3dKmR2SreHY_yaUiOcMrkWX0XknWH0IOQtEY_N1r9Ahu8xJT9QEIU9paOpwc0QDhhqSmU3So8WmlqcEvDEKb_euG5CbKHp9kJS432-35aKsOzSQva30L2cabKwcZqJNwkvQ63M7Mf7bQml9SK5BZJyZ4O8jmT16OwbFNdmA4iIdQPETiq8EtUkhM0AYMCHDkNmJWs9CLPkgFuBJ9Gm536kw1DQb34I9xF_4qE2BX4fk_uJIOKl6ZIYuH9LdpaMPAxi5mILRn5XgxmNLi8v56VywqLGnJaGDJ4mKWZXC0_G15',
        },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Dữ liệu đầu vào không hợp lệ',
  })
  @ApiResponse({
    status: 404,
    description: 'Không tìm thấy Integration hoặc bài viết',
  })
  async updateVideoArticle(
    @CurrentUser() user: JwtPayload,
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateDto: UpdateZaloArticleVideoDto,
  ): Promise<ApiResponseDto<ZaloArticleUpdateResponseDto>> {
    // Kiểm tra Integration có tồn tại và thuộc về user không
    const oa = await this.zaloService.getOfficialAccountByIntegrationId(
      user.id,
      id,
    );

    // Cập nhật bài viết video
    const result = await this.zaloArticleService.updateVideoArticle(
      oa.accessToken,
      updateDto,
    );

    return ApiResponseDto.success(
      result,
      'Cập nhật bài viết dạng video thành công',
    );
  }

  /**
   * Tạo bài viết mới với trạng thái
   */
  @Post(':id/articles')
  @ApiOperation({
    summary: 'Tạo bài viết mới với trạng thái',
    description: `Tạo bài viết mới với khả năng chọn trạng thái:
    - status = 'draft': Lưu bài viết dưới dạng nháp, không publish lên Zalo
    - status = 'publish': Lưu bài viết và publish lên Zalo ngay lập tức
    - Hỗ trợ cả bài viết normal và video
    - Khi publish, sẽ gọi API Zalo để tạo bài viết thực sự
    - Trả về thông tin bài viết đã tạo trong database`,
  })
  @ApiParam({
    name: 'id',
    description: 'UUID của Zalo Integration',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @ApiResponse({
    status: 201,
    description: 'Tạo bài viết thành công',
    schema: {
      example: {
        success: true,
        message: 'Tạo bài viết thành công',
        data: {
          id: '550e8400-e29b-41d4-a716-************',
          type: 'normal',
          title: 'Tiêu đề bài viết',
          status: 'draft',
          articleId: null,
          token: null,
          createdAt: '2023-07-25T10:15:30Z',
        },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Dữ liệu đầu vào không hợp lệ',
  })
  @ApiResponse({
    status: 404,
    description: 'Không tìm thấy Integration',
  })
  async createArticleWithStatus(
    @CurrentUser() user: JwtPayload,
    @Param('id', ParseUUIDPipe) id: string,
    @Body() createDto: CreateZaloArticleWithStatusDto,
  ): Promise<ApiResponseDto<ZaloArticleManagementResponseDto>> {
    // Xử lý cover với media_id nếu có
    let processedCover = createDto.cover;
    if (createDto.cover?.media_id) {
      processedCover = await this.processCoverWithMediaId(
        createDto.cover,
        user.id,
      );
    }

    // Tạo DTO đã được xử lý
    const processedDto = {
      ...createDto,
      cover: processedCover,
    };

    // Kiểm tra Integration có tồn tại và thuộc về user không
    const oa = await this.zaloService.getOfficialAccountByIntegrationId(
      user.id,
      id,
    );

    const articleId: string | null = null;
    let token: string | null = null;
    let status =
      processedDto.status === 'publish'
        ? ZaloArticleStatus.PENDING
        : ZaloArticleStatus.DRAFT;

    // Nếu status là publish, gọi API Zalo để tạo bài viết
    if (processedDto.status === 'publish') {
      let zaloResult: any;

      if (processedDto.type === 'normal') {
        // Tạo bài viết normal trên Zalo
        const normalDto: CreateZaloArticleNormalDto = {
          type: 'normal',
          title: processedDto.title,
          author: processedDto.author || 'RedAI',
          cover: processedDto.cover || {
            cover_type: 'photo',
            photo_url: processedDto.coverPhotoUrl || '',
            status: 'show',
          },
          description: processedDto.description || '',
          body: processedDto.body || [
            { type: 'text', content: processedDto.description || '' },
          ],
          related_medias: processedDto.relatedMedias,
          tracking_link: processedDto.trackingLink,
          status: 'show',
          comment: 'show',
        };

        // Cast type để tương thích với Zalo API (loại bỏ 'draft' từ status)
        const zaloCompatibleDto = {
          ...normalDto,
          status: (normalDto.status === 'draft' ? 'hide' : normalDto.status) as
            | 'show'
            | 'hide'
            | undefined,
        };
        zaloResult = await this.zaloArticleService.createNormalArticle(
          oa.accessToken,
          zaloCompatibleDto,
          user.id,
        );
      } else if (processedDto.type === 'video') {
        // Tạo bài viết video trên Zalo
        const videoDto: CreateZaloArticleVideoDto = {
          type: 'video',
          title: processedDto.title,
          description: processedDto.description || '',
          video_id: processedDto.videoId || '',
          avatar: processedDto.thumbnailUrl || '',
          status: 'show',
          comment: 'show',
        };

        zaloResult = await this.zaloArticleService.createVideoArticle(
          oa.accessToken,
          videoDto,
        );
      }

      if (zaloResult?.token) {
        token = zaloResult.token;
        status = ZaloArticleStatus.PENDING;
      }
    }

    // Lưu bài viết vào database
    const articleData: CreateZaloArticleData = {
      userId: user.id,
      integrationId: id,
      type:
        processedDto.type === 'normal'
          ? ZaloArticleType.NORMAL
          : ZaloArticleType.VIDEO,
      title: processedDto.title,
      description: processedDto.description,
      content:
        processedDto.type === 'normal'
          ? JSON.stringify(processedDto.body || [])
          : '',
      author: processedDto.author,
      coverPhotoUrl:
        processedDto.cover?.photo_url || processedDto.coverPhotoUrl,
      videoUrl: processedDto.videoId,
      thumbnailUrl: processedDto.thumbnailUrl,
      trackingLink: processedDto.trackingLink,
      token: token || undefined,
      articleId: articleId || undefined,
      status: status,
      comment: processedDto.comment,
      metadata: {
        originalStatus: processedDto.status,
        createdVia: 'api_v2',
      },
    };

    const article =
      await this.zaloArticleManagementService.createArticle(articleData);

    // Map sang response DTO
    const responseDto = this.mapToResponseDto(article);

    const message =
      processedDto.status === 'publish'
        ? status === ZaloArticleStatus.PENDING
          ? 'Tạo bài viết và đang publish lên Zalo'
          : 'Tạo bài viết thành công nhưng lỗi khi publish lên Zalo'
        : 'Tạo bài viết nháp thành công';

    return ApiResponseDto.success(responseDto, message);
  }

  /**
   * API test để tạo bài viết với trạng thái
   */
  @Post(':id/articles/test')
  @ApiOperation({
    summary: 'API test để tạo bài viết với trạng thái',
    description: `API test để kiểm tra chức năng tạo bài viết với trạng thái:
    - Tạo bài viết test với dữ liệu mẫu
    - Hỗ trợ cả draft và publish
    - Dùng để test chức năng mới`,
  })
  @ApiParam({
    name: 'id',
    description: 'UUID của Zalo Integration',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @ApiResponse({
    status: 201,
    description: 'Tạo bài viết test thành công',
  })
  async testCreateArticleWithStatus(
    @CurrentUser() user: JwtPayload,
    @Param('id', ParseUUIDPipe) id: string,
    @Body() body: { status: 'draft' | 'publish'; type?: 'normal' | 'video' },
  ): Promise<ApiResponseDto<ZaloArticleManagementResponseDto>> {
    const testDto: CreateZaloArticleWithStatusDto = {
      type: body.type || 'normal',
      title: `Test Article - ${new Date().toISOString()}`,
      description: 'Đây là bài viết test được tạo từ API mới',
      author: 'Test Author',
      coverPhotoUrl: 'https://via.placeholder.com/800x600.jpg',
      body: [
        {
          type: 'text',
          content: 'Đây là nội dung test của bài viết',
        },
      ],
      status: body.status,
      comment: 'Bài viết test',
    };

    return this.createArticleWithStatus(user, id, testDto);
  }

  /**
   * Publish bài viết draft lên Zalo
   */
  @Post(':id/articles/:articleId/publish')
  @ApiOperation({
    summary: 'Publish bài viết draft lên Zalo',
    description: `Publish bài viết đang ở trạng thái draft lên Zalo:
    - Chỉ có thể publish bài viết có status = 'draft'
    - Sau khi publish thành công, status sẽ chuyển thành 'pending' hoặc 'published'
    - Nếu lỗi khi publish, status sẽ chuyển thành 'failed'`,
  })
  @ApiParam({
    name: 'id',
    description: 'UUID của Zalo Integration',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @ApiParam({
    name: 'articleId',
    description: 'UUID của bài viết cần publish',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @ApiResponse({
    status: 200,
    description: 'Publish bài viết thành công',
  })
  async publishDraftArticle(
    @CurrentUser() user: JwtPayload,
    @Param('id', ParseUUIDPipe) id: string,
    @Param('articleId', ParseUUIDPipe) articleId: string,
  ): Promise<ApiResponseDto<ZaloArticleManagementResponseDto>> {
    // Kiểm tra Integration có tồn tại và thuộc về user không
    const oa = await this.zaloService.getOfficialAccountByIntegrationId(
      user.id,
      id,
    );

    // Lấy bài viết từ database
    const article = await this.zaloArticleManagementService.getArticleById(
      articleId,
      user.id,
    );
    if (!article) {
      throw new AppException(ErrorCode.NOT_FOUND, 'Không tìm thấy bài viết');
    }

    // Kiểm tra bài viết có thuộc về integration này không
    if (article.integrationId !== id) {
      throw new AppException(
        ErrorCode.FORBIDDEN,
        'Bài viết không thuộc về integration này',
      );
    }

    // Kiểm tra trạng thái bài viết
    if (article.status !== ZaloArticleStatus.DRAFT) {
      throw new AppException(
        ErrorCode.VALIDATION_ERROR,
        'Chỉ có thể publish bài viết ở trạng thái draft',
      );
    }

    let token: string | null = null;
    let status = ZaloArticleStatus.PENDING;

    // Gọi Zalo API để tạo bài viết
    let zaloResult: any;

    if (article.type === ZaloArticleType.NORMAL) {
      // Tạo bài viết normal trên Zalo
      const normalDto: CreateZaloArticleNormalDto = {
        type: 'normal',
        title: article.title,
        author: article.author || 'RedAI',
        cover: {
          cover_type: 'photo',
          photo_url: article.coverPhotoUrl || '',
          status: 'show',
        },
        description: article.description || '',
        body: article.content
          ? JSON.parse(article.content)
          : [{ type: 'text', content: article.description || '' }],
        related_medias: [],
        tracking_link: article.trackingLink,
        status: 'show',
        comment: 'show',
      };

      // Cast type để tương thích với Zalo API (loại bỏ 'draft' từ status)
      const zaloCompatibleDto = {
        ...normalDto,
        status: (normalDto.status === 'draft' ? 'hide' : normalDto.status) as
          | 'show'
          | 'hide'
          | undefined,
      };
      zaloResult = await this.zaloArticleService.createNormalArticle(
        oa.accessToken,
        zaloCompatibleDto,
        user.id,
      );
    } else if (article.type === ZaloArticleType.VIDEO) {
      // Tạo bài viết video trên Zalo
      const videoDto: CreateZaloArticleVideoDto = {
        type: 'video',
        title: article.title,
        description: article.description || '',
        video_id: article.videoUrl || '',
        avatar: article.thumbnailUrl || '',
        status: 'show',
        comment: 'show',
      };

      zaloResult = await this.zaloArticleService.createVideoArticle(
        oa.accessToken,
        videoDto,
      );
    }

    if (zaloResult?.token) {
      token = zaloResult.token;
      status = ZaloArticleStatus.PENDING;
    }

    // Cập nhật bài viết trong database
    const updatedArticle =
      await this.zaloArticleManagementService.updateArticle(
        articleId,
        user.id,
        {
          token: token || undefined,
          status: status,
          metadata: {
            ...article.metadata,
            publishedAt: new Date().toISOString(),
            publishedVia: 'api_publish',
          },
        },
      );

    // Map sang response DTO
    const responseDto = this.mapToResponseDto(updatedArticle);

    const message =
      status === ZaloArticleStatus.PENDING
        ? 'Publish bài viết thành công, đang chờ xử lý trên Zalo'
        : 'Lỗi khi publish bài viết lên Zalo';

    return ApiResponseDto.success(responseDto, message);
  }

  /**
   * Lấy danh sách bài viết từ database với phân trang chuẩn
   */
  @Get('article/paginated')
  @ApiOperation({
    summary: 'Lấy danh sách bài viết Zalo từ database với phân trang chuẩn',
    description: `Lấy danh sách bài viết từ database với phân trang theo chuẩn hệ thống:
    - Sử dụng page/limit thay vì offset/limit
    - Trả về PaginatedResult với meta information
    - Hỗ trợ lọc theo loại: normal hoặc video
    - Hỗ trợ lọc theo trạng thái: draft, pending, published, failed, deleted
    - Hỗ trợ lọc theo integrationId
    - Hỗ trợ tìm kiếm theo tiêu đề
    - Trả về thông tin đầy đủ từ database: ID, title, status, view, share, like, comment, dates, thumbnail`,
  })
  @ApiQuery({
    name: 'page',
    description: 'Số trang',
    required: false,
    type: Number,
    example: 1,
  })
  @ApiQuery({
    name: 'limit',
    description: 'Số lượng item mỗi trang',
    required: false,
    type: Number,
    example: 10,
  })
  @ApiQuery({
    name: 'type',
    description: 'Loại bài viết',
    required: false,
    enum: ['normal', 'video'],
  })
  @ApiQuery({
    name: 'status',
    description: 'Trạng thái bài viết',
    required: false,
    enum: ['draft', 'pending', 'published', 'failed', 'deleted'],
  })
  @ApiQuery({
    name: 'search',
    description: 'Từ khóa tìm kiếm theo tiêu đề',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'integrationId',
    description: 'ID của integration để lọc',
    required: false,
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy danh sách bài viết thành công',
    schema: {
      example: {
        success: true,
        message: 'Lấy danh sách bài viết thành công',
        data: {
          items: [
            {
              id: '550e8400-e29b-41d4-a716-************',
              type: 'normal',
              title: 'Tiêu đề bài viết',
              status: 'published',
              viewCount: 100,
              shareCount: 20,
              likeCount: 50,
              commentCount: 10,
              publishTime: 1690260497732,
              createdAt: '2023-07-25T10:15:30Z',
              updatedAt: '2023-07-25T10:20:45Z',
              thumbnailUrl: 'https://example.com/thumbnail.jpg',
              coverPhotoUrl: 'https://example.com/cover.jpg',
              integrationId: '550e8400-e29b-41d4-a716-************',
            },
          ],
          meta: {
            totalItems: 2,
            itemCount: 1,
            itemsPerPage: 10,
            totalPages: 1,
            currentPage: 1,
          },
        },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Tham số không hợp lệ',
  })
  async getArticleListPaginatedFromDatabase(
    @CurrentUser() user: JwtPayload,
    @Query() queryDto: QueryZaloArticleManagementDto,
  ): Promise<
    ApiResponseDto<PaginatedResult<ZaloArticleManagementResponseDto>>
  > {
    // Lấy danh sách bài viết từ database với các filter
    const result = await this.zaloArticleManagementService.getAllUserArticles(
      user.id,
      {
        page: queryDto.page || 1,
        limit: queryDto.limit || 10,
        type: queryDto.type,
        status: queryDto.status,
        search: queryDto.search,
        integrationId: queryDto.integrationId,
      },
    );

    // Map dữ liệu sang response DTO
    const mappedItems = result.items.map((item) => this.mapToResponseDto(item));

    // Tạo PaginatedResult theo chuẩn hệ thống
    const paginatedResult: PaginatedResult<ZaloArticleManagementResponseDto> = {
      items: mappedItems,
      meta: {
        totalItems: result.total,
        itemCount: mappedItems.length,
        itemsPerPage: result.limit,
        totalPages: Math.ceil(result.total / result.limit),
        currentPage: result.page,
      },
    };

    return ApiResponseDto.success(
      paginatedResult,
      'Lấy danh sách bài viết thành công',
    );
  }

  /**
   * Map ZaloArticle entity sang ZaloArticleManagementResponseDto
   */
  private mapToResponseDto(article: any): ZaloArticleManagementResponseDto {
    return {
      id: article.id,
      articleId: article.articleId || article.article_id || article.id,
      userId: article.userId,
      integrationId: article.integrationId,
      type: article.type,
      title: article.title,
      description: article.description,
      author: article.author,
      status: article.status,
      coverPhotoUrl: article.coverPhotoUrl,
      videoUrl: article.videoUrl,
      thumbnailUrl: article.thumbnailUrl,
      trackingLink: article.trackingLink,
      comment: article.comment,
      publishTime: article.publishTime,
      viewCount: article.viewCount || 0,
      likeCount: article.likeCount || 0,
      commentCount: article.commentCount || 0,
      shareCount: article.shareCount || 0,
      watchTimeTotal: article.watchTimeTotal,
      watchTimeAverage: article.watchTimeAverage,
      errorMessage: article.errorMessage,
      metadata: article.metadata,
      createdAt: article.createdAt,
      updatedAt: article.updatedAt,
    };
  }

  /**
   * Xóa nhiều bài viết cùng lúc
   */
  @Delete('articles/bulk-delete')
  @ApiOperation({
    summary: 'Xóa nhiều bài viết Zalo cùng lúc',
    description: `Xóa nhiều bài viết dựa trên danh sách ID từ database:
    - Tự động kiểm tra bài viết thuộc integration nào
    - Có thể xử lý bài viết từ nhiều integration khác nhau
    - Kiểm tra status bài viết trong database:
      + Nếu chưa publish lên Zalo (status = draft/pending): chỉ xóa trong database
      + Nếu đã publish lên Zalo (status = published): xóa cả trên Zalo và database
    - Trả về chi tiết kết quả xóa từng bài viết
    - Tối đa 50 bài viết mỗi lần
    - Chỉ có thể xóa bài viết thuộc về user hiện tại`,
  })
  @ApiResponse({
    status: 200,
    description: 'Xóa bài viết thành công',
    type: BulkDeleteZaloArticlesResponseDto,
    schema: {
      example: {
        success: true,
        message: 'Xử lý xong 3 bài viết. Thành công: 2, Thất bại: 1',
        data: {
          totalRequested: 3,
          totalSuccess: 2,
          totalFailed: 1,
          results: [
            {
              id: '550e8400-e29b-41d4-a716-************',
              title: 'Bài viết test 1',
              success: true,
              message: 'Xóa thành công trên cả Zalo và database',
              details: {
                deletedFromZalo: true,
                deletedFromDatabase: true,
                zaloArticleId: 'as4d5sfrhhjko5142s54f1',
              },
            },
          ],
          integrationStats: {
            '550e8400-e29b-41d4-a716-************': {
              integrationName: 'Zalo OA Test',
              processed: 3,
              success: 2,
              failed: 1,
            },
          },
        },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Dữ liệu đầu vào không hợp lệ',
  })
  @ApiResponse({
    status: 404,
    description: 'Không tìm thấy bài viết',
  })
  async bulkDeleteArticles(
    @CurrentUser() user: JwtPayload,
    @Body() bulkDeleteDto: BulkDeleteZaloArticlesDto,
  ): Promise<ApiResponseDto<BulkDeleteZaloArticlesResponseDto>> {
    this.logger.debug(
      `Bulk deleting ${bulkDeleteDto.articleIds.length} articles for user ${user.id}`,
    );

    // Lấy tất cả articles thuộc về user
    const articles = await this.zaloArticleManagementService.getArticlesByIds(
      bulkDeleteDto.articleIds,
      user.id,
    );

    // Kiểm tra articles không tồn tại hoặc không thuộc về user
    const foundIds = articles.map((article) => article.id);
    const notFoundIds = bulkDeleteDto.articleIds.filter(
      (id) => !foundIds.includes(id),
    );

    if (notFoundIds.length > 0) {
      throw new AppException(
        ErrorCode.NOT_FOUND,
        `Không tìm thấy các bài viết: ${notFoundIds.join(', ')}`,
      );
    }

    // Nhóm articles theo integration để lấy access token
    const articlesByIntegration = new Map<string, typeof articles>();
    for (const article of articles) {
      const integrationId = article.integrationId;
      if (!articlesByIntegration.has(integrationId)) {
        articlesByIntegration.set(integrationId, []);
      }
      articlesByIntegration.get(integrationId)!.push(article);
    }

    const results: any[] = [];
    const integrationStats: Record<string, any> = {};
    let totalSuccess = 0;
    let totalFailed = 0;

    // Xử lý từng integration
    for (const [integrationId, integrationArticles] of articlesByIntegration) {
      let oa: any = null;
      let integrationName = 'Unknown Integration';

      // Lấy thông tin integration và access token (nếu cần)
      try {
        // Skip placeholder integration ID
        if (integrationId !== '********-0000-0000-0000-********0000') {
          oa = await this.zaloService.getOfficialAccountByIntegrationId(
            user.id,
            integrationId,
          );
          integrationName = oa.name || `Integration ${integrationId}`;
        } else {
          integrationName = 'Draft Articles (No Integration)';
        }
      } catch (error) {
        this.logger.warn(
          `Cannot get integration ${integrationId}: ${error.message}`,
        );
      }

      let integrationSuccess = 0;
      let integrationFailed = 0;

      // Xử lý từng article trong integration
      for (const article of integrationArticles) {
        const result: any = {
          id: article.id,
          title: article.title,
          success: false,
          message: '',
          details: {
            deletedFromZalo: false,
            deletedFromDatabase: false,
            zaloArticleId: article.articleId,
          },
        };

        try {
          // Kiểm tra status để quyết định có cần xóa trên Zalo không
          const needDeleteFromZalo =
            article.status === 'published' && article.articleId && oa;

          if (needDeleteFromZalo) {
            // Xóa trên Zalo trước
            try {
              await this.zaloArticleService.removeArticle(
                oa.accessToken,
                article.articleId,
              );
              result.details.deletedFromZalo = true;
              this.logger.debug(
                `Deleted article ${article.articleId} from Zalo`,
              );
            } catch (zaloError) {
              this.logger.warn(
                `Failed to delete article ${article.articleId} from Zalo: ${zaloError.message}`,
              );
              // Vẫn tiếp tục xóa database
            }
          }

          // Xóa trong database
          const dbDeleted =
            await this.zaloArticleManagementService.deleteArticle(
              article.id,
              user.id,
            );
          result.details.deletedFromDatabase = dbDeleted;

          if (result.details.deletedFromDatabase) {
            result.success = true;
            result.message = needDeleteFromZalo
              ? result.details.deletedFromZalo
                ? 'Xóa thành công trên cả Zalo và database'
                : 'Xóa thành công trong database (có thể đã bị xóa trên Zalo trước đó)'
              : 'Xóa thành công trong database (bài viết chưa publish lên Zalo)';
            totalSuccess++;
            integrationSuccess++;
          } else {
            result.message = 'Không thể xóa bài viết trong database';
            totalFailed++;
            integrationFailed++;
          }
        } catch (error) {
          result.message = `Lỗi khi xóa bài viết: ${error.message}`;
          result.details.error = error.message;
          totalFailed++;
          integrationFailed++;
          this.logger.error(
            `Error deleting article ${article.id}: ${error.message}`,
            error.stack,
          );
        }

        results.push(result);
      }

      // Thống kê theo integration
      integrationStats[integrationId] = {
        integrationName,
        processed: integrationArticles.length,
        success: integrationSuccess,
        failed: integrationFailed,
      };
    }

    const response: BulkDeleteZaloArticlesResponseDto = {
      totalRequested: bulkDeleteDto.articleIds.length,
      totalSuccess,
      totalFailed,
      results,
      integrationStats,
    };

    return ApiResponseDto.success(
      response,
      `Xử lý xong ${bulkDeleteDto.articleIds.length} bài viết. Thành công: ${totalSuccess}, Thất bại: ${totalFailed}`,
    );
  }
}

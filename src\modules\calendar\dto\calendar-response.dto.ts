import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  CalendarActionType,
  CalendarEventStatus,
  CalendarPriority,
  ExecutionStatus,
} from '../entities';

/**
 * DTO response sự kiện calendar
 */
export class CalendarEventResponseDto {
  @ApiProperty({ description: 'ID sự kiện' })
  id: string;

  @ApiProperty({ description: 'ID người dùng' })
  userId: number;

  @ApiProperty({
    description: 'Loại action calendar',
    enum: CalendarActionType,
  })
  actionType: CalendarActionType;

  @ApiProperty({ description: 'Tiêu đề sự kiện' })
  title: string;

  @ApiPropertyOptional({ description: 'Mô tả sự kiện' })
  description?: string;

  @ApiProperty({ description: 'Thời gian bắt đầu' })
  startTime: Date;

  @ApiPropertyOptional({ description: 'Thời gian kết thúc' })
  endTime?: Date;

  @ApiProperty({ description: 'Múi giờ' })
  timeZone: string;

  @ApiProperty({ description: 'Trạng thái sự kiện', enum: CalendarEventStatus })
  status: CalendarEventStatus;

  @ApiProperty({ description: 'Mức độ ưu tiên', enum: CalendarPriority })
  priority: CalendarPriority;

  @ApiProperty({ description: 'Cấu hình action cụ thể' })
  actionConfig: any;

  @ApiProperty({ description: 'Trạng thái thực thi', enum: ExecutionStatus })
  executionStatus: ExecutionStatus;

  @ApiPropertyOptional({ description: 'Thời gian bắt đầu thực thi' })
  executionStartTime?: Date;

  @ApiPropertyOptional({ description: 'Thời gian kết thúc thực thi' })
  executionEndTime?: Date;

  @ApiPropertyOptional({ description: 'Kết quả thực thi' })
  executionResult?: any;

  @ApiPropertyOptional({ description: 'Lỗi thực thi' })
  executionError?: string;

  @ApiPropertyOptional({ description: 'ID job trong queue' })
  jobId?: string;

  @ApiProperty({ description: 'Số lần retry' })
  retryCount: number;

  @ApiPropertyOptional({ description: 'Thời gian retry tiếp theo' })
  nextRetryTime?: Date;

  @ApiProperty({ description: 'Có được kích hoạt' })
  isActive: boolean;

  @ApiPropertyOptional({ description: 'Metadata bổ sung' })
  metadata?: any;

  @ApiProperty({ description: 'Thời gian tạo' })
  createdAt: Date;

  @ApiProperty({ description: 'Thời gian cập nhật' })
  updatedAt: Date;

  @ApiPropertyOptional({ description: 'Lịch sử thực thi' })
  executionHistory?: CalendarExecutionHistoryResponseDto[];
}

/**
 * DTO response lịch sử thực thi calendar
 */
export class CalendarExecutionHistoryResponseDto {
  @ApiProperty({ description: 'ID lịch sử thực thi' })
  id: string;

  @ApiProperty({ description: 'ID sự kiện' })
  eventId: string;

  @ApiProperty({ description: 'Thời gian thực thi' })
  executionTime: Date;

  @ApiProperty({ description: 'Trạng thái thực thi', enum: ExecutionStatus })
  status: ExecutionStatus;

  @ApiPropertyOptional({ description: 'Thời gian bắt đầu' })
  startTime?: Date;

  @ApiPropertyOptional({ description: 'Thời gian kết thúc' })
  endTime?: Date;

  @ApiPropertyOptional({ description: 'Thời gian thực thi (milliseconds)' })
  duration?: number;

  @ApiPropertyOptional({ description: 'Kết quả thực thi' })
  result?: any;

  @ApiPropertyOptional({ description: 'Lỗi thực thi' })
  error?: string;

  @ApiPropertyOptional({ description: 'ID job trong queue' })
  jobId?: string;

  @ApiProperty({ description: 'Số lần retry' })
  retryAttempt: number;

  @ApiPropertyOptional({ description: 'Cấu hình thực thi' })
  executionConfig?: any;

  @ApiPropertyOptional({ description: 'Metadata' })
  metadata?: any;

  @ApiProperty({ description: 'Thời gian tạo' })
  createdAt: Date;
}

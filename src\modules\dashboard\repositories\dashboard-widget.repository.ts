import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, SelectQueryBuilder } from 'typeorm';
import { DashboardWidget } from '../entities/dashboard-widget.entity';
import { QueryDashboardWidgetDto } from '../dto/dashboard-widget.dto';
import { DashboardWidgetType } from '../enums/dashboard-widget-type.enum';

@Injectable()
export class DashboardWidgetRepository {
  private readonly logger = new Logger(DashboardWidgetRepository.name);

  constructor(
    @InjectRepository(DashboardWidget)
    private readonly repository: Repository<DashboardWidget>,
  ) {}

  /**
   * Tạo dashboard widget mới
   */
  async create(data: Partial<DashboardWidget>): Promise<DashboardWidget> {
    try {
      const widget = this.repository.create(data);
      const result = await this.repository.save(widget);
      this.logger.log(`Created dashboard widget: ${result.id}`);
      return result;
    } catch (error) {
      this.logger.error(
        `Error creating dashboard widget: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Tạo nhiều widgets cùng lúc
   */
  async createMany(
    widgets: Partial<DashboardWidget>[],
  ): Promise<DashboardWidget[]> {
    try {
      const entities = widgets.map((data) => this.repository.create(data));
      const result = await this.repository.save(entities);
      this.logger.log(`Created ${result.length} dashboard widgets`);
      return result;
    } catch (error) {
      this.logger.error(
        `Error creating dashboard widgets: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Tìm widget theo ID
   */
  async findById(id: string): Promise<DashboardWidget | null> {
    try {
      const result = await this.repository.findOne({
        where: { id },
        relations: ['dashboardPage'],
      });
      return result;
    } catch (error) {
      this.logger.error(
        `Error finding dashboard widget by ID ${id}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Tìm widget theo widgetKey và dashboardPageId
   */
  async findByKeyAndPage(
    widgetKey: string,
    dashboardPageId: string,
  ): Promise<DashboardWidget | null> {
    try {
      const result = await this.repository.findOne({
        where: {
          widgetKey,
          dashboardPageId,
        },
      });
      return result;
    } catch (error) {
      this.logger.error(
        `Error finding widget by key ${widgetKey} and page ${dashboardPageId}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Kiểm tra widgetKey có tồn tại trong dashboard page không
   */
  async isWidgetKeyExists(
    widgetKey: string,
    dashboardPageId: string,
    excludeId?: string,
  ): Promise<boolean> {
    try {
      const queryBuilder = this.repository.createQueryBuilder('widget');

      queryBuilder
        .where('widget.widgetKey = :widgetKey', { widgetKey })
        .andWhere('widget.dashboardPageId = :dashboardPageId', {
          dashboardPageId,
        });

      if (excludeId) {
        queryBuilder.andWhere('widget.id != :excludeId', { excludeId });
      }

      const count = await queryBuilder.getCount();
      return count > 0;
    } catch (error) {
      this.logger.error(
        `Error checking widget key existence: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Lấy tất cả widgets của một dashboard page
   */
  async findByDashboardPageId(
    dashboardPageId: string,
  ): Promise<DashboardWidget[]> {
    try {
      const result = await this.repository.find({
        where: { dashboardPageId },
        order: { zIndex: 'ASC', createdAt: 'ASC' },
      });
      return result;
    } catch (error) {
      this.logger.error(
        `Error finding widgets by dashboard page ${dashboardPageId}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Query widgets với filter
   */
  async findMany(query: QueryDashboardWidgetDto): Promise<DashboardWidget[]> {
    try {
      const queryBuilder = this.createQueryBuilder(query);
      const result = await queryBuilder.getMany();
      return result;
    } catch (error) {
      this.logger.error(
        `Error querying dashboard widgets: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Cập nhật widget
   */
  async update(
    id: string,
    data: Partial<DashboardWidget>,
  ): Promise<DashboardWidget | null> {
    try {
      await this.repository.update(id, data as any);
      const result = await this.findById(id);

      if (result) {
        this.logger.log(`Updated dashboard widget: ${id}`);
      }

      return result;
    } catch (error) {
      this.logger.error(
        `Error updating dashboard widget ${id}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Cập nhật layout của nhiều widgets cùng lúc
   */
  async updateLayouts(
    updates: Array<{ id: string; layout: any; responsiveLayout?: any }>,
  ): Promise<void> {
    try {
      const promises = updates.map(({ id, layout, responsiveLayout }) => {
        const updateData: any = { layout };
        if (responsiveLayout) {
          updateData.responsiveLayout = responsiveLayout;
        }
        return this.repository.update(id, updateData);
      });

      await Promise.all(promises);
      this.logger.log(`Updated layouts for ${updates.length} widgets`);
    } catch (error) {
      this.logger.error(
        `Error updating widget layouts: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Xóa widget
   */
  async delete(id: string): Promise<boolean> {
    try {
      const result = await this.repository.delete(id);
      const deleted = result.affected && result.affected > 0;

      if (deleted) {
        this.logger.log(`Deleted dashboard widget: ${id}`);
      }

      return !!deleted;
    } catch (error) {
      this.logger.error(
        `Error deleting dashboard widget ${id}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Xóa tất cả widgets của một dashboard page
   */
  async deleteByDashboardPageId(dashboardPageId: string): Promise<number> {
    try {
      const result = await this.repository.delete({ dashboardPageId });
      const deletedCount = result.affected || 0;

      if (deletedCount > 0) {
        this.logger.log(
          `Deleted ${deletedCount} widgets from dashboard page: ${dashboardPageId}`,
        );
      }

      return deletedCount;
    } catch (error) {
      this.logger.error(
        `Error deleting widgets from dashboard page ${dashboardPageId}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Lấy widgets theo loại
   */
  async findByWidgetType(
    widgetType: DashboardWidgetType,
  ): Promise<DashboardWidget[]> {
    try {
      const result = await this.repository.find({
        where: { widgetType },
        relations: ['dashboardPage'],
        order: { createdAt: 'DESC' },
      });
      return result;
    } catch (error) {
      this.logger.error(
        `Error finding widgets by type ${widgetType}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Lấy thống kê widgets
   */
  async getWidgetStatistics(): Promise<{
    totalWidgets: number;
    widgetsByType: Record<string, number>;
    widgetsByPage: Record<string, number>;
  }> {
    try {
      // Total widgets
      const totalWidgets = await this.repository.count();

      // Widgets by type
      const typeStats = await this.repository
        .createQueryBuilder('widget')
        .select('widget.widgetType', 'type')
        .addSelect('COUNT(*)', 'count')
        .groupBy('widget.widgetType')
        .getRawMany();

      const widgetsByType = typeStats.reduce((acc, stat) => {
        acc[stat.type] = parseInt(stat.count);
        return acc;
      }, {});

      // Widgets by page
      const pageStats = await this.repository
        .createQueryBuilder('widget')
        .leftJoin('widget.dashboardPage', 'page')
        .select('page.name', 'pageName')
        .addSelect('COUNT(*)', 'count')
        .groupBy('page.name')
        .getRawMany();

      const widgetsByPage = pageStats.reduce((acc, stat) => {
        acc[stat.pageName || 'Unknown'] = parseInt(stat.count);
        return acc;
      }, {});

      return {
        totalWidgets,
        widgetsByType,
        widgetsByPage,
      };
    } catch (error) {
      this.logger.error(
        `Error getting widget statistics: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Tìm vị trí trống cho widget mới
   */
  async findEmptyPosition(
    dashboardPageId: string,
    width: number = 4,
    height: number = 3,
  ): Promise<{ x: number; y: number }> {
    try {
      const widgets = await this.findByDashboardPageId(dashboardPageId);

      // Tạo grid để track vị trí đã sử dụng
      const gridCols = 12; // Giả sử grid có 12 cột
      const occupiedPositions = new Set<string>();

      // Đánh dấu các vị trí đã được sử dụng
      widgets.forEach((widget) => {
        const { x, y, w, h } = widget.layout;
        for (let row = y; row < y + h; row++) {
          for (let col = x; col < x + w; col++) {
            occupiedPositions.add(`${col},${row}`);
          }
        }
      });

      // Tìm vị trí trống
      for (let y = 0; y < 100; y++) {
        // Giới hạn 100 rows
        for (let x = 0; x <= gridCols - width; x++) {
          let canPlace = true;

          // Kiểm tra xem có thể đặt widget tại vị trí này không
          for (let row = y; row < y + height && canPlace; row++) {
            for (let col = x; col < x + width && canPlace; col++) {
              if (occupiedPositions.has(`${col},${row}`)) {
                canPlace = false;
              }
            }
          }

          if (canPlace) {
            return { x, y };
          }
        }
      }

      // Nếu không tìm được vị trí trống, đặt ở cuối
      const maxY = Math.max(0, ...widgets.map((w) => w.layout.y + w.layout.h));
      return { x: 0, y: maxY };
    } catch (error) {
      this.logger.error(
        `Error finding empty position: ${error.message}`,
        error.stack,
      );
      return { x: 0, y: 0 };
    }
  }

  /**
   * Lấy tất cả widgets của employee
   */
  async findByEmployeeId(employeeId: number): Promise<DashboardWidget[]> {
    try {
      const result = await this.repository
        .createQueryBuilder('widget')
        .leftJoin('widget.dashboardPage', 'page')
        .where('page.employeeId = :employeeId', { employeeId })
        .andWhere('page.ownerType = :ownerType', { ownerType: 'EMPLOYEE' })
        .orderBy('widget.createdAt', 'DESC')
        .getMany();

      return result;
    } catch (error) {
      this.logger.error(
        `Error finding widgets by employee ID ${employeeId}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Tạo query builder với filters
   */
  private createQueryBuilder(
    query: QueryDashboardWidgetDto,
  ): SelectQueryBuilder<DashboardWidget> {
    const queryBuilder = this.repository.createQueryBuilder('widget');

    // Dashboard page filter
    if (query.dashboardPageId) {
      queryBuilder.where('widget.dashboardPageId = :dashboardPageId', {
        dashboardPageId: query.dashboardPageId,
      });
    }

    // Search
    if (query.search) {
      queryBuilder.andWhere(
        '(widget.name ILIKE :search OR widget.widgetKey ILIKE :search)',
        { search: `%${query.search}%` },
      );
    }

    // Widget type filter
    if (query.widgetType) {
      queryBuilder.andWhere('widget.widgetType = :widgetType', {
        widgetType: query.widgetType,
      });
    }

    // Visibility filter
    if (query.isVisible !== undefined) {
      queryBuilder.andWhere('widget.isVisible = :isVisible', {
        isVisible: query.isVisible,
      });
    }

    // Sorting
    const sortBy = query.sortBy || 'zIndex';
    const sortOrder = query.sortOrder || 'ASC';
    queryBuilder.orderBy(`widget.${sortBy}`, sortOrder);

    return queryBuilder;
  }
}

import { Injectable, Logger } from '@nestjs/common';
import { AppException, ErrorCode } from '@common/exceptions';
import { PaginatedResult } from '@common/response';
import { ZaloGmfGroupManagementService } from '@shared/services/zalo';
import { ZaloTokenService } from './zalo-token.service';
import {
  ZaloGroupRepository,
  ZaloGroupMemberRepository,
} from '../repositories';
import { ZaloGroup, ZaloGroupStatus } from '../entities/zalo-group.entity';
import {
  ZaloGroupMemberStatus,
  ZaloGroupMemberRole,
} from '../entities/zalo-group-member.entity';
import { ZaloOALegacyWrapperService } from '@/modules/integration/services/zalo-oa-legacy-wrapper.service';
import {
  CreateZaloGroupDto,
  UpdateZaloGroupDto,
  UpdateZaloGroupAvatarDto,
  ZaloGroupQueryDto,
  InviteZaloGroupMembersDto,
  InviteAudiencesToZaloGroupDto,
  ManageZaloGroupMembersDto,
  ManageZaloGroupAdminsDto,
  ZaloGroupMemberQueryDto,
  ZaloAssetListDto,
  ZaloPendingMembersQueryDto,
  ZaloAcceptPendingMembersDto,
  ZaloAcceptPendingMembersResponseDto,
  ZaloRejectPendingMembersDto,
  ZaloRejectPendingMembersResponseDto,
  ZaloRemoveMembersDto,
  ZaloRemoveMembersResponseDto,
  ZaloAddAdminsDto,
  ZaloAddAdminsResponseDto,
  ZaloRemoveAdminsDto,
  ZaloRemoveAdminsResponseDto,
  SyncGroupMembersDto,
  SyncGroupMembersResponseDto,
} from '../dto';
import { ZaloService } from './zalo.service';
import { UserAudienceService } from './user-audience.service';

@Injectable()
export class ZaloGroupManagementService {
  private readonly logger = new Logger(ZaloGroupManagementService.name);

  constructor(
    private readonly zaloGmfGroupService: ZaloGmfGroupManagementService,
    private readonly zaloTokenService: ZaloTokenService,
    private readonly zaloGroupRepository: ZaloGroupRepository,
    private readonly zaloGroupMemberRepository: ZaloGroupMemberRepository,
    private readonly zaloOALegacyWrapperService: ZaloOALegacyWrapperService,
    private readonly zaloService: ZaloService,
    private readonly userAudienceService: UserAudienceService,
  ) {}

  /**
   * Tạo nhóm chat mới
   */
  async createGroup(
    userId: number,
    oaId: string,
    integrationId: string,
    createDto: CreateZaloGroupDto,
  ): Promise<ZaloGroup> {
    try {
      this.logger.debug(
        `Creating new group for user ${userId} with oaId ${oaId}`,
      );

      const oa = await this.zaloService.getOfficialAccountByIntegrationId(
        userId,
        integrationId,
      );

      // Tạo nhóm qua Zalo API
      const zaloResult = await this.zaloGmfGroupService.createGroup(
        oa.accessToken,
        {
          group_name: createDto.groupName,
          description: createDto.description,
          ...(createDto.avatarUrl && { avatar_url: createDto.avatarUrl }),
          asset_id: createDto.assetId,
          member_uids: createDto.memberUids,
        },
      );

      if (!zaloResult.group_id) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          'Không thể tạo nhóm chat trên Zalo',
        );
      }

      // Lưu thông tin nhóm vào database
      const groupData: Partial<ZaloGroup> = {
        userId: userId,
        zaloOfficialAccountId: integrationId, // UUID string từ URL parameter
        integrationId: integrationId, // Thêm integration_id cho hệ thống integration mới
        groupId: zaloResult.group_id,
        groupName: createDto.groupName,
        description: createDto.description,
        avatarUrl: createDto.avatarUrl || zaloResult.avatar,
        memberCount: createDto.memberUids.length + 1, // +1 cho OA
        adminCount: 1, // OA là admin
        status: ZaloGroupStatus.ACTIVE,
        isOaAdmin: true,
        createdByOa: true,
        lastActivityAt: Date.now(),
        metadata: createDto.metadata,
      };

      const savedGroup = await this.zaloGroupRepository.create(groupData);

      // Lưu thông tin thành viên ban đầu
      const memberData = createDto.memberUids.map((memberUid) => ({
        userId: userId, // Keep as string for ZaloGroupMember entity
        zaloGroupId: savedGroup.id,
        memberUserId: memberUid,
        role: ZaloGroupMemberRole.MEMBER,
        status: ZaloGroupMemberStatus.ACTIVE,
        joinedAt: Date.now(),
        invitedBy: 'OA',
      }));

      if (memberData.length > 0) {
        await this.zaloGroupMemberRepository.createMany(memberData);
      }

      return savedGroup;
    } catch (error) {
      this.logger.error(`Error creating group: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Lỗi khi tạo nhóm chat',
      );
    }
  }

  /**
   * Lấy danh sách nhóm từ Zalo API
   */
  async getGroups(
    userId: number,
    queryDto: ZaloGroupQueryDto,
  ): Promise<PaginatedResult<ZaloGroup>> {
    try {
      this.logger.debug(
        `Getting groups for userId: ${userId}, integrationId: ${queryDto.integrationId}`,
      );

      const oa = await this.zaloService.getOfficialAccountByIntegrationId(
        userId,
        queryDto.integrationId,
      );

      this.logger.debug(
        `Found OA: ${oa.oaId}, access token length: ${oa.accessToken?.length || 0}`,
      );

      if (!oa.accessToken) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          'Official Account không có access token hợp lệ',
        );
      }

      // Tính toán offset từ page và limit
      const page = queryDto.page || 1;
      const limit = Math.min(queryDto.limit || 20, 50); // Zalo API giới hạn tối đa 50
      const offset = (page - 1) * limit;

      this.logger.debug(
        `Calling getGroupsOfOA with offset: ${offset}, limit: ${limit}`,
      );

      // Lấy dữ liệu từ Zalo API
      const zaloResult = await this.zaloGmfGroupService.getGroupsOfOA(
        oa.accessToken,
        offset,
        limit,
      );

      // Chuyển đổi ZaloGroupInfo sang ZaloGroup entity format
      const groups: ZaloGroup[] = zaloResult.groups.map((zaloGroup) => {
        const group = new ZaloGroup();
        group.id = `${userId}-${queryDto.integrationId}-${zaloGroup.group_id}`; // Tạo ID tạm thời
        group.userId = userId;
        group.zaloOfficialAccountId = queryDto.integrationId;
        group.groupId = zaloGroup.group_id;
        group.groupName = zaloGroup.name;
        group.description = zaloGroup.group_description || '';
        group.avatarUrl = zaloGroup.avatar || '';
        group.memberCount = zaloGroup.total_member;
        group.adminCount = 1; // Mặc định OA là admin
        group.status = this.mapZaloStatusToEntityStatus(zaloGroup.status);
        group.isOaAdmin = true; // OA luôn là admin trong các nhóm mà nó tham gia
        group.createdByOa = false; // Không biết chính xác, mặc định false
        group.lastActivityAt = Date.now(); // Không có thông tin created_time trong API mới
        group.metadata = {};
        group.createdAt = Date.now(); // Không có thông tin created_time trong API mới
        group.updatedAt = Date.now();
        return group;
      });

      // Áp dụng filter nếu có
      let filteredGroups = groups;

      // Filter theo search
      if (queryDto.search) {
        const searchLower = queryDto.search.toLowerCase();
        filteredGroups = filteredGroups.filter(
          (group) =>
            group.groupName.toLowerCase().includes(searchLower) ||
            (group.description &&
              group.description.toLowerCase().includes(searchLower)),
        );
      }

      // Filter theo status
      if (queryDto.status) {
        filteredGroups = filteredGroups.filter(
          (group) => group.status === queryDto.status,
        );
      }

      // Filter theo isOaAdmin
      if (queryDto.isOaAdmin !== undefined) {
        filteredGroups = filteredGroups.filter(
          (group) => group.isOaAdmin === queryDto.isOaAdmin,
        );
      }

      // Filter theo createdByOa
      if (queryDto.createdByOa !== undefined) {
        filteredGroups = filteredGroups.filter(
          (group) => group.createdByOa === queryDto.createdByOa,
        );
      }

      // Tạo pagination metadata
      const totalItems = zaloResult.total;
      const totalPages = Math.ceil(totalItems / limit);

      return {
        items: filteredGroups,
        meta: {
          totalItems,
          itemCount: filteredGroups.length,
          itemsPerPage: limit,
          totalPages,
          currentPage: page,
        },
      };
    } catch (error) {
      this.logger.error(`Error getting groups: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Lỗi khi lấy danh sách nhóm',
      );
    }
  }

  /**
   * Chuyển đổi status từ Zalo API sang entity status
   */
  private mapZaloStatusToEntityStatus(
    zaloStatus: 'enabled' | 'disabled',
  ): ZaloGroupStatus {
    switch (zaloStatus) {
      case 'enabled':
        return ZaloGroupStatus.ACTIVE;
      case 'disabled':
        return ZaloGroupStatus.INACTIVE;
      default:
        return ZaloGroupStatus.ACTIVE;
    }
  }

  /**
   * Lấy thông tin chi tiết nhóm theo ID entity từ Zalo API và đồng bộ vào database
   */
  async getGroupDetailById(userId: number, id: string): Promise<ZaloGroup> {
    try {
      this.logger.log(
        `Lấy chi tiết nhóm theo ID entity ${id} cho user ${userId}`,
      );

      // Tìm nhóm trong database theo ID khóa chính
      const existingGroup = await this.zaloGroupRepository.findOne({
        where: { id, userId },
      });

      if (!existingGroup) {
        throw new AppException(
          ErrorCode.RESOURCE_NOT_FOUND,
          'Không tìm thấy nhóm',
        );
      }

      // Lấy thông tin chi tiết nhóm từ Zalo API sử dụng integrationId và groupId từ database
      const result = await this.getGroupDetail(
        userId,
        existingGroup.zaloOfficialAccountId,
        existingGroup.groupId,
      );

      this.logger.log(
        `Lấy chi tiết nhóm theo ID entity thành công: ${result.groupName}`,
      );

      return result;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(
        `Lỗi khi lấy chi tiết nhóm theo ID entity: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Lỗi khi lấy chi tiết nhóm theo ID entity',
      );
    }
  }

  /**
   * Lấy thông tin chi tiết nhóm từ Zalo API và đồng bộ vào database
   */
  async getGroupDetail(
    userId: number,
    integrationId: string,
    groupId: string,
  ): Promise<ZaloGroup> {
    try {
      // Validate integration exists và user có quyền truy cập
      const oa = await this.zaloService.getOfficialAccountByIntegrationId(
        userId,
        integrationId,
      );

      // Kiểm tra xem groupId là UUID database hay Zalo group_id
      let zaloGroupId = groupId;

      // Nếu groupId có dạng UUID (36 ký tự với dấu gạch ngang),
      // thì đây là database ID, cần lấy Zalo group_id từ database
      if (groupId.length === 36 && groupId.includes('-')) {
        const existingGroup = await this.zaloGroupRepository.findOne({
          where: {
            id: groupId,
            userId,
            zaloOfficialAccountId: integrationId,
          },
        });

        if (!existingGroup) {
          throw new AppException(ErrorCode.NOT_FOUND, 'Không tìm thấy nhóm');
        }

        zaloGroupId = existingGroup.groupId;
      }

      // Lấy thông tin nhóm từ Zalo API
      const zaloGroupInfo = await this.zaloGmfGroupService.getGroupInfo(
        oa.accessToken,
        zaloGroupId,
      );

      // Tìm nhóm trong database hoặc tạo mới
      const existingGroup = await this.zaloGroupRepository.findOne({
        where: {
          userId,
          zaloOfficialAccountId: integrationId,
          groupId: zaloGroupInfo.group_info.group_id,
        },
      });

      // Chuẩn bị dữ liệu để cập nhật/tạo mới
      const groupData: Partial<ZaloGroup> = {
        userId,
        zaloOfficialAccountId: integrationId,
        groupId: zaloGroupInfo.group_info.group_id,
        groupName: zaloGroupInfo.group_info.name,
        description: zaloGroupInfo.group_info.group_description || '',
        avatarUrl: zaloGroupInfo.group_info.avatar || '',
        memberCount: zaloGroupInfo.group_info.total_member,
        status: this.mapZaloStatusToEntityStatus(
          zaloGroupInfo.group_info.status,
        ),

        // Thông tin từ group_info
        groupLink: zaloGroupInfo.group_info.group_link,
        zaloStatus: zaloGroupInfo.group_info.status,
        totalMember: zaloGroupInfo.group_info.total_member,
        maxMember: zaloGroupInfo.group_info.max_member,
        autoDeleteDate: zaloGroupInfo.group_info.auto_delete_date,

        // Thông tin từ asset_info
        assetType: zaloGroupInfo.asset_info?.asset_type,
        assetId: zaloGroupInfo.asset_info?.asset_id,
        validThrough: zaloGroupInfo.asset_info?.valid_through,
        autoRenew:
          zaloGroupInfo.asset_info?.auto_renew === 'true' ||
          zaloGroupInfo.asset_info?.auto_renew === 'True',

        // Thông tin từ group_setting
        lockSendMsg: zaloGroupInfo.group_setting?.lock_send_msg,
        joinAppr: zaloGroupInfo.group_setting?.join_appr,
        enableMsgHistory: zaloGroupInfo.group_setting?.enable_msg_history,
        enableLinkJoin: zaloGroupInfo.group_setting?.enable_link_join,

        // Thông tin đồng bộ
        lastSyncAt: Date.now(),
        lastActivityAt: Date.now(),

        // Metadata để backward compatibility
        metadata: {
          asset_info: zaloGroupInfo.asset_info,
          group_setting: zaloGroupInfo.group_setting,
        },
      };

      if (existingGroup) {
        // Cập nhật nhóm hiện có
        Object.assign(existingGroup, groupData);
        existingGroup.updatedAt = Date.now();
        const updatedGroup = await this.zaloGroupRepository.save(existingGroup);
        this.logger.debug(`Updated existing group ${groupId} in database`);
        return updatedGroup;
      } else {
        // Tạo nhóm mới
        const newGroupData = {
          ...groupData,
          adminCount: 1, // Mặc định OA là admin
          isOaAdmin: true, // OA luôn là admin trong các nhóm mà nó tham gia
          createdByOa: false, // Không biết chính xác, mặc định false
        };
        const newGroup = await this.zaloGroupRepository.create(newGroupData);
        this.logger.debug(`Created new group ${groupId} in database`);
        return newGroup;
      }
    } catch (error) {
      this.logger.error(
        `Error getting group detail: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Lỗi khi lấy thông tin nhóm',
      );
    }
  }

  /**
   * Cập nhật thông tin nhóm
   */
  async updateGroup(
    integrationId: string,
    userId: number,
    groupId: string,
    updateDto: UpdateZaloGroupDto,
  ): Promise<ZaloGroup> {
    try {
      const group = await this.getGroupDetail(userId, integrationId, groupId);

      // Lấy Integration để có oaId
      const integration = await this.zaloOALegacyWrapperService.findById(
        group.zaloOfficialAccountId,
      );
      if (!integration) {
        throw new AppException(
          ErrorCode.NOT_FOUND,
          'Zalo Official Account Integration không tồn tại',
        );
      }

      // Lấy access token
      const accessToken = await this.zaloTokenService.getValidAccessToken(
        integration.oaId,
      );

      // Cập nhật thông tin nhóm qua Zalo API
      if (updateDto.groupName || updateDto.description) {
        await this.zaloGmfGroupService.updateGroupInfo(
          accessToken,
          group.groupId,
          {
            group_name: updateDto.groupName,
            description: updateDto.description,
          },
        );
      }

      // Cập nhật trong database
      const updatedGroup = await this.zaloGroupRepository.update(groupId, {
        groupName: updateDto.groupName || group.groupName,
        description: updateDto.description || group.description,
        metadata: { ...group.metadata, ...updateDto.metadata },
      });

      return updatedGroup!;
    } catch (error) {
      this.logger.error(`Error updating group: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Lỗi khi cập nhật thông tin nhóm',
      );
    }
  }

  /**
   * Cập nhật avatar nhóm
   */
  async updateGroupAvatar(
    userId: number,
    integrationId: string,
    groupId: string,
    updateDto: UpdateZaloGroupAvatarDto,
  ): Promise<ZaloGroup> {
    try {
      const oa = await this.zaloService.getOfficialAccountByIntegrationId(
        userId,
        integrationId,
      );

      // Lấy thông tin nhóm
      const group = await this.getGroupDetail(userId, integrationId, groupId);

      // Lấy Integration để có oaId
      const integration = await this.zaloOALegacyWrapperService.findById(
        group.zaloOfficialAccountId,
      );
      if (!integration) {
        throw new AppException(
          ErrorCode.NOT_FOUND,
          'Zalo Official Account Integration không tồn tại',
        );
      }

      // Lấy access token
      const accessToken = await this.zaloTokenService.getValidAccessToken(
        integration.oaId,
      );

      // Cập nhật avatar qua Zalo API
      await this.zaloGmfGroupService.updateGroupAvatar(
        accessToken,
        group.groupId,
        {
          avatar_url: updateDto.avatarUrl,
        },
      );

      // Cập nhật trong database
      const updatedGroup = await this.zaloGroupRepository.update(groupId, {
        avatarUrl: updateDto.avatarUrl,
      });

      return updatedGroup!;
    } catch (error) {
      this.logger.error(
        `Error updating group avatar: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Lỗi khi cập nhật avatar nhóm',
      );
    }
  }

  /**
   * Mời thành viên vào nhóm
   */
  async inviteMembers(
    userId: number,
    integrationId: string,
    groupId: string,
    inviteDto: InviteZaloGroupMembersDto,
  ): Promise<{ success: boolean; invitedCount: number }> {
    try {
      // Kiểm tra xem groupId là UUID database hay Zalo group_id
      let group: ZaloGroup | null;
      let zaloGroupId: string;

      // Nếu groupId có dạng UUID (36 ký tự với dấu gạch ngang),
      // thì đây là database ID
      if (groupId.length === 36 && groupId.includes('-')) {
        group = await this.zaloGroupRepository.findOne({
          where: {
            id: groupId,
            userId,
            zaloOfficialAccountId: integrationId,
          },
        });

        if (!group) {
          throw new AppException(ErrorCode.NOT_FOUND, 'Không tìm thấy nhóm');
        }

        zaloGroupId = group.groupId;
      } else {
        // Đây là Zalo group_id, tìm trong database
        group = await this.zaloGroupRepository.findOne({
          where: {
            groupId: groupId,
            userId,
            zaloOfficialAccountId: integrationId,
          },
        });

        if (!group) {
          throw new AppException(ErrorCode.NOT_FOUND, 'Không tìm thấy nhóm');
        }

        zaloGroupId = groupId;
      }

      // Lấy Integration để có oaId
      const integration = await this.zaloOALegacyWrapperService.findById(
        group.zaloOfficialAccountId,
      );
      if (!integration) {
        throw new AppException(
          ErrorCode.NOT_FOUND,
          'Zalo Official Account Integration không tồn tại',
        );
      }

      // Lấy access token
      const accessToken = await this.zaloTokenService.getValidAccessToken(
        integration.oaId,
      );

      // Mời thành viên qua Zalo API
      const result = await this.zaloGmfGroupService.inviteMembers(
        accessToken,
        zaloGroupId,
        { member_uids: inviteDto.memberUids },
      );

      // Kiểm tra thành viên đã tồn tại
      const existingMembers =
        await this.zaloGroupMemberRepository.findExistingMembers(
          group.id,
          inviteDto.memberUids,
        );

      const existingMemberUids = existingMembers.map(
        (member) => member.memberUserId,
      );
      const newMemberUids = inviteDto.memberUids.filter(
        (uid) => !existingMemberUids.includes(uid),
      );

      this.logger.debug(
        `Found ${existingMembers.length} existing members, adding ${newMemberUids.length} new members`,
      );

      // Chỉ thêm thành viên mới chưa tồn tại
      if (newMemberUids.length > 0) {
        const memberData = newMemberUids.map((memberUid) => ({
          userId,
          zaloGroupId: group.id, // Sử dụng database ID cho foreign key
          memberUserId: memberUid,
          role: ZaloGroupMemberRole.MEMBER,
          status: ZaloGroupMemberStatus.PENDING,
          invitedBy: 'OA',
        }));

        await this.zaloGroupMemberRepository.createMany(memberData);
      }

      // Cập nhật số lượng thành viên
      await this.zaloGroupRepository.updateMemberCount(
        group.id, // Sử dụng database ID
        group.memberCount + result.invited_count,
      );

      return {
        success: result.success,
        invitedCount: result.invited_count,
      };
    } catch (error) {
      this.logger.error(
        `Error inviting members: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Lỗi khi mời thành viên vào nhóm',
      );
    }
  }

  /**
   * Mời audience vào nhóm Zalo
   */
  async inviteAudiencesToGroups(
    userId: number,
    inviteDto: InviteAudiencesToZaloGroupDto,
  ): Promise<{
    success: boolean;
    totalInvited: number;
    results: Array<{
      groupId: string;
      groupName: string;
      invitedCount: number;
      success: boolean;
      error?: string;
    }>;
  }> {
    try {
      this.logger.debug(`Inviting audiences to groups for user ${userId}`);

      // Validate input
      if (
        !inviteDto.inviteAllAudiences &&
        (!inviteDto.audienceIds || inviteDto.audienceIds.length === 0)
      ) {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          'Phải chọn ít nhất một audience hoặc chọn mời tất cả audience',
        );
      }

      if (
        !inviteDto.inviteToAllGroups &&
        (!inviteDto.groupIds || inviteDto.groupIds.length === 0)
      ) {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          'Phải chọn ít nhất một nhóm hoặc chọn mời vào tất cả nhóm',
        );
      }

      if (inviteDto.inviteToAllGroups && !inviteDto.integrationId) {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          'Phải cung cấp integrationId khi mời vào tất cả nhóm',
        );
      }

      if (
        !inviteDto.inviteToAllGroups &&
        (!inviteDto.groupIds || inviteDto.groupIds.length === 0) &&
        !inviteDto.integrationId
      ) {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          'Phải cung cấp integrationId khi không chọn nhóm cụ thể',
        );
      }

      // Lấy danh sách audience
      let audiences: any[] = [];
      if (inviteDto.inviteAllAudiences) {
        // Lấy tất cả audience có zaloSocialId
        const audienceQuery = {
          limit: 10000, // Giới hạn tạm thời
          basicInfo: true,
        };
        audiences = await this.userAudienceService.findAllAudiences(
          userId,
          audienceQuery,
        );
        // Lọc chỉ những audience có zaloSocialId và đã follow OA
        audiences = audiences.filter(
          (audience) =>
            audience.zaloSocialId && audience.zaloUserIsFollower === true,
        );
      } else {
        // Lấy audience theo IDs
        for (const audienceId of inviteDto.audienceIds!) {
          try {
            const audience = await this.userAudienceService.findOne(
              userId,
              audienceId,
            );
            if (audience.zaloSocialId && audience.zaloUserIsFollower === true) {
              audiences.push(audience);
            }
          } catch (error) {
            this.logger.warn(
              `Audience ${audienceId} not found, has no zaloSocialId, or is not following OA`,
            );
          }
        }
      }

      if (audiences.length === 0) {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          'Không tìm thấy audience nào có Zalo Social ID và đã follow OA',
        );
      }

      // Lấy danh sách nhóm
      let groups: any[] = [];
      if (inviteDto.inviteToAllGroups) {
        // Lấy tất cả nhóm của integration
        const groupQuery = {
          integrationId: inviteDto.integrationId!,
          limit: 1000,
          status: 'active' as any,
        };
        const groupResult = await this.getGroupsFromDatabase(
          userId,
          groupQuery as any,
        );
        groups = groupResult.items;
      } else {
        // Lấy nhóm theo IDs từ database
        for (const groupId of inviteDto.groupIds!) {
          try {
            const group = await this.zaloGroupRepository.findOne({
              where: { id: groupId, userId },
            });
            if (group) {
              groups.push(group);
            } else {
              this.logger.warn(`Group ${groupId} not found in database`);
            }
          } catch (error) {
            this.logger.warn(
              `Error finding group ${groupId}: ${error.message}`,
            );
          }
        }
      }

      if (groups.length === 0) {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          'Không tìm thấy nhóm nào để mời',
        );
      }

      // Chuẩn bị danh sách member_user_ids từ audience
      const memberUids = audiences
        .map((audience) => audience.zaloSocialId)
        .filter(
          (uid) => uid && typeof uid === 'string' && uid.trim().length > 0,
        )
        .map((uid) => uid.trim());

      this.logger.debug(
        `Prepared ${memberUids.length} member UIDs for invitation:`,
        memberUids,
      );

      if (memberUids.length === 0) {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          'Không có audience nào đã follow OA để mời vào nhóm',
        );
      }

      // Mời vào từng nhóm
      const results: Array<{
        groupId: string;
        groupName: string;
        invitedCount: number;
        success: boolean;
        error?: string;
      }> = [];
      let totalInvited = 0;

      for (const group of groups) {
        try {
          // Lấy integrationId từ group hoặc từ input
          const integrationIdToUse =
            inviteDto.integrationId || group.zaloOfficialAccountId;

          const inviteResult = await this.inviteMembers(
            userId,
            integrationIdToUse,
            group.id, // Sử dụng group.id (UUID database) vì getGroupDetail đã được sửa để xử lý cả hai trường hợp
            { memberUids },
          );

          results.push({
            groupId: group.id,
            groupName: group.groupName,
            invitedCount: inviteResult.invitedCount,
            success: inviteResult.success,
          });

          if (inviteResult.success) {
            totalInvited += inviteResult.invitedCount;
          }
        } catch (error) {
          this.logger.error(
            `Error inviting to group ${group.id}: ${error.message}`,
          );
          results.push({
            groupId: group.id,
            groupName: group.groupName,
            invitedCount: 0,
            success: false,
            error: error.message,
          });
        }
      }

      return {
        success: results.some((r) => r.success),
        totalInvited,
        results,
      };
    } catch (error) {
      this.logger.error(
        `Error inviting audiences to groups: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Lỗi khi mời audience vào nhóm',
      );
    }
  }

  /**
   * Cập nhật thông tin nhóm theo ID entity
   */
  async updateGroupById(
    userId: number,
    id: string,
    updateDto: UpdateZaloGroupDto,
  ): Promise<ZaloGroup> {
    try {
      this.logger.debug(`Updating group by entity ID ${id} for user ${userId}`);

      // Tìm nhóm trong database theo ID khóa chính
      const existingGroup = await this.zaloGroupRepository.findOne({
        where: { id, userId },
      });

      if (!existingGroup) {
        throw new AppException(
          ErrorCode.RESOURCE_NOT_FOUND,
          'Không tìm thấy nhóm',
        );
      }

      // Gọi method updateGroup hiện tại với integrationId và groupId từ database
      const result = await this.updateGroup(
        existingGroup.zaloOfficialAccountId,
        userId,
        existingGroup.groupId,
        updateDto,
      );

      this.logger.debug(
        `Successfully updated group by entity ID: ${existingGroup.groupName}`,
      );

      return result;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(
        `Error updating group by entity ID: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi cập nhật thông tin nhóm',
      );
    }
  }

  /**
   * Giải tán nhóm chat theo ID entity
   */
  async deleteGroupById(
    userId: number,
    id: string,
  ): Promise<{ success: boolean }> {
    try {
      this.logger.debug(`Deleting group by entity ID ${id} for user ${userId}`);

      // Tìm nhóm trong database theo ID khóa chính
      const existingGroup = await this.zaloGroupRepository.findOne({
        where: { id, userId },
      });

      if (!existingGroup) {
        throw new AppException(
          ErrorCode.RESOURCE_NOT_FOUND,
          'Không tìm thấy nhóm',
        );
      }

      // Gọi method deleteGroup hiện tại với integrationId và groupId từ database
      const result = await this.deleteGroup(
        userId,
        existingGroup.groupId,
        existingGroup.zaloOfficialAccountId,
      );

      this.logger.debug(
        `Successfully deleted group by entity ID: ${existingGroup.groupName}`,
      );

      return result;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(
        `Error deleting group by entity ID: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi giải tán nhóm chat',
      );
    }
  }

  /**
   * Cập nhật avatar nhóm theo ID entity
   */
  async updateGroupAvatarById(
    userId: number,
    id: string,
    avatarDto: UpdateZaloGroupAvatarDto,
  ): Promise<{ success: boolean }> {
    try {
      this.logger.debug(
        `Updating group avatar by entity ID ${id} for user ${userId}`,
      );

      // Tìm nhóm trong database theo ID khóa chính
      const existingGroup = await this.zaloGroupRepository.findOne({
        where: { id, userId },
      });

      if (!existingGroup) {
        throw new AppException(
          ErrorCode.RESOURCE_NOT_FOUND,
          'Không tìm thấy nhóm',
        );
      }

      // Gọi method updateGroupAvatar hiện tại với integrationId và groupId từ database
      const result = await this.updateGroupAvatar(
        userId,
        existingGroup.zaloOfficialAccountId,
        existingGroup.groupId,
        avatarDto,
      );

      this.logger.debug(
        `Successfully updated group avatar by entity ID: ${existingGroup.groupName}`,
      );

      return { success: true };
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(
        `Error updating group avatar by entity ID: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi cập nhật avatar nhóm',
      );
    }
  }

  /**
   * Lấy danh sách thành viên nhóm theo ID entity từ Zalo API
   */
  async getGroupMembersById(
    userId: number,
    id: string,
    queryDto: ZaloGroupMemberQueryDto,
  ): Promise<PaginatedResult<any>> {
    try {
      this.logger.debug(
        `Getting group members by entity ID ${id} for user ${userId}`,
      );

      // Tìm nhóm trong database theo ID khóa chính
      const existingGroup = await this.zaloGroupRepository.findOne({
        where: { id, userId },
      });

      if (!existingGroup) {
        throw new AppException(
          ErrorCode.RESOURCE_NOT_FOUND,
          'Không tìm thấy nhóm',
        );
      }

      // Gọi method getGroupMembers hiện tại với integrationId và groupId từ database
      const result = await this.getGroupMembers(
        userId,
        existingGroup.zaloOfficialAccountId,
        existingGroup.groupId,
        queryDto,
      );

      this.logger.debug(
        `Successfully got group members by entity ID: ${existingGroup.groupName}`,
      );

      return result;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(
        `Error getting group members by entity ID: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi lấy danh sách thành viên nhóm',
      );
    }
  }

  /**
   * Mời thành viên vào nhóm theo ID entity
   */
  async inviteMembersById(
    userId: number,
    id: string,
    inviteDto: InviteZaloGroupMembersDto,
  ): Promise<{ success: boolean; invitedCount: number }> {
    try {
      this.logger.debug(
        `Inviting members to group by entity ID ${id} for user ${userId}`,
      );

      // Tìm nhóm trong database theo ID khóa chính
      const existingGroup = await this.zaloGroupRepository.findOne({
        where: { id, userId },
      });

      if (!existingGroup) {
        throw new AppException(
          ErrorCode.RESOURCE_NOT_FOUND,
          'Không tìm thấy nhóm',
        );
      }

      // Gọi method inviteMembers hiện tại với integrationId và groupId từ database
      const result = await this.inviteMembers(
        userId,
        existingGroup.zaloOfficialAccountId,
        existingGroup.groupId,
        inviteDto,
      );

      this.logger.debug(
        `Successfully invited members to group by entity ID: ${existingGroup.groupName}`,
      );

      return result;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(
        `Error inviting members to group by entity ID: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi mời thành viên vào nhóm',
      );
    }
  }

  /**
   * Xóa thành viên khỏi nhóm theo ID entity
   */
  async removeMembersById(
    userId: number,
    id: string,
    removeDto: ZaloRemoveMembersDto,
  ): Promise<ZaloRemoveMembersResponseDto> {
    try {
      this.logger.debug(
        `Removing members from group by entity ID ${id} for user ${userId}`,
      );

      // Tìm nhóm trong database theo ID khóa chính
      const existingGroup = await this.zaloGroupRepository.findOne({
        where: { id, userId },
      });

      if (!existingGroup) {
        throw new AppException(
          ErrorCode.RESOURCE_NOT_FOUND,
          'Không tìm thấy nhóm',
        );
      }

      // Gọi method removeMembers hiện tại với integrationId và groupId từ database
      const result = await this.removeMembers(
        userId,
        existingGroup.zaloOfficialAccountId,
        existingGroup.groupId,
        removeDto,
      );

      this.logger.debug(
        `Successfully removed members from group by entity ID: ${existingGroup.groupName}`,
      );

      return result;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(
        `Error removing members from group by entity ID: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi xóa thành viên khỏi nhóm',
      );
    }
  }

  /**
   * Đồng bộ danh sách thành viên nhóm theo ID entity
   */
  async syncGroupMembersById(
    userId: number,
    id: string,
    syncDto: SyncGroupMembersDto,
  ): Promise<SyncGroupMembersResponseDto> {
    try {
      this.logger.debug(
        `Syncing group members by entity ID ${id} for user ${userId}`,
      );

      // Tìm nhóm trong database theo ID khóa chính
      const existingGroup = await this.zaloGroupRepository.findOne({
        where: { id, userId },
      });

      if (!existingGroup) {
        throw new AppException(
          ErrorCode.RESOURCE_NOT_FOUND,
          'Không tìm thấy nhóm',
        );
      }

      // Gọi method syncGroupMembers hiện tại với integrationId và groupId từ database
      const result = await this.syncGroupMembers(
        userId,
        existingGroup.zaloOfficialAccountId,
        existingGroup.groupId,
        syncDto,
      );

      this.logger.debug(
        `Successfully synced group members by entity ID: ${existingGroup.groupName}`,
      );

      return result;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(
        `Error syncing group members by entity ID: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi đồng bộ danh sách thành viên nhóm',
      );
    }
  }

  /**
   * Lấy danh sách thành viên nhóm từ Zalo API
   */
  async getGroupMembers(
    userId: number,
    integrationId: string,
    groupId: string,
    queryDto: ZaloGroupMemberQueryDto,
  ): Promise<PaginatedResult<any>> {
    try {
      this.logger.debug(
        `Getting group members for group ${groupId} by user ${userId}`,
      );

      // Lấy thông tin OA từ integration ID
      const oa = await this.zaloService.getOfficialAccountByIntegrationId(
        userId,
        integrationId,
      );

      // Chuyển đổi page/limit sang offset/count cho Zalo API
      const page = queryDto.page || 1;
      const limit = queryDto.limit || 10;
      const offset = (page - 1) * limit;
      const count = Math.min(limit, 50); // Zalo API giới hạn tối đa 50

      // Gọi Zalo API để lấy danh sách thành viên
      const zaloResponse = await this.zaloGmfGroupService.getGroupMembers(
        oa.accessToken,
        groupId,
        offset,
        count,
      );

      // Transform data từ Zalo API sang format PaginatedResult
      const transformedMembers = zaloResponse.data.members.map((member) => ({
        id: member.user_id || member.oa_id || '', // ID duy nhất
        userId: member.user_id || null, // User ID (nếu là user thường)
        oaId: member.oa_id || null, // OA ID (nếu là OA)
        name: member.name,
        avatar: member.avatar,
        isOA: !!member.oa_id, // Đánh dấu có phải OA không
        // Các field khác có thể được set default hoặc null
        role: member.oa_id ? 'admin' : 'member', // OA thường là admin
        status: 'active', // Default status
        joinedAt: Date.now(), // Default joined time
      }));

      // Tạo PaginatedResult theo format chuẩn
      const totalPages = Math.ceil(zaloResponse.data.total / limit);

      const paginatedResult: PaginatedResult<any> = {
        items: transformedMembers,
        meta: {
          totalItems: zaloResponse.data.total,
          itemCount: transformedMembers.length,
          itemsPerPage: limit,
          totalPages: totalPages,
          currentPage: page,
        },
      };

      return paginatedResult;
    } catch (error) {
      this.logger.error(
        `Error getting group members: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Lỗi khi lấy danh sách thành viên nhóm',
      );
    }
  }

  /**
   * Xóa nhóm chat
   */
  async deleteGroup(
    userId: number,
    groupId: string,
    integrationId: string,
  ): Promise<{ success: boolean }> {
    try {
      const group = await this.getGroupDetail(userId, integrationId, groupId);

      if (!group.createdByOa) {
        throw new AppException(
          ErrorCode.FORBIDDEN,
          'Chỉ có thể xóa nhóm do OA tạo ra',
        );
      }

      // Lấy Integration để có oaId
      const integration = await this.zaloOALegacyWrapperService.findById(
        group.zaloOfficialAccountId,
      );
      if (!integration) {
        throw new AppException(
          ErrorCode.NOT_FOUND,
          'Zalo Official Account Integration không tồn tại',
        );
      }

      // Lấy access token
      const accessToken = await this.zaloTokenService.getValidAccessToken(
        integration.oaId,
      );

      // Xóa nhóm qua Zalo API
      const zaloResponse = await this.zaloGmfGroupService.deleteGroup(
        accessToken,
        group.groupId,
      );

      // Kiểm tra response từ Zalo API
      if (zaloResponse.error !== 0) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi từ Zalo API: ${zaloResponse.message}`,
        );
      }

      // Soft delete trong database
      await this.zaloGroupRepository.softDelete(groupId);

      return { success: true };
    } catch (error) {
      this.logger.error(`Error deleting group: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Lỗi khi xóa nhóm chat',
      );
    }
  }

  /**
   * Lấy danh sách Asset ID có thể sử dụng để tạo nhóm GMF
   */
  async getAssetIds(
    userId: number,
    integrationId: string,
  ): Promise<ZaloAssetListDto> {
    try {
      this.logger.debug(
        `Getting asset IDs for user ${userId} and integration ${integrationId}`,
      );

      // Lấy thông tin OA từ integration ID
      const oa = await this.zaloService.getOfficialAccountByIntegrationId(
        userId,
        integrationId,
      );

      // Gọi service để lấy danh sách asset IDs
      const assetData = await this.zaloGmfGroupService.getAssetIds(
        oa.accessToken,
      );

      // Transform data để phù hợp với DTO
      return {
        assets: (assetData.data || []).map((asset) => ({
          asset_id: asset.asset_id,
          product_type: asset.product_type,
          quota_type: asset.quota_type,
          valid_through: asset.valid_through,
          auto_renew: asset.auto_renew,
          status: asset.status,
          used_id: asset.used_id,
        })),
        total: assetData.data?.length || 0,
      };
    } catch (error) {
      this.logger.error(
        `Error getting asset IDs: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Lỗi khi lấy danh sách Asset ID',
      );
    }
  }

  /**
   * Lấy danh sách thành viên chờ duyệt của nhóm
   */
  async getPendingMembers(
    userId: number,
    integrationId: string,
    groupId: string,
    queryDto: ZaloPendingMembersQueryDto,
  ): Promise<PaginatedResult<any>> {
    try {
      this.logger.debug(
        `Getting pending members for group ${groupId} by user ${userId}`,
      );

      // Lấy thông tin OA từ integration ID
      const oa = await this.zaloService.getOfficialAccountByIntegrationId(
        userId,
        integrationId,
      );

      // Chuyển đổi page/limit sang offset/count cho Zalo API
      const page = queryDto.page || 1;
      const limit = queryDto.limit || 10;
      const offset = (page - 1) * limit;
      const count = Math.min(limit, 50); // Zalo API giới hạn tối đa 50

      // Gọi service để lấy danh sách thành viên chờ duyệt
      const pendingData = await this.zaloGmfGroupService.getPendingMembers(
        oa.accessToken,
        groupId,
        offset,
        count,
      );

      // Transform data sang PaginatedResult format
      const transformedMembers = pendingData.data.members.map((member) => ({
        user_id: member.user_id,
        name: member.name,
        id: member.user_id, // Thêm id field cho consistency
        isPending: true, // Đánh dấu là pending member
      }));

      // Tạo PaginatedResult theo format chuẩn
      const totalPages = Math.ceil(pendingData.data.total / limit);

      const paginatedResult: PaginatedResult<any> = {
        items: transformedMembers,
        meta: {
          totalItems: pendingData.data.total,
          itemCount: transformedMembers.length,
          itemsPerPage: limit,
          totalPages: totalPages,
          currentPage: page,
        },
      };

      return paginatedResult;
    } catch (error) {
      this.logger.error(
        `Error getting pending members: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Lỗi khi lấy danh sách thành viên chờ duyệt',
      );
    }
  }

  /**
   * Duyệt thành viên chờ duyệt vào nhóm
   */
  async acceptPendingMembers(
    userId: number,
    integrationId: string,
    groupId: string,
    acceptDto: ZaloAcceptPendingMembersDto,
  ): Promise<ZaloAcceptPendingMembersResponseDto> {
    try {
      this.logger.debug(
        `Accepting pending members for group ${groupId} by user ${userId}`,
      );

      // Lấy thông tin OA từ integration ID
      const oa = await this.zaloService.getOfficialAccountByIntegrationId(
        userId,
        integrationId,
      );

      // Gọi service để duyệt thành viên chờ duyệt
      const result = await this.zaloGmfGroupService.acceptPendingMembers(
        oa.accessToken,
        groupId,
        acceptDto.memberUserIds,
      );

      // Transform data để phù hợp với DTO
      return {
        error: result.error,
        message: result.message,
        acceptedCount: acceptDto.memberUserIds.length, // Số lượng đã gửi request duyệt
      };
    } catch (error) {
      this.logger.error(
        `Error accepting pending members: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Lỗi khi duyệt thành viên chờ duyệt',
      );
    }
  }

  /**
   * Từ chối thành viên chờ duyệt vào nhóm
   */
  async rejectPendingMembers(
    userId: number,
    integrationId: string,
    groupId: string,
    rejectDto: ZaloRejectPendingMembersDto,
  ): Promise<ZaloRejectPendingMembersResponseDto> {
    try {
      this.logger.debug(
        `Rejecting pending members for group ${groupId} by user ${userId}`,
      );

      // Lấy thông tin OA từ integration ID
      const oa = await this.zaloService.getOfficialAccountByIntegrationId(
        userId,
        integrationId,
      );

      // Gọi service để từ chối thành viên chờ duyệt
      const result = await this.zaloGmfGroupService.rejectPendingMembers(
        oa.accessToken,
        groupId,
        rejectDto.memberUserIds,
      );

      // Transform data để phù hợp với DTO
      return {
        error: result.error,
        message: result.message,
        rejectedCount: rejectDto.memberUserIds.length, // Số lượng đã gửi request từ chối
      };
    } catch (error) {
      this.logger.error(
        `Error rejecting pending members: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Lỗi khi từ chối thành viên chờ duyệt',
      );
    }
  }

  /**
   * Xóa thành viên khỏi nhóm
   */
  async removeMembers(
    userId: number,
    integrationId: string,
    groupId: string,
    removeDto: ZaloRemoveMembersDto,
  ): Promise<ZaloRemoveMembersResponseDto> {
    try {
      this.logger.debug(
        `Removing members from group ${groupId} by user ${userId}`,
      );

      // Lấy thông tin OA từ integration ID
      const oa = await this.zaloService.getOfficialAccountByIntegrationId(
        userId,
        integrationId,
      );

      // Gọi service để xóa thành viên
      const result = await this.zaloGmfGroupService.removeMembers(
        oa.accessToken,
        groupId,
        removeDto.memberUserIds,
      );

      // Transform data để phù hợp với DTO
      return {
        error: result.error,
        message: result.message,
        removedCount: removeDto.memberUserIds.length, // Số lượng đã gửi request xóa
      };
    } catch (error) {
      this.logger.error(
        `Error removing members: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Lỗi khi xóa thành viên khỏi nhóm',
      );
    }
  }

  /**
   * Thêm phó nhóm
   */
  async addAdmins(
    userId: number,
    integrationId: string,
    groupId: string,
    addAdminsDto: ZaloAddAdminsDto,
  ): Promise<ZaloAddAdminsResponseDto> {
    try {
      this.logger.debug(
        `Adding ${addAdminsDto.memberUserIds.length} admins to group ${groupId} for user ${userId}`,
      );

      // Validate integration exists và user có quyền truy cập
      const oa = await this.zaloService.getOfficialAccountByIntegrationId(
        userId,
        integrationId,
      );

      // Gọi service để thêm phó nhóm
      const result = await this.zaloGmfGroupService.addAdmins(
        oa.accessToken,
        groupId,
        {
          admin_uids: addAdminsDto.memberUserIds,
        },
      );

      // Transform data để phù hợp với DTO
      return {
        error: result.error,
        message: result.message,
        addedCount: addAdminsDto.memberUserIds.length, // Số lượng đã gửi request thêm
      };
    } catch (error) {
      this.logger.error(`Error adding admins: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Lỗi khi thêm phó nhóm',
      );
    }
  }

  /**
   * Xóa phó nhóm
   */
  async removeAdmins(
    userId: number,
    integrationId: string,
    groupId: string,
    removeAdminsDto: ZaloRemoveAdminsDto,
  ): Promise<ZaloRemoveAdminsResponseDto> {
    try {
      this.logger.debug(
        `Removing ${removeAdminsDto.memberUserIds.length} admins from group ${groupId} for user ${userId}`,
      );

      // Validate integration exists và user có quyền truy cập
      const oa = await this.zaloService.getOfficialAccountByIntegrationId(
        userId,
        integrationId,
      );

      // Gọi service để xóa phó nhóm
      const result = await this.zaloGmfGroupService.removeAdmins(
        oa.accessToken,
        groupId,
        {
          admin_uids: removeAdminsDto.memberUserIds,
        },
      );

      // Transform data để phù hợp với DTO
      return {
        error: result.error,
        message: result.message,
        removedCount: removeAdminsDto.memberUserIds.length, // Số lượng đã gửi request xóa
      };
    } catch (error) {
      this.logger.error(`Error removing admins: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Lỗi khi xóa phó nhóm',
      );
    }
  }

  /**
   * Đồng bộ danh sách thành viên nhóm từ Zalo API vào database
   */
  async syncGroupMembers(
    userId: number,
    integrationId: string,
    groupId: string,
    syncDto: SyncGroupMembersDto,
  ): Promise<SyncGroupMembersResponseDto> {
    const startedAt = Date.now();
    let totalFromZalo = 0;
    let syncedCount = 0;
    let addedCount = 0;
    let updatedCount = 0;
    let removedCount = 0;
    let errorCount = 0;
    const errors: Array<{
      memberUserId: string;
      memberName?: string;
      error: string;
    }> = [];

    try {
      this.logger.debug(
        `Starting sync group members for group ${groupId} by user ${userId}`,
      );

      // Lấy thông tin OA từ integration ID
      const oa = await this.zaloService.getOfficialAccountByIntegrationId(
        userId,
        integrationId,
      );

      // Tìm nhóm trong database
      const group = await this.zaloGroupRepository.findOne({
        where: {
          userId,
          zaloOfficialAccountId: integrationId,
          groupId: groupId,
        },
      });

      if (!group) {
        throw new AppException(
          ErrorCode.RESOURCE_NOT_FOUND,
          'Không tìm thấy nhóm trong database',
        );
      }

      // Lấy danh sách thành viên hiện tại từ database
      const existingMembers = await this.zaloGroupMemberRepository.findByGroup(
        group.id,
      );
      const existingMemberMap = new Map(
        existingMembers.map((member) => [member.memberUserId, member]),
      );

      let allZaloMembers: any[] = [];

      if (syncDto.syncAll) {
        // Đồng bộ toàn bộ thành viên
        let offset = 0;
        const limit = 50; // Zalo API giới hạn tối đa 50
        let hasMore = true;

        while (hasMore) {
          const zaloResponse = await this.zaloGmfGroupService.getGroupMembers(
            oa.accessToken,
            groupId,
            offset,
            limit,
          );

          if (
            zaloResponse.data.members &&
            zaloResponse.data.members.length > 0
          ) {
            allZaloMembers.push(...zaloResponse.data.members);
            offset += limit;

            // Kiểm tra xem còn dữ liệu không
            hasMore = zaloResponse.data.members.length === limit;
          } else {
            hasMore = false;
          }

          // Tránh vòng lặp vô hạn
          if (offset > 10000) {
            this.logger.warn(
              `Reached maximum offset limit for group ${groupId}`,
            );
            break;
          }
        }
      } else {
        // Đồng bộ theo phân trang
        const page = syncDto.page || 1;
        const limit = Math.min(syncDto.limit || 50, 100);
        const offset = (page - 1) * limit;

        const zaloResponse = await this.zaloGmfGroupService.getGroupMembers(
          oa.accessToken,
          groupId,
          offset,
          limit,
        );

        allZaloMembers = zaloResponse.data.members || [];
      }

      totalFromZalo = allZaloMembers.length;
      this.logger.debug(`Found ${totalFromZalo} members from Zalo API`);

      // Đồng bộ từng thành viên
      for (const zaloMember of allZaloMembers) {
        try {
          const memberUserId = zaloMember.user_id || zaloMember.oa_id || '';
          const memberName = zaloMember.name || '';
          const memberAvatar = zaloMember.avatar || '';
          const isOA = !!zaloMember.oa_id;
          const role = isOA
            ? ZaloGroupMemberRole.ADMIN
            : ZaloGroupMemberRole.MEMBER;

          const existingMember = existingMemberMap.get(memberUserId);

          if (existingMember) {
            // Cập nhật thành viên đã tồn tại
            if (syncDto.overwrite !== false) {
              await this.zaloGroupMemberRepository.update(existingMember.id, {
                memberName,
                memberAvatar,
                role,
                status: ZaloGroupMemberStatus.ACTIVE,
                lastSeenAt: Date.now(),
                updatedAt: Date.now(),
              });
              updatedCount++;
            }
          } else {
            // Thêm thành viên mới
            await this.zaloGroupMemberRepository.create({
              userId,
              zaloGroupId: group.id,
              memberUserId,
              memberName,
              memberAvatar,
              role,
              status: ZaloGroupMemberStatus.ACTIVE,
              joinedAt: Date.now(),
              invitedBy: 'SYSTEM_SYNC',
            });
            addedCount++;
          }

          syncedCount++;

          // Xóa khỏi map để track những member không còn trong nhóm
          existingMemberMap.delete(memberUserId);
        } catch (error) {
          this.logger.error(
            `Error syncing member ${zaloMember.user_id || zaloMember.oa_id}: ${error.message}`,
          );
          errors.push({
            memberUserId: zaloMember.user_id || zaloMember.oa_id || 'unknown',
            memberName: zaloMember.name,
            error: error.message,
          });
          errorCount++;
        }
      }

      // Xử lý thành viên không còn trong nhóm
      if (syncDto.removeInactive && existingMemberMap.size > 0) {
        for (const [memberUserId, member] of existingMemberMap) {
          try {
            await this.zaloGroupMemberRepository.update(member.id, {
              status: ZaloGroupMemberStatus.LEFT,
              updatedAt: Date.now(),
            });
            removedCount++;
          } catch (error) {
            this.logger.error(
              `Error removing inactive member ${memberUserId}: ${error.message}`,
            );
            errors.push({
              memberUserId,
              memberName: member.memberName,
              error: `Failed to mark as inactive: ${error.message}`,
            });
            errorCount++;
          }
        }
      }

      // Cập nhật số lượng thành viên trong nhóm
      await this.zaloGroupRepository.update(group.id, {
        memberCount: totalFromZalo,
        updatedAt: Date.now(),
      });

      const completedAt = Date.now();
      const processingTime = completedAt - startedAt;

      // Tính toán thống kê
      const finalMembers = await this.zaloGroupMemberRepository.findByGroup(
        group.id,
      );
      const statistics = {
        adminCount: finalMembers.filter(
          (m) => m.role === ZaloGroupMemberRole.ADMIN,
        ).length,
        memberCount: finalMembers.filter(
          (m) => m.role === ZaloGroupMemberRole.MEMBER,
        ).length,
        activeCount: finalMembers.filter(
          (m) => m.status === ZaloGroupMemberStatus.ACTIVE,
        ).length,
        pendingCount: finalMembers.filter(
          (m) => m.status === ZaloGroupMemberStatus.PENDING,
        ).length,
      };

      // Xác định trạng thái
      let status: 'completed' | 'partial' | 'failed';
      if (errorCount === 0) {
        status = 'completed';
      } else if (syncedCount > 0) {
        status = 'partial';
      } else {
        status = 'failed';
      }

      const result: SyncGroupMembersResponseDto = {
        groupId,
        totalFromZalo,
        syncedCount,
        addedCount,
        updatedCount,
        removedCount,
        errorCount,
        startedAt,
        completedAt,
        processingTime,
        status,
        statistics,
      };

      if (errors.length > 0) {
        result.errors = errors;
      }

      if (errorCount > 0) {
        result.warning = `${errorCount} thành viên không thể đồng bộ do lỗi`;
      }

      this.logger.log(
        `Completed sync for group ${groupId}: ${syncedCount}/${totalFromZalo} members synced in ${processingTime}ms`,
      );
      return result;
    } catch (error) {
      const completedAt = Date.now();
      const processingTime = completedAt - startedAt;

      this.logger.error(
        `Error syncing group members for group ${groupId}: ${error.message}`,
        error.stack,
      );

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Lỗi khi đồng bộ danh sách thành viên nhóm',
      );
    }
  }

  /**
   * Lấy danh sách nhóm từ database
   */
  async getGroupsFromDatabase(
    userId: number,
    queryDto: ZaloGroupQueryDto,
  ): Promise<PaginatedResult<ZaloGroup>> {
    try {
      this.logger.log(`Lấy danh sách nhóm từ database cho user ${userId}`);

      const options = {
        userId,
        zaloOfficialAccountId: queryDto.integrationId,
        status: queryDto.status as ZaloGroupStatus,
        search: queryDto.search,
        isOaAdmin: queryDto.isOaAdmin,
        createdByOa: queryDto.createdByOa,
        page: queryDto.page || 1,
        limit: Math.min(queryDto.limit || 20, 100),
        sortBy: queryDto.sortBy || 'createdAt',
        sortOrder: queryDto.sortOrder || 'DESC',
      };

      const result = await this.zaloGroupRepository.findMany(options);

      this.logger.log(
        `Lấy danh sách nhóm từ database thành công: ${result.items.length}/${result.meta.totalItems} nhóm`,
      );

      return result;
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy danh sách nhóm từ database: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Lỗi khi lấy danh sách nhóm từ database',
      );
    }
  }

  /**
   * Lấy chi tiết nhóm từ database
   */
  async getGroupFromDatabase(
    userId: number,
    groupId: string,
  ): Promise<ZaloGroup> {
    try {
      this.logger.log(
        `Lấy chi tiết nhóm ${groupId} từ database cho user ${userId}`,
      );

      const group = await this.zaloGroupRepository.findOne({
        where: { id: groupId, userId },
        relations: ['user', 'members'],
      });

      if (!group) {
        throw new AppException(
          ErrorCode.RESOURCE_NOT_FOUND,
          'Không tìm thấy nhóm',
        );
      }

      this.logger.log(
        `Lấy chi tiết nhóm từ database thành công: ${group.groupName}`,
      );

      return group;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(
        `Lỗi khi lấy chi tiết nhóm từ database: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Lỗi khi lấy chi tiết nhóm từ database',
      );
    }
  }

  /**
   * Thêm phó nhóm theo ID entity
   */
  async addAdminsById(
    userId: number,
    id: string,
    addAdminsDto: ZaloAddAdminsDto,
  ): Promise<ZaloAddAdminsResponseDto> {
    try {
      this.logger.debug(
        `Adding admins to group by entity ID ${id} for user ${userId}`,
      );

      // Tìm nhóm trong database theo ID khóa chính
      const existingGroup = await this.zaloGroupRepository.findOne({
        where: { id, userId },
      });

      if (!existingGroup) {
        throw new AppException(
          ErrorCode.RESOURCE_NOT_FOUND,
          'Không tìm thấy nhóm',
        );
      }

      // Gọi method addAdmins hiện tại với integrationId và groupId từ database
      const result = await this.addAdmins(
        userId,
        existingGroup.zaloOfficialAccountId,
        existingGroup.groupId,
        addAdminsDto,
      );

      this.logger.debug(
        `Successfully added admins to group by entity ID: ${existingGroup.groupName}`,
      );

      return result;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(
        `Error adding admins to group by entity ID: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi thêm phó nhóm',
      );
    }
  }

  /**
   * Xóa phó nhóm theo ID entity
   */
  async removeAdminsById(
    userId: number,
    id: string,
    removeAdminsDto: ZaloRemoveAdminsDto,
  ): Promise<ZaloRemoveAdminsResponseDto> {
    try {
      this.logger.debug(
        `Removing admins from group by entity ID ${id} for user ${userId}`,
      );

      // Tìm nhóm trong database theo ID khóa chính
      const existingGroup = await this.zaloGroupRepository.findOne({
        where: { id, userId },
      });

      if (!existingGroup) {
        throw new AppException(
          ErrorCode.RESOURCE_NOT_FOUND,
          'Không tìm thấy nhóm',
        );
      }

      // Gọi method removeAdmins hiện tại với integrationId và groupId từ database
      const result = await this.removeAdmins(
        userId,
        existingGroup.zaloOfficialAccountId,
        existingGroup.groupId,
        removeAdminsDto,
      );

      this.logger.debug(
        `Successfully removed admins from group by entity ID: ${existingGroup.groupName}`,
      );

      return result;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(
        `Error removing admins from group by entity ID: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi xóa phó nhóm',
      );
    }
  }

  /**
   * Lấy danh sách thành viên chờ duyệt theo ID entity
   */
  async getPendingMembersById(
    userId: number,
    id: string,
    queryDto: ZaloPendingMembersQueryDto,
  ): Promise<PaginatedResult<any>> {
    try {
      this.logger.debug(
        `Getting pending members by entity ID ${id} for user ${userId}`,
      );

      // Tìm nhóm trong database theo ID khóa chính
      const existingGroup = await this.zaloGroupRepository.findOne({
        where: { id, userId },
      });

      if (!existingGroup) {
        throw new AppException(
          ErrorCode.RESOURCE_NOT_FOUND,
          'Không tìm thấy nhóm',
        );
      }

      // Gọi method getPendingMembers hiện tại với integrationId và groupId từ database
      const result = await this.getPendingMembers(
        userId,
        existingGroup.zaloOfficialAccountId,
        existingGroup.groupId,
        queryDto,
      );

      this.logger.debug(
        `Successfully got pending members by entity ID: ${existingGroup.groupName}`,
      );

      return result;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(
        `Error getting pending members by entity ID: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi lấy danh sách thành viên chờ duyệt',
      );
    }
  }

  /**
   * Duyệt thành viên chờ duyệt theo ID entity
   */
  async acceptPendingMembersById(
    userId: number,
    id: string,
    acceptDto: ZaloAcceptPendingMembersDto,
  ): Promise<ZaloAcceptPendingMembersResponseDto> {
    try {
      this.logger.debug(
        `Accepting pending members by entity ID ${id} for user ${userId}`,
      );

      // Tìm nhóm trong database theo ID khóa chính
      const existingGroup = await this.zaloGroupRepository.findOne({
        where: { id, userId },
      });

      if (!existingGroup) {
        throw new AppException(
          ErrorCode.RESOURCE_NOT_FOUND,
          'Không tìm thấy nhóm',
        );
      }

      // Gọi method acceptPendingMembers hiện tại với integrationId và groupId từ database
      const result = await this.acceptPendingMembers(
        userId,
        existingGroup.zaloOfficialAccountId,
        existingGroup.groupId,
        acceptDto,
      );

      this.logger.debug(
        `Successfully accepted pending members by entity ID: ${existingGroup.groupName}`,
      );

      return result;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(
        `Error accepting pending members by entity ID: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi duyệt thành viên chờ duyệt',
      );
    }
  }

  /**
   * Từ chối thành viên chờ duyệt theo ID entity
   */
  async rejectPendingMembersById(
    userId: number,
    id: string,
    rejectDto: ZaloRejectPendingMembersDto,
  ): Promise<ZaloRejectPendingMembersResponseDto> {
    try {
      this.logger.debug(
        `Rejecting pending members by entity ID ${id} for user ${userId}`,
      );

      // Tìm nhóm trong database theo ID khóa chính
      const existingGroup = await this.zaloGroupRepository.findOne({
        where: { id, userId },
      });

      if (!existingGroup) {
        throw new AppException(
          ErrorCode.RESOURCE_NOT_FOUND,
          'Không tìm thấy nhóm',
        );
      }

      // Gọi method rejectPendingMembers hiện tại với integrationId và groupId từ database
      const result = await this.rejectPendingMembers(
        userId,
        existingGroup.zaloOfficialAccountId,
        existingGroup.groupId,
        rejectDto,
      );

      this.logger.debug(
        `Successfully rejected pending members by entity ID: ${existingGroup.groupName}`,
      );

      return result;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(
        `Error rejecting pending members by entity ID: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi từ chối thành viên chờ duyệt',
      );
    }
  }
}

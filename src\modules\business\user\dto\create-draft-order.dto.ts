import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsBoolean,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsNumber,
  IsUUID,
  ValidateNested,
  Min,
  IsEnum,
} from 'class-validator';
import {
  OrderProductItemDto,
  OrderLogisticInfoDto,
} from './create-user-order.dto';
import {
  PaymentMethodEnum,
  PaymentStatusEnum,
  OrderStatusEnum,
  ShippingStatusEnum,
} from '../../enums';
import { BillInfo } from '../../interfaces';

/**
 * DTO cho thông tin khách hàng trong đơn hàng nháp
 */
export class DraftOrderCustomerInfoDto {
  @ApiProperty({
    description: 'ID khách hàng (UUID)',
    example: 'a5541891-75e8-4d55-9185-586813d4bada',
  })
  @IsNotEmpty()
  @IsUUID()
  customerId: string;
}

/**
 * DTO cho thông tin hóa đơn đơn giản (draft order)
 */
export class DraftOrderBillInfoDto implements Partial<BillInfo> {
  @ApiProperty({
    description: 'Tổng tiền hàng (subtotal)',
    example: 100000,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  subtotal?: number;

  @ApiProperty({
    description: 'Thuế (nếu có)',
    example: 10000,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  tax?: number;

  @ApiProperty({
    description: 'Giảm giá (nếu có)',
    example: 5000,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  discount?: number;

  @ApiProperty({
    description: 'Tổng tiền đơn hàng (sẽ được tính tự động nếu không cung cấp)',
    example: 125000,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  total?: number;

  @ApiProperty({
    description: 'Nhà vận chuyển được chọn',
    example: 'GHN',
    enum: ['GHN', 'GHTK', 'AHAMOVE'],
    required: false,
  })
  @IsOptional()
  @IsString()
  selectedCarrier?: string;

  @ApiProperty({
    description: 'Loại dịch vụ vận chuyển',
    example: 'standard',
    enum: ['standard', 'express'],
    required: false,
  })
  @IsOptional()
  @IsString()
  shippingServiceType?: string;

  @ApiProperty({
    description: 'Phương thức thanh toán',
    enum: PaymentMethodEnum,
    example: PaymentMethodEnum.CASH,
  })
  @IsNotEmpty()
  paymentMethod: PaymentMethodEnum;

  @ApiProperty({
    description: 'Trạng thái thanh toán (sẽ được set mặc định là PENDING)',
    enum: PaymentStatusEnum,
    example: PaymentStatusEnum.PENDING,
    required: false,
  })
  @IsOptional()
  @IsEnum(PaymentStatusEnum)
  paymentStatus?: PaymentStatusEnum;

  @ApiProperty({
    description:
      'Phí vận chuyển do người dùng nhập. Khi ENABLE_MOCK_ADDRESSES=true thì bỏ qua field này và tính phí tự động. Khi ENABLE_MOCK_ADDRESSES=false thì sử dụng giá trị này.',
    example: 25000,
    required: false,
    examples: {
      'Mock enabled (tính tự động)': {
        summary: 'ENABLE_MOCK_ADDRESSES=true',
        description:
          'Hệ thống sẽ bỏ qua userShippingFee và tính phí từ API nhà vận chuyển',
        value: undefined,
      },
      'Mock disabled (người dùng nhập)': {
        summary: 'ENABLE_MOCK_ADDRESSES=false',
        description:
          'Hệ thống sẽ sử dụng userShippingFee mà người dùng cung cấp',
        value: 25000,
      },
    },
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  userShippingFee?: number;

  @ApiProperty({
    description: 'Thông tin thanh toán bổ sung',
    example: {
      note: 'Thanh toán tiền mặt',
      currency: 'VND',
    },
    required: false,
  })
  @IsOptional()
  paymentInfo?: any;

  @ApiProperty({
    description:
      'ID của payment gateway (chỉ cần khi paymentMethod là QR_CODE)',
    example: 'a5541891-75e8-4d55-9185-586813d4bada',
    required: false,
  })
  @IsOptional()
  @IsUUID()
  paymentGatewayId?: string;
}

/**
 * DTO cho việc tạo đơn hàng nháp
 * Cấu trúc chuẩn với userShopAddressId và customerInfo
 */
export class CreateDraftOrderDto {
  @ApiProperty({
    description: 'ID địa chỉ shop để gửi hàng',
    example: 36,
    required: true,
  })
  @IsNotEmpty()
  @IsNumber()
  @Min(1)
  userShopAddressId: number;

  @ApiProperty({
    description: 'Thông tin khách hàng',
    type: DraftOrderCustomerInfoDto,
    example: {
      customerId: 'a5541891-75e8-4d55-9185-586813d4bada',
    },
  })
  @IsNotEmpty()
  @ValidateNested()
  @Type(() => DraftOrderCustomerInfoDto)
  customerInfo: DraftOrderCustomerInfoDto;

  @ApiProperty({
    description: 'Danh sách sản phẩm trong đơn hàng',
    type: [OrderProductItemDto],
    example: [
      {
        productId: 'product-uuid-1',
        quantity: 2,
        unitPrice: 50000,
        variantId: 'variant-uuid-1',
        customFields: {},
      },
    ],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => OrderProductItemDto)
  products: OrderProductItemDto[];

  @ApiProperty({
    description: 'Thông tin thanh toán đơn giản',
    type: DraftOrderBillInfoDto,
    example: {
      selectedCarrier: 'GHN',
      shippingServiceType: 'standard',
      paymentMethod: 'CASH',
      userShippingFee: 25000,
    },
    examples: {
      'Mock enabled (tính phí tự động)': {
        summary: 'ENABLE_MOCK_ADDRESSES=true',
        description: 'Hệ thống tự động tính phí vận chuyển từ API',
        value: {
          selectedCarrier: 'GHN',
          shippingServiceType: 'standard',
          paymentMethod: 'CASH',
        },
      },
      'Mock disabled (người dùng nhập phí)': {
        summary: 'ENABLE_MOCK_ADDRESSES=false',
        description: 'Người dùng cung cấp phí vận chuyển',
        value: {
          selectedCarrier: 'GHN',
          shippingServiceType: 'standard',
          paymentMethod: 'CASH',
          userShippingFee: 25000,
        },
      },
    },
  })
  @ValidateNested()
  @Type(() => DraftOrderBillInfoDto)
  billInfo: DraftOrderBillInfoDto;

  @ApiProperty({
    description: 'Thông tin vận chuyển (tùy chọn)',
    type: OrderLogisticInfoDto,
    required: false,
    example: {
      carrier: 'GHN',
      shippingService: 'standard',
      deliveryAddress: {
        addressId: 1,
      },
    },
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => OrderLogisticInfoDto)
  logisticInfo?: OrderLogisticInfoDto;

  @ApiProperty({
    description: 'Đơn hàng có yêu cầu vận chuyển hay không',
    example: true,
    default: true,
  })
  @IsOptional()
  @IsBoolean()
  hasShipping?: boolean = true;

  @ApiProperty({
    description: 'Ghi chú đơn hàng',
    example: 'Giao hàng trong giờ hành chính',
    required: false,
  })
  @IsOptional()
  @IsString()
  note?: string;

  @ApiProperty({
    description: 'Tags đơn hàng',
    example: ['urgent', 'vip-customer'],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @ApiProperty({
    description: 'Nguồn đơn hàng',
    example: 'website',
    default: 'website',
    required: false,
  })
  @IsOptional()
  @IsString()
  source?: string;
}

/**
 * DTO cho việc tạo đơn hàng public (sử dụng API Key)
 * Kế thừa từ CreateDraftOrderDto và thêm khả năng set status
 */
export class CreatePublicOrderDto extends CreateDraftOrderDto {
  @ApiProperty({
    description: 'Trạng thái đơn hàng',
    enum: OrderStatusEnum,
    example: OrderStatusEnum.PENDING,
    required: false,
    default: OrderStatusEnum.PENDING,
  })
  @IsOptional()
  @IsEnum(OrderStatusEnum, { message: 'Trạng thái đơn hàng không hợp lệ' })
  orderStatus?: OrderStatusEnum;

  @ApiProperty({
    description: 'Trạng thái vận chuyển',
    enum: ShippingStatusEnum,
    example: ShippingStatusEnum.PENDING,
    required: false,
    default: ShippingStatusEnum.PENDING,
  })
  @IsOptional()
  @IsEnum(ShippingStatusEnum, { message: 'Trạng thái vận chuyển không hợp lệ' })
  shippingStatus?: ShippingStatusEnum;

  @ApiProperty({
    description: 'ID người dùng sở hữu đơn hàng',
    example: 123,
    required: true,
  })
  @IsNotEmpty()
  @IsNumber()
  @Min(1)
  userId: number;

  @ApiProperty({
    description:
      'ID của agent (để lấy payment gateway nếu không truyền paymentGatewayId)',
    example: 'a5541891-75e8-4d55-9185-586813d4bada',
    required: false,
  })
  @IsOptional()
  @IsUUID()
  agentId?: string;
}

# Facebook Messenger Webhook Controller

This controller handles webhook events from Facebook Messenger Platform with robust security verification.

## Security Features

### 🔐 Signature Verification
All incoming webhook requests are verified using HMAC SHA256 signature verification to ensure:
- **Authenticity**: Requests come from Facebook
- **Integrity**: Payload hasn't been tampered with  
- **Security**: Protection against unauthorized requests

### 🛡️ Components

1. **FacebookSignatureGuard**: Verifies webhook signatures using `X-Hub-Signature-256` header
2. **RawBodyInterceptor**: Preserves raw request body for signature verification
3. **Enhanced Logging**: Detailed security and processing logs

## Environment Configuration

### Required Environment Variables

```bash
# Facebook App Configuration
FACEBOOK_APP_ID=your_facebook_app_id
FACEBOOK_APP_SECRET=your_facebook_app_secret
FACEBOOK_GRAPH_API_VERSION=v18.0
FACEBOOK_REDIRECT_URI=http://localhost:3000/auth/facebook/callback

# Webhook Configuration
FACEBOOK_WEBHOOK_VERIFY_TOKEN=your_unique_verify_token
FACEBOOK_WEBHOOK_SECRET=your_facebook_app_secret  # Usually same as FACEBOOK_APP_SECRET
```

### Configuration Notes

- **FACEBOOK_WEBHOOK_SECRET**: Must be your Facebook App Secret (same value as `FACEBOOK_APP_SECRET`)
- **FACEBOOK_WEBHOOK_VERIFY_TOKEN**: A unique token you set in Facebook Developer Console
- All values have secure defaults for development/testing

## How Signature Verification Works

1. **Facebook sends webhook** with `X-Hub-Signature-256` header containing HMAC SHA256 hash
2. **RawBodyInterceptor** captures the raw request body before JSON parsing
3. **FacebookSignatureGuard** computes HMAC SHA256 of raw body using app secret
4. **Timing-safe comparison** validates Facebook's signature against computed signature
5. **Request processed** only if signature verification succeeds

### Signature Format
```
X-Hub-Signature-256: sha256=<hex_encoded_hash>
```

## Webhook Flow

```mermaid
graph TD
    A[Facebook Webhook] --> B[RawBodyInterceptor]
    B --> C[FacebookSignatureGuard]
    C --> D{Signature Valid?}
    D -->|Yes| E[Process Webhook]
    D -->|No| F[Reject Request]
    E --> G[Queue Event]
    G --> H[Return 'EVENT_RECEIVED']
    F --> I[Return 401 Unauthorized]
```

## Supported Event Types

- `MESSAGE` - New messages
- `MESSAGE_EDIT` - Message edits  
- `MESSAGE_REACTION` - Message reactions
- `MESSAGE_READ` - Read receipts
- `MESSAGING_FEEDBACK` - Customer feedback
- `MESSAGING_POSTBACK` - Button clicks

## Error Handling

### Signature Verification Failures
- **Invalid signature**: Returns `401 Unauthorized`
- **Missing signature**: Returns `401 Unauthorized`  
- **Missing raw body**: Returns `401 Unauthorized`

### Processing Errors
- **Invalid page object**: Returns `404 Not Found`
- **Queue errors**: Returns `500 Internal Server Error`

## Testing

### Development Setup
1. Set environment variables with test values
2. Use Facebook's webhook testing tools
3. Verify signature verification in logs

### Production Checklist
- [ ] `FACEBOOK_WEBHOOK_SECRET` matches your Facebook App Secret
- [ ] Webhook URL configured in Facebook Developer Console
- [ ] SSL certificate valid (required by Facebook)
- [ ] Logs show successful signature verification

## Security Best Practices

1. **Keep App Secret Secure**: Never expose in logs or client-side code
2. **Use HTTPS**: Facebook requires SSL for webhooks
3. **Monitor Logs**: Watch for signature verification failures
4. **Rotate Secrets**: Periodically update app secrets
5. **Validate Payloads**: Additional validation beyond signature verification

## Troubleshooting

### Common Issues

**Signature verification fails**:
- Check `FACEBOOK_WEBHOOK_SECRET` matches Facebook App Secret
- Verify raw body is being captured correctly
- Check Facebook Developer Console webhook configuration

**Webhook not received**:
- Verify webhook URL in Facebook Developer Console
- Check SSL certificate validity
- Ensure webhook verify token matches

### Debug Logs

Enable debug logging to see:
- Raw body capture status
- Signature verification details
- Event processing flow

```typescript
// In your logger configuration
LOG_LEVEL=debug
```

## Integration Example

```typescript
// The controller is automatically configured with security guards
@Post('/webhook')
@UseGuards(FacebookSignatureGuard)        // Signature verification
@UseInterceptors(RawBodyInterceptor)      // Raw body capture
async postWebhook(@Body() body: any): Promise<string> {
  // Webhook processing logic
  // Only executes if signature verification passes
}
```
import { Injectable, NestMiddleware, Logger } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { GenericSessionService } from '../services';

/**
 * Middleware để track session activity và auto-update last activity
 */
@Injectable()
export class SessionActivityMiddleware implements NestMiddleware {
  private readonly logger = new Logger(SessionActivityMiddleware.name);

  constructor(
    private readonly genericSessionService: GenericSessionService,
  ) {}

  async use(req: Request, res: Response, next: NextFunction) {
    try {
      // Extract sessionId từ params, query, hoặc headers
      const sessionId = this.extractSessionId(req);
      
      if (sessionId) {
        // Update last activity async (không block request)
        this.updateSessionActivity(sessionId, req);
      }

      next();
    } catch (error) {
      this.logger.error(`Session activity middleware error: ${error.message}`, error.stack);
      // Không block request nếu có lỗi
      next();
    }
  }

  /**
   * Extract sessionId từ request
   */
  private extractSessionId(req: Request): string | null {
    // Từ URL params
    if (req.params.sessionId) {
      return req.params.sessionId;
    }

    // Từ query params
    if (req.query.sessionId) {
      return req.query.sessionId as string;
    }

    // Từ headers
    if (req.headers['x-session-id']) {
      return req.headers['x-session-id'] as string;
    }

    // Từ body (cho POST requests)
    if (req.body && req.body.sessionId) {
      return req.body.sessionId;
    }

    return null;
  }

  /**
   * Update session activity async
   */
  private async updateSessionActivity(sessionId: string, req: Request): Promise<void> {
    try {
      // Update last activity
      await this.genericSessionService.getSessionBySessionIdOptional(sessionId);
      
      // Log activity nếu cần
      this.logger.debug(`Session activity updated: ${sessionId} - ${req.method} ${req.path}`);
    } catch (error) {
      // Silent fail - không log error để tránh spam logs
      // Chỉ log nếu là critical error
      if (error.message.includes('database') || error.message.includes('connection')) {
        this.logger.warn(`Failed to update session activity: ${sessionId} - ${error.message}`);
      }
    }
  }
}

/**
 * Interface để extend Request với session data
 */
declare global {
  namespace Express {
    interface Request {
      genericSession?: any;
      sessionId?: string;
    }
  }
}

import { 
  Controller, 
  Get, 
  Post, 
  Put, 
  Delete, 
  Body, 
  Param, 
  Query, 
  UseGuards,
  Logger 
} from '@nestjs/common';
import { 
  ApiTags, 
  ApiOperation, 
  ApiResponse, 
  ApiParam, 
  ApiQuery,
  ApiBearerAuth 
} from '@nestjs/swagger';
import { GenericSessionService } from '../services';
import { 
  CreateGenericSessionDto, 
  UpdateGenericSessionDto, 
  GenericSessionResponseDto,
  QueryGenericSessionDto 
} from '../dto/generic-session.dto';
import { ApiResponseDto } from '@/common/response';

/**
 * Controller cho Generic Session Management
 * Quản lý sessions cho trang generic với WebSocket real-time control
 */
@ApiTags('Generic Sessions')
@Controller('generic/sessions')
export class GenericSessionController {
  private readonly logger = new Logger(GenericSessionController.name);

  constructor(
    private readonly genericSessionService: GenericSessionService,
  ) {}

  /**
   * Tạo session mới
   */
  @Post()
  @ApiOperation({ 
    summary: 'Tạo session mới',
    description: 'Tạo session mới cho trang generic với cấu hình tùy chỉnh'
  })
  @ApiResponse({
    status: 201,
    description: 'Session đã được tạo thành công',
    type: ApiResponseDto<GenericSessionResponseDto>
  })
  @ApiResponse({
    status: 400,
    description: 'Dữ liệu đầu vào không hợp lệ'
  })
  @ApiResponse({
    status: 409,
    description: 'Session ID đã tồn tại'
  })
  async createSession(
    @Body() createDto: CreateGenericSessionDto
  ): Promise<ApiResponseDto<GenericSessionResponseDto>> {
    try {
      const session = await this.genericSessionService.createSession(createDto);
      
      this.logger.log(`Created session: ${session.sessionId}`);
      
      return ApiResponseDto.created(session, 'Session đã được tạo thành công');
    } catch (error) {
      this.logger.error(`Error creating session: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Lấy session theo sessionId
   */
  @Get(':sessionId')
  @ApiOperation({ 
    summary: 'Lấy session theo ID',
    description: 'Lấy thông tin chi tiết của session theo sessionId'
  })
  @ApiParam({
    name: 'sessionId',
    description: 'Session ID',
    example: 'session-123-abc'
  })
  @ApiResponse({
    status: 200,
    description: 'Thông tin session',
    type: ApiResponseDto<GenericSessionResponseDto>
  })
  @ApiResponse({
    status: 404,
    description: 'Không tìm thấy session'
  })
  async getSession(
    @Param('sessionId') sessionId: string
  ): Promise<ApiResponseDto<GenericSessionResponseDto>> {
    try {
      const session = await this.genericSessionService.getSessionBySessionId(sessionId);
      
      return ApiResponseDto.success(session, 'Lấy thông tin session thành công');
    } catch (error) {
      this.logger.error(`Error getting session: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Cập nhật session
   */
  @Put(':sessionId')
  @ApiOperation({ 
    summary: 'Cập nhật session',
    description: 'Cập nhật thông tin và cấu hình của session'
  })
  @ApiParam({
    name: 'sessionId',
    description: 'Session ID',
    example: 'session-123-abc'
  })
  @ApiResponse({
    status: 200,
    description: 'Session đã được cập nhật thành công',
    type: ApiResponseDto<GenericSessionResponseDto>
  })
  @ApiResponse({
    status: 404,
    description: 'Không tìm thấy session'
  })
  async updateSession(
    @Param('sessionId') sessionId: string,
    @Body() updateDto: UpdateGenericSessionDto
  ): Promise<ApiResponseDto<GenericSessionResponseDto>> {
    try {
      const session = await this.genericSessionService.updateSession(sessionId, updateDto);
      
      this.logger.log(`Updated session: ${sessionId}`);
      
      return ApiResponseDto.updated(session, 'Session đã được cập nhật thành công');
    } catch (error) {
      this.logger.error(`Error updating session: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Xóa session
   */
  @Delete(':sessionId')
  @ApiOperation({ 
    summary: 'Xóa session',
    description: 'Xóa session và tất cả dữ liệu liên quan (widgets, layout)'
  })
  @ApiParam({
    name: 'sessionId',
    description: 'Session ID',
    example: 'session-123-abc'
  })
  @ApiResponse({
    status: 200,
    description: 'Session đã được xóa thành công'
  })
  @ApiResponse({
    status: 404,
    description: 'Không tìm thấy session'
  })
  async deleteSession(
    @Param('sessionId') sessionId: string
  ): Promise<ApiResponseDto<{ deletedWidgets: number; deletedLayouts: number; deletedSessions: number }>> {
    try {
      const result = await this.genericSessionService.deleteSession(sessionId);
      
      this.logger.log(`Deleted session: ${sessionId}`);
      
      return ApiResponseDto.deleted(result, 'Session đã được xóa thành công');
    } catch (error) {
      this.logger.error(`Error deleting session: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Deactivate session
   */
  @Put(':sessionId/deactivate')
  @ApiOperation({ 
    summary: 'Deactivate session',
    description: 'Deactivate session mà không xóa dữ liệu'
  })
  @ApiParam({
    name: 'sessionId',
    description: 'Session ID',
    example: 'session-123-abc'
  })
  @ApiResponse({
    status: 200,
    description: 'Session đã được deactivate thành công',
    type: ApiResponseDto<GenericSessionResponseDto>
  })
  async deactivateSession(
    @Param('sessionId') sessionId: string
  ): Promise<ApiResponseDto<GenericSessionResponseDto>> {
    try {
      const session = await this.genericSessionService.deactivateSession(sessionId);
      
      this.logger.log(`Deactivated session: ${sessionId}`);
      
      return ApiResponseDto.updated(session, 'Session đã được deactivate thành công');
    } catch (error) {
      this.logger.error(`Error deactivating session: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Lấy tất cả sessions active
   */
  @Get()
  @ApiOperation({ 
    summary: 'Lấy danh sách sessions',
    description: 'Lấy danh sách tất cả sessions active hoặc filter theo user'
  })
  @ApiQuery({
    name: 'userId',
    description: 'User ID để filter',
    required: false,
    type: Number
  })
  @ApiResponse({
    status: 200,
    description: 'Danh sách sessions',
    type: ApiResponseDto<GenericSessionResponseDto[]>
  })
  async getSessions(
    @Query() query: QueryGenericSessionDto
  ): Promise<ApiResponseDto<GenericSessionResponseDto[]>> {
    try {
      let sessions: GenericSessionResponseDto[];

      if (query.userId) {
        sessions = await this.genericSessionService.getActiveSessionsByUserId(query.userId);
      } else {
        sessions = await this.genericSessionService.getAllActiveSessions();
      }
      
      return ApiResponseDto.success(sessions, 'Lấy danh sách sessions thành công');
    } catch (error) {
      this.logger.error(`Error getting sessions: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Cleanup sessions hết hạn
   */
  @Post('cleanup')
  @ApiOperation({ 
    summary: 'Cleanup sessions hết hạn',
    description: 'Deactivate tất cả sessions đã hết hạn'
  })
  @ApiResponse({
    status: 200,
    description: 'Cleanup thành công'
  })
  async cleanupExpiredSessions(): Promise<ApiResponseDto<{ cleanedCount: number }>> {
    try {
      const cleanedCount = await this.genericSessionService.cleanupExpiredSessions();
      
      this.logger.log(`Cleaned up ${cleanedCount} expired sessions`);
      
      return ApiResponseDto.success(
        { cleanedCount },
        `Đã cleanup ${cleanedCount} sessions hết hạn`
      );
    } catch (error) {
      this.logger.error(`Error cleaning up expired sessions: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Kiểm tra session có valid không
   */
  @Get(':sessionId/validate')
  @ApiOperation({ 
    summary: 'Validate session',
    description: 'Kiểm tra session có tồn tại và active không'
  })
  @ApiParam({
    name: 'sessionId',
    description: 'Session ID',
    example: 'session-123-abc'
  })
  @ApiResponse({
    status: 200,
    description: 'Kết quả validation'
  })
  async validateSession(
    @Param('sessionId') sessionId: string
  ): Promise<ApiResponseDto<{ isValid: boolean }>> {
    try {
      const isValid = await this.genericSessionService.isSessionActiveAndValid(sessionId);
      
      return ApiResponseDto.success(
        { isValid },
        isValid ? 'Session hợp lệ' : 'Session không hợp lệ hoặc đã hết hạn'
      );
    } catch (error) {
      this.logger.error(`Error validating session: ${error.message}`, error.stack);
      throw error;
    }
  }
}

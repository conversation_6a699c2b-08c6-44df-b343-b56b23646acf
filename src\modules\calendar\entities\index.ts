// Calendar entities exports - T<PERSON>i <PERSON>u hóa từ 8 entity xuống 2 entity
export * from './calendar-event.entity';
export * from './calendar-execution-history.entity';

// Entity classes array for TypeORM
import { CalendarEvent } from './calendar-event.entity';
import { CalendarExecutionHistory } from './calendar-execution-history.entity';

export const CALENDAR_ENTITIES = [CalendarEvent, CalendarExecutionHistory];

// Export enums để sử dụng ở nơi khác
export {
  CalendarActionType,
  CalendarEventStatus,
  ExecutionStatus,
  CalendarPriority,
} from './calendar-event.entity';

export { ExecutionHistoryStatus } from './calendar-execution-history.entity';

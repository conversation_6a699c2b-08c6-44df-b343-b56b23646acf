import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CalendarEvent } from '../../entities/calendar-event.entity';
import {
  CalendarEventStatus,
  ExecutionStatus,
  CalendarActionType,
} from '../../entities';
import { EventStatus } from '../../constants';

/**
 * Calendar Admin Service
 * Xử lý các tác vụ quản lý calendar system-wide
 */
@Injectable()
export class CalendarAdminService {
  private readonly logger = new Logger(CalendarAdminService.name);

  constructor(
    @InjectRepository(CalendarEvent)
    private readonly calendarEventRepository: Repository<CalendarEvent>,
  ) {}

  /**
   * Lấy thống kê tổng quan hệ thống
   */
  async getSystemStatistics(): Promise<any> {
    this.logger.log('Getting system statistics');

    const [
      totalEvents,
      activeEvents,
      taskEvents,
      reminderEvents,
      reportEvents,
      runningEvents,
      todayEvents,
      thisWeekEvents,
    ] = await Promise.all([
      this.calendarEventRepository.count(),
      this.calendarEventRepository.count({
        where: { status: CalendarEventStatus.SCHEDULED },
      }),
      this.calendarEventRepository.count({
        where: { actionType: CalendarActionType.TASK },
      }),
      this.calendarEventRepository.count({
        where: { actionType: CalendarActionType.REMINDER },
      }),
      this.calendarEventRepository.count({
        where: { actionType: CalendarActionType.REPORT },
      }),
      this.calendarEventRepository.count({
        where: { executionStatus: ExecutionStatus.RUNNING },
      }),
      this.getTodayEventsCount(),
      this.getThisWeekEventsCount(),
    ]);

    return {
      events: {
        total: totalEvents,
        active: activeEvents,
        today: todayEvents,
        thisWeek: thisWeekEvents,
      },
      actionTypes: {
        tasks: taskEvents,
        reminders: reminderEvents,
        reports: reportEvents,
      },
      execution: {
        running: runningEvents,
      },
      generatedAt: new Date(),
    };
  }

  /**
   * Lấy tất cả events trong hệ thống
   */
  async getAllEvents(query: {
    page: number;
    limit: number;
    search?: string;
    status?: string;
    type?: string;
  }): Promise<any> {
    this.logger.log(`Getting all events with query: ${JSON.stringify(query)}`);

    const queryBuilder = this.calendarEventRepository
      .createQueryBuilder('event')
      .leftJoinAndSelect('event.executionHistory', 'executionHistory');

    if (query.search) {
      queryBuilder.andWhere(
        '(event.title ILIKE :search OR event.description ILIKE :search)',
        { search: `%${query.search}%` },
      );
    }

    if (query.status) {
      queryBuilder.andWhere('event.status = :status', { status: query.status });
    }

    if (query.type) {
      queryBuilder.andWhere('event.actionType = :type', { type: query.type });
    }

    const [events, total] = await queryBuilder
      .orderBy('event.startTime', 'DESC')
      .skip((query.page - 1) * query.limit)
      .take(query.limit)
      .getManyAndCount();

    return {
      items: events,
      total,
      page: query.page,
      limit: query.limit,
      totalPages: Math.ceil(total / query.limit),
    };
  }

  /**
   * Xóa event (admin)
   */
  async deleteEvent(eventId: string): Promise<void> {
    this.logger.log(`Admin deleting event: ${eventId}`);

    const event = await this.calendarEventRepository.findOne({
      where: { id: eventId },
      relations: ['attendees', 'resources', 'reminders', 'recurrences'],
    });

    if (!event) {
      throw new Error('Event not found');
    }

    // NOTE: Trong phiên bản tối ưu, các entity liên quan đã được loại bỏ
    // Thông tin attendees, resources được lưu trong actionConfig
    // Execution history sẽ được tự động xóa bởi CASCADE foreign key

    // Xóa event
    await this.calendarEventRepository.delete(eventId);

    this.logger.log(`Event deleted successfully: ${eventId}`);
  }

  /**
   * Chạy maintenance tasks
   */
  async runMaintenance(): Promise<any> {
    this.logger.log('Running maintenance tasks');

    const results = {
      cleanupOldEvents: 0,
      cleanupFailedJobs: 0,
      updateStatistics: true,
      optimizeDatabase: true,
    };

    try {
      // Cleanup old events (older than 1 year)
      const oneYearAgo = new Date();
      oneYearAgo.setFullYear(oneYearAgo.getFullYear() - 1);

      const deleteResult = await this.calendarEventRepository
        .createQueryBuilder()
        .delete()
        .where('startTime < :date AND status = :status', {
          date: oneYearAgo,
          status: EventStatus.COMPLETED,
        })
        .execute();

      results.cleanupOldEvents = deleteResult.affected || 0;

      // TODO: Cleanup failed jobs from queue
      // TODO: Update cached statistics
      // TODO: Run database optimization

      this.logger.log(`Maintenance completed: ${JSON.stringify(results)}`);
      return results;
    } catch (error) {
      this.logger.error(`Maintenance failed: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Retry failed jobs
   */
  async retryFailedJobs(): Promise<any> {
    this.logger.log('Retrying failed jobs');

    // TODO: Implement retry logic for failed queue jobs
    const results = {
      retriedJobs: 0,
      successfulRetries: 0,
      failedRetries: 0,
    };

    this.logger.log(`Failed jobs retry completed: ${JSON.stringify(results)}`);
    return results;
  }

  /**
   * Lấy số lượng events hôm nay
   */
  private async getTodayEventsCount(): Promise<number> {
    const today = new Date();
    const startOfDay = new Date(
      today.getFullYear(),
      today.getMonth(),
      today.getDate(),
    );
    const endOfDay = new Date(
      today.getFullYear(),
      today.getMonth(),
      today.getDate() + 1,
    );

    return await this.calendarEventRepository
      .createQueryBuilder('event')
      .where('event.startTime >= :startOfDay AND event.startTime < :endOfDay', {
        startOfDay,
        endOfDay,
      })
      .getCount();
  }

  /**
   * Lấy số lượng events tuần này
   */
  private async getThisWeekEventsCount(): Promise<number> {
    const today = new Date();
    const startOfWeek = new Date(
      today.getFullYear(),
      today.getMonth(),
      today.getDate() - today.getDay(),
    );
    const endOfWeek = new Date(
      today.getFullYear(),
      today.getMonth(),
      today.getDate() - today.getDay() + 7,
    );

    return await this.calendarEventRepository
      .createQueryBuilder('event')
      .where(
        'event.startTime >= :startOfWeek AND event.startTime < :endOfWeek',
        {
          startOfWeek,
          endOfWeek,
        },
      )
      .getCount();
  }
}

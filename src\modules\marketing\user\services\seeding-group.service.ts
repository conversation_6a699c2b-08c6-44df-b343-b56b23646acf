import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AppException, ErrorCode } from '@common/exceptions';
import { PaginatedResult } from '@common/response';
import { MARKETING_ERROR_CODES } from '../exceptions/marketing-error.codes';
import {
  SeedingGroup,
  SeedingGroupAccount,
  SeedingGroupStatus,
  SeedingAccountStatus,
} from '../entities/seeding-group.entity';
import {
  SeedingGroupRepository,
  SeedingGroupAccountRepository,
} from '../repositories/seeding-group.repository';
import {
  CreateSeedingGroupDto,
  UpdateSeedingGroupDto,
  SeedingGroupQueryDto,
  BulkDeleteSeedingGroupDto,
} from '../dto/seeding-group/seeding-group.dto';

/**
 * Service xử lý logic nghiệp vụ cho Seeding Groups
 */
@Injectable()
export class SeedingGroupService {
  private readonly logger = new Logger(SeedingGroupService.name);

  constructor(
    private readonly seedingGroupRepository: SeedingGroupRepository,
    private readonly seedingGroupAccountRepository: SeedingGroupAccountRepository,
    @InjectRepository(SeedingGroup)
    private readonly seedingGroupEntityRepository: Repository<SeedingGroup>,
    @InjectRepository(SeedingGroupAccount)
    private readonly seedingGroupAccountEntityRepository: Repository<SeedingGroupAccount>,
  ) {}

  /**
   * Lấy danh sách seeding groups với phân trang
   */
  async findAll(
    userId: number,
    queryDto: SeedingGroupQueryDto,
  ): Promise<PaginatedResult<SeedingGroup>> {
    try {
      this.logger.log(`Getting seeding groups for user ${userId}`);
      return await this.seedingGroupRepository.findWithPagination(
        userId,
        queryDto,
      );
    } catch (error) {
      this.logger.error(
        `Error getting seeding groups: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Lỗi khi lấy danh sách seeding groups',
      );
    }
  }

  /**
   * Lấy chi tiết seeding group
   */
  async findOne(id: string, userId: number): Promise<SeedingGroup> {
    try {
      this.logger.log(`Getting seeding group ${id} for user ${userId}`);

      const seedingGroup = await this.seedingGroupRepository.findByIdAndUserId(
        id,
        userId,
      );
      if (!seedingGroup) {
        throw new AppException(MARKETING_ERROR_CODES.SEEDING_GROUP_NOT_FOUND);
      }

      return seedingGroup;
    } catch (error) {
      this.logger.error(
        `Error getting seeding group: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Lỗi khi lấy chi tiết seeding group',
      );
    }
  }

  /**
   * Tạo seeding group mới
   */
  async create(
    userId: number,
    createDto: CreateSeedingGroupDto,
  ): Promise<SeedingGroup> {
    try {
      this.logger.log(`Creating seeding group for user ${userId}`);

      // Validate business rules
      await this.validateCreateDto(userId, createDto);

      const now = Date.now();

      // Tạo seeding group
      const seedingGroupData: Partial<SeedingGroup> = {
        userId,
        name: createDto.name,
        description: createDto.description,
        oaAccountId: createDto.oaAccountId,
        oaAgentId: createDto.oaAgentId,
        groupId: createDto.groupId,
        status: SeedingGroupStatus.DRAFT,
        startTime: createDto.startTime,
        endTime: createDto.endTime,
        endDate: createDto.endDate,
        intervalMinutes: createDto.intervalMinutes,
        maxPerDay: createDto.maxPerDay,
        randomize: createDto.randomize,
        totalAccounts: createDto.accounts.length,
        activeAccounts: 0,
        totalMessagesSent: 0,
        createdAt: now,
        updatedAt: now,
      };

      const savedSeedingGroup =
        await this.seedingGroupEntityRepository.save(seedingGroupData);

      // Tạo accounts
      if (createDto.accounts.length > 0) {
        const accountsData = createDto.accounts.map((account) => ({
          seedingGroupId: savedSeedingGroup.id,
          personalAccountId: account.personalAccountId,
          personalAccountName: `Account ${account.personalAccountId}`, // TODO: Get real name
          agentId: account.agentId,
          agentName: `Agent ${account.agentId}`, // TODO: Get real name
          status: SeedingAccountStatus.ACTIVE,
          messagesSentToday: 0,
          totalMessagesSent: 0,
          errorCount: 0,
          createdAt: now,
          updatedAt: now,
        }));

        await this.seedingGroupAccountEntityRepository.save(accountsData);
      }

      // Lấy lại với relations
      return await this.findOne(savedSeedingGroup.id, userId);
    } catch (error) {
      this.logger.error(
        `Error creating seeding group: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Lỗi khi tạo seeding group',
      );
    }
  }

  /**
   * Cập nhật seeding group
   */
  async update(
    id: string,
    userId: number,
    updateDto: UpdateSeedingGroupDto,
  ): Promise<SeedingGroup> {
    try {
      this.logger.log(`Updating seeding group ${id} for user ${userId}`);

      const existingSeedingGroup = await this.findOne(id, userId);

      // Validate business rules
      await this.validateUpdateDto(userId, updateDto, existingSeedingGroup);

      const now = Date.now();

      // Cập nhật seeding group (exclude accounts from direct update)
      const { accounts, ...updateFields } = updateDto;
      const updateData: Partial<SeedingGroup> = {
        ...updateFields,
        updatedAt: now,
      };

      await this.seedingGroupEntityRepository.update(id, updateData);

      // Cập nhật accounts nếu có
      if (updateDto.accounts) {
        // TODO: Implement account updates logic
        this.logger.log(`Updating accounts for seeding group ${id}`);
      }

      return await this.findOne(id, userId);
    } catch (error) {
      this.logger.error(
        `Error updating seeding group: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Lỗi khi cập nhật seeding group',
      );
    }
  }

  /**
   * Xóa seeding group
   */
  async remove(id: string, userId: number): Promise<void> {
    try {
      this.logger.log(`Deleting seeding group ${id} for user ${userId}`);

      const existingSeedingGroup = await this.findOne(id, userId);

      // Kiểm tra trạng thái trước khi xóa
      if (existingSeedingGroup.status === SeedingGroupStatus.ACTIVE) {
        throw new AppException(
          ErrorCode.BAD_REQUEST,
          'Không thể xóa seeding group đang hoạt động',
        );
      }

      // Xóa accounts trước
      await this.seedingGroupAccountRepository.bulkDeleteBySeedingGroupId(id);

      // Xóa seeding group
      await this.seedingGroupEntityRepository.delete({ id, userId });

      this.logger.log(`Successfully deleted seeding group ${id}`);
    } catch (error) {
      this.logger.error(
        `Error deleting seeding group: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Lỗi khi xóa seeding group',
      );
    }
  }

  /**
   * Bulk delete seeding groups
   */
  async bulkDelete(
    userId: number,
    bulkDeleteDto: BulkDeleteSeedingGroupDto,
  ): Promise<void> {
    try {
      this.logger.log(`Bulk deleting seeding groups for user ${userId}`);

      // Kiểm tra tất cả seeding groups có tồn tại và thuộc về user
      for (const id of bulkDeleteDto.ids) {
        const seedingGroup =
          await this.seedingGroupRepository.findByIdAndUserId(id, userId);
        if (!seedingGroup) {
          throw new AppException(MARKETING_ERROR_CODES.SEEDING_GROUP_NOT_FOUND);
        }

        if (seedingGroup.status === SeedingGroupStatus.ACTIVE) {
          throw new AppException(
            ErrorCode.BAD_REQUEST,
            `Không thể xóa seeding group ${id} đang hoạt động`,
          );
        }
      }

      // Xóa tất cả accounts
      for (const id of bulkDeleteDto.ids) {
        await this.seedingGroupAccountRepository.bulkDeleteBySeedingGroupId(id);
      }

      // Xóa seeding groups
      await this.seedingGroupRepository.bulkDelete(bulkDeleteDto.ids, userId);

      this.logger.log(
        `Successfully bulk deleted ${bulkDeleteDto.ids.length} seeding groups`,
      );
    } catch (error) {
      this.logger.error(
        `Error bulk deleting seeding groups: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Lỗi khi xóa hàng loạt seeding groups',
      );
    }
  }

  /**
   * Start seeding group
   */
  async start(id: string, userId: number): Promise<SeedingGroup> {
    try {
      this.logger.log(`Starting seeding group ${id} for user ${userId}`);

      const seedingGroup = await this.findOne(id, userId);

      if (seedingGroup.status === SeedingGroupStatus.ACTIVE) {
        throw new AppException(
          ErrorCode.BAD_REQUEST,
          'Seeding group đã đang hoạt động',
        );
      }

      await this.seedingGroupEntityRepository.update(id, {
        status: SeedingGroupStatus.ACTIVE,
        updatedAt: Date.now(),
      });

      return await this.findOne(id, userId);
    } catch (error) {
      this.logger.error(
        `Error starting seeding group: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Lỗi khi khởi động seeding group',
      );
    }
  }

  /**
   * Pause seeding group
   */
  async pause(id: string, userId: number): Promise<SeedingGroup> {
    try {
      this.logger.log(`Pausing seeding group ${id} for user ${userId}`);

      const seedingGroup = await this.findOne(id, userId);

      if (seedingGroup.status !== SeedingGroupStatus.ACTIVE) {
        throw new AppException(
          ErrorCode.BAD_REQUEST,
          'Chỉ có thể tạm dừng seeding group đang hoạt động',
        );
      }

      await this.seedingGroupEntityRepository.update(id, {
        status: SeedingGroupStatus.PAUSED,
        updatedAt: Date.now(),
      });

      return await this.findOne(id, userId);
    } catch (error) {
      this.logger.error(
        `Error pausing seeding group: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Lỗi khi tạm dừng seeding group',
      );
    }
  }

  /**
   * Stop seeding group
   */
  async stop(id: string, userId: number): Promise<SeedingGroup> {
    try {
      this.logger.log(`Stopping seeding group ${id} for user ${userId}`);

      const seedingGroup = await this.findOne(id, userId);

      if (
        ![SeedingGroupStatus.ACTIVE, SeedingGroupStatus.PAUSED].includes(
          seedingGroup.status,
        )
      ) {
        throw new AppException(
          ErrorCode.BAD_REQUEST,
          'Chỉ có thể dừng seeding group đang hoạt động hoặc tạm dừng',
        );
      }

      await this.seedingGroupEntityRepository.update(id, {
        status: SeedingGroupStatus.STOPPED,
        updatedAt: Date.now(),
      });

      return await this.findOne(id, userId);
    } catch (error) {
      this.logger.error(
        `Error stopping seeding group: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Lỗi khi dừng seeding group',
      );
    }
  }

  /**
   * Validate create DTO
   */
  private async validateCreateDto(
    userId: number,
    createDto: CreateSeedingGroupDto,
  ): Promise<void> {
    // Kiểm tra tên trùng lặp
    const existingGroupByName = await this.seedingGroupEntityRepository.findOne(
      {
        where: { userId, name: createDto.name },
      },
    );

    if (existingGroupByName) {
      throw new AppException(MARKETING_ERROR_CODES.SEEDING_GROUP_NAME_EXISTS);
    }

    // Kiểm tra groupId trùng lặp (quan hệ 1-1)
    const existingGroupByGroupId =
      await this.seedingGroupEntityRepository.findOne({
        where: { groupId: createDto.groupId },
      });

    if (existingGroupByGroupId) {
      throw new AppException(
        MARKETING_ERROR_CODES.SEEDING_GROUP_ZALO_GROUP_IN_USE,
      );
    }

    // Validate time range
    if (createDto.startTime >= createDto.endTime) {
      throw new AppException(
        ErrorCode.BAD_REQUEST,
        'Giờ bắt đầu phải nhỏ hơn giờ kết thúc',
      );
    }

    // Validate accounts
    if (createDto.accounts.length === 0) {
      throw new AppException(
        ErrorCode.BAD_REQUEST,
        'Phải có ít nhất 1 tài khoản',
      );
    }

    // TODO: Validate OA account, agent, group existence
  }

  /**
   * Validate update DTO
   */
  private async validateUpdateDto(
    userId: number,
    updateDto: UpdateSeedingGroupDto,
    existingGroup: SeedingGroup,
  ): Promise<void> {
    // Kiểm tra tên trùng lặp nếu có thay đổi tên
    if (updateDto.name && updateDto.name !== existingGroup.name) {
      const duplicateGroup = await this.seedingGroupEntityRepository.findOne({
        where: { userId, name: updateDto.name },
      });

      if (duplicateGroup) {
        throw new AppException(
          ErrorCode.BAD_REQUEST,
          'Tên seeding group đã tồn tại',
        );
      }
    }

    // Validate time range nếu có thay đổi
    const startTime = updateDto.startTime || existingGroup.startTime;
    const endTime = updateDto.endTime || existingGroup.endTime;

    if (startTime >= endTime) {
      throw new AppException(
        ErrorCode.BAD_REQUEST,
        'Giờ bắt đầu phải nhỏ hơn giờ kết thúc',
      );
    }
  }
}

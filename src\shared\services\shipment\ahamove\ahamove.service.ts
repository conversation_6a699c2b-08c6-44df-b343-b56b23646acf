import { Injectable, Logger } from '@nestjs/common';
import axios, { AxiosInstance, AxiosResponse, AxiosRequestConfig } from 'axios';
import {
  IAhamoveConfig,
  IAhamoveService,
  IAhamoveAuthRequest,
  IAhamoveAuthResponse,
  IAhamoveRefreshTokenRequest,
  IAhamoveEstimateRequest,
  IAhamoveEstimateResponse,
  IAhamoveCreateOrderRequest,
  IAhamoveCreateOrderResponse,
  IAhamoveOrderDetail,
  IAhamoveCity,
  IAhamoveServiceDetail,
  IAhamoveCancelOrderRequest,
  IAhamoveCancelOrderResponse,
  IAhamoveGetOrdersByTrackingRequest,
  IAhamoveGetOrdersByTrackingResponse,
  IAhamoveSharedLinkResponse,
  IAhamoveErrorResponse,
  AhamoveError
} from './ahamove.interface';
import {
  AHAMOVE_ENDPOINTS,
  AHAMOVE_DEFAULT_HEADERS,
  AHAMOVE_TIMEOUT,
  AHAMOVE_BASE_URLS,
  AHAMOVE_ERROR_CODES
} from './ahamove.constants';

/**
 * Optimized Ahamove Service for third-party API communication
 * Handles direct communication with Ahamove API with enhanced error handling,
 * token management, and performance optimizations
 */
@Injectable()
export class AhamoveService implements IAhamoveService {
  private readonly logger = new Logger(AhamoveService.name);
  private config: IAhamoveConfig;
  private httpClient: AxiosInstance;
  private currentToken?: string;
  private currentRefreshToken?: string;

  constructor() {
    this.initializeHttpClient();
  }

  // ==================== INITIALIZATION & CONFIGURATION ====================

  /**
   * Initialize HTTP client with optimized configuration and interceptors
   */
  private initializeHttpClient(): void {
    this.httpClient = axios.create({
      timeout: AHAMOVE_TIMEOUT,
      headers: { ...AHAMOVE_DEFAULT_HEADERS },
      maxRedirects: 3,
      validateStatus: (status) => status < 500, // Don't throw on 4xx errors
    });

    this.setupRequestInterceptor();
    this.setupResponseInterceptor();
  }

  /**
   * Setup request interceptor with enhanced logging and token management
   */
  private setupRequestInterceptor(): void {
    this.httpClient.interceptors.request.use(
      (config) => {
        this.logger.debug(`Ahamove API Request: ${config.method?.toUpperCase()} ${config.url}`);

        // Auto-inject token if available and not already set
        if (this.currentToken && !config.headers?.Authorization) {
          config.headers = config.headers as any || {};
          (config.headers as any).Authorization = `Bearer ${this.currentToken}`;
        }

        return config;
      },
      (error) => {
        this.logger.error('Ahamove API Request Error:', error);
        return Promise.reject(this.createAhamoveError('Request failed', 'REQUEST_ERROR', undefined, 'Request Interceptor', error));
      }
    );
  }

  /**
   * Setup response interceptor with automatic token refresh on 401 errors
   */
  private setupResponseInterceptor(): void {
    this.httpClient.interceptors.response.use(
      (response) => {
        this.logger.debug(`Ahamove API Response: ${response.status} ${response.config.url}`);
        return response;
      },
      async (error) => {
        const originalRequest = error.config;

        // Handle 401 errors with automatic token refresh
        if (error.response?.status === 401 && !originalRequest._retry && this.currentRefreshToken) {
          originalRequest._retry = true;

          try {
            const refreshResponse = await this.refreshToken(this.currentRefreshToken);
            this.setAuthToken(refreshResponse.token);
            originalRequest.headers.Authorization = `Bearer ${refreshResponse.token}`;
            return this.httpClient(originalRequest);
          } catch (refreshError) {
            this.logger.error('Token refresh failed:', refreshError);
            this.clearAuthToken();
          }
        }

        this.logger.error('Ahamove API Response Error:', error.response?.data || error.message);
        return Promise.reject(error);
      }
    );
  }

  /**
   * Set configuration for Ahamove service with validation
   */
  setConfig(config: IAhamoveConfig): void {
    if (!config.apiKey || !config.mobile || !config.baseUrl) {
      throw new AhamoveError('Invalid configuration: apiKey, mobile, and baseUrl are required', 'INVALID_CONFIG');
    }

    this.config = { ...config };
    this.httpClient.defaults.baseURL = config.baseUrl;
    this.httpClient.defaults.timeout = config.timeout || AHAMOVE_TIMEOUT;

    this.logger.log(`Ahamove service configured for ${config.isTestMode ? 'TEST' : 'PRODUCTION'} mode`);
  }

  /**
   * Set authorization token with enhanced token management
   */
  setAuthToken(token: string): void {
    if (!token) {
      throw new AhamoveError('Token cannot be empty', 'INVALID_TOKEN');
    }

    this.currentToken = token;
    this.httpClient.defaults.headers.common['Authorization'] = `Bearer ${token}`;
    this.logger.debug('Authorization token updated');
  }

  /**
   * Clear authorization token and refresh token
   */
  clearAuthToken(): void {
    this.currentToken = undefined;
    this.currentRefreshToken = undefined;
    delete this.httpClient.defaults.headers.common['Authorization'];
    this.logger.debug('Authorization tokens cleared');
  }

  // ==================== AUTHENTICATION METHODS ====================

  /**
   * Authenticate with Ahamove API with enhanced token management
   */
  async authenticate(mobile: string): Promise<IAhamoveAuthResponse> {
    this.validateConfig();

    if (!mobile) {
      throw new AhamoveError('Mobile number is required', 'MISSING_MOBILE');
    }

    const requestData: IAhamoveAuthRequest = {
      mobile,
      api_key: this.config.apiKey,
    };

    try {
      const response = await this.makeRequest<IAhamoveAuthResponse>(
        'POST',
        AHAMOVE_ENDPOINTS.AUTHENTICATE,
        requestData
      );

      // Store tokens for automatic management
      this.currentToken = response.token;
      this.currentRefreshToken = response.refresh_token;
      this.setAuthToken(response.token);

      this.logger.log('Authentication successful');
      return response;
    } catch (error) {
      throw this.handleError(error, 'Authentication failed', 'AUTHENTICATION_ERROR');
    }
  }

  /**
   * Refresh authentication token
   */
  async refreshToken(refreshToken: string): Promise<IAhamoveAuthResponse> {
    this.validateConfig();

    if (!refreshToken) {
      throw new AhamoveError('Refresh token is required', 'MISSING_REFRESH_TOKEN');
    }

    const requestData: IAhamoveRefreshTokenRequest = {
      refresh_token: refreshToken,
    };

    try {
      const response = await this.makeRequest<IAhamoveAuthResponse>(
        'POST',
        AHAMOVE_ENDPOINTS.AUTHENTICATE, // Using same endpoint as per API docs
        requestData
      );

      // Update stored tokens
      this.currentToken = response.token;
      this.currentRefreshToken = response.refresh_token;
      this.setAuthToken(response.token);

      this.logger.log('Token refresh successful');
      return response;
    } catch (error) {
      throw this.handleError(error, 'Token refresh failed', 'TOKEN_REFRESH_ERROR');
    }
  }

  // ==================== MASTER DATA METHODS ====================

  /**
   * Get list of cities where Ahamove operates
   */
  async getCities(token?: string): Promise<IAhamoveCity[]> {
    this.validateConfig();

    try {
      const url = this.buildUrlWithParams(AHAMOVE_ENDPOINTS.GET_CITIES, { country_id: 'VN' });
      const config = token ? { headers: { Authorization: `Bearer ${token}` } } : {};

      const response = await this.makeRequest<IAhamoveCity[]>('GET', url, undefined, config);

      this.logger.debug(`Retrieved ${response.length} cities`);
      return response;
    } catch (error) {
      throw this.handleError(error, 'Failed to get cities', 'GET_CITIES_ERROR');
    }
  }

  /**
   * Get list of available services with optimized parameter handling
   */
  async getServices(token: string, queryParams?: {
    city_id?: string;
    lat?: number;
    lng?: number;
    delivery_type?: string;
  }): Promise<IAhamoveServiceDetail[]> {
    this.validateConfig();
    this.validateToken(token);

    try {
      const url = this.buildUrlWithParams(AHAMOVE_ENDPOINTS.GET_SERVICES, queryParams);
      const response = await this.makeRequest<IAhamoveServiceDetail[]>(
        'GET',
        url,
        undefined,
        { headers: { Authorization: `Bearer ${token}` } }
      );

      this.logger.debug(`Retrieved ${response.length} services`);
      return response;
    } catch (error) {
      throw this.handleError(error, 'Failed to get services', 'GET_SERVICES_ERROR');
    }
  }

  // ==================== ORDER MANAGEMENT METHODS ====================

  /**
   * Estimate order fee with enhanced validation
   */
  async estimateOrderFee(request: IAhamoveEstimateRequest, token: string): Promise<IAhamoveEstimateResponse[]> {
    this.validateConfig();
    this.validateToken(token);
    this.validateOrderRequest(request);

    try {
      const response = await this.makeRequest<IAhamoveEstimateResponse[]>(
        'POST',
        AHAMOVE_ENDPOINTS.ESTIMATE_FEE,
        request,
        { headers: { Authorization: `Bearer ${token}` } }
      );

      this.logger.debug(`Estimated fee for ${response.length} service options`);
      return response;
    } catch (error) {
      throw this.handleError(error, 'Failed to estimate order fee', 'ESTIMATE_FEE_ERROR');
    }
  }

  /**
   * Create order with comprehensive validation and logging
   */
  async createOrder(request: IAhamoveCreateOrderRequest, token: string): Promise<IAhamoveCreateOrderResponse> {
    this.validateConfig();
    this.validateToken(token);
    this.validateOrderRequest(request);

    try {
      const response = await this.makeRequest<IAhamoveCreateOrderResponse>(
        'POST',
        AHAMOVE_ENDPOINTS.CREATE_ORDER,
        request,
        { headers: { Authorization: `Bearer ${token}` } }
      );

      this.logger.log(`Order created successfully: ${response.order_id}`);
      return response;
    } catch (error) {
      throw this.handleError(error, 'Failed to create order', 'CREATE_ORDER_ERROR');
    }
  }

  /**
   * Get order details with enhanced validation
   */
  async getOrder(orderId: string, token: string): Promise<IAhamoveOrderDetail> {
    this.validateConfig();
    this.validateToken(token);

    if (!orderId?.trim()) {
      throw new AhamoveError('Order ID is required', 'MISSING_ORDER_ID');
    }

    try {
      const response = await this.makeRequest<IAhamoveOrderDetail>(
        'GET',
        `${AHAMOVE_ENDPOINTS.GET_ORDER}/${orderId}`,
        undefined,
        { headers: { Authorization: `Bearer ${token}` } }
      );

      this.logger.debug(`Retrieved order details: ${orderId}`);
      return response;
    } catch (error) {
      throw this.handleError(error, `Failed to get order ${orderId}`, 'GET_ORDER_ERROR');
    }
  }

  /**
   * Cancel order with proper authorization header (FIXED)
   */
  async cancelOrder(request: IAhamoveCancelOrderRequest, token: string): Promise<IAhamoveCancelOrderResponse> {
    this.validateConfig();
    this.validateToken(token);

    if (!request.order_id?.trim()) {
      throw new AhamoveError('Order ID is required for cancellation', 'MISSING_ORDER_ID');
    }

    try {
      const response = await this.makeRequest<IAhamoveCancelOrderResponse>(
        'PUT',
        `${AHAMOVE_ENDPOINTS.CANCEL_ORDER}/${request.order_id}/cancel`,
        { comment: request.comment },
        { headers: { Authorization: `Bearer ${token}` } }
      );

      this.logger.log(`Order cancelled successfully: ${request.order_id}`);
      return response;
    } catch (error) {
      throw this.handleError(error, `Failed to cancel order ${request.order_id}`, 'CANCEL_ORDER_ERROR');
    }
  }

  // ==================== TRACKING METHODS ====================

  /**
   * Get orders by tracking number with proper authorization header (FIXED)
   */
  async getOrdersByTracking(request: IAhamoveGetOrdersByTrackingRequest, token: string): Promise<IAhamoveGetOrdersByTrackingResponse> {
    this.validateConfig();
    this.validateToken(token);

    if (!request.tracking_number?.trim()) {
      throw new AhamoveError('Tracking number is required', 'MISSING_TRACKING_NUMBER');
    }

    try {
      const queryParams = {
        ...(request.from_time && { from_time: request.from_time.toString() }),
        ...(request.to_time && { to_time: request.to_time.toString() })
      };

      const url = this.buildUrlWithParams(
        `${AHAMOVE_ENDPOINTS.GET_ORDERS_BY_TRACKING}/${encodeURIComponent(request.tracking_number)}`,
        queryParams
      );

      const response = await this.makeRequest<IAhamoveGetOrdersByTrackingResponse>(
        'GET',
        url,
        undefined,
        { headers: { Authorization: `Bearer ${token}` } }
      );

      this.logger.debug(`Retrieved ${response.total} orders for tracking: ${request.tracking_number}`);
      return response;
    } catch (error) {
      throw this.handleError(error, `Failed to get orders by tracking ${request.tracking_number}`, 'GET_ORDERS_BY_TRACKING_ERROR');
    }
  }

  /**
   * Get shared link for order tracking with optional token
   */
  async getSharedLink(orderId: string, token?: string): Promise<IAhamoveSharedLinkResponse> {
    this.validateConfig();

    if (!orderId?.trim()) {
      throw new AhamoveError('Order ID is required', 'MISSING_ORDER_ID');
    }

    try {
      const config = token ? { headers: { Authorization: `Bearer ${token}` } } : {};

      const response = await this.makeRequest<IAhamoveSharedLinkResponse>(
        'GET',
        `${AHAMOVE_ENDPOINTS.GET_SHARED_LINK}/${orderId}`,
        undefined,
        config
      );

      this.logger.debug(`Retrieved shared link for order: ${orderId}`);
      return response;
    } catch (error) {
      throw this.handleError(error, `Failed to get shared link for order ${orderId}`, 'GET_SHARED_LINK_ERROR');
    }
  }

  // ==================== HELPER METHODS ====================

  /**
   * Generic request method with enhanced error handling and retry logic
   */
  private async makeRequest<T>(
    method: 'GET' | 'POST' | 'PUT' | 'DELETE',
    url: string,
    data?: any,
    config?: AxiosRequestConfig
  ): Promise<T> {
    try {
      const response: AxiosResponse<T> = await this.httpClient.request({
        method,
        url,
        data,
        ...config,
      });

      return response.data;
    } catch (error) {
      // Let the response interceptor handle 401 errors
      throw error;
    }
  }

  /**
   * Build URL with query parameters efficiently
   */
  private buildUrlWithParams(baseUrl: string, params?: Record<string, any>): string {
    if (!params || Object.keys(params).length === 0) {
      return baseUrl;
    }

    const searchParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        searchParams.append(key, value.toString());
      }
    });

    const queryString = searchParams.toString();
    return queryString ? `${baseUrl}?${queryString}` : baseUrl;
  }

  /**
   * Validate configuration
   */
  private validateConfig(): void {
    if (!this.config) {
      throw new AhamoveError('Ahamove config not set. Call setConfig() first.', 'CONFIG_NOT_SET');
    }
  }

  /**
   * Validate token
   */
  private validateToken(token: string): void {
    if (!token?.trim()) {
      throw new AhamoveError('Authentication token is required', 'MISSING_TOKEN');
    }
  }

  /**
   * Validate order request structure
   */
  private validateOrderRequest(request: IAhamoveEstimateRequest | IAhamoveCreateOrderRequest): void {
    if (!request) {
      throw new AhamoveError('Order request is required', 'MISSING_REQUEST');
    }

    if (!request.path || !Array.isArray(request.path) || request.path.length < 2) {
      throw new AhamoveError('Order must have at least 2 path points (pickup and delivery)', 'INVALID_PATH');
    }

    if (!request.payment_method) {
      throw new AhamoveError('Payment method is required', 'MISSING_PAYMENT_METHOD');
    }

    // Validate path structure
    request.path.forEach((path, index) => {
      if (!path.address?.trim()) {
        throw new AhamoveError(`Path[${index}] address is required`, 'MISSING_ADDRESS');
      }
      if (!path.name?.trim()) {
        throw new AhamoveError(`Path[${index}] name is required`, 'MISSING_NAME');
      }
      if (!path.mobile?.trim()) {
        throw new AhamoveError(`Path[${index}] mobile is required`, 'MISSING_MOBILE');
      }
    });
  }

  /**
   * Create structured Ahamove error
   */
  private createAhamoveError(
    message: string,
    code: string,
    status?: number,
    context?: string,
    originalError?: any
  ): AhamoveError {
    return new AhamoveError(message, code, status, context, originalError);
  }

  /**
   * Enhanced error handling with structured exceptions
   */
  private handleError(error: any, context: string, defaultCode: string = 'UNKNOWN_ERROR'): AhamoveError {
    const errorMessage = error.response?.data?.message || error.message || 'Unknown error occurred';
    const errorCode = error.response?.data?.code || defaultCode;
    const status = error.response?.status;

    // Map common HTTP status codes to meaningful error codes
    const mappedCode = this.mapHttpStatusToErrorCode(status) || errorCode;

    this.logger.error(`${context}: ${errorMessage}`, {
      code: mappedCode,
      status,
      originalCode: errorCode,
      data: error.response?.data,
      url: error.config?.url,
      method: error.config?.method
    });

    return this.createAhamoveError(errorMessage, mappedCode, status, context, error);
  }

  /**
   * Map HTTP status codes to meaningful error codes
   */
  private mapHttpStatusToErrorCode(status?: number): string | undefined {
    const statusCodeMap: Record<number, string> = {
      400: AHAMOVE_ERROR_CODES.INVALID_PHONE_NUMBER,
      401: AHAMOVE_ERROR_CODES.NOT_AUTHORIZED,
      404: AHAMOVE_ERROR_CODES.USER_NOT_FOUND,
      500: AHAMOVE_ERROR_CODES.INTERNAL_SERVER_ERROR,
      503: AHAMOVE_ERROR_CODES.SERVICE_UNAVAILABLE,
    };

    return status ? statusCodeMap[status] : undefined;
  }
}

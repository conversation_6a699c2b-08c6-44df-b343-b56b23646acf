import {
  registerDecorator,
  ValidationOptions,
  ValidatorConstraint,
  ValidatorConstraintInterface,
  ValidationArguments,
} from 'class-validator';
import { ConvertConfigItemDto } from '../convert-config-item.dto';

/**
 * Validator constraint để kiểm tra tên field không trùng lặp
 */
@ValidatorConstraint({ name: 'uniqueFieldNames', async: false })
export class UniqueFieldNamesConstraint implements ValidatorConstraintInterface {
  validate(convertConfigItems: ConvertConfigItemDto[], args: ValidationArguments) {
    if (!Array.isArray(convertConfigItems)) {
      return false;
    }

    const fieldNames: string[] = [];
    const duplicates: string[] = [];

    for (const item of convertConfigItems) {
      if (!item || !item.name || typeof item.name !== 'string') {
        continue; // Sẽ được validate bởi decorator khác
      }

      const fieldName = item.name.trim().toLowerCase();
      
      if (fieldNames.includes(fieldName)) {
        if (!duplicates.includes(fieldName)) {
          duplicates.push(fieldName);
        }
      } else {
        fieldNames.push(fieldName);
      }
    }

    return duplicates.length === 0;
  }

  defaultMessage(args: ValidationArguments) {
    const convertConfigItems = args.value as ConvertConfigItemDto[];
    
    if (!Array.isArray(convertConfigItems)) {
      return 'Danh sách cấu hình conversion phải là một array';
    }

    const fieldNames: string[] = [];
    const duplicates: string[] = [];

    for (const item of convertConfigItems) {
      if (!item || !item.name || typeof item.name !== 'string') {
        continue;
      }

      const fieldName = item.name.trim().toLowerCase();
      
      if (fieldNames.includes(fieldName)) {
        if (!duplicates.includes(fieldName)) {
          duplicates.push(fieldName);
        }
      } else {
        fieldNames.push(fieldName);
      }
    }

    if (duplicates.length > 0) {
      return `Tên field bị trùng lặp: ${duplicates.join(', ')}`;
    }

    return 'Tên field không được trùng lặp';
  }
}

/**
 * Decorator để validate tên field không trùng lặp
 * @param validationOptions Tùy chọn validation
 */
export function UniqueFieldNames(validationOptions?: ValidationOptions) {
  return function (object: object, propertyName: string) {
    registerDecorator({
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      constraints: [],
      validator: UniqueFieldNamesConstraint,
    });
  };
}

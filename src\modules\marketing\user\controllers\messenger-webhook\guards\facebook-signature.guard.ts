import {
  Injectable,
  CanActivate,
  ExecutionContext,
  UnauthorizedException,
  Logger,
} from '@nestjs/common';
import { Request } from 'express';
import { createHmac, timingSafeEqual } from 'crypto';
import { ConfigService } from '@/config';

/**
 * Guard để xác thực chữ ký webhook từ Facebook Messenger
 * 
 * Facebook gửi webhook với header X-Hub-Signature-256 chứa HMAC SHA256
 * của request body sử dụng app secret làm key.
 * 
 * Điều này đảm bảo:
 * 1. Request thực sự đến từ Facebook
 * 2. Payload không bị thay đổi
 * 3. Bảo vệ khỏi replay attacks
 */
@Injectable()
export class FacebookSignatureGuard implements CanActivate {
  private readonly logger = new Logger(FacebookSignatureGuard.name);
  private readonly webhookSecret: string;

  constructor(private readonly configService: ConfigService) {
    this.webhookSecret = this.configService.facebook.webhookSecret;
  }

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest<Request>();
    
    try {
      // Get raw body - reconstruct from parsed body if needed
      const rawBody = this.getRawBody(request);

      // Lấy signature từ header
      const signature = this.extractSignature(request);
      if (!signature) {
        this.logger.warn('Facebook webhook signature header missing');
        throw new UnauthorizedException('Webhook signature required');
      }

      // Xác thực signature
      const isValid = this.verifySignature(rawBody, signature);
      
      if (!isValid) {
        this.logger.warn('Facebook webhook signature verification failed', {
          receivedSignature: signature.substring(0, 20) + '...', // Log partial signature for debugging
          requestIP: request.ip,
          userAgent: request.get('User-Agent'),
        });
        throw new UnauthorizedException('Invalid webhook signature');
      }

      this.logger.debug('Facebook webhook signature verified successfully');
      return true;

    } catch (error) {
      if (error instanceof UnauthorizedException) {
        throw error;
      }
      
      this.logger.error('Error during webhook signature verification:', error.message);
      throw new UnauthorizedException('Webhook verification failed');
    }
  }

  /**
   * Get raw body for signature verification
   */
  private getRawBody(request: Request): Buffer {
    // Check if rawBody already exists (from middleware)
    if ((request as any).rawBody && Buffer.isBuffer((request as any).rawBody)) {
      return (request as any).rawBody;
    }

    // Reconstruct from parsed body
    if (request.body) {
      try {
        const bodyString = typeof request.body === 'string' 
          ? request.body 
          : JSON.stringify(request.body);
        return Buffer.from(bodyString, 'utf8');
      } catch (error) {
        this.logger.error('Failed to reconstruct raw body:', error.message);
        throw new UnauthorizedException('Invalid request body format');
      }
    }

    throw new UnauthorizedException('No request body found');
  }

  /**
   * Trích xuất signature từ header X-Hub-Signature-256
   */
  private extractSignature(request: Request): string | null {
    const signatureHeader = request.get('X-Hub-Signature-256');
    
    if (!signatureHeader) {
      return null;
    }

    // Facebook gửi signature với format: "sha256=<hash>"
    if (!signatureHeader.startsWith('sha256=')) {
      this.logger.warn('Invalid signature format. Expected sha256= prefix');
      return null;
    }

    return signatureHeader.substring(7); // Bỏ prefix "sha256="
  }

  /**
   * Xác thực signature sử dụng HMAC SHA256
   */
  private verifySignature(body: Buffer, receivedSignature: string): boolean {
    try {
      // Tính toán signature từ raw body
      const computedSignature = createHmac('sha256', this.webhookSecret)
        .update(body)
        .digest('hex');

      // So sánh signatures sử dụng timing-safe comparison để tránh timing attacks
      const receivedBuffer = Buffer.from(receivedSignature, 'hex');
      const computedBuffer = Buffer.from(computedSignature, 'hex');

      // Kiểm tra độ dài trước để tránh timing attacks
      if (receivedBuffer.length !== computedBuffer.length) {
        return false;
      }

      return timingSafeEqual(receivedBuffer, computedBuffer);

    } catch (error) {
      this.logger.error('Error computing webhook signature:', error.message);
      return false;
    }
  }
}
import { Entity, Column, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn, Index, ManyToOne, JoinColumn } from 'typeorm';
import { GenericSession } from './generic-session.entity';

/**
 * Entity đại diện cho bảng generic_session_widgets trong cơ sở dữ liệu
 * Bảng lưu trữ thông tin widgets trong mỗi session của trang generic
 */
@Entity('generic_session_widgets')
@Index(['sessionId'])
@Index(['widgetId'])
@Index(['widgetType'])
@Index(['sessionId', 'widgetId'], { unique: true })
export class GenericSessionWidget {
  /**
   * ID duy nhất của widget record, dạng UUID
   */
  @PrimaryGeneratedColumn('uuid')
  id: string;

  /**
   * Session ID tham chiếu đến GenericSession
   */
  @Column({ name: 'session_id', length: 255 })
  sessionId: string;

  /**
   * Widget ID duy nhất trong session
   */
  @Column({ name: 'widget_id', length: 255 })
  widgetId: string;

  /**
   * Loại widget (data-count, marketing-overview, etc.)
   */
  @Column({ name: 'widget_type', length: 100 })
  widgetType: string;

  /**
   * Dữ liệu cấu hình widget dạng JSON
   */
  @Column({ name: 'widget_data', type: 'jsonb' })
  widgetData: Record<string, any>;

  /**
   * Vị trí và kích thước widget trong grid layout
   * Format: { x: number, y: number, w: number, h: number, minW?: number, minH?: number, maxW?: number, maxH?: number }
   */
  @Column({ type: 'jsonb' })
  position: {
    x: number;
    y: number;
    w: number;
    h: number;
    minW?: number;
    minH?: number;
    maxW?: number;
    maxH?: number;
  };

  /**
   * Thứ tự hiển thị widget (z-index)
   */
  @Column({ name: 'display_order', type: 'int', default: 0 })
  displayOrder: number;

  /**
   * Trạng thái hiển thị widget
   */
  @Column({ name: 'is_visible', type: 'boolean', default: true })
  isVisible: boolean;

  /**
   * Thời gian tạo widget
   */
  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  /**
   * Thời gian cập nhật widget
   */
  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  /**
   * Metadata bổ sung cho widget
   */
  @Column({ type: 'jsonb', nullable: true })
  metadata?: Record<string, any>;

  /**
   * Relation đến GenericSession (không sử dụng TypeORM relation mapping theo user preference)
   * Chỉ để reference, không dùng @ManyToOne decorator
   */
  // @ManyToOne(() => GenericSession)
  // @JoinColumn({ name: 'session_id', referencedColumnName: 'sessionId' })
  // session: GenericSession;
}

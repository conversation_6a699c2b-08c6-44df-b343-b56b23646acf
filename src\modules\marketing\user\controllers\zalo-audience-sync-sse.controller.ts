import { <PERSON>, Get, Param, Sse, UseGuards, Logger } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiParam } from '@nestjs/swagger';
import { Observable, fromEvent, map, catchError, of, EMPTY } from 'rxjs';
import { JwtUserGuard } from '@/modules/auth/guards';
import { CurrentUser } from '@modules/auth/decorators/current-user.decorator';
import { JwtPayload } from '@/modules/auth/guards/jwt.util';
import { RedisService } from '@shared/services/redis.service';
import { SWAGGER_API_TAGS } from '@/common/swagger';

/**
 * Interface cho SSE message
 */
interface SseMessage {
  id: string;
  event: string;
  data: string;
  retry?: number;
}

/**
 * Controller xử lý SSE cho Zalo audience sync progress
 */
@ApiTags(SWAGGER_API_TAGS.ZALO_AUDIENCE_SYNC_SSE)
@Controller('marketing/user/zalo-audience-sync/sse')
// @UseGuards(JwtUserGuard)
// @ApiBearerAuth('JWT-auth')
export class ZaloAudienceSyncSseController {
  private readonly logger = new Logger(ZaloAudienceSyncSseController.name);

  constructor(
    private readonly redisService: RedisService,
  ) {}

  /**
   * SSE endpoint để theo dõi tiến độ đồng bộ Zalo audience
   */
  @Get(':syncId/progress')
  @ApiOperation({
    summary: 'Stream tiến độ đồng bộ Zalo audience qua SSE',
    description: `Endpoint SSE để theo dõi tiến độ đồng bộ Zalo audience real-time:
    - Sử dụng syncId từ API sync để kết nối
    - Stream các event: started, progress, completed, failed
    - Tự động đóng kết nối khi hoàn thành hoặc lỗi
    - Retry connection nếu bị ngắt
    - Chỉ user tạo job mới có thể theo dõi`
  })
  @ApiParam({
    name: 'syncId',
    description: 'ID của job đồng bộ để theo dõi',
    example: 'sync_zalo_users_1703123456789_abc123def'
  })
  @ApiResponse({
    status: 200,
    description: 'SSE stream đang hoạt động',
    content: {
      'text/event-stream': {
        schema: {
          type: 'string',
          example: `event: sync_progress
data: {"status":"started","message":"Bắt đầu đồng bộ người dùng Zalo vào audience","progress":0,"timestamp":1703123456789}

event: sync_progress  
data: {"status":"progress","message":"Đã xử lý 10/50 người dùng","progress":20,"timestamp":1703123456890}

event: sync_progress
data: {"status":"completed","message":"Đồng bộ hoàn thành thành công","progress":100,"result":{"processedCount":50,"newAudienceCreated":30,"existingAudienceUpdated":20},"timestamp":1703123457000}`
        }
      }
    }
  })
  @ApiResponse({ status: 401, description: 'Chưa xác thực' })
  @ApiResponse({ status: 404, description: 'Không tìm thấy sync job' })
  @Sse()
  streamSyncProgress(
    @CurrentUser() user: JwtPayload,
    @Param('syncId') syncId: string,
  ): Observable<SseMessage> {
    this.logger.log(`Starting SSE stream for syncId: ${syncId}, userId: ${user.id}`);

    const streamKey = `zalo_audience_sync:${syncId}`;
    
    return new Observable<SseMessage>((observer) => {
      let isCompleted = false;
      const consumerGroup = `consumer_${user.id}_${Date.now()}`;
      const consumerName = `consumer_${Math.random().toString(36).substr(2, 9)}`;

      // Tạo consumer group nếu chưa có
      this.redisService.getClient().xgroup('CREATE', streamKey, consumerGroup, '0', 'MKSTREAM')
        .catch(() => {
          // Group đã tồn tại, bỏ qua lỗi
        });

      const readMessages = async () => {
        try {
          while (!isCompleted) {
            // Đọc messages từ stream
            const messages = await this.redisService.getClient().xreadgroup(
              'GROUP', consumerGroup, consumerName,
              'COUNT', '10',
              'BLOCK', '1000', // Block 1 giây
              'STREAMS', streamKey, '>'
            );

            if (messages && messages.length > 0) {
              for (const [stream, streamMessages] of messages as any[]) {
                for (const [messageId, fields] of streamMessages as any[]) {
                  try {
                    const eventData = JSON.parse((fields as string[])[3]); // fields[3] là 'data' value
                    
                    observer.next({
                      id: messageId,
                      event: 'sync_progress',
                      data: JSON.stringify(eventData),
                      retry: 5000,
                    });

                    // Acknowledge message
                    await this.redisService.getClient().xack(streamKey, consumerGroup, messageId as string);

                    // Kiểm tra nếu đã hoàn thành hoặc lỗi
                    if (eventData.status === 'completed' || eventData.status === 'failed') {
                      isCompleted = true;
                      observer.complete();
                      break;
                    }
                  } catch (parseError) {
                    this.logger.error(`Error parsing message ${messageId}: ${parseError.message}`);
                  }
                }
                if (isCompleted) break;
              }
            }
          }
        } catch (error) {
          this.logger.error(`Error reading from stream ${streamKey}: ${error.message}`);
          observer.error(error);
        }
      };

      // Bắt đầu đọc messages
      readMessages();

      // Cleanup khi connection đóng
      return () => {
        isCompleted = true;
        this.logger.log(`SSE connection closed for syncId: ${syncId}, userId: ${user.id}`);
        
        // Cleanup consumer group (optional)
        this.redisService.getClient().xgroup('DELCONSUMER', streamKey, consumerGroup, consumerName)
          .catch(() => {
            // Ignore errors
          });
      };
    }).pipe(
      catchError((error) => {
        this.logger.error(`SSE error for syncId ${syncId}: ${error.message}`);
        return of({
          id: Date.now().toString(),
          event: 'error',
          data: JSON.stringify({
            status: 'error',
            message: 'Lỗi kết nối SSE',
            error: error.message,
            timestamp: Date.now(),
          }),
        });
      })
    );
  }

  /**
   * Endpoint để kiểm tra trạng thái sync job
   */
  @Get(':syncId/status')
  @ApiOperation({
    summary: 'Kiểm tra trạng thái sync job',
    description: 'Endpoint để kiểm tra trạng thái hiện tại của sync job'
  })
  @ApiParam({
    name: 'syncId',
    description: 'ID của job đồng bộ',
    example: 'sync_zalo_users_1703123456789_abc123def'
  })
  async getSyncStatus(
    @CurrentUser() user: JwtPayload,
    @Param('syncId') syncId: string,
  ) {
    const streamKey = `zalo_audience_sync:${syncId}`;
    
    try {
      // Lấy message cuối cùng từ stream
      const messages = await this.redisService.getClient().xrevrange(streamKey, '+', '-', 'COUNT', '1');
      
      if (!messages || (messages as any[]).length === 0) {
        return {
          syncId,
          status: 'not_found',
          message: 'Không tìm thấy thông tin sync job',
          timestamp: Date.now(),
        };
      }

      const [messageId, fields] = (messages as any[])[0];

      // fields là array dạng [key1, value1, key2, value2, ...]
      // Tìm index của 'event' key và lấy value tiếp theo
      const fieldArray = fields as string[];
      const eventIndex = fieldArray.indexOf('event');

      if (eventIndex === -1 || eventIndex + 1 >= fieldArray.length) {
        return {
          syncId,
          status: 'error',
          message: 'Dữ liệu sync job không hợp lệ',
          timestamp: Date.now(),
        };
      }

      const eventData = JSON.parse(fieldArray[eventIndex + 1]);

      return {
        syncId,
        messageId,
        ...eventData,
      };
    } catch (error) {
      this.logger.error(`Error getting sync status for ${syncId}: ${error.message}`);
      return {
        syncId,
        status: 'error',
        message: 'Lỗi khi lấy trạng thái sync job',
        error: error.message,
        timestamp: Date.now(),
      };
    }
  }
}

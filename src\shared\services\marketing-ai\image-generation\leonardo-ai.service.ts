import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AxiosRequestConfig } from 'axios';
import { BaseMarketingAiServiceImpl } from '../base-marketing-ai.service';
import {
  ImageGenerationOptions,
  ImageGenerationResult,
  ImageGenerationService,
  ImageSize,
  ImageStyle,
  MarketingAiResponse,
} from '../interfaces';

/**
 * Leonardo.AI service for image generation
 */
@Injectable()
export class LeonardoAiService extends BaseMarketingAiServiceImpl implements ImageGenerationService {
  readonly serviceName = 'Leonardo AI';
  protected readonly baseUrl = 'https://cloud.leonardo.ai/api/rest/v1';
  protected readonly apiKey: string | undefined;

  constructor(private readonly configService: ConfigService) {
    super(LeonardoAiService.name);
    this.apiKey = this.configService.get<string>('LEONARDO_API_KEY');

    if (!this.apiKey) {
      this.logger.warn('LEONARDO_API_KEY is not defined in environment variables');
    }
  }

  /**
   * Test the connection to the Leonardo.AI API
   * @returns A promise that resolves to a boolean indicating if the connection was successful
   */
  async testConnection(): Promise<boolean> {
    try {
      const url = `${this.baseUrl}/me`;
      const config = this.createRequestConfig();

      const response = await this.axiosInstance.get(url, config);
      return response.status === 200;
    } catch (error) {
      this.logger.error(`Connection test failed: ${error.message}`, error.stack);
      return false;
    }
  }

  /**
   * Create a request configuration with authentication
   * @param config Additional request configuration
   * @returns Request configuration with authentication
   */
  protected createRequestConfig(config?: AxiosRequestConfig): AxiosRequestConfig {
    if (!this.apiKey) {
      throw new Error('Leonardo AI API key is not defined');
    }

    return {
      ...config,
      headers: {
        ...config?.headers,
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    };
  }

  /**
   * Map size option to Leonardo.AI dimensions
   * @param size Size option
   * @param width Custom width (if size is CUSTOM)
   * @param height Custom height (if size is CUSTOM)
   * @returns Object with width and height
   */
  private mapSizeToLeonardoSize(
    size: ImageSize = ImageSize.MEDIUM,
    width?: number,
    height?: number,
  ): { width: number; height: number } {
    if (size === ImageSize.CUSTOM && width && height) {
      // Leonardo.AI supports specific dimension ratios
      const supportedDimensions = [
        { width: 512, height: 512 },
        { width: 768, height: 512 },
        { width: 512, height: 768 },
        { width: 1024, height: 576 },
        { width: 576, height: 1024 },
        { width: 1024, height: 1024 },
      ];

      // Find the closest supported dimensions
      const closestDimension = supportedDimensions.reduce((prev, curr) => {
        const prevRatio = Math.abs((prev.width / prev.height) - (width / height));
        const currRatio = Math.abs((curr.width / curr.height) - (width / height));
        return prevRatio < currRatio ? prev : curr;
      });

      return closestDimension;
    }

    switch (size) {
      case ImageSize.SMALL:
        return { width: 512, height: 512 };
      case ImageSize.MEDIUM:
        return { width: 768, height: 768 };
      case ImageSize.LARGE:
        return { width: 1024, height: 1024 };
      default:
        return { width: 768, height: 768 };
    }
  }

  /**
   * Map style option to Leonardo.AI model ID
   * @param style Style option
   * @returns Leonardo.AI model ID
   */
  private getModelIdForStyle(style?: ImageStyle): string {
    // Leonardo.AI has different models for different styles
    // These are example model IDs and may need to be updated
    switch (style) {
      case ImageStyle.REALISTIC:
        return 'e316348f-7773-490e-adcd-46757c738eb7'; // Realistic Vision
      case ImageStyle.ARTISTIC:
        return '6bef9f1b-29cb-40c7-b9df-32b51c1f67d3'; // Leonardo Creative
      case ImageStyle.CARTOON:
        return 'cd2b2a15-9760-4174-a5ff-4d2925057376'; // Leonardo Anime
      case ImageStyle.ABSTRACT:
        return '291be633-cb24-434f-898f-e9dd4c2e29b7'; // Leonardo Abstract
      case ImageStyle.PHOTOGRAPHIC:
        return 'e316348f-7773-490e-adcd-46757c738eb7'; // Realistic Vision
      default:
        return 'e316348f-7773-490e-adcd-46757c738eb7'; // Default to Realistic Vision
    }
  }

  /**
   * Generate images from a text prompt using Leonardo.AI
   * @param prompt Text prompt to generate images from
   * @param options Options for image generation
   * @returns A promise that resolves to a response containing the generated images
   */
  async generateImage(
    prompt: string,
    options?: ImageGenerationOptions,
  ): Promise<MarketingAiResponse<ImageGenerationResult>> {
    try {
      const { width, height } = this.mapSizeToLeonardoSize(
        options?.size,
        options?.width,
        options?.height,
      );

      const count = options?.count || 1;
      const modelId = this.getModelIdForStyle(options?.style);

      // Step 1: Create a generation
      const createUrl = `${this.baseUrl}/generations`;
      const createData = {
        prompt,
        modelId,
        width,
        height,
        num_images: count,
        negative_prompt: options?.negativePrompt || '',
        seed: options?.seed || 0,
        public: false,
        promptMagic: true,
      };

      const config = this.createRequestConfig({
        timeout: options?.timeout || 30000,
      });

      const createResponse = await this.axiosInstance.post(createUrl, createData, config);
      const generationId = createResponse.data.sdGenerationJob.generationId;

      // Step 2: Poll for generation results
      const maxAttempts = 30;
      let attempts = 0;
      let generationResult = null;

      while (attempts < maxAttempts) {
        attempts++;

        const statusUrl = `${this.baseUrl}/generations/${generationId}`;
        const statusResponse = await this.axiosInstance.get(statusUrl, config);

        const generation = statusResponse.data.generations_by_pk;

        if (generation.status === 'COMPLETE') {
          generationResult = generation;
          break;
        } else if (generation.status === 'FAILED') {
          throw new Error('Leonardo.AI generation failed');
        }

        // Wait before polling again
        await new Promise(resolve => setTimeout(resolve, 2000));
      }

      if (!generationResult) {
        throw new Error('Leonardo.AI generation timed out');
      }

      // Extract image URLs
      const imageUrls = (generationResult as any).generated_images?.map(
        (image: any) => image.url,
      ) || [];

      // Create result
      const result: ImageGenerationResult = {
        imageUrls,
        seeds: [(generationResult as any).seed],
        prompt,
      };

      return this.createSuccessResponse(result, generationResult);
    } catch (error) {
      return this.handleApiError<ImageGenerationResult>(error, 'Leonardo.AI image generation');
    }
  }

  /**
   * Edit an existing image using a text prompt
   * @param imageUrl URL of the image to edit
   * @param prompt Text prompt to guide the editing
   * @param options Options for image editing
   * @returns A promise that resolves to a response containing the edited image
   */
  async editImage(
     
    imageUrl: string,
    prompt: string,
    options?: ImageGenerationOptions,
  ): Promise<MarketingAiResponse<ImageGenerationResult>> {
    try {
      // Leonardo.AI supports image-to-image generation
      // We would need to upload the image first, then use it as a reference

      this.logger.warn('Image editing with Leonardo.AI requires uploading the image first. Generating a new image based on the prompt.');

      return this.generateImage(
        `Based on the reference image: ${prompt}`,
        options,
      );
    } catch (error) {
      return this.handleApiError<ImageGenerationResult>(error, 'Leonardo.AI image editing');
    }
  }

  /**
   * Generate image variations from an existing image
   * @param imageUrl URL of the image to create variations from
   * @param options Options for image variation generation
   * @returns A promise that resolves to a response containing the image variations
   */
  async generateImageVariations(
     
    imageUrl: string,
    options?: ImageGenerationOptions,
  ): Promise<MarketingAiResponse<ImageGenerationResult>> {
    try {
      // Leonardo.AI supports image variations
      // We would need to upload the image first, then use it as a reference

      this.logger.warn('Image variations with Leonardo.AI requires uploading the image first. Generating a new image with a generic prompt.');

      return this.generateImage(
        'Create a variation of the reference image',
        options,
      );
    } catch (error) {
      return this.handleApiError<ImageGenerationResult>(error, 'Leonardo.AI image variations');
    }
  }
}

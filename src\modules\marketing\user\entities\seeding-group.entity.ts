import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  OneToMany,
  JoinColumn,
  Index,
} from 'typeorm';
import { User } from '@modules/user/entities';

/**
 * Enum cho trạng thái Seeding Group
 */
export enum SeedingGroupStatus {
  DRAFT = 'draft',
  ACTIVE = 'active',
  PAUSED = 'paused',
  STOPPED = 'stopped',
  COMPLETED = 'completed',
}

/**
 * Enum cho trạng thái Seeding Account
 */
export enum SeedingAccountStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  ERROR = 'error',
  BANNED = 'banned',
}

/**
 * Entity cho Seeding Group
 */
@Entity('seeding_groups')
@Index(['userId'])
@Index(['status'])
@Index(['oaAccountId'])
@Index(['groupId'], { unique: true }) // groupId có quan hệ 1-1 với SeedingGroup
export class SeedingGroup {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'user_id', type: 'int' })
  userId: number;

  @Column({ name: 'name', type: 'varchar', length: 255 })
  name: string;

  @Column({ name: 'description', type: 'text', nullable: true })
  description?: string;

  @Column({ name: 'oa_account_id', type: 'varchar', length: 255 })
  oaAccountId: string;

  @Column({ name: 'oa_agent_id', type: 'varchar', length: 255 })
  oaAgentId: string;

  @Column({ name: 'group_id', type: 'varchar', length: 255 })
  groupId: string;

  @Column({
    name: 'status',
    type: 'enum',
    enum: SeedingGroupStatus,
    default: SeedingGroupStatus.DRAFT,
  })
  status: SeedingGroupStatus;

  // Cấu hình thời gian hoạt động
  @Column({ name: 'start_time', type: 'varchar', length: 10 })
  startTime: string; // Format: "HH:mm"

  @Column({ name: 'end_time', type: 'varchar', length: 10 })
  endTime: string; // Format: "HH:mm"

  @Column({ name: 'end_date', type: 'date', nullable: true })
  endDate?: string; // Format: "YYYY-MM-DD"

  // Cấu hình tần xuất hoạt động
  @Column({ name: 'interval_minutes', type: 'int', default: 30 })
  intervalMinutes: number;

  @Column({ name: 'max_per_day', type: 'int', default: 20 })
  maxPerDay: number;

  @Column({ name: 'randomize', type: 'boolean', default: false })
  randomize: boolean;

  // Thống kê
  @Column({ name: 'total_accounts', type: 'int', default: 0 })
  totalAccounts: number;

  @Column({ name: 'active_accounts', type: 'int', default: 0 })
  activeAccounts: number;

  @Column({ name: 'total_messages_sent', type: 'int', default: 0 })
  totalMessagesSent: number;

  @Column({ name: 'last_activity_at', type: 'bigint', nullable: true })
  lastActivityAt?: number;

  @Column({ name: 'created_at', type: 'bigint' })
  createdAt: number;

  @Column({ name: 'updated_at', type: 'bigint' })
  updatedAt: number;

  // Relations
  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'user_id' })
  user: User;

  @OneToMany(() => SeedingGroupAccount, (account) => account.seedingGroup)
  accounts: SeedingGroupAccount[];
}

/**
 * Entity cho Seeding Group Account
 */
@Entity('seeding_group_accounts')
@Index(['seedingGroupId'])
@Index(['personalAccountId'])
@Index(['status'])
export class SeedingGroupAccount {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'seeding_group_id', type: 'uuid' })
  seedingGroupId: string;

  @Column({ name: 'personal_account_id', type: 'varchar', length: 255 })
  personalAccountId: string;

  @Column({ name: 'personal_account_name', type: 'varchar', length: 255 })
  personalAccountName: string;

  @Column({ name: 'agent_id', type: 'varchar', length: 255 })
  agentId: string;

  @Column({ name: 'agent_name', type: 'varchar', length: 255 })
  agentName: string;

  @Column({
    name: 'status',
    type: 'enum',
    enum: SeedingAccountStatus,
    default: SeedingAccountStatus.ACTIVE,
  })
  status: SeedingAccountStatus;

  @Column({ name: 'messages_sent_today', type: 'int', default: 0 })
  messagesSentToday: number;

  @Column({ name: 'total_messages_sent', type: 'int', default: 0 })
  totalMessagesSent: number;

  @Column({ name: 'last_message_at', type: 'bigint', nullable: true })
  lastMessageAt?: number;

  @Column({ name: 'last_active_at', type: 'bigint', nullable: true })
  lastActiveAt?: number;

  @Column({ name: 'error_count', type: 'int', default: 0 })
  errorCount: number;

  @Column({ name: 'last_error', type: 'text', nullable: true })
  lastError?: string;

  @Column({ name: 'created_at', type: 'bigint' })
  createdAt: number;

  @Column({ name: 'updated_at', type: 'bigint' })
  updatedAt: number;

  // Relations
  @ManyToOne(() => SeedingGroup, (group) => group.accounts, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'seeding_group_id' })
  seedingGroup: SeedingGroup;
}

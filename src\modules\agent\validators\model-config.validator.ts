import { registerDecorator, ValidationOptions, ValidationArguments } from 'class-validator';

/**
 * Validator cho model configuration
 * Đ<PERSON>m bảo ít nhất một trong các options sau được cung cấp:
 * - model_base_id
 * - model_finetuning_id  
 * - model_id + provider_id (phải có cả 2)
 */
export function IsValidModelConfig(validationOptions?: ValidationOptions) {
  return function (object: object, propertyName: string) {
    registerDecorator({
      name: 'isValidModelConfig',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: {
        validate(value: any, args: ValidationArguments) {
          const obj = args.object as any;
          
          // Kiểm tra có model_base_id
          if (obj.model_base_id) {
            return true;
          }
          
          // Kiểm tra có model_finetuning_id
          if (obj.model_finetuning_id) {
            return true;
          }
          
          // Kiểm tra có cả model_id và provider_id
          if (obj.model_id && obj.provider_id) {
            return true;
          }
          
          return false;
        },
        defaultMessage(args: ValidationArguments) {
          return 'Phải cung cấp ít nhất một trong các options: model_base_id, model_finetuning_id, hoặc cả model_id và provider_id';
        },
      },
    });
  };
}

/**
 * Validator cho admin model configuration
 * Đảm bảo ít nhất một trong các options sau được cung cấp:
 * - model_base_id
 * - model_finetuning_id
 * Không cho phép model_id + provider_id
 */
export function IsValidAdminModelConfig(validationOptions?: ValidationOptions) {
  return function (object: object, propertyName: string) {
    registerDecorator({
      name: 'isValidAdminModelConfig',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: {
        validate(value: any, args: ValidationArguments) {
          const obj = args.object as any;
          
          // Admin không được sử dụng model_id + provider_id
          if (obj.model_id || obj.provider_id) {
            return false;
          }
          
          // Kiểm tra có model_base_id hoặc model_finetuning_id
          if (obj.model_base_id || obj.model_finetuning_id) {
            return true;
          }
          
          return false;
        },
        defaultMessage(args: ValidationArguments) {
          return 'Admin chỉ có thể sử dụng model_base_id hoặc model_finetuning_id, không được sử dụng model_id/provider_id';
        },
      },
    });
  };
}

/**
 * Validator đảm bảo model_id và provider_id phải đi cùng nhau
 */
export function IsValidUserPersonalModel(validationOptions?: ValidationOptions) {
  return function (object: object, propertyName: string) {
    registerDecorator({
      name: 'isValidUserPersonalModel',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: {
        validate(value: any, args: ValidationArguments) {
          const obj = args.object as any;
          
          // Nếu có model_id thì phải có provider_id
          if (obj.model_id && !obj.provider_id) {
            return false;
          }
          
          // Nếu có provider_id thì phải có model_id
          if (obj.provider_id && !obj.model_id) {
            return false;
          }
          
          return true;
        },
        defaultMessage(args: ValidationArguments) {
          return 'model_id và provider_id phải được cung cấp cùng nhau';
        },
      },
    });
  };
}

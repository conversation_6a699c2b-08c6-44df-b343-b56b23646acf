import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import Stripe from 'stripe';
import {
  CreateStripeCustomerDto,
  CreatePaymentIntentDto,
  CreatePaymentMethodDto,
  CreateStripeProductDto,
  CreateStripePriceDto,
  CreateStripeRefundDto,
  StripeCustomerResponse,
  StripePaymentIntentResponse,
  StripeRefundResponse,
  StripeCurrency,
  StripePaymentMethodType,
} from './dto/stripe.dto';

@Injectable()
export class StripeService {
  private readonly logger = new Logger(StripeService.name);
  private readonly stripe: Stripe;

  constructor(private readonly configService: ConfigService) {
    const secretKey = this.configService.get<string>('STRIPE_SECRET_KEY');
    
    if (!secretKey) {
      throw new Error('Stripe secret key is required');
    }

    this.stripe = new Stripe(secretKey, {
      apiVersion: '2025-06-30.basil',
      typescript: true,
    });

    this.logger.log('Stripe service initialized successfully');
  }

  /**
   * T<PERSON><PERSON> khách hàng mới
   */
  async createCustomer(createCustomerDto: CreateStripeCustomerDto): Promise<StripeCustomerResponse> {
    try {
      this.logger.log(`Creating customer: ${createCustomerDto.email}`);

      const customer = await this.stripe.customers.create({
        email: createCustomerDto.email,
        name: createCustomerDto.name,
        phone: createCustomerDto.phone,
        description: createCustomerDto.description,
        address: createCustomerDto.address,
        metadata: createCustomerDto.metadata || {},
      });

      this.logger.log(`Customer created successfully: ${customer.id}`);

      return {
        id: customer.id,
        email: customer.email || '',
        name: customer.name || '',
        phone: customer.phone || undefined,
        description: customer.description || undefined,
        created: customer.created,
        metadata: customer.metadata || {},
      };
    } catch (error) {
      this.logger.error('Error creating Stripe customer:', error);
      throw new Error(`Failed to create Stripe customer: ${error.message}`);
    }
  }

  /**
   * Lấy thông tin khách hàng
   */
  async getCustomer(customerId: string): Promise<StripeCustomerResponse> {
    try {
      this.logger.log(`Getting customer: ${customerId}`);

      const customer = await this.stripe.customers.retrieve(customerId);

      if (customer.deleted) {
        throw new Error('Customer has been deleted');
      }

      return {
        id: customer.id,
        email: 'email' in customer ? customer.email || '' : '',
        name: 'name' in customer ? customer.name || '' : '',
        phone: 'phone' in customer ? customer.phone || undefined : undefined,
        description: 'description' in customer ? customer.description || undefined : undefined,
        created: 'created' in customer ? customer.created : 0,
        metadata: 'metadata' in customer ? customer.metadata : {},
      };
    } catch (error) {
      this.logger.error('Error getting Stripe customer:', error);
      throw new Error(`Failed to get Stripe customer: ${error.message}`);
    }
  }

  /**
   * Cập nhật thông tin khách hàng
   */
  async updateCustomer(customerId: string, updateData: Partial<CreateStripeCustomerDto>): Promise<StripeCustomerResponse> {
    try {
      this.logger.log(`Updating customer: ${customerId}`);

      const customer = await this.stripe.customers.update(customerId, {
        email: updateData.email,
        name: updateData.name,
        phone: updateData.phone,
        description: updateData.description,
        address: updateData.address,
        metadata: updateData.metadata,
      });

      this.logger.log(`Customer updated successfully: ${customer.id}`);

      return {
        id: customer.id,
        email: customer.email || '',
        name: customer.name || '',
        phone: customer.phone || undefined,
        description: customer.description || undefined,
        created: customer.created,
        metadata: customer.metadata,
      };
    } catch (error) {
      this.logger.error('Error updating Stripe customer:', error);
      throw new Error(`Failed to update Stripe customer: ${error.message}`);
    }
  }

  /**
   * Xóa khách hàng
   */
  async deleteCustomer(customerId: string): Promise<{ deleted: boolean; id: string }> {
    try {
      this.logger.log(`Deleting customer: ${customerId}`);

      const result = await this.stripe.customers.del(customerId);

      this.logger.log(`Customer deleted successfully: ${customerId}`);

      return {
        deleted: result.deleted,
        id: result.id,
      };
    } catch (error) {
      this.logger.error('Error deleting Stripe customer:', error);
      throw new Error(`Failed to delete Stripe customer: ${error.message}`);
    }
  }

  /**
   * Tạo Payment Intent
   */
  async createPaymentIntent(createPaymentIntentDto: CreatePaymentIntentDto): Promise<StripePaymentIntentResponse> {
    try {
      this.logger.log(`Creating payment intent: ${createPaymentIntentDto.amount} ${createPaymentIntentDto.currency}`);

      const paymentIntent = await this.stripe.paymentIntents.create({
        amount: createPaymentIntentDto.amount,
        currency: createPaymentIntentDto.currency,
        customer: createPaymentIntentDto.customer,
        description: createPaymentIntentDto.description,
        payment_method: createPaymentIntentDto.payment_method,
        payment_method_types: createPaymentIntentDto.payment_method_types || [StripePaymentMethodType.CARD],
        confirm: createPaymentIntentDto.confirm,
        return_url: createPaymentIntentDto.return_url,
        metadata: createPaymentIntentDto.metadata || {},
        shipping: createPaymentIntentDto.shipping,
        application_fee_amount: createPaymentIntentDto.application_fee_amount,
        on_behalf_of: createPaymentIntentDto.on_behalf_of,
      });

      this.logger.log(`Payment intent created successfully: ${paymentIntent.id}`);

      return {
        id: paymentIntent.id,
        amount: paymentIntent.amount,
        currency: paymentIntent.currency,
        status: paymentIntent.status as any,
        customer: paymentIntent.customer as string,
        description: paymentIntent.description || undefined,
        client_secret: paymentIntent.client_secret || undefined,
        created: paymentIntent.created,
        metadata: paymentIntent.metadata || {},
      };
    } catch (error) {
      this.logger.error('Error creating payment intent:', error);
      throw new Error(`Failed to create payment intent: ${error.message}`);
    }
  }

  /**
   * Xác nhận Payment Intent
   */
  async confirmPaymentIntent(paymentIntentId: string, paymentMethodId?: string): Promise<StripePaymentIntentResponse> {
    try {
      this.logger.log(`Confirming payment intent: ${paymentIntentId}`);

      const paymentIntent = await this.stripe.paymentIntents.confirm(paymentIntentId, {
        payment_method: paymentMethodId,
      });

      this.logger.log(`Payment intent confirmed: ${paymentIntent.id}`);

      return {
        id: paymentIntent.id,
        amount: paymentIntent.amount,
        currency: paymentIntent.currency,
        status: paymentIntent.status as any,
        customer: paymentIntent.customer as string,
        description: paymentIntent.description || undefined,
        client_secret: paymentIntent.client_secret || undefined,
        created: paymentIntent.created,
        metadata: paymentIntent.metadata,
      };
    } catch (error) {
      this.logger.error('Error confirming payment intent:', error);
      throw new Error(`Failed to confirm payment intent: ${error.message}`);
    }
  }

  /**
   * Lấy thông tin Payment Intent
   */
  async getPaymentIntent(paymentIntentId: string): Promise<StripePaymentIntentResponse> {
    try {
      this.logger.log(`Getting payment intent: ${paymentIntentId}`);

      const paymentIntent = await this.stripe.paymentIntents.retrieve(paymentIntentId);

      return {
        id: paymentIntent.id,
        amount: paymentIntent.amount,
        currency: paymentIntent.currency,
        status: paymentIntent.status as any,
        customer: paymentIntent.customer as string,
        description: paymentIntent.description || undefined,
        client_secret: paymentIntent.client_secret || undefined,
        created: paymentIntent.created,
        metadata: paymentIntent.metadata,
      };
    } catch (error) {
      this.logger.error('Error getting payment intent:', error);
      throw new Error(`Failed to get payment intent: ${error.message}`);
    }
  }

  /**
   * Hủy Payment Intent
   */
  async cancelPaymentIntent(paymentIntentId: string): Promise<StripePaymentIntentResponse> {
    try {
      this.logger.log(`Canceling payment intent: ${paymentIntentId}`);

      const paymentIntent = await this.stripe.paymentIntents.cancel(paymentIntentId);

      this.logger.log(`Payment intent canceled: ${paymentIntent.id}`);

      return {
        id: paymentIntent.id,
        amount: paymentIntent.amount,
        currency: paymentIntent.currency,
        status: paymentIntent.status as any,
        customer: paymentIntent.customer as string,
        description: paymentIntent.description || undefined,
        client_secret: paymentIntent.client_secret || undefined,
        created: paymentIntent.created,
        metadata: paymentIntent.metadata,
      };
    } catch (error) {
      this.logger.error('Error canceling payment intent:', error);
      throw new Error(`Failed to cancel payment intent: ${error.message}`);
    }
  }

  /**
   * Tạo phương thức thanh toán
   */
  async createPaymentMethod(createPaymentMethodDto: CreatePaymentMethodDto): Promise<Stripe.PaymentMethod> {
    try {
      this.logger.log(`Creating payment method: ${createPaymentMethodDto.type}`);

      const paymentMethod = await this.stripe.paymentMethods.create({
        type: createPaymentMethodDto.type as any,
        card: createPaymentMethodDto.card,
        billing_details: createPaymentMethodDto.billing_details,
        metadata: createPaymentMethodDto.metadata || {},
      });

      this.logger.log(`Payment method created successfully: ${paymentMethod.id}`);

      return paymentMethod;
    } catch (error) {
      this.logger.error('Error creating payment method:', error);
      throw new Error(`Failed to create payment method: ${error.message}`);
    }
  }

  /**
   * Gắn phương thức thanh toán vào khách hàng
   */
  async attachPaymentMethod(paymentMethodId: string, customerId: string): Promise<Stripe.PaymentMethod> {
    try {
      this.logger.log(`Attaching payment method ${paymentMethodId} to customer ${customerId}`);

      const paymentMethod = await this.stripe.paymentMethods.attach(paymentMethodId, {
        customer: customerId,
      });

      this.logger.log(`Payment method attached successfully`);

      return paymentMethod;
    } catch (error) {
      this.logger.error('Error attaching payment method:', error);
      throw new Error(`Failed to attach payment method: ${error.message}`);
    }
  }

  /**
   * Tách phương thức thanh toán khỏi khách hàng
   */
  async detachPaymentMethod(paymentMethodId: string): Promise<Stripe.PaymentMethod> {
    try {
      this.logger.log(`Detaching payment method: ${paymentMethodId}`);

      const paymentMethod = await this.stripe.paymentMethods.detach(paymentMethodId);

      this.logger.log(`Payment method detached successfully`);

      return paymentMethod;
    } catch (error) {
      this.logger.error('Error detaching payment method:', error);
      throw new Error(`Failed to detach payment method: ${error.message}`);
    }
  }

  /**
   * Lấy danh sách phương thức thanh toán của khách hàng
   */
  async listCustomerPaymentMethods(customerId: string, type?: StripePaymentMethodType): Promise<Stripe.PaymentMethod[]> {
    try {
      this.logger.log(`Getting payment methods for customer: ${customerId}`);

      const paymentMethods = await this.stripe.paymentMethods.list({
        customer: customerId,
        type: type as any,
      });

      return paymentMethods.data;
    } catch (error) {
      this.logger.error('Error getting customer payment methods:', error);
      throw new Error(`Failed to get customer payment methods: ${error.message}`);
    }
  }

  /**
   * Tạo sản phẩm
   */
  async createProduct(createProductDto: CreateStripeProductDto): Promise<Stripe.Product> {
    try {
      this.logger.log(`Creating product: ${createProductDto.name}`);

      const product = await this.stripe.products.create({
        name: createProductDto.name,
        description: createProductDto.description,
        images: createProductDto.images,
        metadata: createProductDto.metadata || {},
        url: createProductDto.url,
        active: createProductDto.active !== false,
      });

      this.logger.log(`Product created successfully: ${product.id}`);

      return product;
    } catch (error) {
      this.logger.error('Error creating product:', error);
      throw new Error(`Failed to create product: ${error.message}`);
    }
  }

  /**
   * Lấy thông tin sản phẩm
   */
  async getProduct(productId: string): Promise<Stripe.Product> {
    try {
      this.logger.log(`Getting product: ${productId}`);

      const product = await this.stripe.products.retrieve(productId);

      return product;
    } catch (error) {
      this.logger.error('Error getting product:', error);
      throw new Error(`Failed to get product: ${error.message}`);
    }
  }

  /**
   * Cập nhật sản phẩm
   */
  async updateProduct(productId: string, updateData: Partial<CreateStripeProductDto>): Promise<Stripe.Product> {
    try {
      this.logger.log(`Updating product: ${productId}`);

      const product = await this.stripe.products.update(productId, {
        name: updateData.name,
        description: updateData.description,
        images: updateData.images,
        metadata: updateData.metadata,
        url: updateData.url,
        active: updateData.active,
      });

      this.logger.log(`Product updated successfully: ${product.id}`);

      return product;
    } catch (error) {
      this.logger.error('Error updating product:', error);
      throw new Error(`Failed to update product: ${error.message}`);
    }
  }

  /**
   * Tạo giá cho sản phẩm
   */
  async createPrice(createPriceDto: CreateStripePriceDto): Promise<Stripe.Price> {
    try {
      this.logger.log(`Creating price for product: ${createPriceDto.product}`);

      const price = await this.stripe.prices.create({
        product: createPriceDto.product,
        unit_amount: createPriceDto.unit_amount,
        currency: createPriceDto.currency,
        recurring: createPriceDto.recurring,
        metadata: createPriceDto.metadata || {},
        active: createPriceDto.active !== false,
      });

      this.logger.log(`Price created successfully: ${price.id}`);

      return price;
    } catch (error) {
      this.logger.error('Error creating price:', error);
      throw new Error(`Failed to create price: ${error.message}`);
    }
  }

  /**
   * Lấy thông tin giá
   */
  async getPrice(priceId: string): Promise<Stripe.Price> {
    try {
      this.logger.log(`Getting price: ${priceId}`);

      const price = await this.stripe.prices.retrieve(priceId);

      return price;
    } catch (error) {
      this.logger.error('Error getting price:', error);
      throw new Error(`Failed to get price: ${error.message}`);
    }
  }

  /**
   * Hoàn tiền
   */
  async createRefund(createRefundDto: CreateStripeRefundDto): Promise<StripeRefundResponse> {
    try {
      this.logger.log(`Creating refund for payment intent: ${createRefundDto.payment_intent}`);

      const refund = await this.stripe.refunds.create({
        payment_intent: createRefundDto.payment_intent,
        amount: createRefundDto.amount,
        reason: createRefundDto.reason,
        metadata: createRefundDto.metadata || {},
      });

      this.logger.log(`Refund created successfully: ${refund.id}`);

      return {
        id: refund.id,
        amount: refund.amount,
        currency: refund.currency,
        status: refund.status as any,
        reason: refund.reason as any,
        created: refund.created,
        metadata: refund.metadata || {},
      };
    } catch (error) {
      this.logger.error('Error creating refund:', error);
      throw new Error(`Failed to create refund: ${error.message}`);
    }
  }

  /**
   * Lấy thông tin hoàn tiền
   */
  async getRefund(refundId: string): Promise<StripeRefundResponse> {
    try {
      this.logger.log(`Getting refund: ${refundId}`);

      const refund = await this.stripe.refunds.retrieve(refundId);

      return {
        id: refund.id,
        amount: refund.amount,
        currency: refund.currency,
        status: refund.status as any,
        reason: refund.reason as any,
        created: refund.created,
        metadata: refund.metadata || {},
      };
    } catch (error) {
      this.logger.error('Error getting refund:', error);
      throw new Error(`Failed to get refund: ${error.message}`);
    }
  }

  /**
   * Tạo Setup Intent (để lưu phương thức thanh toán cho tương lai)
   */
  async createSetupIntent(customerId: string, paymentMethodTypes: StripePaymentMethodType[] = [StripePaymentMethodType.CARD]): Promise<Stripe.SetupIntent> {
    try {
      this.logger.log(`Creating setup intent for customer: ${customerId}`);

      const setupIntent = await this.stripe.setupIntents.create({
        customer: customerId,
        payment_method_types: paymentMethodTypes as any[],
        usage: 'off_session',
      });

      this.logger.log(`Setup intent created successfully: ${setupIntent.id}`);

      return setupIntent;
    } catch (error) {
      this.logger.error('Error creating setup intent:', error);
      throw new Error(`Failed to create setup intent: ${error.message}`);
    }
  }

  /**
   * Lấy balance của tài khoản
   */
  async getBalance(): Promise<Stripe.Balance> {
    try {
      this.logger.log('Getting account balance');

      const balance = await this.stripe.balance.retrieve();

      return balance;
    } catch (error) {
      this.logger.error('Error getting balance:', error);
      throw new Error(`Failed to get balance: ${error.message}`);
    }
  }
}

import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

// Entities
import { DashboardPage } from './entities/dashboard-page.entity';
import { DashboardWidget } from './entities/dashboard-widget.entity';
import { DashboardTemplate } from './entities/dashboard-template.entity';

// Repositories
import { DashboardPageRepository } from './repositories/dashboard-page.repository';
import { DashboardWidgetRepository } from './repositories/dashboard-widget.repository';
import { DashboardTemplateRepository } from './repositories/dashboard-template.repository';

// Services
import { DashboardPageService } from './services/dashboard-page.service';
import { DashboardWidgetService } from './services/dashboard-widget.service';
import { DashboardTemplateService } from './services/dashboard-template.service';
import { AdminDashboardPageService } from './services/admin-dashboard-page.service';

// Controllers
import { DashboardPageController } from './controllers/dashboard-page.controller';
import { DashboardWidgetController } from './controllers/dashboard-widget.controller';
import { DashboardTemplateController } from './controllers/dashboard-template.controller';
import { AdminDashboardPageController } from './controllers/admin-dashboard-page.controller';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      DashboardPage,
      DashboardWidget,
      DashboardTemplate,
    ]),
  ],
  controllers: [
    DashboardPageController,
    DashboardWidgetController,
    DashboardTemplateController,
    AdminDashboardPageController,
  ],
  providers: [
    DashboardPageRepository,
    DashboardWidgetRepository,
    DashboardTemplateRepository,
    DashboardPageService,
    DashboardWidgetService,
    DashboardTemplateService,
    AdminDashboardPageService,
  ],
  exports: [
    DashboardPageService,
    DashboardWidgetService,
    DashboardTemplateService,
    AdminDashboardPageService,
    DashboardPageRepository,
    DashboardWidgetRepository,
    DashboardTemplateRepository,
  ],
})
export class DashboardModule {}

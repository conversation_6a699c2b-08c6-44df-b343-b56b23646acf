/**
 * @file Google Calendar Validation Functions
 * 
 * Đ<PERSON><PERSON> nghĩa validation functions cho Google Calendar integration
 * Theo patterns từ Make.com chuẩn
 * 
 * @version 1.0.0
 * <AUTHOR> Assistant
 */

import { EGoogleCalendarOperation } from './google-calendar.types';
import { 
    IGoogleCalendarParameters,
    ISearchEventsParameters,
    IGetEventParameters,
    ICreateEventParameters,
    IDuplicateEventParameters,
    IUpdateEventParameters,
    IDeleteEventParameters,
    IListCalendarsParameters,
    IGetCalendarParameters,
    ICreateCalendarParameters,
    IUpdateCalendarParameters,
    IDeleteCalendarParameters,
    IClearCalendarParameters,
    IListAccessControlRulesParameters,
    IGetAccessControlRuleParameters,
    ICreateAccessControlRuleParameters,
    IUpdateAccessControlRuleParameters,
    IDeleteAccessControlRuleParameters,
    IMakeApiCallParameters,
    IGetFreeBusyParameters
} from './google-calendar.interface';

// =================================================================
// GOOGLE CALENDAR VALIDATION FUNCTIONS
// =================================================================

/**
 * Validate Google Calendar parameters (detailed validation)
 */
export function validateGoogleCalendarParametersDetailed(
    params: Partial<IGoogleCalendarParameters>
): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Check required integration_id
    if (!params.integration_id) {
        errors.push('Integration ID is required');
    }

    // Check required operation
    if (!params.operation) {
        errors.push('Operation is required');
    }

    // Note: Connection is only required for specific operations
    // Individual operation validators will check for connection when needed

    // Operation specific validation
    switch (params.operation) {
        case EGoogleCalendarOperation.SEARCH_EVENTS:
            validateSearchEventsParameters(params as ISearchEventsParameters, errors);
            break;

        case EGoogleCalendarOperation.GET_EVENT:
            validateGetEventParameters(params as IGetEventParameters, errors);
            break;

        case EGoogleCalendarOperation.CREATE_EVENT:
            validateCreateEventParameters(params as ICreateEventParameters, errors);
            break;

        case EGoogleCalendarOperation.DUPLICATE_EVENT:
            validateDuplicateEventParameters(params as IDuplicateEventParameters, errors);
            break;

        case EGoogleCalendarOperation.UPDATE_EVENT:
            validateUpdateEventParameters(params as IUpdateEventParameters, errors);
            break;

        case EGoogleCalendarOperation.DELETE_EVENT:
            validateDeleteEventParameters(params as IDeleteEventParameters, errors);
            break;

        case EGoogleCalendarOperation.LIST_CALENDARS:
            validateListCalendarsParameters(params as IListCalendarsParameters, errors);
            break;

        case EGoogleCalendarOperation.GET_CALENDAR:
            validateGetCalendarParameters(params as IGetCalendarParameters, errors);
            break;

        case EGoogleCalendarOperation.CREATE_CALENDAR:
            validateCreateCalendarParameters(params as ICreateCalendarParameters, errors);
            break;

        case EGoogleCalendarOperation.UPDATE_CALENDAR:
            validateUpdateCalendarParameters(params as IUpdateCalendarParameters, errors);
            break;

        case EGoogleCalendarOperation.DELETE_CALENDAR:
            validateDeleteCalendarParameters(params as IDeleteCalendarParameters, errors);
            break;

        case EGoogleCalendarOperation.CLEAR_CALENDAR:
            validateClearCalendarParameters(params as IClearCalendarParameters, errors);
            break;

        case EGoogleCalendarOperation.LIST_ACCESS_CONTROL_RULES:
            validateListAccessControlRulesParameters(params as IListAccessControlRulesParameters, errors);
            break;

        case EGoogleCalendarOperation.GET_ACCESS_CONTROL_RULE:
            validateGetAccessControlRuleParameters(params as IGetAccessControlRuleParameters, errors);
            break;

        case EGoogleCalendarOperation.CREATE_ACCESS_CONTROL_RULE:
            validateCreateAccessControlRuleParameters(params as ICreateAccessControlRuleParameters, errors);
            break;

        case EGoogleCalendarOperation.UPDATE_ACCESS_CONTROL_RULE:
            validateUpdateAccessControlRuleParameters(params as IUpdateAccessControlRuleParameters, errors);
            break;

        case EGoogleCalendarOperation.DELETE_ACCESS_CONTROL_RULE:
            validateDeleteAccessControlRuleParameters(params as IDeleteAccessControlRuleParameters, errors);
            break;

        case EGoogleCalendarOperation.MAKE_API_CALL:
            validateMakeApiCallParameters(params as IMakeApiCallParameters, errors);
            break;

        case EGoogleCalendarOperation.GET_FREE_BUSY:
            validateGetFreeBusyParameters(params as IGetFreeBusyParameters, errors);
            break;

        default:
            errors.push(`Unknown operation: ${(params as any).operation}`);
    }

    return {
        isValid: errors.length === 0,
        errors
    };
}

/**
 * Quick validation for Google Calendar parameters
 */
export function validateGoogleCalendarParameters(
    params: Partial<IGoogleCalendarParameters>
): boolean {
    const result = validateGoogleCalendarParametersDetailed(params);
    return result.isValid;
}

// =================================================================
// OPERATION-SPECIFIC VALIDATION FUNCTIONS
// =================================================================

/**
 * Validate Search Events parameters
 */
function validateSearchEventsParameters(params: ISearchEventsParameters, errors: string[]): void {
    if (!params.calendar_id) {
        errors.push('Calendar ID is required for Search Events');
    }
}

/**
 * Validate Get Event parameters
 */
function validateGetEventParameters(params: IGetEventParameters, errors: string[]): void {
    if (!params.calendar_id) {
        errors.push('Calendar ID is required for Get Event');
    }
    if (!params.event_id) {
        errors.push('Event ID is required for Get Event');
    }
}

/**
 * Validate Create Event parameters
 */
function validateCreateEventParameters(params: ICreateEventParameters, errors: string[]): void {
    if (!params.calendar_id) {
        errors.push('Calendar ID is required for Create Event');
    }
    if (!params.event_name) {
        errors.push('Event Name is required for Create Event');
    }
    if (!params.start_date) {
        errors.push('Start Date is required for Create Event');
    }
    if (!params.end_date && !params.duration) {
        errors.push('Either End Date or Duration is required for Create Event');
    }
}

/**
 * Validate Duplicate Event parameters
 */
function validateDuplicateEventParameters(params: IDuplicateEventParameters, errors: string[]): void {
    if (!params.calendar_id) {
        errors.push('Calendar ID is required for Duplicate Event');
    }
    if (!params.event_id) {
        errors.push('Event ID is required for Duplicate Event');
    }
}

/**
 * Validate Update Event parameters
 */
function validateUpdateEventParameters(params: IUpdateEventParameters, errors: string[]): void {
    if (!params.calendar_id) {
        errors.push('Calendar ID is required for Update Event');
    }
    if (!params.event_id) {
        errors.push('Event ID is required for Update Event');
    }
}

/**
 * Validate Delete Event parameters
 */
function validateDeleteEventParameters(params: IDeleteEventParameters, errors: string[]): void {
    if (!params.calendar_id) {
        errors.push('Calendar ID is required for Delete Event');
    }
    // Note: event_id is not defined in IDeleteEventParameters interface
    // If event_id is needed, it should be added to the interface first
}

/**
 * Validate List Calendars parameters
 */
function validateListCalendarsParameters(params: IListCalendarsParameters, errors: string[]): void {
    // No additional validation needed - only connection is required
}

/**
 * Validate Get Calendar parameters
 */
function validateGetCalendarParameters(params: IGetCalendarParameters, errors: string[]): void {
    if (!params.calendar_id) {
        errors.push('Calendar ID is required for Get Calendar');
    }
}

/**
 * Validate Create Calendar parameters
 */
function validateCreateCalendarParameters(params: ICreateCalendarParameters, errors: string[]): void {
    if (!params.calendar_name) {
        errors.push('Calendar Name is required for Create Calendar');
    }
}

/**
 * Validate Update Calendar parameters
 */
function validateUpdateCalendarParameters(params: IUpdateCalendarParameters, errors: string[]): void {
    if (!params.calendar_id) {
        errors.push('Calendar ID is required for Update Calendar');
    }
}

/**
 * Validate Delete Calendar parameters
 */
function validateDeleteCalendarParameters(params: IDeleteCalendarParameters, errors: string[]): void {
    if (!params.calendar_id) {
        errors.push('Calendar ID is required for Delete Calendar');
    }
}

/**
 * Validate Clear Calendar parameters
 */
function validateClearCalendarParameters(params: IClearCalendarParameters, errors: string[]): void {
    if (!params.calendar_id) {
        errors.push('Calendar ID is required for Clear Calendar');
    }
}

/**
 * Validate List Access Control Rules parameters
 */
function validateListAccessControlRulesParameters(params: IListAccessControlRulesParameters, errors: string[]): void {
    if (!params.calendar_id) {
        errors.push('Calendar ID is required for List Access Control Rules');
    }
}

/**
 * Validate Get Access Control Rule parameters
 */
function validateGetAccessControlRuleParameters(params: IGetAccessControlRuleParameters, errors: string[]): void {
    if (!params.calendar_id) {
        errors.push('Calendar ID is required for Get Access Control Rule');
    }
    if (!params.rule_id) {
        errors.push('Rule ID is required for Get Access Control Rule');
    }
}

/**
 * Validate Create Access Control Rule parameters
 */
function validateCreateAccessControlRuleParameters(params: ICreateAccessControlRuleParameters, errors: string[]): void {
    if (!params.calendar_id) {
        errors.push('Calendar ID is required for Create Access Control Rule');
    }
    if (!params.role) {
        errors.push('Role is required for Create Access Control Rule');
    }
    if (!params.scope || !params.scope.type) {
        errors.push('Scope type is required for Create Access Control Rule');
    }
}

/**
 * Validate Update Access Control Rule parameters
 */
function validateUpdateAccessControlRuleParameters(params: IUpdateAccessControlRuleParameters, errors: string[]): void {
    if (!params.calendar_id) {
        errors.push('Calendar ID is required for Update Access Control Rule');
    }
    if (!params.rule_id) {
        errors.push('Rule ID is required for Update Access Control Rule');
    }
}

/**
 * Validate Delete Access Control Rule parameters
 */
function validateDeleteAccessControlRuleParameters(params: IDeleteAccessControlRuleParameters, errors: string[]): void {
    if (!params.calendar_id) {
        errors.push('Calendar ID is required for Delete Access Control Rule');
    }
    if (!params.rule_id) {
        errors.push('Rule ID is required for Delete Access Control Rule');
    }
}

/**
 * Validate Make API Call parameters
 */
function validateMakeApiCallParameters(params: IMakeApiCallParameters, errors: string[]): void {
    if (!params.url) {
        errors.push('URL is required for Make API Call');
    }
    if (!params.method) {
        errors.push('Method is required for Make API Call');
    }
}

/**
 * Validate Get Free/Busy parameters
 */
function validateGetFreeBusyParameters(params: IGetFreeBusyParameters, errors: string[]): void {
    if (!params.minimum_time) {
        errors.push('Minimum Time is required for Get Free/Busy');
    }
    if (!params.maximum_time) {
        errors.push('Maximum Time is required for Get Free/Busy');
    }
    if (!params.calendars || params.calendars.length === 0) {
        errors.push('At least one calendar is required for Get Free/Busy');
    }
    
    // Validate each calendar in the array
    if (params.calendars) {
        params.calendars.forEach((calendar, index) => {
            if (!calendar.calendar_id) {
                errors.push(`Calendar ID is required for calendar ${index + 1} in Get Free/Busy`);
            }
        });
    }
}

// =================================================================
// UTILITY VALIDATION FUNCTIONS
// =================================================================

/**
 * Validate email format
 */
export function isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

/**
 * Validate URL format
 */
export function isValidUrl(url: string): boolean {
    try {
        new URL(url);
        return true;
    } catch {
        return false;
    }
}

/**
 * Validate date format
 */
export function isValidDate(date: string): boolean {
    const parsedDate = new Date(date);
    return !isNaN(parsedDate.getTime());
}

/**
 * Validate timezone format
 */
export function isValidTimezone(timezone: string): boolean {
    try {
        Intl.DateTimeFormat(undefined, { timeZone: timezone });
        return true;
    } catch {
        return false;
    }
}

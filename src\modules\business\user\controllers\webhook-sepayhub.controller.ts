import { Body, Controller, Headers, HttpStatus, Post } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import {
  WebhooksSepayHubRequestDto,
  WebhookSepayHubResponseDto,
} from '../dto/webhook-sepayhub-request.dto';
import { WebhookSepayHubService } from '../services/webhook-sepayhub.service';
import { ApiResponseDto } from '@/common/response';
import { SWAGGER_API_TAGS } from '@/common/swagger';
import { Public } from '@/common/decorators/public.decorator';

@ApiTags(SWAGGER_API_TAGS.BUSINESS_WEBHOOK)
@Controller('sepay-hub')
@Public()
export class WebhookSepayHubController {
  constructor(
    private readonly webhookSepayHubService: WebhookSepayHubService,
  ) {}

  /**
   * API xử lý webhook từ SepayHub
   * @param authorization Authorization header với format "Apikey API_KEY"
   * @param webhookRequest Dữ liệu webhook
   * @returns Kết quả xử lý
   */
  @Post('ipn')
  @ApiOperation({ summary: 'Xử lý webhook từ SepayHub' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Webhook đã được xử lý',
    schema: {
      properties: {
        code: { type: 'number', example: 200 },
        message: {
          type: 'string',
          example: 'Webhook đã được xử lý thành công',
        },
        result: {
          type: 'object',
          properties: {
            success: { type: 'boolean', example: true },
            message: {
              type: 'string',
              example: 'SepayHub webhook processed successfully',
            },
          },
        },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Dữ liệu không hợp lệ',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'API key không hợp lệ',
  })
  async handleWebhook(
    @Headers('authorization') authorization: string,
    @Body() webhookRequest: WebhooksSepayHubRequestDto,
  ): Promise<ApiResponseDto<WebhookSepayHubResponseDto>> {
    // Trích xuất API key từ Authorization header
    // Format: "Apikey API_KEY_CUA_BAN"
    const apiKey = this.extractApiKeyFromAuthorization(authorization);

    // Kiểm tra API key
    if (!apiKey || !this.webhookSepayHubService.validateApiKey(apiKey)) {
      const errorResponse: WebhookSepayHubResponseDto = {
        success: false,
        message: 'Invalid API key',
      };
      return ApiResponseDto.success(errorResponse, 'API key không hợp lệ');
    }

    // Xử lý webhook
    const result =
      await this.webhookSepayHubService.processWebhook(webhookRequest);
    return ApiResponseDto.success(result, 'Webhook đã được xử lý thành công');
  }

  /**
   * Trích xuất API key từ Authorization header
   * @param authorization Authorization header với format "Apikey API_KEY"
   * @returns API key hoặc null nếu format không đúng
   */
  private extractApiKeyFromAuthorization(authorization: string): string | null {
    if (!authorization) {
      return null;
    }

    // Kiểm tra format "Apikey API_KEY"
    const parts = authorization.split(' ');
    if (parts.length !== 2 || parts[0] !== 'Apikey') {
      return null;
    }

    return parts[1];
  }
}

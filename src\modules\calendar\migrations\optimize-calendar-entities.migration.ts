import { MigrationInterface, QueryRunner, Table, Index } from 'typeorm';

export class OptimizeCalendarEntities1234567890 implements MigrationInterface {
  name = 'OptimizeCalendarEntities1234567890';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // 1. Backup dữ liệu cũ trước khi migration
    await queryRunner.query(`
      CREATE TABLE calendar_events_backup AS SELECT * FROM calendar_events;
      CREATE TABLE calendar_tasks_backup AS SELECT * FROM calendar_tasks;
      CREATE TABLE calendar_reminders_backup AS SELECT * FROM calendar_reminders;
      CREATE TABLE calendar_reports_backup AS SELECT * FROM calendar_reports;
    `);

    // 2. Tạo bảng calendar_events mới (thay thế bảng cũ)
    await queryRunner.dropTable('calendar_events', true);
    await queryRunner.createTable(
      new Table({
        name: 'calendar_events',
        columns: [
          {
            name: 'id',
            type: 'uuid',
            isPrimary: true,
            generationStrategy: 'uuid',
            default: 'gen_random_uuid()',
          },
          {
            name: 'user_id',
            type: 'integer',
            isNullable: false,
          },
          {
            name: 'action_type',
            type: 'enum',
            enum: ['task', 'reminder', 'report'],
            isNullable: false,
          },
          {
            name: 'title',
            type: 'varchar',
            length: '255',
            isNullable: false,
          },
          {
            name: 'description',
            type: 'text',
            isNullable: true,
          },
          {
            name: 'start_time',
            type: 'timestamp',
            isNullable: false,
          },
          {
            name: 'end_time',
            type: 'timestamp',
            isNullable: true,
          },
          {
            name: 'time_zone',
            type: 'varchar',
            length: '50',
            default: "'Asia/Ho_Chi_Minh'",
          },
          {
            name: 'status',
            type: 'enum',
            enum: [
              'scheduled',
              'in_progress',
              'completed',
              'failed',
              'cancelled',
            ],
            default: "'scheduled'",
          },
          {
            name: 'priority',
            type: 'enum',
            enum: ['low', 'medium', 'high', 'urgent'],
            default: "'medium'",
          },
          {
            name: 'action_config',
            type: 'jsonb',
            isNullable: false,
          },
          {
            name: 'execution_status',
            type: 'enum',
            enum: ['pending', 'running', 'completed', 'failed', 'cancelled'],
            default: "'pending'",
          },
          {
            name: 'execution_start_time',
            type: 'timestamp',
            isNullable: true,
          },
          {
            name: 'execution_end_time',
            type: 'timestamp',
            isNullable: true,
          },
          {
            name: 'execution_result',
            type: 'jsonb',
            isNullable: true,
          },
          {
            name: 'execution_error',
            type: 'text',
            isNullable: true,
          },
          {
            name: 'job_id',
            type: 'varchar',
            isNullable: true,
          },
          {
            name: 'retry_count',
            type: 'integer',
            default: 0,
          },
          {
            name: 'next_retry_time',
            type: 'timestamp',
            isNullable: true,
          },
          {
            name: 'metadata',
            type: 'jsonb',
            isNullable: true,
          },
          {
            name: 'created_at',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'updated_at',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
          },
        ],
        foreignKeys: [
          {
            columnNames: ['user_id'],
            referencedTableName: 'users',
            referencedColumnNames: ['id'],
            onDelete: 'CASCADE',
          },
        ],
        indices: [
          {
            name: 'IDX_calendar_events_user_id',
            columnNames: ['user_id'],
          },
          {
            name: 'IDX_calendar_events_action_type',
            columnNames: ['action_type'],
          },
          {
            name: 'IDX_calendar_events_start_time',
            columnNames: ['start_time'],
          },
          {
            name: 'IDX_calendar_events_status',
            columnNames: ['status'],
          },
          {
            name: 'IDX_calendar_events_execution_status',
            columnNames: ['execution_status'],
          },
          {
            name: 'IDX_calendar_events_job_id',
            columnNames: ['job_id'],
          },
        ],
      }),
      true,
    );

    // 3. Tạo bảng calendar_execution_history
    await queryRunner.createTable(
      new Table({
        name: 'calendar_execution_history',
        columns: [
          {
            name: 'id',
            type: 'uuid',
            isPrimary: true,
            generationStrategy: 'uuid',
            default: 'gen_random_uuid()',
          },
          {
            name: 'event_id',
            type: 'uuid',
            isNullable: false,
          },
          {
            name: 'execution_time',
            type: 'timestamp',
            isNullable: false,
          },
          {
            name: 'status',
            type: 'enum',
            enum: ['pending', 'running', 'completed', 'failed', 'cancelled'],
            default: "'pending'",
          },
          {
            name: 'start_time',
            type: 'timestamp',
            isNullable: true,
          },
          {
            name: 'end_time',
            type: 'timestamp',
            isNullable: true,
          },
          {
            name: 'duration',
            type: 'integer',
            isNullable: true,
          },
          {
            name: 'result',
            type: 'jsonb',
            isNullable: true,
          },
          {
            name: 'error',
            type: 'text',
            isNullable: true,
          },
          {
            name: 'job_id',
            type: 'varchar',
            isNullable: true,
          },
          {
            name: 'retry_attempt',
            type: 'integer',
            default: 0,
          },
          {
            name: 'execution_config',
            type: 'jsonb',
            isNullable: true,
          },
          {
            name: 'metadata',
            type: 'jsonb',
            isNullable: true,
          },
          {
            name: 'created_at',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
          },
        ],
        foreignKeys: [
          {
            columnNames: ['event_id'],
            referencedTableName: 'calendar_events',
            referencedColumnNames: ['id'],
            onDelete: 'CASCADE',
          },
        ],
        indices: [
          {
            name: 'IDX_calendar_execution_history_event_id',  
            columnNames: ['event_id'],
          },
          {
            name: 'IDX_calendar_execution_history_execution_time',
            columnNames: ['execution_time'],
          },
          {
            name: 'IDX_calendar_execution_history_status',
            columnNames: ['status'],
          },
          {
            name: 'IDX_calendar_execution_history_job_id',
            columnNames: ['job_id'],
          },
        ],
      }),
      true,
    );

    // 4. Migration dữ liệu từ các bảng cũ sang bảng mới
    await this.migrateData(queryRunner);

    // 5. Xóa các bảng cũ không cần thiết
    await this.dropOldTables(queryRunner);
  }

  private async migrateData(queryRunner: QueryRunner): Promise<void> {
    // Migrate calendar_events cũ
    await queryRunner.query(`
      INSERT INTO calendar_events (
        id, user_id, action_type, title, description, start_time, end_time,
        time_zone, status, priority, action_config, execution_status,
        is_active, metadata, created_at, updated_at
      )
      SELECT
        id, user_id,
        CASE
          WHEN event_type = 'task' THEN 'task'
          WHEN event_type = 'reminder' THEN 'reminder'
          WHEN event_type = 'report' THEN 'report'
          ELSE 'reminder'
        END as action_type,
        title, description, start_time, end_time, time_zone,
        CASE
          WHEN status = 'scheduled' THEN 'scheduled'
          WHEN status = 'confirmed' THEN 'scheduled'
          WHEN status = 'in_progress' THEN 'in_progress'
          WHEN status = 'completed' THEN 'completed'
          WHEN status = 'cancelled' THEN 'cancelled'
          WHEN status = 'failed' THEN 'failed'
          ELSE 'scheduled'
        END as status,
        CASE
          WHEN priority = 'low' THEN 'low'
          WHEN priority = 'medium' THEN 'medium'
          WHEN priority = 'high' THEN 'high'
          WHEN priority = 'urgent' THEN 'urgent'
          ELSE 'medium'
        END as priority,
        '{}' as action_config,
        'pending' as execution_status,
        true as is_active,
        metadata,
        created_at, updated_at
      FROM calendar_events_backup;
    `);

    // Migrate calendar_tasks
    await queryRunner.query(`
      INSERT INTO calendar_events (
        id, user_id, action_type, title, description, start_time,
        time_zone, status, priority, action_config, execution_status,
        execution_start_time, execution_end_time, execution_result, execution_error,
        job_id, retry_count, next_retry_time, is_active, metadata, created_at, updated_at
      )
      SELECT
        gen_random_uuid(), user_id, 'task', name, description,
        COALESCE(execution_start_time, created_at),
        'Asia/Ho_Chi_Minh',
        CASE
          WHEN status = 'pending' THEN 'scheduled'
          WHEN status = 'running' THEN 'in_progress'
          WHEN status = 'completed' THEN 'completed'
          WHEN status = 'failed' THEN 'failed'
          WHEN status = 'cancelled' THEN 'cancelled'
          ELSE 'scheduled'
        END as status,
        'medium' as priority,
        jsonb_build_object(
          'agentId', agent_id,
          'taskId', task_id,
          'resources', resources,
          'executionConfig', execution_config
        ) as action_config,
        CASE
          WHEN status = 'pending' THEN 'pending'
          WHEN status = 'running' THEN 'running'
          WHEN status = 'completed' THEN 'completed'
          WHEN status = 'failed' THEN 'failed'
          WHEN status = 'cancelled' THEN 'cancelled'
          ELSE 'pending'
        END as execution_status,
        execution_start_time, execution_end_time, execution_result, execution_error,
        job_id, retry_count, next_retry_time, true, metadata, created_at, updated_at
      FROM calendar_tasks_backup;
    `);

    // Migrate calendar_reminders
    await queryRunner.query(`
      INSERT INTO calendar_events (
        id, user_id, action_type, title, description, start_time,
        time_zone, status, priority, action_config, execution_status,
        execution_start_time, execution_end_time, execution_result, execution_error,
        job_id, retry_count, next_retry_time, is_active, metadata, created_at, updated_at
      )
      SELECT
        gen_random_uuid(), user_id, 'reminder', title, message,
        reminder_time,
        'Asia/Ho_Chi_Minh',
        CASE
          WHEN status = 'pending' THEN 'scheduled'
          WHEN status = 'running' THEN 'in_progress'
          WHEN status = 'completed' THEN 'completed'
          WHEN status = 'failed' THEN 'failed'
          WHEN status = 'cancelled' THEN 'cancelled'
          ELSE 'scheduled'
        END as status,
        'medium' as priority,
        jsonb_build_object(
          'reminderMinutes', reminder_minutes,
          'channelType', channel_type,
          'channelConfig', channel_config,
          'message', message,
          'messageTemplate', message_template
        ) as action_config,
        CASE
          WHEN status = 'pending' THEN 'pending'
          WHEN status = 'running' THEN 'running'
          WHEN status = 'completed' THEN 'completed'
          WHEN status = 'failed' THEN 'failed'
          WHEN status = 'cancelled' THEN 'cancelled'
          ELSE 'pending'
        END as execution_status,
        sent_time, sent_time, send_result, send_error,
        job_id, retry_count, next_retry_time, is_active, metadata, created_at, updated_at
      FROM calendar_reminders_backup;
    `);

    // Migrate calendar_reports
    await queryRunner.query(`
      INSERT INTO calendar_events (
        id, user_id, action_type, title, description, start_time,
        time_zone, status, priority, action_config, execution_status,
        execution_start_time, execution_end_time, execution_result, execution_error,
        job_id, retry_count, next_retry_time, is_active, metadata, created_at, updated_at
      )
      SELECT
        gen_random_uuid(), user_id, 'report', name, description,
        COALESCE(generation_start_time, created_at),
        'Asia/Ho_Chi_Minh',
        CASE
          WHEN status = 'pending' THEN 'scheduled'
          WHEN status = 'running' THEN 'in_progress'
          WHEN status = 'completed' THEN 'completed'
          WHEN status = 'failed' THEN 'failed'
          WHEN status = 'cancelled' THEN 'cancelled'
          ELSE 'scheduled'
        END as status,
        'medium' as priority,
        jsonb_build_object(
          'reportType', report_type,
          'reportConfig', report_config,
          'dataSources', data_sources,
          'reportChannels', report_channels,
          'reportFormat', report_format,
          'reportFilePath', report_file_path,
          'reportUrl', report_url
        ) as action_config,
        CASE
          WHEN status = 'pending' THEN 'pending'
          WHEN status = 'running' THEN 'running'
          WHEN status = 'completed' THEN 'completed'
          WHEN status = 'failed' THEN 'failed'
          WHEN status = 'cancelled' THEN 'cancelled'
          ELSE 'pending'
        END as execution_status,
        generation_start_time, generation_end_time, generation_result, generation_error,
        job_id, retry_count, next_retry_time, true, metadata, created_at, updated_at
      FROM calendar_reports_backup;
    `);
  }

  private async dropOldTables(queryRunner: QueryRunner): Promise<void> {
    // Drop các bảng cũ không cần thiết
    const tablesToDrop = [
      'calendar_tasks',
      'calendar_reminders',
      'calendar_reports',
      'calendar_recurrences',
      'calendar_attendees',
      'calendar_resources',
      'calendar_notification_channels',
    ];

    for (const table of tablesToDrop) {
      try {
        await queryRunner.dropTable(table, true);
      } catch (error) {
        // Ignore nếu bảng không tồn tại
        console.log(`Table ${table} does not exist, skipping...`);
      }
    }
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Khôi phục từ backup
    await queryRunner.dropTable('calendar_execution_history');
    await queryRunner.dropTable('calendar_events');

    // Khôi phục bảng cũ từ backup
    await queryRunner.query(`
      CREATE TABLE calendar_events AS SELECT * FROM calendar_events_backup;
      DROP TABLE calendar_events_backup;
      DROP TABLE calendar_tasks_backup;
      DROP TABLE calendar_reminders_backup;
      DROP TABLE calendar_reports_backup;
    `);
  }
}

/**
 * Interface cho thông tin thanh toán chi tiết
 */
export interface PaymentInfo {
  /**
   * <PERSON><PERSON> chú thanh toán
   */
  note?: string;

  /**
   * Số tiền thanh toán
   */
  amount?: number;

  /**
   * Ph<PERSON><PERSON>ng thức thanh toán (c<PERSON> thể khác với paymentMethod chính)
   */
  method?: string;

  /**
   * Đơn vị tiền tệ
   */
  currency?: string;

  /**
   * Thời gian xác nhận thanh toán
   */
  validatedAt?: string;

  /**
   * ID giao dịch từ payment gateway
   */
  transactionId?: string;

  /**
   * Mã tham chiếu từ ngân hàng
   */
  referenceCode?: string;

  /**
   * Tên gateway thanh toán
   */
  gateway?: string;

  /**
   * Thông tin bổ sung khác
   */
  [key: string]: any;
}

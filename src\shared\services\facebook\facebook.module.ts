import { HttpModule } from '@nestjs/axios';
import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';

import { S3Service } from '../s3.service';
import { FacebookAuthService } from './auth/facebook-auth.service';
import { FacebookAdsService } from './business/facebook-ads.service';
import { FacebookAudiencesService } from './business/facebook-audiences.service';
import { FacebookBusinessApiService } from './business/facebook-business-api.service';
import { FacebookCampaignsService } from './business/facebook-campaigns.service';
import { FacebookConversionsService } from './business/facebook-conversions.service';
import { FacebookCreativesService } from './business/facebook-creatives.service';
import { FacebookInsightsService } from './business/facebook-insights.service';
import { FacebookLeadsService } from './business/facebook-leads.service';
import { FacebookMediaService } from './business/facebook-media.service';
import { FacebookPageService } from './page/facebook-page.service';
import { FacebookPersonalService } from './personal/facebook-personal.service';
import { FacebookWebhookService } from './webhook/facebook-webhook.service';

@Module({
  imports: [
    HttpModule.register({
      timeout: 30000, // 30 seconds
      maxRedirects: 5,
    }),
    ConfigModule,
  ],
  providers: [
    // Core Facebook Business API service
    FacebookBusinessApiService,

    // Specialized services
    FacebookCampaignsService,
    FacebookAdsService,
    FacebookInsightsService,
    FacebookAudiencesService,
    FacebookCreativesService,
    FacebookMediaService,
    FacebookConversionsService,
    FacebookLeadsService,

    // Auth, Page, Personal services
    FacebookAuthService,
    FacebookPageService,
    FacebookPersonalService,
    FacebookWebhookService,

    // Shared services
    S3Service,
  ],
  exports: [
    // Core Facebook Business API service
    FacebookBusinessApiService,

    // Specialized services
    FacebookCampaignsService,
    FacebookAdsService,
    FacebookInsightsService,
    FacebookAudiencesService,
    FacebookCreativesService,
    FacebookMediaService,
    FacebookConversionsService,
    FacebookLeadsService,

    // Auth, Page, Personal services
    FacebookAuthService,
    FacebookPageService,
    FacebookPersonalService,
    FacebookWebhookService,
  ],
})
export class FacebookModule { }

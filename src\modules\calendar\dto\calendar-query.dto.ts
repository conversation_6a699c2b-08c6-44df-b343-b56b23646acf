import { ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsOptional,
  IsDateString,
  IsEnum,
  IsString,
  IsNumber,
  IsBoolean,
  Min,
  Max,
} from 'class-validator';
import { Transform } from 'class-transformer';
import { QueryDto } from '@common/dto';
import {
  CalendarActionType,
  CalendarEventStatus,
  CalendarPriority,
  ExecutionStatus,
} from '../entities';
import {
  TaskStatus,
  ReportType,
  NotificationChannelType,
} from '../constants/calendar.constants';

/**
 * DTO query sự kiện calendar
 */
export class CalendarEventQueryDto extends QueryDto {
  @ApiPropertyOptional({
    description: 'Loại action calendar',
    enum: CalendarActionType,
  })
  @IsOptional()
  @IsEnum(CalendarActionType)
  actionType?: CalendarActionType;

  @ApiPropertyOptional({
    description: 'Trạng thái sự kiện',
    enum: CalendarEventStatus,
  })
  @IsOptional()
  @IsEnum(CalendarEventStatus)
  status?: CalendarEventStatus;

  @ApiPropertyOptional({
    description: '<PERSON><PERSON><PERSON> độ ưu tiên',
    enum: CalendarPriority,
  })
  @IsOptional()
  @IsEnum(CalendarPriority)
  priority?: CalendarPriority;

  @ApiPropertyOptional({ description: 'Thời gian bắt đầu từ (ISO 8601)' })
  @IsOptional()
  @IsDateString()
  startTimeFrom?: string;

  @ApiPropertyOptional({ description: 'Thời gian bắt đầu đến (ISO 8601)' })
  @IsOptional()
  @IsDateString()
  startTimeTo?: string;

  @ApiPropertyOptional({ description: 'Thời gian kết thúc từ (ISO 8601)' })
  @IsOptional()
  @IsDateString()
  endTimeFrom?: string;

  @ApiPropertyOptional({ description: 'Thời gian kết thúc đến (ISO 8601)' })
  @IsOptional()
  @IsDateString()
  endTimeTo?: string;

  @ApiPropertyOptional({ description: 'Có phải sự kiện cả ngày' })
  @IsOptional()
  @Transform(({ value }) => value === 'true')
  @IsBoolean()
  allDay?: boolean;

  @ApiPropertyOptional({ description: 'Có đồng bộ với Google Calendar' })
  @IsOptional()
  @Transform(({ value }) => value === 'true')
  @IsBoolean()
  syncWithGoogle?: boolean;

  @ApiPropertyOptional({ description: 'Tìm kiếm theo tiêu đề hoặc mô tả' })
  @IsOptional()
  @IsString()
  searchText?: string;

  @ApiPropertyOptional({ description: 'Tìm kiếm theo vị trí' })
  @IsOptional()
  @IsString()
  location?: string;

  @ApiPropertyOptional({ description: 'ID sự kiện cha (cho recurring events)' })
  @IsOptional()
  @IsString()
  parentEventId?: string;
}

/**
 * DTO query nhiệm vụ calendar
 */
export class CalendarTaskQueryDto extends CalendarEventQueryDto {
  @ApiPropertyOptional({ description: 'Trạng thái nhiệm vụ', enum: TaskStatus })
  @IsOptional()
  @IsEnum(TaskStatus)
  taskStatus?: TaskStatus;

  @ApiPropertyOptional({ description: 'ID agent thực hiện' })
  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @IsNumber()
  agentId?: number;

  @ApiPropertyOptional({ description: 'ID task workflow' })
  @IsOptional()
  @IsString()
  taskId?: string;

  @ApiPropertyOptional({ description: 'Tìm kiếm theo tên nhiệm vụ' })
  @IsOptional()
  @IsString()
  taskName?: string;
}

/**
 * DTO query báo cáo calendar
 */
export class CalendarReportQueryDto extends CalendarEventQueryDto {
  @ApiPropertyOptional({ description: 'Loại báo cáo', enum: ReportType })
  @IsOptional()
  @IsEnum(ReportType)
  reportType?: ReportType;

  @ApiPropertyOptional({ description: 'Trạng thái báo cáo', enum: TaskStatus })
  @IsOptional()
  @IsEnum(TaskStatus)
  reportStatus?: TaskStatus;

  @ApiPropertyOptional({ description: 'ID template báo cáo' })
  @IsOptional()
  @IsString()
  templateId?: string;

  @ApiPropertyOptional({ description: 'Định dạng báo cáo' })
  @IsOptional()
  @IsString()
  reportFormat?: string;

  @ApiPropertyOptional({ description: 'Tìm kiếm theo tên báo cáo' })
  @IsOptional()
  @IsString()
  reportName?: string;
}

/**
 * DTO query nhắc nhở calendar
 */
export class CalendarReminderQueryDto extends QueryDto {
  @ApiPropertyOptional({ description: 'ID sự kiện' })
  @IsOptional()
  @IsString()
  eventId?: string;

  @ApiPropertyOptional({
    description: 'Kênh thông báo',
    enum: NotificationChannelType,
  })
  @IsOptional()
  @IsEnum(NotificationChannelType)
  channelType?: NotificationChannelType;

  @ApiPropertyOptional({ description: 'Trạng thái nhắc nhở', enum: TaskStatus })
  @IsOptional()
  @IsEnum(TaskStatus)
  status?: TaskStatus;

  @ApiPropertyOptional({ description: 'Thời gian nhắc nhở từ (ISO 8601)' })
  @IsOptional()
  @IsDateString()
  reminderTimeFrom?: string;

  @ApiPropertyOptional({ description: 'Thời gian nhắc nhở đến (ISO 8601)' })
  @IsOptional()
  @IsDateString()
  reminderTimeTo?: string;

  @ApiPropertyOptional({ description: 'Có được kích hoạt' })
  @IsOptional()
  @Transform(({ value }) => value === 'true')
  @IsBoolean()
  isActive?: boolean;
}

/**
 * DTO query người tham gia
 */
export class CalendarAttendeeQueryDto extends QueryDto {
  @ApiPropertyOptional({ description: 'ID sự kiện' })
  @IsOptional()
  @IsString()
  eventId?: string;

  @ApiPropertyOptional({ description: 'Email người tham gia' })
  @IsOptional()
  @IsString()
  email?: string;

  @ApiPropertyOptional({ description: 'Trạng thái tham gia' })
  @IsOptional()
  @IsString()
  status?: string;

  @ApiPropertyOptional({ description: 'Vai trò' })
  @IsOptional()
  @IsString()
  role?: string;

  @ApiPropertyOptional({ description: 'Có bắt buộc tham gia' })
  @IsOptional()
  @Transform(({ value }) => value === 'true')
  @IsBoolean()
  isRequired?: boolean;
}

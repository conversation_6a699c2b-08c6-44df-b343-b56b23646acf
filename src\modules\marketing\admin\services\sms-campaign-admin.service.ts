import { Injectable, Logger } from '@nestjs/common';
import { InjectQueue } from '@nestjs/bullmq';
import { Queue } from 'bullmq';
import { SelectQueryBuilder, In } from 'typeorm';
import { AppException, ErrorCode } from '@/common/exceptions';
import { PaginatedResult } from '@/common/response';
import { QueueName, SmsJobName } from '@/shared/queue/queue.constants';
import { SmsCampaignAdminRepository } from '../../../integration/repositories/sms-campaign-admin.repository';
import {
  SmsCampaignAdmin,
  SmsCampaignAdminStatus,
  SmsCampaignAdminType,
} from '../../../integration/entities/sms-campaign-admin.entity';
import { UserRepository } from '@/modules/user/repositories';
import { User } from '@/modules/user/entities';
import { IntegrationRepository } from '../../../integration/repositories';
import { SmsServerConfigurationMigrationService } from '../../../integration/services/sms-server-configuration-migration.service';
import { OwnedTypeEnum } from '../../../integration/enums';
import { AdminSegmentRepository } from '@/modules/marketing/admin/repositories';
import { AdminAudienceRepository } from '@/modules/marketing/admin/repositories';
import { AdminTemplateSmsRepository } from '@/modules/marketing/admin/repositories';
import { AdminAudience } from '@/modules/marketing/admin/entities';
import { SenderTypeEnum } from '@/shared/enums/sender-type.enum';

import {
  SmsCampaignAdminQueryDto,
  SmsCampaignAdminResponseDto,
  SmsCampaignAdminOverviewDto,
  CreateSmsCampaignAdminDto,
  CreateSmsCampaignAdminResponseDto,
} from '../../../integration/admin/dto';

/**
 * Service xử lý SMS Campaign Admin
 */
@Injectable()
export class SmsCampaignAdminService {
  private readonly logger = new Logger(SmsCampaignAdminService.name);

  constructor(
    private readonly smsCampaignAdminRepository: SmsCampaignAdminRepository,
    private readonly userRepository: UserRepository,
    private readonly integrationRepository: IntegrationRepository,
    private readonly smsConfigMigrationService: SmsServerConfigurationMigrationService,
    private readonly adminSegmentRepository: AdminSegmentRepository,
    private readonly adminAudienceRepository: AdminAudienceRepository,
    private readonly adminTemplateSmsRepository: AdminTemplateSmsRepository,
    @InjectQueue(QueueName.SMS_MARKETING) private readonly smsQueue: Queue,
  ) {}

  /**
   * Tạo SMS campaign admin mới
   */
  async createSmsCampaign(
    employeeId: number,
    createDto: CreateSmsCampaignAdminDto,
  ): Promise<CreateSmsCampaignAdminResponseDto> {
    try {
      this.logger.log(
        `Employee ${employeeId} creating SMS campaign: ${createDto.name}`,
      );

      // Debug logging
      this.logger.debug(`Campaign type: ${createDto.campaignType}, scheduledAt: ${createDto.scheduledAt}`);
      this.logger.debug(`SmsCampaignAdminType.ADS: ${SmsCampaignAdminType.ADS}`);
      this.logger.debug(`Comparison result: ${createDto.campaignType === SmsCampaignAdminType.ADS}`);

      // Validation cho ADS campaign bắt buộc phải có scheduledAt
      if (createDto.campaignType === SmsCampaignAdminType.ADS && !createDto.scheduledAt) {
        throw new AppException(
          new ErrorCode(
            400,
            'ADS campaign bắt buộc phải có scheduledAt',
            400,
          ),
          'ADS campaign bắt buộc phải có scheduledAt',
        );
      }

      // Validate scheduledAt nếu có (frontend gửi seconds, cần chuyển thành milliseconds)
      if (createDto.scheduledAt && createDto.scheduledAt * 1000 <= Date.now()) {
        throw new AppException(
          new ErrorCode(
            400,
            'Thời gian lên lịch phải lớn hơn thời gian hiện tại',
            400,
          ),
          'Thời gian lên lịch phải lớn hơn thời gian hiện tại',
        );
      }

      // Validate audience, segment, users hoặc phoneNumbers
      if (
        (!createDto.segmentIds || createDto.segmentIds.length === 0) &&
        (!createDto.audienceIds || createDto.audienceIds.length === 0) &&
        (!createDto.userIds || createDto.userIds.length === 0) &&
        (!createDto.phoneNumbers || createDto.phoneNumbers.length === 0)
      ) {
        throw new AppException(
          new ErrorCode(
            400,
            'Phải chọn segment, audience, users hoặc phoneNumbers để gửi SMS',
            400,
          ),
          'Phải chọn segment, audience, users hoặc phoneNumbers để gửi SMS',
        );
      }

      // Validate templateId
      if (!createDto.templateId) {
        throw new AppException(
          new ErrorCode(400, 'Phải chọn template SMS', 400),
          'Phải chọn template SMS',
        );
      }

      // Lấy template từ database
      const template = await this.adminTemplateSmsRepository.findOne({
        where: { id: createDto.templateId },
      });

      if (!template) {
        throw new AppException(
          new ErrorCode(
            400,
            `Không tìm thấy template SMS với ID ${createDto.templateId}`,
            400,
          ),
          `Không tìm thấy template SMS với ID ${createDto.templateId}`,
        );
      }

      // Lấy SMS server config từ serverId hoặc mặc định cho admin
      const smsServerConfig = await this.getSmsServerConfig(createDto.serverId);
      if (!smsServerConfig) {
        throw new AppException(
          new ErrorCode(400, 'Không tìm thấy cấu hình SMS server', 400),
          'Không tìm thấy cấu hình SMS server',
        );
      }

      // Tính toán danh sách recipients trước khi tạo campaign
      const recipients = await this.getRecipientsFromSegmentOrAudience(
        createDto.segmentIds,
        createDto.audienceIds,
        createDto.userIds,
        createDto.phoneNumbers,
      );

      if (recipients.length === 0) {
        throw new AppException(
          new ErrorCode(400, 'Không tìm thấy người nhận nào', 400),
          'Không tìm thấy người nhận nào',
        );
      }

      // Tạo nội dung SMS từ template và placeholders
      const smsContent = this.processTemplate(
        template.content,
        createDto.placeholders || {},
      );

      // Tạo campaign data với SMS server config và recipients đã tính toán
      const campaignData: Partial<SmsCampaignAdmin> = {
        employeeId,
        name: createDto.name,
        description: createDto.description,
        // Lưu tất cả nguồn recipients dưới dạng JSON trong trường segment
        segment: {
          segmentIds: createDto.segmentIds || [],
          audienceIds: createDto.audienceIds || [],
          userIds: createDto.userIds || [],
          phoneNumbers: createDto.phoneNumbers || [],
        },
        // Lưu danh sách số điện thoại đã tính toán (sau khi lọc duplicate)
        audiences: recipients.map(recipient => ({
          name: `Phone ${recipient.phone}`,
          phoneNumber: recipient.phone,
          countryCode: 84, // Default Vietnam
        })),
        campaignType: createDto.campaignType,
        scheduledAt: createDto.scheduledAt
          ? createDto.scheduledAt * 1000
          : null, // Convert seconds to milliseconds
        content: smsContent, // Sử dụng nội dung đã được xử lý từ template
        status: createDto.scheduledAt
          ? SmsCampaignAdminStatus.SCHEDULED
          : SmsCampaignAdminStatus.SENDING, // Gửi ngay lập tức
        totalRecipients: recipients.length,
        sentCount: 0,
        failedCount: 0,

        smsServer: smsServerConfig, // Lưu SMS server config vào campaign
        createdAt: Date.now(), // Timestamp hiện tại
        updatedAt: Date.now(), // Timestamp hiện tại
      };

      // Lưu campaign
      const savedCampaign =
        await this.smsCampaignAdminRepository.create(campaignData);

      // Tạo job và đẩy vào queue với dữ liệu đã tính toán sẵn
      const jobIds = await this.createSmsAdminJobs(
        savedCampaign,
        smsContent,
        recipients,
        smsServerConfig,
      );

      // Cập nhật job IDs
      await this.smsCampaignAdminRepository.update(savedCampaign.id, {
        jobIds,
      });

      return {
        campaignId: savedCampaign.id,
        jobCount: jobIds.length,
        jobIds,
        status: savedCampaign.status,
        scheduledAt: savedCampaign.scheduledAt
          ? Math.floor(savedCampaign.scheduledAt / 1000)
          : undefined, // Convert milliseconds to seconds
        totalRecipients: recipients.length,
        campaignType: savedCampaign.campaignType,
      };
    } catch (error) {
      this.logger.error(
        `Error creating SMS campaign: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Không thể tạo SMS campaign',
      );
    }
  }

  /**
   * Lấy danh sách SMS campaign admin với phân trang
   */
  async getCampaigns(
    employeeId: number,
    queryDto: SmsCampaignAdminQueryDto,
  ): Promise<PaginatedResult<SmsCampaignAdminResponseDto>> {
    try {
      this.logger.log(`Employee ${employeeId} getting SMS campaigns list`);

      const result =
        await this.smsCampaignAdminRepository.findAdminSmsCampaigns(queryDto);

      const items = result.items.map((campaign) =>
        this.mapToResponseDto(campaign),
      );

      return {
        items,
        meta: result.meta,
      };
    } catch (error) {
      this.logger.error(
        `Error getting SMS campaigns: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Không thể lấy danh sách SMS campaign',
      );
    }
  }

  /**
   * Lấy chi tiết SMS campaign admin theo ID
   */
  async getSmsCampaignById(
    employeeId: number,
    campaignId: number,
  ): Promise<SmsCampaignAdminResponseDto> {
    try {
      this.logger.log(
        `Employee ${employeeId} getting SMS campaign ${campaignId}`,
      );

      const campaign =
        await this.smsCampaignAdminRepository.findById(campaignId);
      if (!campaign) {
        throw new AppException(
          ErrorCode.RESOURCE_NOT_FOUND,
          `Không tìm thấy SMS campaign với ID ${campaignId}`,
        );
        // throw new NotFoundException(`Campaign với ID ${campaignId} không tồn tại`);
      }

      return this.mapToResponseDto(campaign);
    } catch (error) {
      this.logger.error(
        `Error getting SMS campaign: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Không thể lấy thông tin SMS campaign',
      );
    }
  }

  /**
   * Lấy thống kê overview SMS campaign admin
   */
  async getOverview(employeeId: number): Promise<SmsCampaignAdminOverviewDto> {
    try {
      this.logger.log(`Employee ${employeeId} getting SMS campaign overview`);

      const stats =
        await this.smsCampaignAdminRepository.getOverviewStatistics();

      // Tính toán các thống kê bổ sung
      const overallSuccessRate =
        stats.totalSent + stats.totalFailed > 0
          ? (stats.totalSent / (stats.totalSent + stats.totalFailed)) * 100
          : 0;

      // Tính toán chi phí từ SMS pricing thực tế
      const { totalCost, averageCostPerMessage } = await this.calculateSmsCosts(
        stats.totalSent,
      );

      // Lấy số SMS server đang hoạt động từ integration table
      const activeServers = await this.getActiveSmsServersCount();

      return {
        totalCampaigns: stats.totalCampaigns,
        draftCampaigns: stats.draftCampaigns,
        scheduledCampaigns: stats.scheduledCampaigns,
        sendingCampaigns: stats.sendingCampaigns,
        sentCampaigns: stats.sentCampaigns,
        failedCampaigns: stats.failedCampaigns,
        totalSent: stats.totalSent,
        totalFailed: stats.totalFailed,
        overallSuccessRate: Math.round(overallSuccessRate * 100) / 100,
        activeServers,
        totalCost,
        averageCostPerMessage: Math.round(averageCostPerMessage),
        recentCampaigns: stats.recentCampaigns,
        campaignTypeStatistics: stats.campaignTypeStatistics,
        employeeStatistics: stats.employeeStatistics,
      };
    } catch (error) {
      this.logger.error(
        `Error getting SMS campaign overview: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Không thể lấy thống kê SMS campaign',
      );
    }
  }

  /**
   * Hủy SMS campaign admin
   */
  async cancelCampaign(employeeId: number, campaignId: number): Promise<void> {
    try {
      this.logger.log(
        `Employee ${employeeId} cancelling SMS campaign ${campaignId}`,
      );

      const campaign =
        await this.smsCampaignAdminRepository.findById(campaignId);
      if (!campaign) {
        throw new AppException(
          ErrorCode.RESOURCE_NOT_FOUND,
          `Không tìm thấy SMS campaign với ID ${campaignId}`,
        );
        // throw new NotFoundException(`Campaign với ID ${campaignId} không tồn tại`);
      }

      // Kiểm tra trạng thái campaign có thể hủy không
      if (
        campaign.status === SmsCampaignAdminStatus.SENT ||
        campaign.status === SmsCampaignAdminStatus.CANCELLED
      ) {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          `Không thể hủy campaign với trạng thái ${campaign.status}`,
        );
      }

      // Hủy job trong queue nếu campaign đang chạy
      if (campaign.jobIds && campaign.jobIds.length > 0) {
        await this.cancelJobs(campaign.jobIds);
      }

      // Cập nhật trạng thái campaign
      await this.smsCampaignAdminRepository.update(campaignId, {
        status: SmsCampaignAdminStatus.CANCELLED,
      });
    } catch (error) {
      this.logger.error(
        `Error cancelling SMS campaign: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException || error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Không thể hủy SMS campaign',
      );
    }
  }

  /**
   * Xóa SMS campaign admin
   */
  async deleteCampaign(employeeId: number, campaignId: number): Promise<void> {
    try {
      this.logger.log(
        `Employee ${employeeId} deleting SMS campaign ${campaignId}`,
      );

      const campaign =
        await this.smsCampaignAdminRepository.findById(campaignId);
      if (!campaign) {
        throw new AppException(
          ErrorCode.RESOURCE_NOT_FOUND,
          `Không tìm thấy SMS campaign với ID ${campaignId}`,
        );
        // throw new NotFoundException(`Campaign với ID ${campaignId} không tồn tại`);
      }

      // Hủy job trong queue nếu campaign đang chạy hoặc đã lên lịch
      if (campaign.jobIds && campaign.jobIds.length > 0) {
        this.logger.log(
          `Cancelling ${campaign.jobIds.length} jobs for campaign ${campaignId}`,
        );
        await this.cancelJobs(campaign.jobIds);
      }

      await this.smsCampaignAdminRepository.delete(campaignId);
    } catch (error) {
      this.logger.error(
        `Error deleting SMS campaign: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Không thể xóa SMS campaign',
      );
    }
  }

  /**
   * Xóa nhiều SMS campaign admin
   */
  async bulkDeleteCampaigns(
    employeeId: number,
    ids: number[],
  ): Promise<{
    deletedCount: number;
    failedCount: number;
    deletedIds: number[];
    failedIds: number[];
    message: string;
  }> {
    this.logger.log(
      `Employee ${employeeId} bulk deleting SMS campaigns: ${ids.join(', ')}`,
    );

    const deletedIds: number[] = [];
    const failedIds: number[] = [];

    for (const id of ids) {
      try {
        await this.deleteCampaign(employeeId, id);
        deletedIds.push(id);
      } catch (error) {
        this.logger.warn(`Failed to delete campaign ${id}: ${error.message}`);
        failedIds.push(id);
      }
    }

    const deletedCount = deletedIds.length;
    const failedCount = failedIds.length;
    const message = `Đã xóa ${deletedCount} SMS campaign thành công${failedCount > 0 ? `, ${failedCount} campaign không thể xóa` : ''}`;

    return {
      deletedCount,
      failedCount,
      deletedIds,
      failedIds,
      message,
    };
  }

  /**
   * Lấy danh sách recipients từ segmentIds, audienceIds, userIds và phoneNumbers (có thể kết hợp cả 4)
   */
  private async getRecipientsFromSegmentOrAudience(
    segmentIds?: number[],
    audienceIds?: number[],
    userIds?: number[],
    phoneNumbers?: string[],
  ): Promise<
    Array<{
      phone: string;
    }>
  > {
    const recipients: Array<{
      phone: string;
    }> = [];

    // Set để tránh duplicate phone numbers
    const phoneSet = new Set<string>();

    // Xử lý audienceIds nếu có
    if (audienceIds && audienceIds.length > 0) {
      this.logger.log(
        `Processing ${audienceIds.length} audience IDs: ${audienceIds.join(', ')}`,
      );

      // Lấy audiences từ database theo IDs với các trường cần thiết
      const audiences = await this.adminAudienceRepository.find({
        where: { id: In(audienceIds) },
        select: ['id', 'name', 'email', 'phoneNumber', 'countryCode'],
      });

      this.logger.log(`Found ${audiences.length} audiences from database`);

      if (audiences.length === 0) {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          `Không tìm thấy audience nào với các ID: ${audienceIds.join(', ')}`,
        );
      }

      // Log chi tiết từng audience để debug
      audiences.forEach((audience) => {
        this.logger.debug(
          `Audience ${audience.id}: name=${audience.name}, phone=${audience.phoneNumber}, country=${audience.countryCode}`,
        );
      });

      // Validate audiences có số điện thoại hợp lệ
      const validAudiences = audiences.filter((audience) => {
        const hasPhone =
          audience.phoneNumber && audience.phoneNumber.trim() !== '';
        const hasCountry = audience.countryCode && audience.countryCode > 0;

        if (!hasPhone || !hasCountry) {
          this.logger.warn(
            `Audience ${audience.id} không có số điện thoại hợp lệ: phone=${audience.phoneNumber}, country=${audience.countryCode}`,
          );
        }

        return hasPhone && hasCountry;
      });

      if (validAudiences.length === 0) {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          'Không có audience nào có số điện thoại hợp lệ trong danh sách',
        );
      }

      this.logger.log(
        `Found ${validAudiences.length}/${audiences.length} valid audiences with phone numbers`,
      );

      // Chuyển đổi audiences thành recipients với số điện thoại thật
      for (const audience of validAudiences) {
        const formattedPhone = `${audience.countryCode}${audience.phoneNumber}`;

        // Kiểm tra duplicate
        if (!phoneSet.has(formattedPhone)) {
          phoneSet.add(formattedPhone);
          recipients.push({
            phone: formattedPhone,
          });

          this.logger.debug(
            `Added recipient: ${formattedPhone} (${audience.name})`,
          );
        } else {
          this.logger.debug(
            `Skipped duplicate recipient: ${formattedPhone} (${audience.name})`,
          );
        }
      }
    }

    // Xử lý segmentIds nếu có
    if (segmentIds && segmentIds.length > 0) {
      this.logger.log(
        `Processing ${segmentIds.length} segment IDs: ${segmentIds.join(', ')}`,
      );

      // Lấy segments từ database theo IDs
      const segments = await this.adminSegmentRepository.find({
        where: { id: In(segmentIds) },
        select: ['id', 'name', 'criteria', 'description'],
      });

      this.logger.log(`Found ${segments.length} segments from database`);

      if (segments.length === 0) {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          `Không tìm thấy segment nào với các ID: ${segmentIds.join(', ')}`,
        );
      }

      // Log chi tiết từng segment để debug
      segments.forEach((segment) => {
        this.logger.debug(
          `Segment ${segment.id}: name=${segment.name}, criteria=${JSON.stringify(segment.criteria)}`,
        );
      });

      // Xử lý từng segment để lấy audiences thực tế dựa trên criteria
      for (const segment of segments) {
        try {
          this.logger.log(
            `Processing segment: ${segment.name || segment.id} with criteria`,
          );

          // Thực hiện query audiences dựa trên segment criteria
          const segmentAudiences =
            await this.queryAudiencesFromSegmentCriteria(segment);

          this.logger.log(
            `Segment ${segment.id} returned ${segmentAudiences.length} audiences`,
          );

          // Validate và thêm audiences có số điện thoại hợp lệ
          const validSegmentAudiences = segmentAudiences.filter((audience) => {
            const hasPhone =
              audience.phoneNumber && audience.phoneNumber.trim() !== '';
            const hasCountry = audience.countryCode && audience.countryCode > 0;

            if (!hasPhone || !hasCountry) {
              this.logger.warn(
                `Audience ${audience.id} từ segment ${segment.id} không có số điện thoại hợp lệ: phone=${audience.phoneNumber}, country=${audience.countryCode}`,
              );
            }

            return hasPhone && hasCountry;
          });

          this.logger.log(
            `Segment ${segment.id}: ${validSegmentAudiences.length}/${segmentAudiences.length} audiences có số điện thoại hợp lệ`,
          );

          // Chuyển đổi audiences thành recipients với số điện thoại thật
          for (const audience of validSegmentAudiences) {
            const formattedPhone = `${audience.countryCode}${audience.phoneNumber}`;

            // Kiểm tra duplicate
            if (!phoneSet.has(formattedPhone)) {
              phoneSet.add(formattedPhone);
              recipients.push({
                phone: formattedPhone,
              });

              this.logger.debug(
                `Added recipient from segment ${segment.id}: ${formattedPhone} (${audience.name})`,
              );
            } else {
              this.logger.debug(
                `Skipped duplicate recipient from segment ${segment.id}: ${formattedPhone} (${audience.name})`,
              );
            }
          }
        } catch (error) {
          this.logger.error(
            `Failed to query audiences from segment ${segment.id}: ${error.message}`,
          );
          throw new AppException(
            ErrorCode.INTERNAL_SERVER_ERROR,
            `Không thể lấy danh sách audiences từ segment ${segment.name || segment.id}: ${error.message}`,
          );
        }
      }
    }

    // Xử lý userIds nếu có
    if (userIds && userIds.length > 0) {
      this.logger.log(`Processing ${userIds.length} user IDs`);

      // Lấy users từ database theo userIds
      const users = await this.userRepository.findByIds(userIds);

      if (users.length === 0) {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          'Không tìm thấy user nào với các ID được cung cấp',
        );
      }

      // Validate users có số điện thoại hợp lệ
      const validUsers = users.filter(
        (user) => user.phoneNumber && user.countryCode,
      );

      if (validUsers.length === 0) {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          'Không có user nào có số điện thoại hợp lệ trong danh sách',
        );
      }

      this.logger.log(
        `Found ${validUsers.length} valid users with phone numbers`,
      );

      // Chuyển đổi users thành recipients
      for (const user of validUsers) {
        const formattedPhone = `${user.countryCode}${user.phoneNumber}`;

        // Kiểm tra duplicate
        if (!phoneSet.has(formattedPhone)) {
          phoneSet.add(formattedPhone);
          recipients.push({
            phone: formattedPhone,
          });

          this.logger.debug(
            `Added user recipient: ${formattedPhone} (${user.fullName})`,
          );
        } else {
          this.logger.debug(
            `Skipped duplicate user recipient: ${formattedPhone} (${user.fullName})`,
          );
        }
      }
    }

    // Xử lý phoneNumbers nếu có
    if (phoneNumbers && phoneNumbers.length > 0) {
      this.logger.log(`Processing ${phoneNumbers.length} phone numbers`);
      for (let i = 0; i < phoneNumbers.length; i++) {
        const phoneNumber = phoneNumbers[i];

        // Validate và format phone number
        const cleanPhone = this.validateAndFormatPhoneNumber(phoneNumber);
        if (cleanPhone && !phoneSet.has(cleanPhone)) {
          phoneSet.add(cleanPhone);
          recipients.push({
            phone: cleanPhone,
          });

          this.logger.debug(`Added direct phone recipient: ${cleanPhone}`);
        } else if (cleanPhone && phoneSet.has(cleanPhone)) {
          this.logger.debug(`Skipped duplicate phone number: ${cleanPhone}`);
        } else {
          this.logger.warn(`Invalid phone number format: ${phoneNumber}`);
        }
      }
    }

    this.logger.log(`Total recipients processed: ${recipients.length}`);
    return recipients;
  }

  /**
   * Validate và format phone number (không thêm dấu +)
   */
  private validateAndFormatPhoneNumber(phoneNumber: string): string | null {
    if (!phoneNumber || typeof phoneNumber !== 'string') {
      return null;
    }

    // Remove all non-digit characters
    const cleanPhone = phoneNumber.replace(/[^\d]/g, '');

    // If starts with 84, keep as is
    if (cleanPhone.startsWith('84') && cleanPhone.length >= 11) {
      return cleanPhone;
    }
    // If starts with 0, replace with 84
    else if (cleanPhone.startsWith('0') && cleanPhone.length >= 10) {
      return `84${cleanPhone.substring(1)}`;
    }
    // If just digits and length >= 9, assume Vietnam number
    else if (cleanPhone.length >= 9 && cleanPhone.length <= 10) {
      return `84${cleanPhone}`;
    }

    return null;
  }

  /**
   * Query audiences từ segment criteria - lấy trực tiếp từ bảng admin_audience
   */
  private async queryAudiencesFromSegmentCriteria(
    segment: any,
  ): Promise<any[]> {
    try {
      this.logger.debug(
        `Querying audiences for segment ${segment.id} with criteria: ${JSON.stringify(segment.criteria)}`,
      );

      // Kiểm tra segment có criteria không
      if (!segment.criteria) {
        this.logger.warn(`Segment ${segment.id} không có criteria`);
        return [];
      }

      // Tạo query builder cho AdminAudience với điều kiện cơ bản
      const queryBuilder = this.adminAudienceRepository
        .createQueryBuilder('audience')
        .where('audience.phoneNumber IS NOT NULL')
        .andWhere('audience.countryCode IS NOT NULL')
        .andWhere('audience.phoneNumber != :emptyPhone', { emptyPhone: '' })
        .andWhere('audience.countryCode > :zeroCountry', { zeroCountry: 0 });

      // Áp dụng criteria filter cho audience
      this.applyCriteriaToAudienceQuery(queryBuilder, segment.criteria);

      // Giới hạn số lượng để tránh quá tải
      queryBuilder.limit(1000);

      // Select các trường cần thiết
      queryBuilder.select([
        'audience.id',
        'audience.name',
        'audience.email',
        'audience.phoneNumber',
        'audience.countryCode',
      ]);

      const audiences = await queryBuilder.getMany();
      this.logger.log(
        `Found ${audiences.length} audiences matching segment criteria for segment ${segment.id}`,
      );

      return audiences;
    } catch (error) {
      this.logger.error(
        `Error querying audiences from segment criteria: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Tính toán chi phí SMS dựa trên provider và số lượng
   */
  private async calculateSmsCosts(
    totalSent: number,
  ): Promise<{ totalCost: number; averageCostPerMessage: number }> {
    try {
      if (totalSent === 0) {
        return { totalCost: 0, averageCostPerMessage: 0 };
      }

      // Lấy pricing từ SMS integrations
      const integrations = await this.integrationRepository.find({
        where: { ownedType: OwnedTypeEnum.ADMIN },
      });

      const smsIntegrations = integrations.filter((integration) => {
        const metadata = integration.metadata as any;
        return (
          metadata &&
          ['FPT_SMS', 'TWILIO', 'VONAGE', 'SPEED_SMS'].includes(
            metadata.provider,
          )
        );
      });

      let totalCost = 0;
      let averageCostPerMessage = 0;

      if (smsIntegrations.length > 0) {
        // Tính toán dựa trên provider thực tế
        const providerCosts = smsIntegrations.map((integration) => {
          const metadata = integration.metadata as any;
          const provider = metadata.provider;

          // Pricing theo provider (VND)
          switch (provider) {
            case 'FPT_SMS':
              return 50; // 50 VND/SMS
            case 'TWILIO':
              return 100; // ~100 VND/SMS (tùy destination)
            case 'VONAGE':
              return 80; // ~80 VND/SMS
            case 'SPEED_SMS':
              return 45; // 45 VND/SMS
            default:
              return 50; // Default
          }
        });

        // Lấy giá trung bình
        averageCostPerMessage =
          providerCosts.reduce((sum, cost) => sum + cost, 0) /
          providerCosts.length;
        totalCost = totalSent * averageCostPerMessage;
      } else {
        // Fallback pricing nếu không có integration
        averageCostPerMessage = 50; // 50 VND/SMS default
        totalCost = totalSent * averageCostPerMessage;
      }

      this.logger.log(
        `Calculated SMS costs: ${totalCost} VND for ${totalSent} messages (avg: ${averageCostPerMessage} VND/SMS)`,
      );

      return { totalCost, averageCostPerMessage };
    } catch (error) {
      this.logger.error(
        `Error calculating SMS costs: ${error.message}`,
        error.stack,
      );
      // Fallback to default pricing
      const averageCostPerMessage = 50;
      const totalCost = totalSent * averageCostPerMessage;
      return { totalCost, averageCostPerMessage };
    }
  }

  /**
   * Đếm số SMS servers đang hoạt động
   */
  private async getActiveSmsServersCount(): Promise<number> {
    try {
      const integrations = await this.integrationRepository.find({
        where: { ownedType: OwnedTypeEnum.ADMIN },
      });

      const activeSmsIntegrations = integrations.filter((integration) => {
        const metadata = integration.metadata as any;
        return (
          metadata &&
          ['FPT_SMS', 'TWILIO', 'VONAGE', 'SPEED_SMS'].includes(
            metadata.provider,
          ) &&
          integration.encryptedConfig
        ); // Có config thì coi như active
      });

      this.logger.log(
        `Found ${activeSmsIntegrations.length} active SMS servers`,
      );
      return activeSmsIntegrations.length;
    } catch (error) {
      this.logger.error(
        `Error counting active SMS servers: ${error.message}`,
        error.stack,
      );
      return 0;
    }
  }

  /**
   * Lấy SMS server config từ serverId hoặc mặc định cho admin
   */
  private async getSmsServerConfig(
    serverId?: string,
  ): Promise<Record<string, any> | null> {
    if (serverId) {
      // Nếu có serverId, lấy config từ integration cụ thể
      return this.getSmsServerConfigFromIntegration(serverId);
    } else {
      // Nếu không có serverId, sử dụng config mặc định
      return this.getDefaultSmsServerConfig();
    }
  }

  /**
   * Lấy SMS server config từ integration ID cụ thể
   */
  private async getSmsServerConfigFromIntegration(
    integrationId: string,
  ): Promise<Record<string, any> | null> {
    try {
      this.logger.log(
        `Getting SMS server config from integration: ${integrationId}`,
      );

      // Lấy integration từ database
      const integration = await this.integrationRepository.findOne({
        where: { id: integrationId },
      });

      if (!integration) {
        this.logger.warn(`Integration ${integrationId} not found`);
        throw new AppException(
          new ErrorCode(
            400,
            `Không tìm thấy SMS server với ID ${integrationId}`,
            400,
          ),
          `Không tìm thấy SMS server với ID ${integrationId}`,
        );
      }

      // Kiểm tra xem integration có phải là SMS provider không
      const metadata = integration.metadata as any;
      if (
        !metadata ||
        !['FPT_SMS', 'TWILIO', 'VONAGE', 'SPEED_SMS'].includes(
          metadata.provider,
        )
      ) {
        this.logger.warn(`Integration ${integrationId} is not a SMS provider`);
        throw new AppException(
          new ErrorCode(
            400,
            `Integration ${integrationId} không phải là SMS server`,
            400,
          ),
          `Integration ${integrationId} không phải là SMS server`,
        );
      }

      try {
        // Lấy SMS config từ integration với decryption
        const smsConfig =
          await this.smsConfigMigrationService.getSmsConfigurationFromIntegration(
            integrationId,
          );

        // Chuyển đổi sang format phù hợp cho worker
        return {
          provider: smsConfig.providerName,
          providerName: smsConfig.providerName,
          apiUrl: smsConfig.endpoint,
          endpoint: smsConfig.endpoint,
          serverId: integrationId, // Lưu serverId trong config
          integrationId: integrationId,
          additionalSettings: smsConfig.additionalSettings || {},
          apiKey: (smsConfig.additionalSettings as any)?.apiKey,
          apiSecret: (smsConfig.additionalSettings as any)?.apiSecret,
          isDefault: false,
        };
      } catch (decryptError) {
        this.logger.error(
          `Failed to decrypt SMS config for integration ${integrationId}: ${decryptError.message}`,
        );
        throw new AppException(
          new ErrorCode(
            400,
            `Không thể giải mã cấu hình SMS server ${integrationId}`,
            400,
          ),
          `Không thể giải mã cấu hình SMS server ${integrationId}`,
        );
      }
    } catch (error) {
      this.logger.error(
        `Error getting SMS server config from integration: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Không thể lấy cấu hình SMS server',
      );
    }
  }

  /**
   * Lấy SMS server config mặc định cho admin
   */
  private async getDefaultSmsServerConfig(): Promise<Record<
    string,
    any
  > | null> {
    try {
      this.logger.log('Getting default SMS server config for admin');

      // Lấy tất cả SMS integrations của admin
      const integrations = await this.integrationRepository.find({
        where: { ownedType: OwnedTypeEnum.ADMIN },
      });

      // Lọc các SMS integrations
      const smsIntegrations = integrations.filter((integration) => {
        const metadata = integration.metadata as any;
        return (
          metadata &&
          ['FPT_SMS', 'TWILIO', 'VONAGE', 'SPEED_SMS'].includes(
            metadata.provider,
          )
        );
      });

      if (smsIntegrations.length === 0) {
        this.logger.warn(
          'No SMS integrations found for admin, using default config',
        );
        // Return default config nếu không có integration nào
        return {
          provider: 'FPT_SMS',
          providerName: 'FPT_SMS',
          apiUrl: 'https://api01.sms.fpt.net',
          endpoint: 'https://api01.sms.fpt.net',
          brandName: 'REDAI',
          additionalSettings: {
            brandName: 'REDAI',
            provider: 'FPT_SMS',
          },
          isDefault: true,
        };
      }

      // Lấy integration đầu tiên (có thể cải thiện logic chọn integration)
      const selectedIntegration = smsIntegrations[0];
      this.logger.log(`Using SMS integration: ${selectedIntegration.id}`);

      try {
        // Lấy SMS config từ integration
        const smsConfig =
          await this.smsConfigMigrationService.getSmsConfigurationFromIntegration(
            selectedIntegration.id,
          );

        // Chuyển đổi sang format phù hợp cho worker
        return {
          provider: smsConfig.providerName,
          providerName: smsConfig.providerName, // For backward compatibility
          apiUrl: smsConfig.endpoint,
          endpoint: smsConfig.endpoint, // For backward compatibility
          integrationId: selectedIntegration.id,
          additionalSettings: smsConfig.additionalSettings || {},
          apiKey: (smsConfig.additionalSettings as any)?.apiKey,
          apiSecret: (smsConfig.additionalSettings as any)?.apiSecret,
          isDefault: false,
        };
      } catch (decryptError) {
        this.logger.error(
          `Failed to decrypt SMS config for integration ${selectedIntegration.id}: ${decryptError.message}`,
        );

        // Fallback to default config
        return {
          provider: 'FPT_SMS',
          providerName: 'FPT_SMS',
          apiUrl: 'https://api01.sms.fpt.net',
          endpoint: 'https://api01.sms.fpt.net',
          brandName: 'REDAI',
          additionalSettings: {
            brandName: 'REDAI',
            provider: 'FPT_SMS',
          },
          isDefault: true,
          error: 'Failed to decrypt integration config',
        };
      }
    } catch (error) {
      this.logger.error(
        `Error getting default SMS server config: ${error.message}`,
        error.stack,
      );

      // Fallback to default config
      return {
        provider: 'FPT_SMS',
        providerName: 'FPT_SMS',
        apiUrl: 'https://api01.sms.fpt.net',
        endpoint: 'https://api01.sms.fpt.net',
        brandName: 'REDAI',
        additionalSettings: {
          brandName: 'REDAI',
          provider: 'FPT_SMS',
        },
        isDefault: true,
        error: error.message,
      };
    }
  }

  /**
   * Tạo SMS jobs và đẩy vào queue với dữ liệu đã tính toán sẵn
   */
  private async createSmsAdminJobs(
    campaign: SmsCampaignAdmin,
    smsContent: string,
    recipients: Array<{
      phone: string;
    }>,
    smsServerConfig: Record<string, any>,
  ): Promise<string[]> {
    // Tạo job data với tất cả thông tin đã được tính toán trước
    const jobData = {
      campaignId: campaign.id,
      recipients: recipients, // Recipients đã được tính toán sẵn
      smsServerConfig: smsServerConfig, // SMS server config đã được lấy sẵn và giải mã
      content: smsContent, // Sử dụng nội dung đã được xử lý từ template
      campaignType: campaign.campaignType,
      campaignName: campaign.name,
      createdAt: Date.now(),
      isAdminCampaign: true, // Flag để phân biệt với user campaign
      senderType: SenderTypeEnum.EMPLOYEE, // SMS được gửi bởi employee
      senderId: campaign.employeeId, // ID của employee
    };

    this.logger.log(
      `Creating SMS admin job for campaign ${campaign.id} with ${recipients.length} recipients`,
    );

    const now = Date.now();
    // Đối với ADS campaign, không delay job - gửi ngay và để FPT API xử lý theo scheduledAt
    // Đối với OTP campaign, có thể delay nếu cần
    const delay = campaign.campaignType === SmsCampaignAdminType.OTP && campaign.scheduledAt
      ? Math.max(0, campaign.scheduledAt - now)
      : 0; // scheduledAt đã là milliseconds

    // Log job data trước khi add vào queue
    this.logger.log(`Adding SMS admin job to queue with delay: ${delay}ms`);
    this.logger.debug(`Job data: ${JSON.stringify(jobData, null, 2)}`);

    // Sử dụng job name khác cho admin
    const job = await this.smsQueue.add(
      SmsJobName.SMS_MARKETING_ADMIN,
      jobData,
      {
        delay,
        attempts: 3,
        backoff: {
          type: 'exponential',
          delay: 2000,
        },
        removeOnComplete: 100,
        removeOnFail: 50,
      },
    );

    this.logger.log(
      `SMS admin job created with ID: ${job.id} for campaign ${campaign.id}`,
    );

    return [job.id?.toString() || 'unknown'];
  }

  /**
   * Hủy jobs trong queue
   */
  private async cancelJobs(jobIds: string[]): Promise<void> {
    let cancelledCount = 0;
    let failedCount = 0;

    for (const jobId of jobIds) {
      try {
        const job = await this.smsQueue.getJob(jobId);
        if (job) {
          // Kiểm tra trạng thái job trước khi hủy
          const jobState = await job.getState();
          this.logger.log(`Cancelling job ${jobId} with state: ${jobState}`);

          await job.remove();
          cancelledCount++;
          this.logger.log(`Successfully cancelled job ${jobId}`);
        } else {
          this.logger.warn(
            `Job ${jobId} not found in queue (may have already completed)`,
          );
        }
      } catch (error) {
        failedCount++;
        this.logger.warn(`Failed to cancel job ${jobId}: ${error.message}`);
      }
    }

    this.logger.log(
      `Job cancellation summary: ${cancelledCount} cancelled, ${failedCount} failed out of ${jobIds.length} total jobs`,
    );
  }

  /**
   * Map entity sang response DTO
   */
  private mapToResponseDto(
    campaign: SmsCampaignAdmin,
  ): SmsCampaignAdminResponseDto {
    const successRate =
      campaign.totalRecipients > 0
        ? (campaign.sentCount / campaign.totalRecipients) * 100
        : 0;

    return {
      id: campaign.id,
      name: campaign.name,
      description: campaign.description,
      campaignType: campaign.campaignType,
      status: campaign.status,
      scheduledAt: campaign.scheduledAt
        ? Math.floor(campaign.scheduledAt / 1000)
        : null, // Convert milliseconds to seconds
      startedAt: campaign.startedAt
        ? Math.floor(campaign.startedAt / 1000)
        : null,
      completedAt: campaign.completedAt
        ? Math.floor(campaign.completedAt / 1000)
        : null,
      totalRecipients: campaign.totalRecipients,
      sentCount: campaign.sentCount,
      failedCount: campaign.failedCount,
      successRate: Math.round(successRate * 100) / 100,
      externalCampaignCode: campaign.externalCampaignCode,
      content: campaign.content,
      employee: campaign.employee
        ? {
            id: campaign.employee.id,
            email: campaign.employee.email,
            fullName: campaign.employee.fullName || campaign.employee.full_name,
            phone: campaign.employee.phone,
          }
        : undefined,

      smsServer: campaign.smsServer || undefined,
      createdAt: Math.floor(campaign.createdAt / 1000), // Convert milliseconds to seconds
      updatedAt: Math.floor(campaign.updatedAt / 1000),
      deletedAt: campaign.deletedAt
        ? Math.floor(campaign.deletedAt / 1000)
        : null,
    };
  }

  /**
   * Áp dụng criteria vào query builder để lọc user (từ admin-segment.service.ts)
   * @param queryBuilder Query builder cho User
   * @param criteria Criteria để lọc
   */
  private applyCriteriaToUserQuery(
    queryBuilder: SelectQueryBuilder<User>,
    criteria: any,
  ): void {
    if (!criteria) {
      this.logger.warn('Criteria không hợp lệ');
      return;
    }

    // Xử lý criteria theo cấu trúc mới với groups
    if (criteria.groups && Array.isArray(criteria.groups)) {
      this.logger.debug(`Áp dụng ${criteria.groups.length} nhóm điều kiện`);

      for (
        let groupIndex = 0;
        groupIndex < criteria.groups.length;
        groupIndex++
      ) {
        const group = criteria.groups[groupIndex];
        this.applyGroupToUserQuery(queryBuilder, group, groupIndex);
      }
    }
    // Xử lý criteria theo cấu trúc cũ với conditions
    else if (criteria.conditions && Array.isArray(criteria.conditions)) {
      const operator = criteria.operator || 'AND';
      const conditions = criteria.conditions;

      this.logger.debug(
        `Áp dụng ${conditions.length} điều kiện với operator: ${operator}`,
      );

      for (let i = 0; i < conditions.length; i++) {
        const condition = conditions[i];
        const paramKey = `param_${i}`;

        this.applyConditionToUserQuery(
          queryBuilder,
          condition,
          paramKey,
          i === 0 ? 'WHERE' : operator,
        );
      }
    }
  }

  /**
   * Áp dụng một nhóm điều kiện vào query builder cho User (từ admin-segment.service.ts)
   * @param queryBuilder Query builder cho User
   * @param group Nhóm điều kiện
   * @param groupIndex Index của nhóm
   */
  private applyGroupToUserQuery(
    queryBuilder: SelectQueryBuilder<User>,
    group: any,
    groupIndex: number,
  ): void {
    if (!group.conditions || !Array.isArray(group.conditions)) {
      return;
    }

    const logicalOperator = group.logicalOperator || 'AND';
    const conditions = group.conditions;

    this.logger.debug(
      `Áp dụng nhóm ${groupIndex} với ${conditions.length} điều kiện, operator: ${logicalOperator}`,
    );

    for (let i = 0; i < conditions.length; i++) {
      const condition = conditions[i];
      const paramKey = `group_${groupIndex}_param_${i}`;

      // Điều kiện đầu tiên của nhóm đầu tiên dùng WHERE, các điều kiện khác dùng operator
      const clauseType =
        groupIndex === 0 && i === 0 ? 'WHERE' : logicalOperator;

      this.applyConditionToUserQuery(
        queryBuilder,
        condition,
        paramKey,
        clauseType,
      );
    }
  }

  /**
   * Áp dụng một điều kiện cụ thể vào query builder cho User (từ admin-segment.service.ts)
   * @param queryBuilder Query builder cho User
   * @param condition Điều kiện
   * @param paramKey Key cho parameter
   * @param clauseType Loại clause (WHERE, AND, OR)
   */
  private applyConditionToUserQuery(
    queryBuilder: SelectQueryBuilder<User>,
    condition: any,
    paramKey: string,
    clauseType: 'WHERE' | 'AND' | 'OR',
  ): void {
    const { field, operator, value } = condition;

    this.logger.debug(
      `Áp dụng điều kiện User: ${field} ${operator} ${JSON.stringify(value)}`,
    );

    // Mapping field names từ segment criteria sang User entity fields
    const fieldMapping: { [key: string]: string } = {
      email: 'email',
      fullName: 'fullName',
      name: 'fullName',
      phoneNumber: 'phoneNumber',
      phone: 'phoneNumber',
      type: 'type',
      isActive: 'isActive',
      createdAt: 'createdAt',
      updatedAt: 'updatedAt',
      pointsBalance: 'pointsBalance',
      points: 'pointsBalance',
      countryCode: 'countryCode',
      address: 'address',
      taxCode: 'taxCode',
      bankName: 'bankName',
      bankAccount: 'bankAccount',
      bankBranch: 'bankBranch',
      isVerifyPhone: 'isVerifyPhone',
      alertThreshold: 'alertThreshold',
      affiliateAccountId: 'affiliateAccountId',
      isSignatureRuleContract: 'isSignatureRuleContract',
    };

    const mappedField = fieldMapping[field] || field;

    // Xử lý các trường của User entity
    let whereClause = '';
    const params: any = {};

    switch (operator) {
      case 'equals':
      case 'eq':
        whereClause = `user.${mappedField} = :${paramKey}`;
        params[paramKey] = value;
        break;

      case 'not_equals':
      case 'notEquals':
      case 'ne':
        whereClause = `user.${mappedField} != :${paramKey}`;
        params[paramKey] = value;
        break;

      case 'contains':
      case 'like':
        whereClause = `user.${mappedField} LIKE :${paramKey}`;
        params[paramKey] = `%${value}%`;
        break;

      case 'not_contains':
      case 'notContains':
        whereClause = `user.${mappedField} NOT LIKE :${paramKey}`;
        params[paramKey] = `%${value}%`;
        break;

      case 'starts_with':
      case 'startsWith':
        whereClause = `user.${mappedField} LIKE :${paramKey}`;
        params[paramKey] = `${value}%`;
        break;

      case 'ends_with':
      case 'endsWith':
        whereClause = `user.${mappedField} LIKE :${paramKey}`;
        params[paramKey] = `%${value}`;
        break;

      case 'greater_than':
      case 'greaterThan':
      case 'gt':
        whereClause = `user.${mappedField} > :${paramKey}`;
        params[paramKey] = value;
        break;

      case 'greater_than_or_equal':
      case 'greaterThanOrEqual':
      case 'gte':
        whereClause = `user.${mappedField} >= :${paramKey}`;
        params[paramKey] = value;
        break;

      case 'less_than':
      case 'lessThan':
      case 'lt':
        whereClause = `user.${mappedField} < :${paramKey}`;
        params[paramKey] = value;
        break;

      case 'less_than_or_equal':
      case 'lessThanOrEqual':
      case 'lte':
        whereClause = `user.${mappedField} <= :${paramKey}`;
        params[paramKey] = value;
        break;

      case 'in':
        if (Array.isArray(value) && value.length > 0) {
          whereClause = `user.${mappedField} IN (:...${paramKey})`;
          params[paramKey] = value;
        }
        break;

      case 'not_in':
      case 'notIn':
        if (Array.isArray(value) && value.length > 0) {
          whereClause = `user.${mappedField} NOT IN (:...${paramKey})`;
          params[paramKey] = value;
        }
        break;

      case 'is_null':
      case 'isNull':
        whereClause = `user.${mappedField} IS NULL`;
        break;

      case 'is_not_null':
      case 'isNotNull':
        whereClause = `user.${mappedField} IS NOT NULL`;
        break;

      case 'between':
        if (Array.isArray(value) && value.length === 2) {
          whereClause = `user.${mappedField} BETWEEN :${paramKey}_start AND :${paramKey}_end`;
          params[`${paramKey}_start`] = value[0];
          params[`${paramKey}_end`] = value[1];
        }
        break;

      default:
        this.logger.warn(
          `Operator không được hỗ trợ cho User query: ${operator}`,
        );
        return;
    }

    if (whereClause) {
      if (clauseType === 'WHERE') {
        queryBuilder.where(whereClause, params);
      } else if (clauseType === 'AND') {
        queryBuilder.andWhere(whereClause, params);
      } else if (clauseType === 'OR') {
        queryBuilder.orWhere(whereClause, params);
      }
    }
  }

  /**
   * Áp dụng criteria vào query builder để lọc audience
   * @param queryBuilder Query builder cho AdminAudience
   * @param criteria Criteria để lọc
   */
  private applyCriteriaToAudienceQuery(
    queryBuilder: SelectQueryBuilder<AdminAudience>,
    criteria: any,
  ): void {
    if (!criteria) {
      this.logger.warn('Criteria không hợp lệ');
      return;
    }

    this.logger.debug(
      `Applying criteria to audience query: ${JSON.stringify(criteria)}`,
    );

    // Xử lý criteria theo cấu trúc với groups
    if (criteria.groups && Array.isArray(criteria.groups)) {
      this.logger.debug(
        `Áp dụng ${criteria.groups.length} nhóm điều kiện cho audience`,
      );

      for (
        let groupIndex = 0;
        groupIndex < criteria.groups.length;
        groupIndex++
      ) {
        const group = criteria.groups[groupIndex];
        this.applyGroupToAudienceQuery(queryBuilder, group, groupIndex);
      }
    }
    // Xử lý criteria theo cấu trúc với conditions
    else if (criteria.conditions && Array.isArray(criteria.conditions)) {
      const operator = criteria.operator || 'AND';
      const conditions = criteria.conditions;

      this.logger.debug(
        `Áp dụng ${conditions.length} điều kiện cho audience với operator: ${operator}`,
      );

      for (let i = 0; i < conditions.length; i++) {
        const condition = conditions[i];
        const paramKey = `audience_param_${i}`;

        this.applyConditionToAudienceQuery(
          queryBuilder,
          condition,
          paramKey,
          i === 0 ? 'WHERE' : operator,
        );
      }
    }
  }

  /**
   * Áp dụng một nhóm điều kiện vào query builder cho AdminAudience
   * @param queryBuilder Query builder cho AdminAudience
   * @param group Nhóm điều kiện
   * @param groupIndex Index của nhóm
   */
  private applyGroupToAudienceQuery(
    queryBuilder: SelectQueryBuilder<AdminAudience>,
    group: any,
    groupIndex: number,
  ): void {
    if (!group.conditions || !Array.isArray(group.conditions)) {
      return;
    }

    const operator = group.operator || 'AND';
    this.logger.debug(
      `Áp dụng nhóm ${groupIndex} với ${group.conditions.length} điều kiện và operator: ${operator}`,
    );

    for (let i = 0; i < group.conditions.length; i++) {
      const condition = group.conditions[i];
      const paramKey = `audience_group_${groupIndex}_param_${i}`;
      const conditionType = groupIndex === 0 && i === 0 ? 'WHERE' : operator;

      this.applyConditionToAudienceQuery(
        queryBuilder,
        condition,
        paramKey,
        conditionType,
      );
    }
  }

  /**
   * Áp dụng một điều kiện cụ thể vào query builder cho AdminAudience
   * @param queryBuilder Query builder cho AdminAudience
   * @param condition Điều kiện
   * @param paramKey Key cho parameter
   * @param conditionType Loại điều kiện (WHERE, AND, OR)
   */
  private applyConditionToAudienceQuery(
    queryBuilder: SelectQueryBuilder<AdminAudience>,
    condition: any,
    paramKey: string,
    conditionType: string,
  ): void {
    if (!condition.field || !condition.operator) {
      this.logger.warn(`Điều kiện không hợp lệ: ${JSON.stringify(condition)}`);
      return;
    }

    const field = `audience.${condition.field}`;
    const operator = condition.operator;
    const value = condition.value;

    let whereClause = '';
    const params: Record<string, any> = {};

    // Xử lý các operator khác nhau
    switch (operator.toLowerCase()) {
      case 'equals':
      case '=':
        whereClause = `${field} = :${paramKey}`;
        params[paramKey] = value;
        break;
      case 'not_equals':
      case '!=':
        whereClause = `${field} != :${paramKey}`;
        params[paramKey] = value;
        break;
      case 'contains':
      case 'like':
        whereClause = `${field} LIKE :${paramKey}`;
        params[paramKey] = `%${value}%`;
        break;
      case 'not_contains':
      case 'not_like':
        whereClause = `${field} NOT LIKE :${paramKey}`;
        params[paramKey] = `%${value}%`;
        break;
      case 'starts_with':
        whereClause = `${field} LIKE :${paramKey}`;
        params[paramKey] = `${value}%`;
        break;
      case 'ends_with':
        whereClause = `${field} LIKE :${paramKey}`;
        params[paramKey] = `%${value}`;
        break;
      case 'greater_than':
      case '>':
        whereClause = `${field} > :${paramKey}`;
        params[paramKey] = value;
        break;
      case 'less_than':
      case '<':
        whereClause = `${field} < :${paramKey}`;
        params[paramKey] = value;
        break;
      case 'in':
        if (Array.isArray(value)) {
          whereClause = `${field} IN (:...${paramKey})`;
          params[paramKey] = value;
        }
        break;
      case 'not_in':
        if (Array.isArray(value)) {
          whereClause = `${field} NOT IN (:...${paramKey})`;
          params[paramKey] = value;
        }
        break;
      case 'is_null':
        whereClause = `${field} IS NULL`;
        break;
      case 'is_not_null':
        whereClause = `${field} IS NOT NULL`;
        break;
      default:
        this.logger.warn(`Operator không được hỗ trợ: ${operator}`);
        return;
    }

    if (whereClause) {
      // Áp dụng điều kiện vào query builder
      if (conditionType === 'WHERE') {
        queryBuilder.andWhere(whereClause, params);
      } else if (conditionType === 'AND') {
        queryBuilder.andWhere(whereClause, params);
      } else if (conditionType === 'OR') {
        queryBuilder.orWhere(whereClause, params);
      }

      this.logger.debug(
        `Applied condition: ${whereClause} with params: ${JSON.stringify(params)}`,
      );
    }
  }

  /**
   * Xử lý template SMS với placeholders
   * @param templateContent Nội dung template
   * @param placeholders Dữ liệu placeholders
   * @returns Nội dung SMS đã được xử lý
   */
  private processTemplate(
    templateContent: string,
    placeholders: Record<string, any>,
  ): string {
    if (!templateContent) {
      return '';
    }

    let processedContent = templateContent;

    // Thay thế các placeholder theo format {{key}}
    Object.keys(placeholders).forEach((key) => {
      const placeholder = `{{${key}}}`;
      const value = placeholders[key] || '';
      processedContent = processedContent.replace(
        new RegExp(placeholder, 'g'),
        value,
      );
    });

    // Log để debug
    this.logger.debug(
      `Template processed: ${templateContent} -> ${processedContent}`,
    );
    this.logger.debug(`Placeholders used: ${JSON.stringify(placeholders)}`);

    return processedContent;
  }
}

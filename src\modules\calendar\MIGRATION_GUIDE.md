# Calendar Module Migration Guide

## 🎯 Tổng Quan

Calendar module đã được tối ưu hóa từ **8 entity xuống 2 entity** để phù hợp với mục đích chính: **đặt lịch thông báo và kích hoạt trigger**.

## 📊 Thay Đổi Cấu Trúc

### ❌ Entities Đã Xóa (6 entities)
- `calendar-task.entity.ts`
- `calendar-reminder.entity.ts` 
- `calendar-report.entity.ts`
- `calendar-recurrence.entity.ts`
- `calendar-attendee.entity.ts`
- `calendar-resource.entity.ts`
- `calendar-notification-channel.entity.ts`

### ✅ Entities Mới (2 entities)
1. **`calendar-event.entity.ts`** - Entity ch<PERSON>h gộp chung task, reminder, report
2. **`calendar-execution-history.entity.ts`** - Lịch sử thực thi

## 🔄 Mapping Dữ Liệu

| **Entity Cũ** | **Entity Mới** | **Cách <PERSON>** |
|----------------|----------------|----------------------|
| `CalendarEvent` | `CalendarEvent` | Thêm `actionType`, `actionConfig` |
| `CalendarTask` | `CalendarEvent` | `actionType = 'task'`, data → `actionConfig` |
| `CalendarReminder` | `CalendarEvent` | `actionType = 'reminder'`, data → `actionConfig` |
| `CalendarReport` | `CalendarEvent` | `actionType = 'report'`, data → `actionConfig` |
| `CalendarAttendee` | ❌ | Data → `actionConfig.recipients` |
| `CalendarResource` | ❌ | Data → `actionConfig.resources` |
| `CalendarRecurrence` | ❌ | Loại bỏ (đơn giản hóa) |
| `CalendarNotificationChannel` | ❌ | Move sang User settings |

## 🏗️ Cấu Trúc Entity Mới

### CalendarEvent
```typescript
{
  id: string (UUID)
  userId: number
  actionType: 'task' | 'reminder' | 'report'
  title: string
  description?: string
  startTime: Date
  endTime?: Date
  timeZone: string
  status: CalendarEventStatus
  priority: CalendarPriority
  actionConfig: JSONB // Cấu hình cụ thể cho từng loại
  executionStatus: ExecutionStatus
  executionStartTime?: Date
  executionEndTime?: Date
  executionResult?: JSONB
  executionError?: string
  jobId?: string
  retryCount: number
  nextRetryTime?: Date
  isActive: boolean
  metadata?: JSONB
  createdAt: Date
  updatedAt: Date
}
```

### CalendarExecutionHistory
```typescript
{
  id: string (UUID)
  eventId: string
  executionTime: Date
  status: ExecutionHistoryStatus
  startTime?: Date
  endTime?: Date
  duration?: number
  result?: JSONB
  error?: string
  jobId?: string
  retryAttempt: number
  executionConfig?: JSONB
  metadata?: JSONB
  createdAt: Date
}
```

## 📝 ActionConfig Examples

### TASK
```json
{
  "agentId": 123,
  "taskId": "uuid",
  "resources": [{"type": "media", "id": "uuid"}],
  "executionConfig": {"timeout": 300000}
}
```

### REMINDER
```json
{
  "reminderMinutes": 15,
  "channels": ["email", "sms"],
  "message": "Nhắc nhở...",
  "template": {"type": "default"},
  "recipients": [{"email": "<EMAIL>"}]
}
```

### REPORT
```json
{
  "reportType": "sales",
  "format": "pdf",
  "dataSources": [{"type": "database", "query": "..."}],
  "channels": ["email"],
  "recipients": [{"email": "<EMAIL>"}]
}
```

## 🚀 Migration Steps

### 1. Chạy Migration
```bash
npm run migration:run
```

### 2. Cập Nhật Code
- Services: Sử dụng `CalendarEvent` thay vì các entity riêng lẻ
- Controllers: Gộp chung logic xử lý
- DTOs: Cập nhật theo cấu trúc mới

### 3. Testing
- Test migration dữ liệu
- Test các API endpoints
- Test job queue functionality

## ⚠️ Breaking Changes

### APIs Affected
- Tất cả calendar APIs cần cập nhật
- Task/Reminder/Report endpoints có thể gộp chung
- Response format thay đổi

### Services Affected
- `CalendarTaskService` → Gộp vào `CalendarService`
- `CalendarReminderService` → Gộp vào `CalendarService`
- `CalendarReportService` → Gộp vào `CalendarService`
- `RecurrenceEngineService` → Loại bỏ

### Repositories Affected
- Chỉ giữ `CalendarEventRepository`
- Loại bỏ các repository khác

## 🎯 Benefits

1. **Giảm 75% số lượng entity** (8 → 2)
2. **Đơn giản hóa relationships** và queries
3. **Dễ maintain và extend** trong tương lai
4. **Tối ưu performance** với ít JOIN operations
5. **Flexible configuration** với JSONB fields
6. **Unified API** cho tất cả calendar actions

## 🔧 Rollback Plan

Nếu cần rollback:
```bash
npm run migration:revert
```

Migration sẽ khôi phục từ backup tables đã tạo.

## 📞 Support

Nếu gặp vấn đề trong quá trình migration, liên hệ team development để được hỗ trợ.

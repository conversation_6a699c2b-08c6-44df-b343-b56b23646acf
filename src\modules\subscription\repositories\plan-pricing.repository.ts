import { Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { PlanPricing, Plan, PackageOptionAddon, Addon } from '@modules/subscription/entities';
import { QueryDto } from '@common/dto';
import { PaginatedResult } from '@common/response/api-response-dto';
import { SqlHelper } from '@common/helpers';
import { PlanTypeEnum } from '../enums/plan-type.enum';
import { BillingCycle } from '../enums/billing-cycle.enum';

@Injectable()
export class PlanPricingRepository extends Repository<PlanPricing> {
  private sqlHelper: SqlHelper;

  constructor(private dataSource: DataSource) {
    super(PlanPricing, dataSource.createEntityManager());
    this.sqlHelper = new SqlHelper(dataSource, { enableLogging: true });
  }

  /**
   * Tìm kiếm plan pricing với các bộ lọc
   * @param paginationParams Tham số phân trang
   * @returns Kết quả phân trang
   */
  async findPlanPricing(
    paginationParams: QueryDto,
  ): Promise<PaginatedResult<PlanPricing>> {
    return this.sqlHelper.getPaginatedData(this, paginationParams, {
      alias: 'plan_pricing',
    });
  }

  /**
   * Tìm kiếm plan pricing với các bộ lọc tùy chỉnh
   * @param paginationParams Tham số phân trang
   * @param filters Điều kiện lọc
   * @returns Kết quả phân trang
   */
  async findPlanPricingWithFilters(
    paginationParams: QueryDto,
    filters: any = {},
  ): Promise<PaginatedResult<PlanPricing>> {
    return this.sqlHelper.getPaginatedData(this, paginationParams, {
      alias: 'plan_pricing',
      customize: qb => {
        // Áp dụng các filter
        if (filters.planId) {
          qb.andWhere('plan_pricing.plan_id = :planId', { planId: filters.planId });
        }

        if (filters.billingCycle) {
          qb.andWhere('plan_pricing.billing_cycle = :billingCycle', { billingCycle: filters.billingCycle });
        }

        if (filters.isActive !== undefined) {
          qb.andWhere('plan_pricing.is_active = :isActive', { isActive: filters.isActive });
        }

        return qb;
      }
    });
  }

  /**
   * Tìm kiếm plan pricing với thông tin plan và các bộ lọc tùy chỉnh
   * @param paginationParams Tham số phân trang
   * @param filters Điều kiện lọc
   * @returns Kết quả phân trang với thông tin plan
   */
  async findPlanPricingWithPlanAndFilters(
    paginationParams: QueryDto,
    filters: any = {},
  ): Promise<PaginatedResult<any>> {
    const queryBuilder = this.createQueryBuilder('plan_pricing')
      .leftJoin('plans', 'plan', 'plan.id = plan_pricing.plan_id')
      .select([
        // Plan pricing fields
        'plan_pricing.id as plan_pricing_id',
        'plan_pricing.plan_id as plan_pricing_plan_id',
        'plan_pricing.name as plan_pricing_name',
        'plan_pricing.billing_cycle as plan_pricing_billing_cycle',
        'plan_pricing.price as plan_pricing_price',
        'plan_pricing.created_at as plan_pricing_created_at',
        'plan_pricing.updated_at as plan_pricing_updated_at',
        'plan_pricing.is_active as plan_pricing_is_active',
        'plan_pricing.description as plan_pricing_description',
        'plan_pricing.currency as plan_pricing_currency',
        'plan_pricing.feature_information as plan_pricing_feature_information',
        // Plan fields
        'plan.id as plan_id',
        'plan.name as plan_name',
        'plan.description as plan_description',
        'plan.category as plan_category',
        'plan.type as plan_type',
        'plan.is_active as plan_is_active',
        'plan.createdAt as plan_created_at',
        'plan.updatedAt as plan_updated_at',
      ]);

    // Áp dụng các filter
    if (filters.planId) {
      queryBuilder.andWhere('plan_pricing.plan_id = :planId', { planId: filters.planId });
    }

    if (filters.billingCycle) {
      queryBuilder.andWhere('plan_pricing.billing_cycle = :billingCycle', { billingCycle: filters.billingCycle });
    }

    if (filters.isActive !== undefined) {
      queryBuilder.andWhere('plan_pricing.is_active = :isActive', { isActive: filters.isActive });
    }

    // Apply sorting
    if (paginationParams.sortBy && paginationParams.sortDirection) {
      queryBuilder.orderBy(`plan_pricing.${paginationParams.sortBy}`, paginationParams.sortDirection);
    }

    // Apply pagination
    const skip = (paginationParams.page - 1) * paginationParams.limit;
    queryBuilder.skip(skip).take(paginationParams.limit);

    const rawItems = await queryBuilder.getRawMany();
    const totalItems = await queryBuilder.getCount();

    // Transform raw data to proper structure
    const items = rawItems.map(raw => ({
      id: raw.plan_pricing_id,
      planId: raw.plan_pricing_plan_id,
      name: raw.plan_pricing_name,
      billingCycle: raw.plan_pricing_billing_cycle,
      price: raw.plan_pricing_price,
      createdAt: raw.plan_pricing_created_at,
      updatedAt: raw.plan_pricing_updated_at,
      isActive: raw.plan_pricing_is_active,
      description: raw.plan_pricing_description,
      currency: raw.plan_pricing_currency,
      plan: {
        id: raw.plan_id,
        name: raw.plan_name,
        description: raw.plan_description,
        category: raw.plan_category,
        type: raw.plan_type,
        isActive: raw.plan_is_active,
        createdAt: raw.plan_created_at,
        updatedAt: raw.plan_updated_at,
      }
    }));

    return {
      items,
      meta: {
        totalItems,
        itemCount: items.length,
        itemsPerPage: paginationParams.limit,
        totalPages: Math.ceil(totalItems / paginationParams.limit),
        currentPage: paginationParams.page,
      },
    };
  }

  /**
   * Tìm kiếm plan pricing theo billingCycle
   * @param billingCycle Chu kỳ thanh toán
   * @param paginationParams Tham số phân trang
   * @returns Kết quả phân trang
   */
  async findByBillingCycle(
    billingCycle: BillingCycle,
    paginationParams: QueryDto,
  ): Promise<PaginatedResult<PlanPricing>> {
    // Sử dụng helper function để lấy kết quả phân trang
    return this.sqlHelper.getPaginatedData(this, paginationParams, {
      alias: 'plan_pricing',
      customize: qb => {
        qb.where('plan_pricing.billing_cycle = :billingCycle', { billingCycle });
        return qb;
      }
    });
  }

  /**
   * Tìm kiếm plan pricing với thông tin plan và addon
   * @param paginationParams Tham số phân trang
   * @returns Kết quả phân trang với thông tin đầy đủ
   */
  async findPlanPricingWithDetails(
    paginationParams: QueryDto,
  ): Promise<PaginatedResult<any>> {
    const queryBuilder = this.createQueryBuilder('plan_pricing')
      .leftJoin('plans', 'plan', 'plan.id = plan_pricing.plan_id')
      .leftJoin('package_option_addons', 'package_addon', 'package_addon.plan_pricing_id = plan_pricing.id')
      .leftJoin('addons', 'addon', 'addon.id = package_addon.addon_id AND addon.is_active = true')
      .select([
        // Plan pricing fields
        'plan_pricing.id as plan_pricing_id',
        'plan_pricing.plan_id as plan_pricing_plan_id',
        'plan_pricing.billing_cycle as plan_pricing_billing_cycle',
        'plan_pricing.price as plan_pricing_price',
        'plan_pricing.created_at as plan_pricing_created_at',
        'plan_pricing.updated_at as plan_pricing_updated_at',
        'plan_pricing.is_active as plan_pricing_is_active',
        'plan_pricing.description as plan_pricing_description',
        'plan_pricing.currency as plan_pricing_currency',
        'plan_pricing.name as plan_pricing_name',
        'plan_pricing.feature_information as plan_pricing_feature_information',
        // Plan fields
        'plan.id as plan_id',
        'plan.name as plan_name',
        'plan.description as plan_description',
        // TODO: Uncomment after running migration
        // 'plan.type as plan_type',
        // 'plan.is_active as plan_is_active',
        'plan.created_at as plan_created_at',
        'plan.updated_at as plan_updated_at',
        // Package addon fields
        'package_addon.id as package_addon_id',
        'package_addon.addon_id as package_addon_addon_id',
        'package_addon.unit as package_addon_unit',
        'package_addon.duration_in_days as package_addon_duration_in_days',
        'package_addon.quantity as package_addon_quantity',
        // Addon fields
        'addon.id as addon_id',
        'addon.name as addon_name',
        'addon.description as addon_description',
        'addon.billing_type as addon_billing_type',
        'addon.monthly_price as addon_monthly_price',
        'addon.volume_unit as addon_volume_unit',
        'addon.price_per_unit as addon_price_per_unit',
        'addon.is_active as addon_is_active',
        'addon.created_at as addon_created_at',
      ])
      .where('plan_pricing.is_active = :isActive', { isActive: true });

    // Apply sorting
    if (paginationParams.sortBy && paginationParams.sortDirection) {
      queryBuilder.orderBy(`plan_pricing.${paginationParams.sortBy}`, paginationParams.sortDirection);
    }

    // Apply pagination
    const skip = (paginationParams.page - 1) * paginationParams.limit;
    queryBuilder.skip(skip).take(paginationParams.limit);

    const items = await queryBuilder.getRawMany();
    const totalItems = await queryBuilder.getCount();

    return {
      items,
      meta: {
        totalItems,
        itemCount: items.length,
        itemsPerPage: paginationParams.limit,
        totalPages: Math.ceil(totalItems / paginationParams.limit),
        currentPage: paginationParams.page,
      },
    };
  }

  /**
   * Tìm kiếm plan pricing với thông tin plan và addon theo billingCycle
   * @param billingCycle Chu kỳ thanh toán
   * @param paginationParams Tham số phân trang
   * @returns Kết quả phân trang với thông tin đầy đủ
   */
  async findPlanPricingWithDetailsByBillingCycle(
    billingCycle: BillingCycle,
    paginationParams: QueryDto,
  ): Promise<PaginatedResult<any>> {
    const queryBuilder = this.createQueryBuilder('plan_pricing')
      .leftJoin('plans', 'plan', 'plan.id = plan_pricing.plan_id')
      .leftJoin('package_option_addons', 'package_addon', 'package_addon.plan_pricing_id = plan_pricing.id')
      .leftJoin('addons', 'addon', 'addon.id = package_addon.addon_id AND addon.is_active = true')
      .select([
        // Plan pricing fields
        'plan_pricing.id as plan_pricing_id',
        'plan_pricing.plan_id as plan_pricing_plan_id',
        'plan_pricing.billing_cycle as plan_pricing_billing_cycle',
        'plan_pricing.price as plan_pricing_price',
        'plan_pricing.created_at as plan_pricing_created_at',
        'plan_pricing.updated_at as plan_pricing_updated_at',
        'plan_pricing.is_active as plan_pricing_is_active',
        'plan_pricing.description as plan_pricing_description',
        'plan_pricing.currency as plan_pricing_currency',
        'plan_pricing.name as plan_pricing_name',
        'plan_pricing.feature_information as plan_pricing_feature_information',
        // Plan fields
        'plan.id as plan_id',
        'plan.name as plan_name',
        'plan.description as plan_description',
        'plan.type as plan_type',
        'plan.is_active as plan_is_active',
        'plan.created_at as plan_created_at',
        'plan.updated_at as plan_updated_at',
        // Package addon fields
        'package_addon.id as package_addon_id',
        'package_addon.addon_id as package_addon_addon_id',
        'package_addon.unit as package_addon_unit',
        'package_addon.duration_in_days as package_addon_duration_in_days',
        'package_addon.quantity as package_addon_quantity',
        // Addon fields
        'addon.id as addon_id',
        'addon.name as addon_name',
        'addon.description as addon_description',
        'addon.billing_type as addon_billing_type',
        'addon.monthly_price as addon_monthly_price',
        'addon.volume_unit as addon_volume_unit',
        'addon.price_per_unit as addon_price_per_unit',
        'addon.is_active as addon_is_active',
        'addon.created_at as addon_created_at',
      ])
      .where('plan_pricing.is_active = :isActive', { isActive: true })
      .andWhere('plan_pricing.billing_cycle = :billingCycle', { billingCycle });

    // Apply sorting
    if (paginationParams.sortBy && paginationParams.sortDirection) {
      queryBuilder.orderBy(`plan_pricing.${paginationParams.sortBy}`, paginationParams.sortDirection);
    }

    // Apply pagination
    const skip = (paginationParams.page - 1) * paginationParams.limit;
    queryBuilder.skip(skip).take(paginationParams.limit);

    const items = await queryBuilder.getRawMany();
    const totalItems = await queryBuilder.getCount();

    return {
      items,
      meta: {
        totalItems,
        itemCount: items.length,
        itemsPerPage: paginationParams.limit,
        totalPages: Math.ceil(totalItems / paginationParams.limit),
        currentPage: paginationParams.page,
      },
    };
  }

  /**
   * Lấy plan pricing với thông tin plan và addon theo ID
   * @param id ID của plan pricing
   * @returns Plan pricing với thông tin đầy đủ
   */
  async findPlanPricingWithDetailsById(id: number): Promise<any[]> {
    return this.createQueryBuilder('plan_pricing')
      .leftJoin('plans', 'plan', 'plan.id = plan_pricing.plan_id')
      .leftJoin('package_option_addons', 'package_addon', 'package_addon.plan_pricing_id = plan_pricing.id')
      .leftJoin('addons', 'addon', 'addon.id = package_addon.addon_id AND addon.is_active = true')
      .select([
        // Plan pricing fields
        'plan_pricing.id as plan_pricing_id',
        'plan_pricing.plan_id as plan_pricing_plan_id',
        'plan_pricing.billing_cycle as plan_pricing_billing_cycle',
        'plan_pricing.price as plan_pricing_price',
        'plan_pricing.created_at as plan_pricing_created_at',
        'plan_pricing.updated_at as plan_pricing_updated_at',
        'plan_pricing.is_active as plan_pricing_is_active',
        'plan_pricing.description as plan_pricing_description',
        'plan_pricing.currency as plan_pricing_currency',
        'plan_pricing.name as plan_pricing_name',
        'plan_pricing.feature_information as plan_pricing_feature_information',
        // Plan fields
        'plan.id as plan_id',
        'plan.name as plan_name',
        'plan.description as plan_description',
        'plan.type as plan_type',
        'plan.is_active as plan_is_active',
        'plan.created_at as plan_created_at',
        'plan.updated_at as plan_updated_at',
        // Package addon fields
        'package_addon.id as package_addon_id',
        'package_addon.addon_id as package_addon_addon_id',
        'package_addon.unit as package_addon_unit',
        'package_addon.duration_in_days as package_addon_duration_in_days',
        'package_addon.quantity as package_addon_quantity',
        // Addon fields
        'addon.id as addon_id',
        'addon.name as addon_name',
        'addon.description as addon_description',
        'addon.billing_type as addon_billing_type',
        'addon.monthly_price as addon_monthly_price',
        'addon.volume_unit as addon_volume_unit',
        'addon.price_per_unit as addon_price_per_unit',
        'addon.is_active as addon_is_active',
        'addon.created_at as addon_created_at',
      ])
      .where('plan_pricing.id = :id', { id })
      .getRawMany();
  }

  /**
   * Tìm kiếm plan pricing với thông tin plan và addon theo các bộ lọc
   * @param paginationParams Tham số phân trang
   * @param billingCycle Chu kỳ thanh toán (tùy chọn)
   * @param planType Loại plan (tùy chọn)
   * @param isActive Trạng thái kích hoạt (tùy chọn)
   * @returns Kết quả phân trang với thông tin đầy đủ
   */
  async findPlanPricingWithDetailsAndFilters(
    paginationParams: QueryDto,
    billingCycle?: BillingCycle | null,
    planType?: PlanTypeEnum | null,
    isActive?: boolean
  ): Promise<PaginatedResult<any>> {
    const queryBuilder = this.createQueryBuilder('plan_pricing')
      .leftJoin('plans', 'plan', 'plan.id = plan_pricing.plan_id')
      .leftJoin('package_option_addons', 'package_addon', 'package_addon.plan_pricing_id = plan_pricing.id')
      .leftJoin('addons', 'addon', 'addon.id = package_addon.addon_id AND addon.is_active = true')
      .select([
        // Plan pricing fields
        'plan_pricing.id as plan_pricing_id',
        'plan_pricing.plan_id as plan_pricing_plan_id',
        'plan_pricing.billing_cycle as plan_pricing_billing_cycle',
        'plan_pricing.price as plan_pricing_price',
        'plan_pricing.created_at as plan_pricing_created_at',
        'plan_pricing.updated_at as plan_pricing_updated_at',
        'plan_pricing.is_active as plan_pricing_is_active',
        'plan_pricing.description as plan_pricing_description',
        'plan_pricing.currency as plan_pricing_currency',
        'plan_pricing.name as plan_pricing_name',
        'plan_pricing.feature_information as plan_pricing_feature_information',
        // Plan fields
        'plan.id as plan_id',
        'plan.name as plan_name',
        'plan.description as plan_description',
        'plan.type as plan_type',
        'plan.is_active as plan_is_active',
        'plan.created_at as plan_created_at',
        'plan.updated_at as plan_updated_at',
        // Package addon fields
        'package_addon.id as package_addon_id',
        'package_addon.addon_id as package_addon_addon_id',
        'package_addon.unit as package_addon_unit',
        'package_addon.duration_in_days as package_addon_duration_in_days',
        'package_addon.quantity as package_addon_quantity',
        // Addon fields
        'addon.id as addon_id',
        'addon.name as addon_name',
        'addon.description as addon_description',
        'addon.billing_type as addon_billing_type',
        'addon.monthly_price as addon_monthly_price',
        'addon.volume_unit as addon_volume_unit',
        'addon.price_per_unit as addon_price_per_unit',
        'addon.is_active as addon_is_active',
        'addon.created_at as addon_created_at',
      ]);

    // Apply filters
    const whereConditions: string[] = [];
    const parameters: any = {};

    if (billingCycle) {
      whereConditions.push('plan_pricing.billing_cycle = :billingCycle');
      parameters.billingCycle = billingCycle;
    }

    if (planType) {
      whereConditions.push('plan.type = :planType');
      parameters.planType = planType;
    }

    if (isActive !== undefined) {
      whereConditions.push('plan.is_active = :isActive');
      parameters.isActive = isActive;
    }

    // Apply where conditions
    if (whereConditions.length > 0) {
      queryBuilder.where(whereConditions.join(' AND '), parameters);
    }

    // Apply sorting
    if (paginationParams.sortBy && paginationParams.sortDirection) {
      queryBuilder.orderBy(`plan_pricing.${paginationParams.sortBy}`, paginationParams.sortDirection);
    }

    // Apply pagination
    const skip = (paginationParams.page - 1) * paginationParams.limit;
    queryBuilder.skip(skip).take(paginationParams.limit);

    const items = await queryBuilder.getRawMany();
    const totalItems = await queryBuilder.getCount();

    return {
      items,
      meta: {
        totalItems,
        itemCount: items.length,
        itemsPerPage: paginationParams.limit,
        totalPages: Math.ceil(totalItems / paginationParams.limit),
        currentPage: paginationParams.page,
      },
    };
  }
}

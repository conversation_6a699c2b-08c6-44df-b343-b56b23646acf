import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BullModule } from '@nestjs/bullmq';
import { QueueName } from '@shared/queue/queue.constants';
import { QueueModule } from '@shared/queue/queue.module';
import { CALENDAR_ENTITIES } from '../entities';
import { CalendarController } from './controllers/calendar.controller';
import { CalendarInternalController } from '../internal/controllers/calendar-internal.controller';
import { CalendarEventRepository } from '../repositories/calendar-event.repository';
import { CalendarNotificationService, CalendarService } from './services';

/**
 * Module quản lý calendar cho user
 */
@Module({
  imports: [
    TypeOrmModule.forFeature(CALENDAR_ENTITIES),
    BullModule.registerQueue({
      name: QueueName.CALENDAR,
    }),
    QueueModule,
  ],
  controllers: [CalendarController, CalendarInternalController],
  providers: [
    // Repositories - chỉ giữ lại repository cần thiết
    CalendarEventRepository,

    // Services - tối ưu hóa services
    CalendarService,
    CalendarNotificationService,
  ],
  exports: [
    TypeOrmModule,
    // Export services for other modules
    CalendarService,
    CalendarNotificationService,
  ],
})
export class CalendarUserModule {}

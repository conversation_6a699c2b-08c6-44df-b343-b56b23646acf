import { Injectable, Logger } from '@nestjs/common';
import { UserOrderRepository, UserConvertCustomerRepository } from '@modules/business/repositories';
import {
  QueryUserOrderDto,
  UserOrderResponseDto,
  UserOrderDetailResponseDto,
  UserConvertCustomerResponseDto,
  UpdateOrderStatusDto,
  UpdateShippingStatusDto,
  AdminCancelOrderDto,
  AdminConfirmOrderDto,
  UpdatePaymentStatusDto,
  BulkUpdateOrderStatusDto,
  OrderStatusUpdateResponseDto,
  BulkOrderUpdateResponseDto,
  OrderAnalyticsQueryDto,
  OrderOverviewStatsDto,
  RevenueReportDto,
  OrderPerformanceStatsDto,
  ExportOrderReportDto
} from '../dto';
import { PaginatedResult } from '@common/response/api-response-dto';
import { AppException } from '@common/exceptions';
import { BUSINESS_ADMIN_ERROR_CODES } from '@modules/business/admin/exceptions';
import { UserOrder, UserConvertCustomer } from '@modules/business/entities';
import { ValidationHelper } from '../helpers/validation.helper';
import { OrderStatusEnum, ShippingStatusEnum } from '../../enums';
import { Transactional } from 'typeorm-transactional';

/**
 * Service xử lý nghiệp vụ liên quan đến đơn hàng của người dùng cho admin
 */
@Injectable()
export class UserOrderAdminService {
  private readonly logger = new Logger(UserOrderAdminService.name);

  constructor(
    private readonly userOrderRepository: UserOrderRepository,
    private readonly userConvertCustomerRepository: UserConvertCustomerRepository,
    private readonly validationHelper: ValidationHelper,
  ) {}

  /**
   * Lấy danh sách đơn hàng với phân trang và lọc
   * @param employeeId ID của nhân viên thực hiện thao tác
   * @param queryDto DTO chứa các tham số truy vấn
   * @returns Danh sách đơn hàng phân trang
   */
  async getUserOrders(
    employeeId: number,
    queryDto: QueryUserOrderDto,
  ): Promise<PaginatedResult<UserOrderResponseDto>> {
    this.logger.log(
      `Nhân viên ${employeeId} đang lấy danh sách đơn hàng với query: ${JSON.stringify(queryDto)}`,
      `method: ${this.getUserOrders.name}`
    );

    // Debug boolean transformation
    this.logger.log(`hasShipping type: ${typeof queryDto.hasShipping}, value: ${queryDto.hasShipping}`);

    try {
      // Gọi repository để lấy dữ liệu
      const result = await this.userOrderRepository.findUserOrders(queryDto);

      // Chuyển đổi từ entity sang DTO
      const orderDtos = result.items.map(order => this.mapToUserOrderResponseDto(order));

      this.logger.log(`Đã tìm thấy ${result.items.length} đơn hàng`);

      return {
        items: orderDtos,
        meta: result.meta
      };
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy danh sách đơn hàng: ${error.message}`,
        error.stack,
        `method: ${this.getUserOrders.name}`
      );
      throw new AppException(
        BUSINESS_ADMIN_ERROR_CODES.USER_ORDER_FETCH_ERROR,
        'Lỗi khi lấy danh sách đơn hàng',
      );
    }
  }

  /**
   * Lấy chi tiết đơn hàng theo ID
   * @param employeeId ID của nhân viên thực hiện thao tác
   * @param orderId ID của đơn hàng
   * @returns Chi tiết đơn hàng
   */
  async getUserOrderById(
    employeeId: number,
    orderId: string,
  ): Promise<UserOrderDetailResponseDto> {
    this.logger.log(
      `Nhân viên ${employeeId} đang lấy chi tiết đơn hàng với ID: ${orderId}`,
      `method: ${this.getUserOrderById.name}`
    );

    try {
      // Lấy thông tin đơn hàng với thông tin khách hàng
      const orderData = await this.userOrderRepository.findUserOrderByIdWithCustomer(orderId);

      // Kiểm tra đơn hàng có tồn tại không
      this.validationHelper.validateUserOrderExists(orderData);

      this.logger.log(`Đã tìm thấy đơn hàng với ID: ${orderId}`);

      // Chuyển đổi từ raw data sang entity
      const order = new UserOrder();
      let customer: UserConvertCustomer | null = null;

      // Log raw data để debug
      this.logger.log(`Raw order data: ${JSON.stringify(orderData)}`);

      // Đảm bảo orderData không null sau khi validate
      if (orderData) {
        // Map các trường của đơn hàng
        order.id = orderData.id;
        // userConvertCustomerId field has been removed - customer info is now stored directly
        order.convertCustomerEmail = orderData.convert_customer_email || null;
        order.convertCustomerPhone = orderData.convert_customer_phone || null;
        order.convertCustomerName = orderData.convert_customer_name || null;
        order.countryCode = orderData.country_code || null;
        order.userId = orderData.user_id ? Number(orderData.user_id) : 0;
        order.productInfo = orderData.product_info;
        order.billInfo = orderData.bill_info;
        order.hasShipping = Boolean(orderData.has_shipping);
        order.shippingStatus = orderData.shipping_status || null;
        order.logisticInfo = orderData.logistic_info;
        order.createdAt = Number(orderData.created_at);
        order.updatedAt = Number(orderData.updated_at);
        order.source = orderData.source || null;

        // Map các trường của khách hàng
        if (orderData.customer_id) {
          customer = new UserConvertCustomer();
          customer.id = orderData.customer_id;
          customer.avatar = orderData.customer_avatar || null;
          customer.name = orderData.customer_name || null;
          customer.email = orderData.customer_email || null;
          customer.phone = orderData.customer_phone || null;
          customer.platform = orderData.customer_platform || null;
          customer.timezone = orderData.customer_timezone || null;
          customer.metadata = orderData.customer_metadata || null;
          customer.createdAt = Number(orderData.customer_created_at);
          customer.updatedAt = Number(orderData.customer_updated_at);
          customer.userId = orderData.customer_user_id ? Number(orderData.customer_user_id) : 0;
          customer.agentId = orderData.customer_agent_id || null;
        }
      }

      // Chuyển đổi từ entity sang DTO
      const orderDto = this.mapToUserOrderResponseDto(order);
      const customerDto = customer ? this.mapToUserConvertCustomerResponseDto(customer) : null;

      // Log DTO để debug
      this.logger.log(`Order DTO: ${JSON.stringify(orderDto)}`);
      if (customerDto) {
        this.logger.log(`Customer DTO: ${JSON.stringify(customerDto)}`);
      }

      return {
        ...orderDto,
        customer: customerDto
      };
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Lỗi khi lấy chi tiết đơn hàng: ${error.message}`,
        error.stack,
        `method: ${this.getUserOrderById.name}`
      );
      throw new AppException(
        BUSINESS_ADMIN_ERROR_CODES.USER_ORDER_FETCH_ERROR,
        'Lỗi khi lấy chi tiết đơn hàng',
      );
    }
  }

  /**
   * Chuyển đổi từ entity UserOrder sang DTO UserOrderResponseDto
   * @param order Entity UserOrder
   * @returns DTO UserOrderResponseDto
   */
  private mapToUserOrderResponseDto(order: UserOrder): UserOrderResponseDto {
    return {
      id: order.id,
      convertCustomerEmail: order.convertCustomerEmail,
      convertCustomerPhone: order.convertCustomerPhone,
      convertCustomerName: order.convertCustomerName,
      countryCode: order.countryCode,
      userId: order.userId,
      productInfo: Array.isArray(order.productInfo) ? order.productInfo.map(item => ({
        productId: item.productId || 0,
        name: item.name || '',
        quantity: item.quantity || 0,
        price: item.price || 0,
        ...item
      })) : (order.productInfo ? [{
        productId: (order.productInfo as any).productId || 0,
        name: (order.productInfo as any).name || '',
        quantity: (order.productInfo as any).quantity || 0,
        price: (order.productInfo as any).price || 0,
        ...(order.productInfo as any)
      }] : []),
      billInfo: order.billInfo ? {
        subtotal: typeof order.billInfo.subtotal === 'number' ? order.billInfo.subtotal : 0,
        total: typeof order.billInfo.total === 'number' ? order.billInfo.total : 0,
        tax: typeof order.billInfo.tax === 'number' ? order.billInfo.tax : undefined,
        shipping: typeof order.billInfo.shipping === 'number' ? order.billInfo.shipping : undefined,
        paymentMethod: typeof order.billInfo.paymentMethod === 'string' ? order.billInfo.paymentMethod : undefined
      } : null,
      hasShipping: order.hasShipping,
      shippingStatus: order.shippingStatus,
      logisticInfo: order.logisticInfo ? {
        address: typeof order.logisticInfo.address === 'string' ? order.logisticInfo.address : '',
        carrier: typeof order.logisticInfo.carrier === 'string' ? order.logisticInfo.carrier : undefined,
        trackingNumber: typeof order.logisticInfo.trackingNumber === 'string' ? order.logisticInfo.trackingNumber : undefined,
        recipient: order.logisticInfo.recipient ? {
          name: typeof (order.logisticInfo.recipient as any).name === 'string' ? (order.logisticInfo.recipient as any).name : '',
          phone: typeof (order.logisticInfo.recipient as any).phone === 'string' ? (order.logisticInfo.recipient as any).phone : ''
        } : undefined
      } : null,
      createdAt: order.createdAt,
      updatedAt: order.updatedAt,
      source: order.source,
    };
  }

  /**
   * Chuyển đổi từ entity UserConvertCustomer sang DTO UserConvertCustomerResponseDto
   * @param customer Entity UserConvertCustomer
   * @returns DTO UserConvertCustomerResponseDto
   */
  private mapToUserConvertCustomerResponseDto(customer: UserConvertCustomer): UserConvertCustomerResponseDto {
    return {
      id: customer.id, // ID đã là UUID string
      avatar: customer.avatar,
      name: customer.name,
      email: customer.email,
      phone: customer.phone,
      platform: customer.platform,
      timezone: customer.timezone,
      createdAt: customer.createdAt,
      updatedAt: customer.updatedAt,
      userId: customer.userId,
      agentId: customer.agentId,
      metadata: customer.metadata ? Object.entries(customer.metadata).map(([fieldName, fieldValue]) => ({
        fieldName,
        fieldValue: fieldValue as string | number | boolean | string[] | number[]
      })) : [],
      address: customer.address
    };
  }

  // ==================== ORDER MANAGEMENT METHODS ====================

  /**
   * Cập nhật trạng thái đơn hàng
   * @param employeeId ID của nhân viên thực hiện thao tác
   * @param updateDto DTO chứa thông tin cập nhật
   * @returns Kết quả cập nhật
   */
  @Transactional()
  async updateOrderStatus(
    employeeId: number,
    updateDto: UpdateOrderStatusDto,
  ): Promise<OrderStatusUpdateResponseDto> {
    this.logger.log(
      `Nhân viên ${employeeId} đang cập nhật trạng thái đơn hàng ${updateDto.orderId} thành ${updateDto.orderStatus}`,
      `method: ${this.updateOrderStatus.name}`
    );

    try {
      // Tìm đơn hàng
      const order = await this.userOrderRepository.findUserOrderById(updateDto.orderId);
      if (!order) {
        throw new AppException(
          BUSINESS_ADMIN_ERROR_CODES.USER_ORDER_NOT_FOUND,
          `Không tìm thấy đơn hàng với ID ${updateDto.orderId}`
        );
      }

      const oldStatus = order.orderStatus;

      // Validate chuyển đổi trạng thái
      this.validateStatusTransition(oldStatus, updateDto.orderStatus);

      // Cập nhật trạng thái
      await this.userOrderRepository.updateOrder(updateDto.orderId, order.userId, {
        orderStatus: updateDto.orderStatus,
        updatedAt: Date.now()
      });

      this.logger.log(`Đã cập nhật trạng thái đơn hàng ${updateDto.orderId} từ ${oldStatus} thành ${updateDto.orderStatus}`);

      return {
        orderId: updateDto.orderId,
        oldStatus: oldStatus || 'unknown',
        newStatus: updateDto.orderStatus,
        updatedAt: Date.now(),
        updatedBy: employeeId
      };
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Lỗi khi cập nhật trạng thái đơn hàng: ${error.message}`,
        error.stack,
        `method: ${this.updateOrderStatus.name}`
      );
      throw new AppException(
        BUSINESS_ADMIN_ERROR_CODES.USER_ORDER_UPDATE_ERROR,
        'Lỗi khi cập nhật trạng thái đơn hàng',
      );
    }
  }

  /**
   * Cập nhật trạng thái vận chuyển
   * @param employeeId ID của nhân viên thực hiện thao tác
   * @param updateDto DTO chứa thông tin cập nhật
   * @returns Kết quả cập nhật
   */
  @Transactional()
  async updateShippingStatus(
    employeeId: number,
    updateDto: UpdateShippingStatusDto,
  ): Promise<OrderStatusUpdateResponseDto> {
    this.logger.log(
      `Nhân viên ${employeeId} đang cập nhật trạng thái vận chuyển đơn hàng ${updateDto.orderId} thành ${updateDto.shippingStatus}`,
      `method: ${this.updateShippingStatus.name}`
    );

    try {
      // Tìm đơn hàng
      const order = await this.userOrderRepository.findUserOrderById(updateDto.orderId);
      if (!order) {
        throw new AppException(
          BUSINESS_ADMIN_ERROR_CODES.USER_ORDER_NOT_FOUND,
          `Không tìm thấy đơn hàng với ID ${updateDto.orderId}`
        );
      }

      const oldStatus = order.shippingStatus;

      // Cập nhật trạng thái vận chuyển
      await this.userOrderRepository.updateOrder(updateDto.orderId, order.userId, {
        shippingStatus: updateDto.shippingStatus,
        updatedAt: Date.now()
      });

      // Tự động cập nhật order status nếu cần
      if (updateDto.shippingStatus === ShippingStatusEnum.DELIVERED) {
        await this.userOrderRepository.updateOrder(updateDto.orderId, order.userId, {
          orderStatus: OrderStatusEnum.COMPLETED
        });
      }

      this.logger.log(`Đã cập nhật trạng thái vận chuyển đơn hàng ${updateDto.orderId} từ ${oldStatus} thành ${updateDto.shippingStatus}`);

      return {
        orderId: updateDto.orderId,
        oldStatus: oldStatus || 'unknown',
        newStatus: updateDto.shippingStatus,
        updatedAt: Date.now(),
        updatedBy: employeeId
      };
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Lỗi khi cập nhật trạng thái vận chuyển: ${error.message}`,
        error.stack,
        `method: ${this.updateShippingStatus.name}`
      );
      throw new AppException(
        BUSINESS_ADMIN_ERROR_CODES.USER_ORDER_UPDATE_ERROR,
        'Lỗi khi cập nhật trạng thái vận chuyển',
      );
    }
  }

  /**
   * Hủy đơn hàng
   * @param employeeId ID của nhân viên thực hiện thao tác
   * @param cancelDto DTO chứa thông tin hủy đơn
   * @returns Kết quả hủy đơn
   */
  @Transactional()
  async cancelOrder(
    employeeId: number,
    cancelDto: AdminCancelOrderDto,
  ): Promise<OrderStatusUpdateResponseDto> {
    this.logger.log(
      `Nhân viên ${employeeId} đang hủy đơn hàng ${cancelDto.orderId}`,
      `method: ${this.cancelOrder.name}`
    );

    try {
      // Tìm đơn hàng
      const order = await this.userOrderRepository.findUserOrderById(cancelDto.orderId);
      if (!order) {
        throw new AppException(
          BUSINESS_ADMIN_ERROR_CODES.USER_ORDER_NOT_FOUND,
          `Không tìm thấy đơn hàng với ID ${cancelDto.orderId}`
        );
      }

      // Kiểm tra có thể hủy không
      if (order.orderStatus === OrderStatusEnum.COMPLETED) {
        throw new AppException(
          BUSINESS_ADMIN_ERROR_CODES.USER_ORDER_UPDATE_ERROR,
          'Không thể hủy đơn hàng đã hoàn thành'
        );
      }

      if (order.orderStatus === OrderStatusEnum.CANCELLED) {
        throw new AppException(
          BUSINESS_ADMIN_ERROR_CODES.USER_ORDER_UPDATE_ERROR,
          'Đơn hàng đã được hủy trước đó'
        );
      }

      const oldStatus = order.orderStatus;

      // Cập nhật trạng thái
      await this.userOrderRepository.updateOrder(cancelDto.orderId, order.userId, {
        orderStatus: OrderStatusEnum.CANCELLED,
        shippingStatus: ShippingStatusEnum.CANCELLED,
        updatedAt: Date.now()
      });

      this.logger.log(`Đã hủy đơn hàng ${cancelDto.orderId}. Lý do: ${cancelDto.cancelReason}`);

      return {
        orderId: cancelDto.orderId,
        oldStatus: oldStatus || 'unknown',
        newStatus: OrderStatusEnum.CANCELLED,
        updatedAt: Date.now(),
        updatedBy: employeeId
      };
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Lỗi khi hủy đơn hàng: ${error.message}`,
        error.stack,
        `method: ${this.cancelOrder.name}`
      );
      throw new AppException(
        BUSINESS_ADMIN_ERROR_CODES.USER_ORDER_UPDATE_ERROR,
        'Lỗi khi hủy đơn hàng',
      );
    }
  }

  /**
   * Validate chuyển đổi trạng thái
   * @param currentStatus Trạng thái hiện tại
   * @param newStatus Trạng thái mới
   */
  private validateStatusTransition(currentStatus: OrderStatusEnum | null, newStatus: OrderStatusEnum): void {
    const validTransitions: Record<string, OrderStatusEnum[]> = {
      [OrderStatusEnum.PENDING]: [OrderStatusEnum.CONFIRMED, OrderStatusEnum.CANCELLED],
      [OrderStatusEnum.CONFIRMED]: [OrderStatusEnum.PROCESSING, OrderStatusEnum.CANCELLED],
      [OrderStatusEnum.PROCESSING]: [OrderStatusEnum.COMPLETED, OrderStatusEnum.CANCELLED],
      [OrderStatusEnum.COMPLETED]: [], // Không thể chuyển từ completed
      [OrderStatusEnum.CANCELLED]: [], // Không thể chuyển từ cancelled
    };

    if (!currentStatus) {
      // Nếu chưa có trạng thái, cho phép set bất kỳ trạng thái nào
      return;
    }

    const allowedStatuses = validTransitions[currentStatus] || [];
    if (!allowedStatuses.includes(newStatus)) {
      throw new AppException(
        BUSINESS_ADMIN_ERROR_CODES.USER_ORDER_UPDATE_ERROR,
        `Không thể chuyển từ trạng thái ${currentStatus} sang ${newStatus}`
      );
    }
  }

  /**
   * Xác nhận đơn hàng
   * @param employeeId ID của nhân viên thực hiện thao tác
   * @param confirmDto DTO chứa thông tin xác nhận
   * @returns Kết quả xác nhận
   */
  @Transactional()
  async confirmOrder(
    employeeId: number,
    confirmDto: AdminConfirmOrderDto,
  ): Promise<OrderStatusUpdateResponseDto> {
    this.logger.log(
      `Nhân viên ${employeeId} đang xác nhận đơn hàng ${confirmDto.orderId}`,
      `method: ${this.confirmOrder.name}`
    );

    try {
      // Tìm đơn hàng
      const order = await this.userOrderRepository.findUserOrderById(confirmDto.orderId);
      if (!order) {
        throw new AppException(
          BUSINESS_ADMIN_ERROR_CODES.USER_ORDER_NOT_FOUND,
          `Không tìm thấy đơn hàng với ID ${confirmDto.orderId}`
        );
      }

      // Kiểm tra có thể xác nhận không
      if (order.orderStatus !== OrderStatusEnum.PENDING) {
        throw new AppException(
          BUSINESS_ADMIN_ERROR_CODES.USER_ORDER_UPDATE_ERROR,
          `Chỉ có thể xác nhận đơn hàng ở trạng thái pending. Trạng thái hiện tại: ${order.orderStatus}`
        );
      }

      const oldStatus = order.orderStatus;

      // Cập nhật trạng thái
      await this.userOrderRepository.updateOrder(confirmDto.orderId, order.userId, {
        orderStatus: OrderStatusEnum.CONFIRMED,
        updatedAt: Date.now()
      });

      this.logger.log(`Đã xác nhận đơn hàng ${confirmDto.orderId}`);

      return {
        orderId: confirmDto.orderId,
        oldStatus: oldStatus || 'unknown',
        newStatus: OrderStatusEnum.CONFIRMED,
        updatedAt: Date.now(),
        updatedBy: employeeId
      };
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Lỗi khi xác nhận đơn hàng: ${error.message}`,
        error.stack,
        `method: ${this.confirmOrder.name}`
      );
      throw new AppException(
        BUSINESS_ADMIN_ERROR_CODES.USER_ORDER_UPDATE_ERROR,
        'Lỗi khi xác nhận đơn hàng',
      );
    }
  }

  /**
   * Cập nhật trạng thái thanh toán
   * @param employeeId ID của nhân viên thực hiện thao tác
   * @param updateDto DTO chứa thông tin cập nhật thanh toán
   * @returns Kết quả cập nhật
   */
  @Transactional()
  async updatePaymentStatus(
    employeeId: number,
    updateDto: UpdatePaymentStatusDto,
  ): Promise<OrderStatusUpdateResponseDto> {
    this.logger.log(
      `Nhân viên ${employeeId} đang cập nhật trạng thái thanh toán đơn hàng ${updateDto.orderId} thành ${updateDto.paymentStatus}`,
      `method: ${this.updatePaymentStatus.name}`
    );

    try {
      // Tìm đơn hàng
      const order = await this.userOrderRepository.findUserOrderById(updateDto.orderId);
      if (!order) {
        throw new AppException(
          BUSINESS_ADMIN_ERROR_CODES.USER_ORDER_NOT_FOUND,
          `Không tìm thấy đơn hàng với ID ${updateDto.orderId}`
        );
      }

      // Cập nhật thông tin thanh toán trong billInfo
      const currentBillInfo = order.billInfo || {};
      const updatedBillInfo = {
        ...currentBillInfo,
        // Ensure required properties exist
        total: (currentBillInfo as any)?.total || 0,
        paymentMethod: (currentBillInfo as any)?.paymentMethod || 'unknown',
        // Update payment fields
        paymentStatus: updateDto.paymentStatus,
        paymentNote: updateDto.paymentNote,
        paymentUpdatedAt: Date.now(),
        paymentUpdatedBy: employeeId
      };

      await this.userOrderRepository.updateOrder(updateDto.orderId, order.userId, {
        billInfo: updatedBillInfo,
        updatedAt: Date.now()
      });

      this.logger.log(`Đã cập nhật trạng thái thanh toán đơn hàng ${updateDto.orderId} thành ${updateDto.paymentStatus}`);

      return {
        orderId: updateDto.orderId,
        oldStatus: (currentBillInfo as any)?.paymentStatus || 'unknown',
        newStatus: updateDto.paymentStatus,
        updatedAt: Date.now(),
        updatedBy: employeeId
      };
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Lỗi khi cập nhật trạng thái thanh toán: ${error.message}`,
        error.stack,
        `method: ${this.updatePaymentStatus.name}`
      );
      throw new AppException(
        BUSINESS_ADMIN_ERROR_CODES.USER_ORDER_UPDATE_ERROR,
        'Lỗi khi cập nhật trạng thái thanh toán',
      );
    }
  }

  /**
   * Cập nhật hàng loạt trạng thái đơn hàng
   * @param employeeId ID của nhân viên thực hiện thao tác
   * @param bulkUpdateDto DTO chứa thông tin cập nhật hàng loạt
   * @returns Kết quả cập nhật hàng loạt
   */
  @Transactional()
  async bulkUpdateOrderStatus(
    employeeId: number,
    bulkUpdateDto: BulkUpdateOrderStatusDto,
  ): Promise<BulkOrderUpdateResponseDto> {
    this.logger.log(
      `Nhân viên ${employeeId} đang cập nhật hàng loạt ${bulkUpdateDto.orderIds.length} đơn hàng thành ${bulkUpdateDto.orderStatus}`,
      `method: ${this.bulkUpdateOrderStatus.name}`
    );

    try {
      const results: OrderStatusUpdateResponseDto[] = [];
      let successCount = 0;
      let failureCount = 0;

      // Xử lý từng đơn hàng
      for (const orderId of bulkUpdateDto.orderIds) {
        try {
          const result = await this.updateOrderStatus(employeeId, {
            orderId,
            orderStatus: bulkUpdateDto.orderStatus,
            reason: bulkUpdateDto.reason
          });
          results.push(result);
          successCount++;
        } catch (error) {
          this.logger.error(`Lỗi khi cập nhật đơn hàng ${orderId}: ${error.message}`);
          results.push({
            orderId,
            oldStatus: 'unknown',
            newStatus: 'failed',
            updatedAt: Date.now(),
            updatedBy: employeeId
          });
          failureCount++;
        }
      }

      this.logger.log(`Hoàn thành cập nhật hàng loạt: ${successCount} thành công, ${failureCount} thất bại`);

      return {
        totalRequested: bulkUpdateDto.orderIds.length,
        successCount,
        failureCount,
        results,
        message: `Cập nhật thành công ${successCount}/${bulkUpdateDto.orderIds.length} đơn hàng`
      };
    } catch (error) {
      this.logger.error(
        `Lỗi khi cập nhật hàng loạt: ${error.message}`,
        error.stack,
        `method: ${this.bulkUpdateOrderStatus.name}`
      );
      throw new AppException(
        BUSINESS_ADMIN_ERROR_CODES.USER_ORDER_UPDATE_ERROR,
        'Lỗi khi cập nhật hàng loạt đơn hàng',
      );
    }
  }

  // ==================== ANALYTICS & REPORTING METHODS ====================

  /**
   * Lấy thống kê tổng quan đơn hàng
   * @param employeeId ID của nhân viên thực hiện thao tác
   * @param queryDto DTO chứa các tham số truy vấn
   * @returns Thống kê tổng quan
   */
  async getOrderOverviewStats(
    employeeId: number,
    queryDto: OrderAnalyticsQueryDto,
  ): Promise<OrderOverviewStatsDto> {
    this.logger.log(
      `Nhân viên ${employeeId} đang lấy thống kê tổng quan đơn hàng`,
      `method: ${this.getOrderOverviewStats.name}`
    );

    try {
      // Tạo query builder cơ bản
      const queryBuilder = this.userOrderRepository.createQueryBuilder('order');

      // Áp dụng filters
      this.applyAnalyticsFilters(queryBuilder, queryDto);

      // Lấy tổng số đơn hàng và doanh thu
      const totalStats = await queryBuilder
        .select([
          'COUNT(*) as totalOrders',
          'COALESCE(SUM(CAST(order.billInfo->>\'total\' AS DECIMAL)), 0) as totalRevenue',
          'COALESCE(AVG(CAST(order.billInfo->>\'total\' AS DECIMAL)), 0) as averageOrderValue'
        ])
        .getRawOne();

      // Lấy thống kê theo trạng thái đơn hàng
      const statusStats = await queryBuilder
        .select([
          'order.orderStatus as status',
          'COUNT(*) as count'
        ])
        .groupBy('order.orderStatus')
        .getRawMany();

      // Lấy thống kê theo trạng thái vận chuyển
      const shippingStats = await queryBuilder
        .select([
          'order.shippingStatus as status',
          'COUNT(*) as count'
        ])
        .groupBy('order.shippingStatus')
        .getRawMany();

      // Tính tỷ lệ hoàn thành và hủy
      const completedCount = statusStats.find(s => s.status === OrderStatusEnum.COMPLETED)?.count || 0;
      const cancelledCount = statusStats.find(s => s.status === OrderStatusEnum.CANCELLED)?.count || 0;
      const totalOrders = parseInt(totalStats.totalOrders) || 0;

      const completionRate = totalOrders > 0 ? (completedCount / totalOrders) * 100 : 0;
      const cancellationRate = totalOrders > 0 ? (cancelledCount / totalOrders) * 100 : 0;

      // Chuyển đổi dữ liệu
      const statusBreakdown: Record<string, number> = {};
      statusStats.forEach(stat => {
        statusBreakdown[stat.status || 'unknown'] = parseInt(stat.count);
      });

      const shippingStatusBreakdown: Record<string, number> = {};
      shippingStats.forEach(stat => {
        shippingStatusBreakdown[stat.status || 'unknown'] = parseInt(stat.count);
      });

      return {
        totalOrders,
        totalRevenue: parseFloat(totalStats.totalRevenue) || 0,
        averageOrderValue: parseFloat(totalStats.averageOrderValue) || 0,
        statusBreakdown,
        shippingStatusBreakdown,
        completionRate: Math.round(completionRate * 100) / 100,
        cancellationRate: Math.round(cancellationRate * 100) / 100
      };
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy thống kê tổng quan: ${error.message}`,
        error.stack,
        `method: ${this.getOrderOverviewStats.name}`
      );
      throw new AppException(
        BUSINESS_ADMIN_ERROR_CODES.USER_ORDER_FETCH_ERROR,
        'Lỗi khi lấy thống kê tổng quan đơn hàng',
      );
    }
  }

  /**
   * Lấy báo cáo doanh thu
   * @param employeeId ID của nhân viên thực hiện thao tác
   * @param queryDto DTO chứa các tham số truy vấn
   * @returns Báo cáo doanh thu
   */
  async getRevenueReport(
    employeeId: number,
    queryDto: OrderAnalyticsQueryDto,
  ): Promise<RevenueReportDto> {
    this.logger.log(
      `Nhân viên ${employeeId} đang lấy báo cáo doanh thu`,
      `method: ${this.getRevenueReport.name}`
    );

    try {
      // Lấy thống kê tổng quan
      const overview = await this.getOrderOverviewStats(employeeId, queryDto);

      // Lấy dữ liệu theo thời gian (mock data - cần implement query thực tế)
      const timeSeries = [
        {
          period: '2024-01',
          orderCount: 125,
          revenue: 12500000,
          averageOrderValue: 100000,
          completedOrders: 110,
          cancelledOrders: 5
        },
        {
          period: '2024-02',
          orderCount: 150,
          revenue: 15000000,
          averageOrderValue: 100000,
          completedOrders: 135,
          cancelledOrders: 8
        }
      ];

      // Top sản phẩm (mock data - cần implement query thực tế)
      const topProducts = [
        { productId: 1, productName: 'Áo thun', quantity: 150, revenue: 3000000 },
        { productId: 2, productName: 'Quần jean', quantity: 100, revenue: 2500000 }
      ];

      // Top khách hàng (mock data - cần implement query thực tế)
      const topCustomers = [
        { userId: 1, userName: 'Nguyễn Văn A', orderCount: 15, totalSpent: 1500000 },
        { userId: 2, userName: 'Trần Thị B', orderCount: 12, totalSpent: 1200000 }
      ];

      return {
        overview,
        timeSeries,
        topProducts,
        topCustomers
      };
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy báo cáo doanh thu: ${error.message}`,
        error.stack,
        `method: ${this.getRevenueReport.name}`
      );
      throw new AppException(
        BUSINESS_ADMIN_ERROR_CODES.USER_ORDER_FETCH_ERROR,
        'Lỗi khi lấy báo cáo doanh thu',
      );
    }
  }

  /**
   * Áp dụng filters cho analytics query
   * @param queryBuilder Query builder
   * @param queryDto DTO chứa filters
   */
  private applyAnalyticsFilters(queryBuilder: any, queryDto: OrderAnalyticsQueryDto): void {
    if (queryDto.startDate) {
      queryBuilder.andWhere('order.createdAt >= :startDate', {
        startDate: new Date(queryDto.startDate).getTime()
      });
    }

    if (queryDto.endDate) {
      queryBuilder.andWhere('order.createdAt <= :endDate', {
        endDate: new Date(queryDto.endDate).getTime()
      });
    }

    if (queryDto.userId) {
      queryBuilder.andWhere('order.userId = :userId', { userId: queryDto.userId });
    }

    if (queryDto.orderStatus) {
      queryBuilder.andWhere('order.orderStatus = :orderStatus', { orderStatus: queryDto.orderStatus });
    }

    if (queryDto.shippingStatus) {
      queryBuilder.andWhere('order.shippingStatus = :shippingStatus', { shippingStatus: queryDto.shippingStatus });
    }
  }
}

import { Injectable } from '@nestjs/common';
import { DataSource, Repository, Between, In } from 'typeorm';
import { CalendarEvent } from '../entities/calendar-event.entity';
import { CalendarEventQueryDto } from '../dto/calendar-query.dto';
import { PaginatedResult } from '@common/response/api-response-dto';
import { SqlHelper } from '@common/helpers';
import { CalendarActionType, CalendarEventStatus } from '../entities';

@Injectable()
export class CalendarEventRepository extends Repository<CalendarEvent> {
  private sqlHelper: SqlHelper;

  constructor(private dataSource: DataSource) {
    super(CalendarEvent, dataSource.createEntityManager());
    this.sqlHelper = new SqlHelper(dataSource, { enableLogging: true });
  }

  /**
   * Tìm kiếm sự kiện calendar với các bộ lọc
   */
  async findCalendarEvents(
    paginationParams: CalendarEventQueryDto,
    userId?: number,
  ): Promise<PaginatedResult<CalendarEvent>> {
    return this.sqlHelper.getPaginatedData(this, paginationParams, {
      alias: 'calendar_event',
      searchFields: ['title', 'description'],
      customize: (qb) => {
        if (userId) {
          qb.andWhere('calendar_event.userId = :userId', { userId });
        }

        if (paginationParams.actionType) {
          qb.andWhere('calendar_event.actionType = :actionType', {
            actionType: paginationParams.actionType,
          });
        }

        if (paginationParams.status) {
          qb.andWhere('calendar_event.status = :status', {
            status: paginationParams.status,
          });
        }

        if (paginationParams.priority) {
          qb.andWhere('calendar_event.priority = :priority', {
            priority: paginationParams.priority,
          });
        }

        if (paginationParams.startTimeFrom) {
          qb.andWhere('calendar_event.startTime >= :startTimeFrom', {
            startTimeFrom: new Date(paginationParams.startTimeFrom),
          });
        }

        if (paginationParams.startTimeTo) {
          qb.andWhere('calendar_event.startTime <= :startTimeTo', {
            startTimeTo: new Date(paginationParams.startTimeTo),
          });
        }

        if (paginationParams.endTimeFrom) {
          qb.andWhere('calendar_event.endTime >= :endTimeFrom', {
            endTimeFrom: new Date(paginationParams.endTimeFrom),
          });
        }

        if (paginationParams.endTimeTo) {
          qb.andWhere('calendar_event.endTime <= :endTimeTo', {
            endTimeTo: new Date(paginationParams.endTimeTo),
          });
        }

        if (paginationParams.allDay !== undefined) {
          qb.andWhere('calendar_event.allDay = :allDay', {
            allDay: paginationParams.allDay,
          });
        }

        if (paginationParams.syncWithGoogle !== undefined) {
          qb.andWhere('calendar_event.syncWithGoogle = :syncWithGoogle', {
            syncWithGoogle: paginationParams.syncWithGoogle,
          });
        }

        if (paginationParams.location) {
          qb.andWhere('calendar_event.location ILIKE :location', {
            location: `%${paginationParams.location}%`,
          });
        }

        // parentEventId không còn tồn tại trong entity tối ưu
        // if (paginationParams.parentEventId) {
        //   qb.andWhere('calendar_event.parentEventId = :parentEventId', {
        //     parentEventId: paginationParams.parentEventId,
        //   });
        // }

        // Sắp xếp mặc định theo thời gian bắt đầu
        qb.orderBy('calendar_event.startTime', 'ASC');

        return qb;
      },
    });
  }

  /**
   * Tìm sự kiện theo khoảng thời gian
   */
  async findEventsByTimeRange(
    userId: number,
    startTime: Date,
    endTime: Date,
    eventTypes?: CalendarActionType[],
  ): Promise<CalendarEvent[]> {
    const qb = this.createQueryBuilder('event')
      .where('event.userId = :userId', { userId })
      .andWhere('event.startTime <= :endTime', { endTime })
      .andWhere('(event.endTime >= :startTime OR event.endTime IS NULL)', {
        startTime,
      });

    if (eventTypes && eventTypes.length > 0) {
      qb.andWhere('event.eventType IN (:...eventTypes)', { eventTypes });
    }

    return qb.orderBy('event.startTime', 'ASC').getMany();
  }

  /**
   * Tìm sự kiện xung đột thời gian
   */
  async findConflictingEvents(
    userId: number,
    startTime: Date,
    endTime: Date,
    excludeEventId?: string,
  ): Promise<CalendarEvent[]> {
    const qb = this.createQueryBuilder('event')
      .where('event.userId = :userId', { userId })
      .andWhere('event.status != :cancelledStatus', {
        cancelledStatus: CalendarEventStatus.CANCELLED,
      })
      .andWhere('event.startTime < :endTime', { endTime })
      .andWhere('(event.endTime > :startTime OR event.endTime IS NULL)', {
        startTime,
      });

    if (excludeEventId) {
      qb.andWhere('event.id != :excludeEventId', { excludeEventId });
    }

    return qb.getMany();
  }

  /**
   * Tìm sự kiện cần nhắc nhở
   */
  async findEventsNeedingReminders(
    reminderTime: Date,
  ): Promise<CalendarEvent[]> {
    return this.createQueryBuilder('event')
      .leftJoinAndSelect('event.reminders', 'reminder')
      .where('event.status = :status', { status: CalendarEventStatus.SCHEDULED })
      .andWhere('event.startTime > :now', { now: new Date() })
      .andWhere('reminder.reminderTime <= :reminderTime', { reminderTime })
      .andWhere('reminder.status = :reminderStatus', {
        reminderStatus: 'pending',
      })
      .andWhere('reminder.isActive = true')
      .getMany();
  }

  /**
   * Tìm sự kiện theo Google Calendar ID
   * NOTE: Tính năng này đã bị loại bỏ trong version tối ưu
   */
  // async findByGoogleEventId(
  //   googleEventId: string,
  // ): Promise<CalendarEvent | null> {
  //   return this.findOne({
  //     where: { googleEventId },
  //   });
  // }

  /**
   * Tìm sự kiện con của một sự kiện lặp
   * NOTE: Tính năng này đã bị loại bỏ trong version tối ưu
   */
  // async findChildEvents(parentEventId: string): Promise<CalendarEvent[]> {
  //   return this.find({
  //     where: { parentEventId },
  //     order: { startTime: 'ASC' },
  //   });
  // }

  /**
   * Đếm số sự kiện theo loại
   */
  async countEventsByType(userId: number): Promise<{ [key: string]: number }> {
    const result = await this.createQueryBuilder('event')
      .select('event.eventType', 'eventType')
      .addSelect('COUNT(*)', 'count')
      .where('event.userId = :userId', { userId })
      .groupBy('event.eventType')
      .getRawMany();

    return result.reduce((acc, item) => {
      acc[item.eventType] = parseInt(item.count);
      return acc;
    }, {});
  }

  /**
   * Tìm sự kiện sắp diễn ra
   */
  async findUpcomingEvents(
    userId: number,
    limit: number = 10,
  ): Promise<CalendarEvent[]> {
    return this.find({
      where: {
        userId,
        status: CalendarEventStatus.SCHEDULED,
        startTime: Between(
          new Date(),
          new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
        ), // 7 ngày tới
      },
      order: { startTime: 'ASC' },
      take: limit,
    });
  }

  /**
   * Cập nhật trạng thái hàng loạt
   */
  async updateStatusBatch(
    eventIds: string[],
    status: CalendarEventStatus,
  ): Promise<void> {
    await this.update({ id: In(eventIds) }, { status, updatedAt: new Date() });
  }
}

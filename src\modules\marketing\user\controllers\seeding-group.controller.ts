import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
  ApiBody,
} from '@nestjs/swagger';
import { JwtUserGuard } from '@modules/auth/guards';
import { CurrentUser } from '@modules/auth/decorators';
import { JwtPayload } from '@modules/auth/guards/jwt.util';
import { ApiResponseDto, PaginatedResult } from '@common/response';
import { SWAGGER_API_TAGS } from '@common/swagger';
import { SeedingGroupService } from '../services/seeding-group.service';
import { SeedingGroup } from '../entities/seeding-group.entity';
import {
  CreateSeedingGroupDto,
  UpdateSeedingGroupDto,
  SeedingGroupQueryDto,
  BulkDeleteSeedingGroupDto,
} from '../dto/seeding-group/seeding-group.dto';

/**
 * Controller xử lý tất cả API liên quan đến Seeding Groups
 */
@ApiTags(SWAGGER_API_TAGS.SEEDING_GROUP || 'Seeding Groups')
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtUserGuard)
@Controller('marketing/zalo/seeding-groups')
export class SeedingGroupController {
  constructor(private readonly seedingGroupService: SeedingGroupService) {}

  @Get()
  @ApiOperation({
    summary: 'Lấy danh sách seeding groups',
    description: 'Lấy danh sách seeding groups với phân trang và bộ lọc',
  })
  @ApiQuery({
    name: 'page',
    description: 'Số trang',
    required: false,
    type: Number,
    example: 1,
  })
  @ApiQuery({
    name: 'limit',
    description: 'Số lượng items mỗi trang',
    required: false,
    type: Number,
    example: 20,
  })
  @ApiQuery({
    name: 'search',
    description: 'Tìm kiếm theo tên',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'status',
    description: 'Lọc theo trạng thái',
    required: false,
    enum: ['draft', 'active', 'paused', 'stopped', 'completed'],
  })
  @ApiQuery({
    name: 'oaAccountId',
    description: 'Lọc theo OA Account ID',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'groupId',
    description: 'Lọc theo Group ID',
    required: false,
    type: String,
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lấy danh sách seeding groups thành công',
    type: ApiResponseDto<PaginatedResult<SeedingGroup>>,
  })
  async findAll(
    @CurrentUser() user: JwtPayload,
    @Query() queryDto: SeedingGroupQueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<SeedingGroup>>> {
    const result = await this.seedingGroupService.findAll(user.id, queryDto);
    return new ApiResponseDto(
      result,
      'Lấy danh sách seeding groups thành công',
    );
  }

  @Get(':id')
  @ApiOperation({
    summary: 'Lấy chi tiết seeding group',
    description: 'Lấy thông tin chi tiết của một seeding group',
  })
  @ApiParam({
    name: 'id',
    description: 'ID của seeding group',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lấy chi tiết seeding group thành công',
    type: ApiResponseDto<SeedingGroup>,
  })
  async findOne(
    @CurrentUser() user: JwtPayload,
    @Param('id') id: string,
  ): Promise<ApiResponseDto<SeedingGroup>> {
    const result = await this.seedingGroupService.findOne(id, user.id);
    return new ApiResponseDto(result, 'Lấy chi tiết seeding group thành công');
  }

  @Post()
  @ApiOperation({
    summary: 'Tạo seeding group mới',
    description:
      'Tạo một seeding group mới với cấu hình thời gian và tài khoản',
  })
  @ApiBody({
    description: 'Thông tin seeding group cần tạo',
    type: CreateSeedingGroupDto,
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Tạo seeding group thành công',
    type: ApiResponseDto<SeedingGroup>,
  })
  async create(
    @CurrentUser() user: JwtPayload,
    @Body() createDto: CreateSeedingGroupDto,
  ): Promise<ApiResponseDto<SeedingGroup>> {
    const result = await this.seedingGroupService.create(user.id, createDto);
    return new ApiResponseDto(result, 'Tạo seeding group thành công');
  }

  @Put(':id')
  @ApiOperation({
    summary: 'Cập nhật seeding group',
    description: 'Cập nhật thông tin của một seeding group',
  })
  @ApiParam({
    name: 'id',
    description: 'ID của seeding group',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiBody({
    description: 'Thông tin cần cập nhật',
    type: UpdateSeedingGroupDto,
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Cập nhật seeding group thành công',
    type: ApiResponseDto<SeedingGroup>,
  })
  async update(
    @CurrentUser() user: JwtPayload,
    @Param('id') id: string,
    @Body() updateDto: UpdateSeedingGroupDto,
  ): Promise<ApiResponseDto<SeedingGroup>> {
    const result = await this.seedingGroupService.update(
      id,
      user.id,
      updateDto,
    );
    return new ApiResponseDto(result, 'Cập nhật seeding group thành công');
  }

  @Delete(':id')
  @ApiOperation({
    summary: 'Xóa seeding group',
    description:
      'Xóa một seeding group (chỉ được phép xóa khi không hoạt động)',
  })
  @ApiParam({
    name: 'id',
    description: 'ID của seeding group',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Xóa seeding group thành công',
    type: ApiResponseDto<null>,
  })
  async remove(
    @CurrentUser() user: JwtPayload,
    @Param('id') id: string,
  ): Promise<ApiResponseDto<null>> {
    await this.seedingGroupService.remove(id, user.id);
    return new ApiResponseDto(null, 'Xóa seeding group thành công');
  }

  @Delete()
  @ApiOperation({
    summary: 'Xóa nhiều seeding groups',
    description: 'Xóa nhiều seeding groups cùng lúc',
  })
  @ApiBody({
    description: 'Danh sách ID cần xóa',
    type: BulkDeleteSeedingGroupDto,
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Xóa seeding groups thành công',
    type: ApiResponseDto<null>,
  })
  async bulkDelete(
    @CurrentUser() user: JwtPayload,
    @Body() bulkDeleteDto: BulkDeleteSeedingGroupDto,
  ): Promise<ApiResponseDto<null>> {
    await this.seedingGroupService.bulkDelete(user.id, bulkDeleteDto);
    return new ApiResponseDto(null, 'Xóa seeding groups thành công');
  }

  @Post(':id/start')
  @ApiOperation({
    summary: 'Khởi động seeding group',
    description: 'Bắt đầu hoạt động seeding cho group',
  })
  @ApiParam({
    name: 'id',
    description: 'ID của seeding group',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Khởi động seeding group thành công',
    type: ApiResponseDto<SeedingGroup>,
  })
  async start(
    @CurrentUser() user: JwtPayload,
    @Param('id') id: string,
  ): Promise<ApiResponseDto<SeedingGroup>> {
    const result = await this.seedingGroupService.start(id, user.id);
    return new ApiResponseDto(result, 'Khởi động seeding group thành công');
  }

  @Post(':id/pause')
  @ApiOperation({
    summary: 'Tạm dừng seeding group',
    description: 'Tạm dừng hoạt động seeding cho group',
  })
  @ApiParam({
    name: 'id',
    description: 'ID của seeding group',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Tạm dừng seeding group thành công',
    type: ApiResponseDto<SeedingGroup>,
  })
  async pause(
    @CurrentUser() user: JwtPayload,
    @Param('id') id: string,
  ): Promise<ApiResponseDto<SeedingGroup>> {
    const result = await this.seedingGroupService.pause(id, user.id);
    return new ApiResponseDto(result, 'Tạm dừng seeding group thành công');
  }

  @Post(':id/stop')
  @ApiOperation({
    summary: 'Dừng seeding group',
    description: 'Dừng hoàn toàn hoạt động seeding cho group',
  })
  @ApiParam({
    name: 'id',
    description: 'ID của seeding group',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Dừng seeding group thành công',
    type: ApiResponseDto<SeedingGroup>,
  })
  async stop(
    @CurrentUser() user: JwtPayload,
    @Param('id') id: string,
  ): Promise<ApiResponseDto<SeedingGroup>> {
    const result = await this.seedingGroupService.stop(id, user.id);
    return new ApiResponseDto(result, 'Dừng seeding group thành công');
  }
}

import { Injectable, Logger } from '@nestjs/common';
import {
  GenericSessionRepository,
  GenericSessionWidgetRepository,
  GenericSessionLayoutRepository,
} from '../repositories';
import {
  GenericSession,
  GenericSessionWidget,
  GenericSessionLayout,
} from '../entities';
import {
  CreateGenericSessionDto,
  UpdateGenericSessionDto,
  GenericSessionResponseDto,
} from '../dto/generic-session.dto';
import { AppException } from '@/common';
import { GENERIC_ERROR_CODES } from '../exceptions';
import { v4 as uuidv4 } from 'uuid';

@Injectable()
export class GenericSessionService {
  private readonly logger = new Logger(GenericSessionService.name);

  constructor(
    private readonly genericSessionRepository: GenericSessionRepository,
    private readonly genericSessionWidgetRepository: GenericSessionWidgetRepository,
    private readonly genericSessionLayoutRepository: GenericSessionLayoutRepository,
  ) {}

  /**
   * Tạo session mới
   * @param createDto Data để tạo session
   * @returns Session đã được tạo
   */
  async createSession(
    createDto: CreateGenericSessionDto,
  ): Promise<GenericSessionResponseDto> {
    try {
      // Generate sessionId nếu không được cung cấp
      const sessionId = createDto.sessionId || this.generateSessionId();

      // Kiểm tra sessionId đã tồn tại chưa
      const existingSession =
        await this.genericSessionRepository.findBySessionIdOptional(sessionId);
      if (existingSession) {
        throw new AppException(
          GENERIC_ERROR_CODES.GENERIC_SESSION_ALREADY_EXISTS,
          `Session với ID ${sessionId} đã tồn tại`,
        );
      }

      // Tạo session mới
      const session = this.genericSessionRepository.create({
        sessionId,
        userId: createDto.userId,
        pageConfig: createDto.pageConfig || {},
        metadata: createDto.metadata,
        expiresAt: createDto.expiresAt
          ? new Date(createDto.expiresAt)
          : undefined,
      });

      const savedSession = await this.genericSessionRepository.save(session);

      // Tạo layout mặc định cho session
      await this.genericSessionLayoutRepository.createDefaultLayout(sessionId);

      this.logger.log(`Created new session: ${sessionId}`);
      return this.mapToResponseDto(
        Array.isArray(savedSession) ? savedSession[0] : savedSession,
      );
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Error creating session: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        GENERIC_ERROR_CODES.GENERIC_SESSION_CREATE_ERROR,
        'Lỗi khi tạo session mới',
      );
    }
  }

  /**
   * Lấy session theo sessionId
   * @param sessionId Session ID
   * @returns Session nếu tìm thấy
   */
  async getSessionBySessionId(
    sessionId: string,
  ): Promise<GenericSessionResponseDto> {
    try {
      const session =
        await this.genericSessionRepository.findBySessionId(sessionId);

      // Cập nhật last activity
      await this.genericSessionRepository.updateLastActivity(sessionId);

      return this.mapToResponseDto(session);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error getting session: ${error.message}`, error.stack);
      throw new AppException(
        GENERIC_ERROR_CODES.GENERIC_SESSION_NOT_FOUND,
        `Lỗi khi lấy session ${sessionId}`,
      );
    }
  }

  /**
   * Lấy session theo sessionId (optional - không throw error)
   * @param sessionId Session ID
   * @returns Session nếu tìm thấy, null nếu không tìm thấy
   */
  async getSessionBySessionIdOptional(
    sessionId: string,
  ): Promise<GenericSessionResponseDto | null> {
    try {
      const session =
        await this.genericSessionRepository.findBySessionIdOptional(sessionId);
      if (!session) {
        return null;
      }

      // Cập nhật last activity
      await this.genericSessionRepository.updateLastActivity(sessionId);

      return this.mapToResponseDto(session);
    } catch (error) {
      this.logger.error(`Error getting session: ${error.message}`, error.stack);
      return null;
    }
  }

  /**
   * Cập nhật session
   * @param sessionId Session ID
   * @param updateDto Data để cập nhật
   * @returns Session đã được cập nhật
   */
  async updateSession(
    sessionId: string,
    updateDto: UpdateGenericSessionDto,
  ): Promise<GenericSessionResponseDto> {
    try {
      const session =
        await this.genericSessionRepository.findBySessionId(sessionId);

      // Cập nhật các trường
      if (updateDto.pageConfig !== undefined) {
        session.pageConfig = updateDto.pageConfig;
      }
      if (updateDto.isActive !== undefined) {
        session.isActive = updateDto.isActive;
      }
      if (updateDto.metadata !== undefined) {
        session.metadata = updateDto.metadata;
      }
      if (updateDto.expiresAt !== undefined) {
        session.expiresAt = updateDto.expiresAt
          ? new Date(updateDto.expiresAt)
          : undefined;
      }

      const updatedSession = await this.genericSessionRepository.save(session);

      this.logger.log(`Updated session: ${sessionId}`);
      return this.mapToResponseDto(updatedSession);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Error updating session: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        GENERIC_ERROR_CODES.GENERIC_SESSION_UPDATE_ERROR,
        `Lỗi khi cập nhật session ${sessionId}`,
      );
    }
  }

  /**
   * Xóa session và tất cả dữ liệu liên quan
   * @param sessionId Session ID
   * @returns Số lượng records đã xóa
   */
  async deleteSession(sessionId: string): Promise<{
    deletedWidgets: number;
    deletedLayouts: number;
    deletedSessions: number;
  }> {
    try {
      // Xóa widgets
      const deletedWidgets =
        await this.genericSessionWidgetRepository.removeAllBySessionId(
          sessionId,
        );

      // Xóa layout
      const deletedLayouts =
        await this.genericSessionLayoutRepository.removeBySessionId(sessionId);

      // Xóa session
      const result = await this.genericSessionRepository.delete({ sessionId });
      const deletedSessions = result.affected || 0;

      this.logger.log(
        `Deleted session ${sessionId}: ${deletedWidgets} widgets, ${deletedLayouts} layouts, ${deletedSessions} sessions`,
      );

      return { deletedWidgets, deletedLayouts, deletedSessions };
    } catch (error) {
      this.logger.error(
        `Error deleting session: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        GENERIC_ERROR_CODES.GENERIC_SESSION_DELETE_ERROR,
        `Lỗi khi xóa session ${sessionId}`,
      );
    }
  }

  /**
   * Deactivate session
   * @param sessionId Session ID
   * @returns Session đã được deactivate
   */
  async deactivateSession(
    sessionId: string,
  ): Promise<GenericSessionResponseDto> {
    try {
      const session =
        await this.genericSessionRepository.deactivateSession(sessionId);

      this.logger.log(`Deactivated session: ${sessionId}`);
      return this.mapToResponseDto(session);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Error deactivating session: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        GENERIC_ERROR_CODES.GENERIC_SESSION_UPDATE_ERROR,
        `Lỗi khi deactivate session ${sessionId}`,
      );
    }
  }

  /**
   * Lấy tất cả sessions active của user
   * @param userId User ID
   * @returns Danh sách sessions active
   */
  async getActiveSessionsByUserId(
    userId: number,
  ): Promise<GenericSessionResponseDto[]> {
    try {
      const sessions =
        await this.genericSessionRepository.findActiveSessionsByUserId(userId);
      return sessions.map((session) => this.mapToResponseDto(session));
    } catch (error) {
      this.logger.error(
        `Error getting active sessions by user ID: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        GENERIC_ERROR_CODES.GENERIC_SESSION_QUERY_ERROR,
        `Lỗi khi lấy sessions của user ${userId}`,
      );
    }
  }

  /**
   * Lấy tất cả sessions active
   * @returns Danh sách sessions active
   */
  async getAllActiveSessions(): Promise<GenericSessionResponseDto[]> {
    try {
      const sessions = await this.genericSessionRepository.findActiveSessions();
      return sessions.map((session) => this.mapToResponseDto(session));
    } catch (error) {
      this.logger.error(
        `Error getting all active sessions: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        GENERIC_ERROR_CODES.GENERIC_SESSION_QUERY_ERROR,
        'Lỗi khi lấy tất cả sessions active',
      );
    }
  }

  /**
   * Cleanup sessions hết hạn
   * @returns Số lượng sessions đã được cleanup
   */
  async cleanupExpiredSessions(): Promise<number> {
    try {
      const cleanedCount =
        await this.genericSessionRepository.cleanupExpiredSessions();

      if (cleanedCount > 0) {
        this.logger.log(`Cleaned up ${cleanedCount} expired sessions`);
      }

      return cleanedCount;
    } catch (error) {
      this.logger.error(
        `Error cleaning up expired sessions: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        GENERIC_ERROR_CODES.GENERIC_SESSION_CLEANUP_ERROR,
        'Lỗi khi cleanup sessions hết hạn',
      );
    }
  }

  /**
   * Kiểm tra session có tồn tại và active không
   * @param sessionId Session ID
   * @returns true nếu session tồn tại và active
   */
  async isSessionActiveAndValid(sessionId: string): Promise<boolean> {
    try {
      const session =
        await this.genericSessionRepository.findBySessionIdOptional(sessionId);

      if (!session || !session.isActive) {
        return false;
      }

      // Kiểm tra expiration
      if (session.expiresAt && session.expiresAt < new Date()) {
        // Auto-deactivate expired session
        await this.genericSessionRepository.deactivateSession(sessionId);
        return false;
      }

      return true;
    } catch (error) {
      this.logger.error(
        `Error checking session validity: ${error.message}`,
        error.stack,
      );
      return false;
    }
  }

  /**
   * Generate unique session ID
   * @returns Unique session ID
   */
  private generateSessionId(): string {
    return `session-${Date.now()}-${uuidv4().substring(0, 8)}`;
  }

  /**
   * Map entity to response DTO
   * @param session GenericSession entity
   * @returns GenericSessionResponseDto
   */
  private mapToResponseDto(session: GenericSession): GenericSessionResponseDto {
    return {
      id: session.id,
      sessionId: session.sessionId,
      userId: session.userId,
      pageConfig: session.pageConfig,
      isActive: session.isActive,
      lastActivity: session.lastActivity,
      createdAt: session.createdAt,
      updatedAt: session.updatedAt,
      metadata: session.metadata,
      expiresAt: session.expiresAt,
    };
  }
}

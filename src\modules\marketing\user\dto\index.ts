export * from './tag';
export * from './audience';
// Export từ segment với alias cho ExportFormat để tránh conflict
export {
  ExportFormat as SegmentExportFormat,
  ExportDataType,
  SegmentExportDto,
  SegmentExportResponseDto,
  SegmentImportDto,
  SegmentImportResponseDto,
} from './segment/segment-export.dto';
export * from './segment/create-segment.dto';
// export * from './segment/create-zalo-segment.dto'; // Moved to specific export below
export * from './segment/delete-multiple-segment.dto';
export * from './segment/get-segment-audiences.dto';
export * from './segment/segment-analytics.dto';
export * from './segment/segment-audience-query.dto';
export * from './segment/segment-audience-response.dto';
export * from './segment/segment-automation.dto';
export * from './segment/segment-comparison.dto';
export * from './segment/segment-criteria.dto';
export * from './segment/segment-demographics.dto';
export * from './segment/segment-preview.dto';
export * from './segment/segment-query.dto';
export * from './segment/segment-response.dto';
export * from './segment/segment-stats.dto';
export * from './segment/update-segment.dto';
export * from './segment/user-convert-customer-segment.dto';
export * from './segment/available-fields.dto';

export * from './user-segment-tag.dto';
export * from './user-segment-tag.dto';
export * from './campaign';
export * from './common';
export * from './statistics';
export * from './template-email';
export * from './overview';
export * from './sms-servers';

// Export từ zalo với alias cho ExportFormat để tránh conflict
export {
  ExportFormat as ZaloExportFormat,
  ConversationType,
  ConversationTargetDto,
  ConversationExportResultDto,
  ExportZaloConversationDto,
  ExportZaloConversationResponseDto,
} from './zalo/export-conversation.dto';
export * from './zalo/broadcast-message.dto';
export * from './zalo/bulk-delete-followers.dto';
export * from './zalo/bulk-delete-messages.dto';
export * from './zalo/bulk-delete-official-accounts.dto';
export * from './zalo/bulk-delete-segments.dto';
export * from './zalo/bulk-delete-zalo-campaign.dto';
export * from './zalo/connect-official-account.dto';
export * from './zalo/consultation-message.dto';
export * from './zalo/conversation-message-query.dto';
export * from './zalo/conversation-message-response.dto';
export * from './zalo/delete-tag-request.dto';
export * from './zalo/follower-query.dto';
export * from './zalo/follower-response.dto';
export * from './zalo/list-recent-chat-query.dto';
export * from './zalo/list-recent-chat-response.dto';
export * from './zalo/message-query.dto';
export * from './zalo/message-request.dto';
export * from './zalo/message-response.dto';
export * from './zalo/official-account-query.dto';
export * from './zalo/official-account-response.dto';
export * from './zalo/promotion-message.dto';
export * from './zalo/quota-message.dto';
export * from './zalo/refresh-token.dto';
export * from './zalo/register-zns-template.dto';
export * from './zalo/send-zns-job.dto';
export * from './zalo/send-zns-message.dto';
export * from './zalo/sync-followers-to-audience.dto';
export * from './zalo/sync-zalo-messages.dto';
export * from './zalo/sync-zalo-users-to-audience.dto';
export * from './zalo/tag-request.dto';
export * from './zalo/template-overview-response.dto';
export * from './zalo/transaction-message.dto';
export * from './zalo/unified-message-campaign.dto';
export * from './zalo/upload-zns-image.dto';
export * from './zalo/user-getlist.dto';
export * from './zalo/zalo-ads-overview.dto';
export * from './zalo/zalo-article-management.dto';
export * from './zalo-conversations';
// Export từ zalo-article.dto (exclude conflicting classes)
export {
  ZaloArticleCoverDto,
  ZaloArticleBodyItemDto,
  CreateZaloArticleNormalDto,
  CreateZaloArticleVideoDto,
  ZaloArticleResponseDto,
  CheckZaloArticleProcessDto,
  ZaloArticleProcessResponseDto,
  CheckZaloVideoStatusDto,
  ZaloVideoStatusResponseDto,
  VerifyZaloArticleDto,
  ZaloArticleVerifyResponseDto,
  GetZaloArticleDetailDto,
  ZaloArticleCiteDto,
  ZaloArticleDetailNormalDto,
  ZaloArticleDetailVideoDto,
  GetZaloArticleListDto,
  QueryZaloArticleListDto,
  ZaloArticleListItemDto,
  ZaloArticleListResponseDto,
  RemoveZaloArticleDto,
  ZaloArticleRemoveResponseDto,
  UpdateZaloArticleNormalDto,
  UpdateZaloArticleVideoDto,
  ZaloArticleUpdateResponseDto,
  CreateZaloArticleWithStatusDto,
  ZaloArticleDraftResponseDto,
  BulkDeleteZaloArticlesDto,
  BulkDeleteArticleResultDto,
  BulkDeleteZaloArticlesResponseDto,
  // Export conflicting classes with alias
  ZaloVideoUploadRequestDto as ZaloArticleVideoUploadRequestDto,
  ZaloVideoUploadResponseDto as ZaloArticleVideoUploadResponseDto,
} from './zalo/zalo-article.dto';

export * from './zalo/zalo-automation-log-query.dto';
export * from './zalo/zalo-automation-log-response.dto';
export * from './zalo/zalo-automation.dto';
export * from './zalo/zalo-campaign-log-query.dto';
export * from './zalo/zalo-campaign-log-response.dto';
export * from './zalo/zalo-campaign.dto';
export * from './zalo/zalo-oa-message-campaign.dto';
export * from './zalo/zalo-official-account-info-response.dto';
export * from './zalo/zalo-segment.dto';
export * from './zalo/zalo-statistics.dto';
export * from './zalo/zalo-template.dto';

// Export từ zalo-video-upload.dto với alias cho các class bị conflict
export {
  ZaloVideoUploadRequestDto as ZaloVideoUploadRequestDto,
  ZaloVideoUploadResponseDto as ZaloVideoUploadResponseDto,
  ZaloVideoUploadQueryDto,
  ZaloVideoUploadStatus,
} from './zalo/zalo-video-upload.dto';
export * from './zalo/zalo-zns-template-api.dto';
export * from './zalo/zns-campaign.dto';
export * from './zalo/zns-message-query.dto';
export * from './zalo/zns-message-response.dto';
export * from './zalo/zns-rating-query.dto';
export * from './zalo/zns-rating-response.dto';
export * from './zalo/zns-template-query.dto';
export * from './zalo/zns-template-response.dto';

// Re-export CreateZaloSegmentDto từ cả segment và zalo với alias để tránh conflict
export { CreateZaloSegmentDto as CreateZaloSegmentFromSegmentDto } from './segment/create-zalo-segment.dto';
export { CreateZaloSegmentDto as CreateZaloSegmentFromZaloDto } from './zalo/zalo-segment.dto';
export * from './zalo-upload.dto';
export * from './audience-custom-field-definition';

// Zalo Group & Content DTOs
export * from './zalo-group';
export * from './zalo-group-message';

// Zalo Personal DTOs
export * from './zalo-personal';
export {
  CreateEmailCampaignDto,
  CreateEmailCampaignWithTemplateDto,
  CreateEmailCampaignWithTemplateResponseDto,
  CreateEmailCampaignResponseDto,
  RecentCampaignsResponseDto,
  EmailCampaignOverviewResponseDto,
  RecentCampaignDto,
  CreateEmailCampaignWithEmailListDto,
  CreateEmailCampaignWithAudienceListDto,
  EmailCampaignStatisticsDto,
  EmailCampaignPerformanceDto,
  EmailCampaignStatisticsResponseDto,
  EmailCampaignPerformanceResponseDto,
} from './email-campaign';

export type {
  EmailMarketingJobDto,
  BatchEmailMarketingJobDto,
  EmailRecipientDto,
  EmailCampaignQueryDto,
  EmailCampaignItemDto,
  OverviewDashboardDto,
  PerformanceMetricsDto,
  TrendChartDto,
  TrendsQueryDto,
  CampaignComparisonDto,
  CampaignPerformanceListDto,
  CampaignPerformanceItemDto,
  CampaignComparisonItemDto,
} from './email-campaign';

// Zalo QR Code SSE DTOs
export * from './zalo-qr-code-sse.dto';

// Seeding Group DTOs
export * from './seeding-group/seeding-group.dto';

import { Injectable, Logger } from '@nestjs/common';
import { ZaloGmfGroupManagementService } from '@/shared/services/zalo/zalo-gmf-group-management.service';
import { ZaloOAIntegrationService } from '@/modules/integration/services/zalo-oa-integration.service';
import { ZaloGroupRepository } from '../repositories/zalo-group.repository';
import { ZaloGroup } from '../entities/zalo-group.entity';
import { ZaloGroupStatus } from '../entities/zalo-group.entity';
import { AppException, ErrorCode } from '@/common';
import { Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { Integration } from '@/modules/integration/entities/integration.entity';
import { IntegrationProvider } from '@/modules/integration/entities/integration-provider.entity';
import { ProviderEnum } from '@/modules/integration/constants/provider.enum';
import { KeyPairEncryptionService } from '@/shared/services/encryption/key-pair-encryption.service';
import { ZaloOAPayload } from '@/modules/integration/interfaces/payload_encryption.interface';
import { ZaloOAMetadata } from '@/modules/integration/interfaces/zalo-oa-metadata.interface';

/**
 * Interface cho kết quả đồng bộ
 */
export interface ZaloGroupSyncResult {
  totalIntegrations: number;
  processedIntegrations: number;
  totalGroups: number;
  createdGroups: number;
  updatedGroups: number;
  errors: Array<{
    integrationId: string;
    oaId: string;
    error: string;
  }>;
}

/**
 * Service xử lý đồng bộ danh sách nhóm Zalo từ tất cả integrations
 */
@Injectable()
export class ZaloGroupSyncService {
  private readonly logger = new Logger(ZaloGroupSyncService.name);

  constructor(
    private readonly zaloGmfGroupService: ZaloGmfGroupManagementService,
    private readonly zaloOAIntegrationService: ZaloOAIntegrationService,
    private readonly zaloGroupRepository: ZaloGroupRepository,
    private readonly keyPairEncryptionService: KeyPairEncryptionService,
    @InjectRepository(Integration)
    private readonly integrationRepository: Repository<Integration>,
    @InjectRepository(IntegrationProvider)
    private readonly integrationProviderRepository: Repository<IntegrationProvider>,
  ) {}

  /**
   * Đồng bộ danh sách nhóm từ tất cả Zalo OA integrations
   */
  async syncAllZaloGroups(): Promise<ZaloGroupSyncResult> {
    this.logger.log(
      'Bắt đầu đồng bộ danh sách nhóm Zalo từ tất cả integrations',
    );

    const result: ZaloGroupSyncResult = {
      totalIntegrations: 0,
      processedIntegrations: 0,
      totalGroups: 0,
      createdGroups: 0,
      updatedGroups: 0,
      errors: [],
    };

    try {
      // Lấy tất cả Zalo OA integrations
      const integrations = await this.getAllZaloOAIntegrations();
      result.totalIntegrations = integrations.length;

      this.logger.log(`Tìm thấy ${integrations.length} Zalo OA integrations`);

      // Xử lý từng integration
      for (const integration of integrations) {
        try {
          await this.syncGroupsForIntegration(integration, result);
          result.processedIntegrations++;
        } catch (error) {
          this.logger.error(
            `Lỗi khi đồng bộ integration ${integration.id}: ${error.message}`,
            error.stack,
          );

          const metadata = integration.metadata as ZaloOAMetadata;
          result.errors.push({
            integrationId: integration.id,
            oaId: metadata?.oaId || 'unknown',
            error: error.message,
          });
        }
      }

      this.logger.log(
        `Hoàn thành đồng bộ: ${result.processedIntegrations}/${result.totalIntegrations} integrations, ` +
          `${result.totalGroups} groups (${result.createdGroups} created, ${result.updatedGroups} updated)`,
      );

      return result;
    } catch (error) {
      this.logger.error(
        `Lỗi trong quá trình đồng bộ: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Lỗi trong quá trình đồng bộ danh sách nhóm Zalo',
      );
    }
  }

  /**
   * Lấy tất cả Zalo OA integrations active
   */
  private async getAllZaloOAIntegrations(): Promise<Integration[]> {
    try {
      // Tìm provider Zalo OA
      const provider = await this.integrationProviderRepository.findOne({
        where: { type: ProviderEnum.ZALO_OA },
      });

      if (!provider) {
        throw new AppException(
          ErrorCode.NOT_FOUND,
          'Không tìm thấy Zalo OA provider',
        );
      }

      // Lấy tất cả Integration có type ZALO_OA và status active
      const integrations = await this.integrationRepository
        .createQueryBuilder('integration')
        .where('integration.typeId = :typeId', { typeId: provider.id })
        .andWhere("integration.metadata->>'status' = :status", {
          status: 'active',
        })
        .andWhere('integration.encryptedConfig IS NOT NULL')
        .andWhere('integration.secretKey IS NOT NULL')
        .getMany();

      return integrations;
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy danh sách integrations: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Đồng bộ groups cho một integration cụ thể
   */
  private async syncGroupsForIntegration(
    integration: Integration,
    result: ZaloGroupSyncResult,
  ): Promise<void> {
    const metadata = integration.metadata as ZaloOAMetadata;
    const oaId = metadata?.oaId;

    if (!oaId) {
      throw new Error('Integration không có oaId trong metadata');
    }

    this.logger.debug(
      `Đồng bộ groups cho OA ${oaId} (integration: ${integration.id})`,
    );

    // Giải mã access token
    const tokens = await this.decryptTokens(integration);

    // Lấy danh sách groups từ Zalo API với phân trang
    let offset = 0;
    const limit = 50; // Zalo API giới hạn tối đa 50
    let hasMore = true;

    while (hasMore) {
      try {
        const zaloResult = await this.zaloGmfGroupService.getGroupsOfOA(
          tokens.accessToken,
          offset,
          limit,
        );

        this.logger.debug(
          `Lấy được ${zaloResult.groups.length} groups từ Zalo API (offset: ${offset})`,
        );

        // Xử lý từng group
        for (const zaloGroup of zaloResult.groups) {
          try {
            await this.syncSingleGroup(integration, zaloGroup, result);
          } catch (error) {
            this.logger.warn(
              `Lỗi khi đồng bộ group ${zaloGroup.group_id}: ${error.message}`,
            );
          }
        }

        // Kiểm tra có còn dữ liệu không
        hasMore =
          zaloResult.groups.length === limit &&
          offset + limit < zaloResult.total;
        offset += limit;
      } catch (error) {
        this.logger.error(
          `Lỗi khi lấy groups từ Zalo API (offset: ${offset}): ${error.message}`,
        );
        break; // Dừng vòng lặp nếu có lỗi
      }
    }
  }

  /**
   * Giải mã tokens từ integration
   */
  private async decryptTokens(
    integration: Integration,
  ): Promise<ZaloOAPayload> {
    if (!integration.encryptedConfig || !integration.secretKey) {
      throw new Error('Integration không có encrypted config hoặc secret key');
    }

    return this.keyPairEncryptionService.decryptObject<ZaloOAPayload>(
      integration.encryptedConfig,
      integration.secretKey,
    );
  }

  /**
   * Đồng bộ một group cụ thể
   */
  private async syncSingleGroup(
    integration: Integration,
    zaloGroup: any,
    result: ZaloGroupSyncResult,
  ): Promise<void> {
    const userId = integration.userId || integration.employeeId;

    if (!userId) {
      throw new Error('Integration không có userId hoặc employeeId');
    }

    // Tìm group trong database
    const existingGroup = await this.zaloGroupRepository.findOne({
      where: {
        userId,
        zaloOfficialAccountId: integration.id,
        groupId: zaloGroup.group_id,
      },
    });

    // Chuẩn bị dữ liệu group
    const groupData: Partial<ZaloGroup> = {
      userId,
      zaloOfficialAccountId: integration.id,
      integrationId: integration.id, // Thêm integration_id cho hệ thống integration mới
      groupId: zaloGroup.group_id,
      groupName: zaloGroup.name,
      description: zaloGroup.group_description || '',
      avatarUrl: zaloGroup.avatar || '',
      memberCount: zaloGroup.total_member,
      status: this.mapZaloStatusToEntityStatus(zaloGroup.status),
      lastActivityAt: Date.now(),
    };

    if (existingGroup) {
      // Cập nhật group hiện có
      await this.zaloGroupRepository.update(existingGroup.id, groupData);
      result.updatedGroups++;
      this.logger.debug(`Cập nhật group ${zaloGroup.group_id}`);
    } else {
      // Tạo group mới
      await this.zaloGroupRepository.create(groupData);
      result.createdGroups++;
      this.logger.debug(`Tạo mới group ${zaloGroup.group_id}`);
    }

    result.totalGroups++;
  }

  /**
   * Chuyển đổi status từ Zalo API sang entity status
   */
  private mapZaloStatusToEntityStatus(zaloStatus: string): ZaloGroupStatus {
    switch (zaloStatus) {
      case 'enabled':
        return ZaloGroupStatus.ACTIVE;
      case 'disabled':
        return ZaloGroupStatus.INACTIVE;
      default:
        return ZaloGroupStatus.INACTIVE;
    }
  }
}

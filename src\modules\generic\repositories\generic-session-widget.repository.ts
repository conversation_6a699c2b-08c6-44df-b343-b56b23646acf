import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { GenericSessionWidget } from '../entities/generic-session-widget.entity';
import { AppException } from '@/common';
import { GENERIC_ERROR_CODES } from '../exceptions';

@Injectable()
export class GenericSessionWidgetRepository extends Repository<GenericSessionWidget> {
  private readonly logger = new Logger(GenericSessionWidgetRepository.name);

  constructor(private dataSource: DataSource) {
    super(GenericSessionWidget, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder c<PERSON> bản cho GenericSessionWidget
   * @returns SelectQueryBuilder<GenericSessionWidget>
   */
  private createBaseQuery(): SelectQueryBuilder<GenericSessionWidget> {
    return this.createQueryBuilder('genericSessionWidget');
  }

  /**
   * Tìm widget theo ID
   * @param id ID của widget
   * @returns Widget nếu tìm thấy
   * @throws AppException nếu không tìm thấy widget
   */
  async findById(id: string): Promise<GenericSessionWidget> {
    try {
      const widget = await this.createBaseQuery()
        .where('genericSessionWidget.id = :id', { id })
        .getOne();

      if (!widget) {
        throw new AppException(
          GENERIC_ERROR_CODES.GENERIC_WIDGET_NOT_FOUND,
          `Không tìm thấy widget với ID ${id}`,
        );
      }

      return widget;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Error finding widget by ID: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        GENERIC_ERROR_CODES.GENERIC_WIDGET_NOT_FOUND,
        `Lỗi khi tìm widget với ID ${id}`,
      );
    }
  }

  /**
   * Tìm widget theo sessionId và widgetId
   * @param sessionId Session ID
   * @param widgetId Widget ID
   * @returns Widget nếu tìm thấy
   * @throws AppException nếu không tìm thấy widget
   */
  async findBySessionAndWidgetId(
    sessionId: string,
    widgetId: string,
  ): Promise<GenericSessionWidget> {
    try {
      const widget = await this.createBaseQuery()
        .where('genericSessionWidget.sessionId = :sessionId', { sessionId })
        .andWhere('genericSessionWidget.widgetId = :widgetId', { widgetId })
        .getOne();

      if (!widget) {
        throw new AppException(
          GENERIC_ERROR_CODES.GENERIC_WIDGET_NOT_FOUND,
          `Không tìm thấy widget ${widgetId} trong session ${sessionId}`,
        );
      }

      return widget;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Error finding widget by session and widget ID: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        GENERIC_ERROR_CODES.GENERIC_WIDGET_NOT_FOUND,
        `Lỗi khi tìm widget ${widgetId} trong session ${sessionId}`,
      );
    }
  }

  /**
   * Tìm widget theo sessionId và widgetId (optional - không throw error)
   * @param sessionId Session ID
   * @param widgetId Widget ID
   * @returns Widget nếu tìm thấy, null nếu không tìm thấy
   */
  async findBySessionAndWidgetIdOptional(
    sessionId: string,
    widgetId: string,
  ): Promise<GenericSessionWidget | null> {
    try {
      return await this.createBaseQuery()
        .where('genericSessionWidget.sessionId = :sessionId', { sessionId })
        .andWhere('genericSessionWidget.widgetId = :widgetId', { widgetId })
        .getOne();
    } catch (error) {
      this.logger.error(
        `Error finding widget by session and widget ID: ${error.message}`,
        error.stack,
      );
      return null;
    }
  }

  /**
   * Tìm tất cả widgets của session
   * @param sessionId Session ID
   * @param includeHidden Có bao gồm widgets ẩn không
   * @returns Danh sách widgets
   */
  async findBySessionId(
    sessionId: string,
    includeHidden: boolean = false,
  ): Promise<GenericSessionWidget[]> {
    try {
      const query = this.createBaseQuery().where(
        'genericSessionWidget.sessionId = :sessionId',
        { sessionId },
      );

      if (!includeHidden) {
        query.andWhere('genericSessionWidget.isVisible = :isVisible', {
          isVisible: true,
        });
      }

      return await query
        .orderBy('genericSessionWidget.displayOrder', 'ASC')
        .addOrderBy('genericSessionWidget.createdAt', 'ASC')
        .getMany();
    } catch (error) {
      this.logger.error(
        `Error finding widgets by session ID: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        GENERIC_ERROR_CODES.GENERIC_WIDGET_QUERY_ERROR,
        `Lỗi khi tìm widgets của session ${sessionId}`,
      );
    }
  }

  /**
   * Tìm widgets theo loại
   * @param sessionId Session ID
   * @param widgetType Loại widget
   * @returns Danh sách widgets
   */
  async findBySessionIdAndType(
    sessionId: string,
    widgetType: string,
  ): Promise<GenericSessionWidget[]> {
    try {
      return await this.createBaseQuery()
        .where('genericSessionWidget.sessionId = :sessionId', { sessionId })
        .andWhere('genericSessionWidget.widgetType = :widgetType', {
          widgetType,
        })
        .andWhere('genericSessionWidget.isVisible = :isVisible', {
          isVisible: true,
        })
        .orderBy('genericSessionWidget.displayOrder', 'ASC')
        .addOrderBy('genericSessionWidget.createdAt', 'ASC')
        .getMany();
    } catch (error) {
      this.logger.error(
        `Error finding widgets by session ID and type: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        GENERIC_ERROR_CODES.GENERIC_WIDGET_QUERY_ERROR,
        `Lỗi khi tìm widgets loại ${widgetType} của session ${sessionId}`,
      );
    }
  }

  /**
   * Kiểm tra xem widgetId đã tồn tại trong session chưa
   * @param sessionId Session ID
   * @param widgetId Widget ID cần kiểm tra
   * @param excludeId ID của widget cần loại trừ (dùng khi cập nhật)
   * @returns true nếu widgetId đã tồn tại, false nếu chưa
   */
  async isWidgetIdExistsInSession(
    sessionId: string,
    widgetId: string,
    excludeId?: string,
  ): Promise<boolean> {
    try {
      const query = this.createBaseQuery()
        .where('genericSessionWidget.sessionId = :sessionId', { sessionId })
        .andWhere('genericSessionWidget.widgetId = :widgetId', { widgetId });

      if (excludeId) {
        query.andWhere('genericSessionWidget.id != :excludeId', { excludeId });
      }

      const count = await query.getCount();
      return count > 0;
    } catch (error) {
      this.logger.error(
        `Error checking widget ID existence: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        GENERIC_ERROR_CODES.GENERIC_WIDGET_CHECK_ERROR,
        'Lỗi khi kiểm tra widgetId',
      );
    }
  }

  /**
   * Xóa widget theo sessionId và widgetId
   * @param sessionId Session ID
   * @param widgetId Widget ID
   * @returns Số lượng widgets đã xóa
   */
  async removeBySessionAndWidgetId(
    sessionId: string,
    widgetId: string,
  ): Promise<number> {
    try {
      const result = await this.createQueryBuilder()
        .delete()
        .from(GenericSessionWidget)
        .where('sessionId = :sessionId', { sessionId })
        .andWhere('widgetId = :widgetId', { widgetId })
        .execute();

      return result.affected || 0;
    } catch (error) {
      this.logger.error(`Error removing widget: ${error.message}`, error.stack);
      throw new AppException(
        GENERIC_ERROR_CODES.GENERIC_WIDGET_DELETE_ERROR,
        `Lỗi khi xóa widget ${widgetId} từ session ${sessionId}`,
      );
    }
  }

  /**
   * Xóa tất cả widgets của session
   * @param sessionId Session ID
   * @returns Số lượng widgets đã xóa
   */
  async removeAllBySessionId(sessionId: string): Promise<number> {
    try {
      const result = await this.createQueryBuilder()
        .delete()
        .from(GenericSessionWidget)
        .where('sessionId = :sessionId', { sessionId })
        .execute();

      return result.affected || 0;
    } catch (error) {
      this.logger.error(
        `Error removing all widgets: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        GENERIC_ERROR_CODES.GENERIC_WIDGET_DELETE_ERROR,
        `Lỗi khi xóa tất cả widgets của session ${sessionId}`,
      );
    }
  }

  /**
   * Cập nhật display order của widgets
   * @param sessionId Session ID
   * @param widgetOrders Array of { widgetId, displayOrder }
   * @returns Số lượng widgets đã cập nhật
   */
  async updateDisplayOrders(
    sessionId: string,
    widgetOrders: Array<{ widgetId: string; displayOrder: number }>,
  ): Promise<number> {
    try {
      let updatedCount = 0;

      for (const { widgetId, displayOrder } of widgetOrders) {
        const result = await this.createQueryBuilder()
          .update(GenericSessionWidget)
          .set({
            displayOrder,
            updatedAt: new Date(),
          })
          .where('sessionId = :sessionId', { sessionId })
          .andWhere('widgetId = :widgetId', { widgetId })
          .execute();

        updatedCount += result.affected || 0;
      }

      return updatedCount;
    } catch (error) {
      this.logger.error(
        `Error updating display orders: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        GENERIC_ERROR_CODES.GENERIC_WIDGET_UPDATE_ERROR,
        `Lỗi khi cập nhật display orders cho session ${sessionId}`,
      );
    }
  }

  /**
   * Ẩn/hiện widget
   * @param sessionId Session ID
   * @param widgetId Widget ID
   * @param isVisible Trạng thái hiển thị
   * @returns Widget đã được cập nhật
   */
  async updateVisibility(
    sessionId: string,
    widgetId: string,
    isVisible: boolean,
  ): Promise<GenericSessionWidget> {
    try {
      await this.createQueryBuilder()
        .update(GenericSessionWidget)
        .set({
          isVisible,
          updatedAt: new Date(),
        })
        .where('sessionId = :sessionId', { sessionId })
        .andWhere('widgetId = :widgetId', { widgetId })
        .execute();

      return await this.findBySessionAndWidgetId(sessionId, widgetId);
    } catch (error) {
      this.logger.error(
        `Error updating widget visibility: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        GENERIC_ERROR_CODES.GENERIC_WIDGET_UPDATE_ERROR,
        `Lỗi khi cập nhật visibility cho widget ${widgetId}`,
      );
    }
  }
}

import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';
import * as cheerio from 'cheerio';
import { Url } from '../../entities/url.entity';
import { CrawlDto } from '../dto/crawl.dto';
import { AdvancedCrawlerService } from '../../shared/services/advanced-crawler.service';
import { UserAgentRotationService } from '../../shared/services/user-agent-rotation.service';
import { OwnerType } from '../../constants/owner-type.enum';

interface ExtractedMetadata {
  url: string;
  title: string;
  content: string;
  tags: any;
}

interface CrawlResult {
  html: string;
  urls?: string[];
  metadata?: any;
}

@Injectable()
export class DirectCrawlService {
  private readonly logger = new Logger(DirectCrawlService.name);

  // Cache cho metadata
  private metadataCache = new Map<string, { metadata: ExtractedMetadata; expiry: number }>();
  private readonly METADATA_CACHE_TTL = 30 * 60 * 1000; // 30 phút

  constructor(
    @InjectRepository(Url)
    private readonly urlRepository: Repository<Url>,
    private readonly httpService: HttpService,
    private readonly advancedCrawlerService: AdvancedCrawlerService,
    private readonly userAgentService: UserAgentRotationService,
  ) {}

  /**
   * Crawl URL trực tiếp và lưu vào database
   */
  async crawlUrl(
    userId: number,
    crawlDto: CrawlDto,
  ): Promise<{
    status: string;
    message: string;
    urlsProcessed?: number;
    urlsSaved?: number;
  }> {
    const processedUrls: ExtractedMetadata[] = [];
    const visitedUrls = new Set<string>();
    const urlsToVisit: Array<{ url: string; depth: number }> = [
      { url: crawlDto.url, depth: 0 }
    ];

    const maxUrls = crawlDto.maxUrls || 60; // ✅ Sử dụng giá trị từ user, mặc định 60
    let urlsProcessed = 0;
    let urlsSaved = 0;
    let urlsUpdated = 0;

    this.logger.log(`Bắt đầu crawl trực tiếp: ${crawlDto.url}, depth: ${crawlDto.depth}, maxUrls: ${maxUrls}`);

    try {
      // Main crawling loop - xử lý theo batch như CrawlWorker
      while (urlsToVisit.length > 0 && urlsProcessed < maxUrls) {
        // Lấy batch URLs để xử lý (giới hạn 5 URLs đồng thời cho crawl trực tiếp)
        const BATCH_SIZE = 5;
        const currentBatch: Array<{ url: string; depth: number }> = [];

        // Lấy tối đa BATCH_SIZE URLs chưa được xử lý
        while (currentBatch.length < BATCH_SIZE && urlsToVisit.length > 0) {
          const currentItem = urlsToVisit.shift();
          if (!currentItem) continue;

          const { url } = currentItem;

          // Bỏ qua nếu URL đã được xử lý
          if (visitedUrls.has(url)) continue;

          // Đánh dấu URL đã được xử lý
          visitedUrls.add(url);
          currentBatch.push(currentItem);
        }

        if (currentBatch.length === 0) break;

        this.logger.log(
          `🔄 Xử lý batch ${currentBatch.length} URLs (đã xử lý: ${visitedUrls.size}/${maxUrls}, còn lại trong queue: ${urlsToVisit.length}, tổng: ${visitedUrls.size + urlsToVisit.length})`
        );

        // Xử lý batch URLs song song
        const batchPromises = currentBatch.map(({ url, depth }) =>
          this.processSingleUrl(url, depth, crawlDto, userId, visitedUrls, urlsToVisit, maxUrls)
        );

        const batchResults = await Promise.allSettled(batchPromises);

        // Xử lý kết quả batch
        for (let i = 0; i < batchResults.length; i++) {
          const result = batchResults[i];
          const { url } = currentBatch[i];

          if (result.status === 'fulfilled' && result.value && result.value.metadata) {
            processedUrls.push(result.value.metadata);
            if (result.value.isNewUrl) {
              urlsSaved++;
              this.logger.log(`✅ Đã lưu URL mới: ${url}`);
            } else {
              urlsUpdated++;
              this.logger.log(`🔄 Đã cập nhật URL: ${url}`);
            }
          } else if (result.status === 'rejected') {
            const error = result.reason;
            this.logger.error(`❌ Lỗi khi crawl URL ${url}: ${error.message}`);
          }
        }

        urlsProcessed = visitedUrls.size;

        // Delay nhỏ giữa các batch để tránh spam
        await this.delay(200);
      }

      const message = `Crawl hoàn thành: ${urlsProcessed} URLs processed, ${urlsSaved} URLs saved (${urlsUpdated} updated)`;
      this.logger.log(message);

      return {
        status: 'success',
        message,
        urlsProcessed,
        urlsSaved,
      };

    } catch (error) {
      this.logger.error(`Lỗi trong quá trình crawl: ${error.message}`, error.stack);
      return {
        status: 'error',
        message: `Crawl thất bại: ${error.message}`,
        urlsProcessed,
        urlsSaved,
      };
    }
  }

  /**
   * Smart crawl với fallback
   */
  private async smartCrawl(url: string): Promise<CrawlResult> {
    try {
      // Phát hiện loại website
      const websiteType = await this.detectWebsiteType(url);

      if (websiteType.needsBrowser && websiteType.confidence > 0.4) {
        // Sử dụng browser automation
        this.logger.log(`🚀 Sử dụng browser automation cho ${websiteType.type} website`);

        const result = await this.advancedCrawlerService.crawlWithDedicatedBrowser(url, {
          waitTime: 3000,
          scrollToBottom: true,
          extractUrls: true,
          takeScreenshot: false,
        }, `directCrawl_${Date.now()}`);

        return {
          html: result.html,
          urls: result.urls,
          metadata: result.metadata,
        };
      } else {
        // Sử dụng HTTP crawling
        this.logger.log(`📄 Sử dụng HTTP crawling cho ${websiteType.type} website`);
        const html = await this.fetchHtml(url);
        const urls = await this.extractChildUrlsFromHtml(html, url);

        return { html, urls };
      }
    } catch (error) {
      this.logger.warn(`Smart crawl thất bại cho ${url}, fallback to HTTP: ${error.message}`);

      // Fallback to HTTP
      const html = await this.fetchHtml(url);
      const urls = await this.extractChildUrlsFromHtml(html, url);

      return { html, urls };
    }
  }

  /**
   * Phát hiện loại website đơn giản
   */
  private async detectWebsiteType(url: string): Promise<{ type: string; needsBrowser: boolean; confidence: number }> {
    try {
      const domain = new URL(url).hostname.toLowerCase();

      // Các website thường cần browser
      const spaIndicators = [
        'react', 'angular', 'vue', 'spa', 'app',
        'facebook', 'twitter', 'instagram', 'linkedin',
        'youtube', 'tiktok', 'pinterest'
      ];

      const needsBrowser = spaIndicators.some(indicator => domain.includes(indicator));

      return {
        type: needsBrowser ? 'spa' : 'static',
        needsBrowser,
        confidence: needsBrowser ? 0.8 : 0.2
      };
    } catch (error) {
      return { type: 'unknown', needsBrowser: false, confidence: 0.1 };
    }
  }

  /**
   * Fetch HTML bằng HTTP
   */
  private async fetchHtml(url: string): Promise<string> {
    try {
      const userAgent = this.userAgentService.getRandomUserAgent();

      const response = await firstValueFrom(
        this.httpService.get(url, {
          headers: {
            'User-Agent': userAgent,
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
          },
          timeout: 10000,
          maxRedirects: 5,
        })
      );

      return response.data;
    } catch (error) {
      throw new Error(`Không thể fetch HTML từ ${url}: ${error.message}`);
    }
  }

  /**
   * Trích xuất metadata từ HTML
   */
  private extractHeadMetadata(html: string, url: string): ExtractedMetadata {
    const $ = cheerio.load(html);

    // Trích xuất title
    let title = $('title').text().trim();
    if (!title) {
      title = $('meta[property="og:title"]').attr('content') ||
        $('meta[name="twitter:title"]').attr('content') ||
        $('h1').first().text().trim() || '';
    }

    // Trích xuất description
    let description = $('meta[name="description"]').attr('content') ||
      $('meta[property="og:description"]').attr('content') ||
      $('meta[name="twitter:description"]').attr('content') || '';

    // Trích xuất keywords
    const keywords = $('meta[name="keywords"]').attr('content') || '';

    // Trích xuất text content từ body (giới hạn) - cải thiện để lấy nhiều content hơn
    const bodyText = $('body').text().replace(/\s+/g, ' ').trim();

    // Nếu không có description, lấy đoạn text đầu từ body
    if (!description && bodyText) {
      description = bodyText.substring(0, 300);
    }

    const content = description || bodyText.substring(0, 1000) || `Content from ${url}`;

    // Tạo tags từ keywords và title
    const tags = this.extractTags(title, keywords, content);

    // Đảm bảo luôn có title, dù chỉ là URL
    const finalTitle = title || url.split('/').pop() || url;

    return {
      url,
      title: finalTitle.substring(0, 500),
      content: content.substring(0, 2000),
      tags,
    };
  }

  /**
   * Trích xuất tags từ content
   */
  private extractTags(title: string, keywords: string, content: string): string[] {
    const tags = new Set<string>();

    // Từ keywords
    if (keywords) {
      keywords.split(',').forEach(keyword => {
        const tag = keyword.trim().toLowerCase();
        if (tag.length > 2) tags.add(tag);
      });
    }

    // Từ title (các từ quan trọng)
    if (title) {
      const titleWords = title.toLowerCase().match(/\b\w{3,}\b/g) || [];
      titleWords.slice(0, 5).forEach(word => tags.add(word));
    }

    return Array.from(tags).slice(0, 10);
  }

  /**
   * Xử lý một URL đơn lẻ
   */
  private async processSingleUrl(
    url: string,
    depth: number,
    crawlDto: CrawlDto,
    userId: number,
    visitedUrls: Set<string>,
    urlsToVisit: Array<{ url: string; depth: number }>,
    maxUrls: number
  ): Promise<{ metadata: ExtractedMetadata | null; isNewUrl: boolean }> {
    try {
      // Kiểm tra cache trước
      const normalizedUrl = this.normalizeUrl(url);
      const cached = this.metadataCache.get(normalizedUrl);

      if (cached && cached.expiry > Date.now()) {
        this.logger.debug(`Cache hit cho URL: ${url}`);

        // Vẫn cần trích xuất child URLs nếu chưa đạt độ sâu tối đa
        if (depth < crawlDto.depth) {
          await this.extractAndAddChildUrls(url, depth, visitedUrls, urlsToVisit, maxUrls);
        }

        return { metadata: cached.metadata, isNewUrl: false };
      }

      // Crawl URL
      const crawlResult = await this.smartCrawl(url);

      // Trích xuất metadata
      const metadata = this.extractHeadMetadata(crawlResult.html, url);

      // Kiểm tra metadata có hợp lệ không (ít nghiêm ngặt hơn)
      if (metadata && (metadata.title?.trim() || metadata.content?.trim() || metadata.url)) {
        // Cache metadata
        this.metadataCache.set(normalizedUrl, {
          metadata,
          expiry: Date.now() + this.METADATA_CACHE_TTL,
        });

        // Lưu vào database
        const isNewUrl = await this.saveUrlToDatabase(metadata, userId);
        if (isNewUrl) {
          this.logger.log(`✅ Đã lưu URL mới: ${url}`);
        } else {
          this.logger.log(`🔄 Đã cập nhật URL đã tồn tại: ${url}`);
        }

        // Trích xuất child URLs nếu chưa đạt độ sâu tối đa
        if (depth < crawlDto.depth) {
          this.logger.log(`🔍 Trích xuất child URLs từ ${url} (depth: ${depth}/${crawlDto.depth})`);

          // Sử dụng URLs từ smart crawl result nếu có
          let childUrls: string[] = [];
          if (crawlResult.urls && crawlResult.urls.length > 0) {
            childUrls = crawlResult.urls;
            this.logger.log(`Smart crawl extracted ${childUrls.length} child URLs from ${url}`);
          } else {
            childUrls = await this.extractChildUrls(url);
            this.logger.log(`Fallback extraction: ${childUrls.length} child URLs from ${url}`);
          }

          // Thêm child URLs vào queue
          let addedCount = 0;
          let skippedDuplicate = 0;
          let skippedLimit = 0;

          this.logger.log(`📋 Chuẩn bị thêm ${childUrls.length} child URLs. Current: visited=${visitedUrls.size}/${maxUrls}, queue=${urlsToVisit.length}`);

          for (const childUrl of childUrls) {
            if (visitedUrls.has(childUrl)) {
              skippedDuplicate++;
              continue;
            }

            // ✅ Chỉ kiểm tra visited URLs, cho phép queue lớn hơn để bù trừ URLs fail/duplicate
            if (visitedUrls.size >= maxUrls) {
              skippedLimit++;
              continue;
            }

            urlsToVisit.push({ url: childUrl, depth: depth + 1 });
            addedCount++;
          }

          this.logger.log(`✅ Đã thêm ${addedCount}/${childUrls.length} child URLs vào queue từ ${url} (skipped: ${skippedDuplicate} duplicate, ${skippedLimit} limit)`);
          this.logger.log(`📊 Queue status: visited=${visitedUrls.size}/${maxUrls}, queue=${urlsToVisit.length}, total_discovered=${visitedUrls.size + urlsToVisit.length}`);

          if (addedCount === 0 && childUrls.length > 0) {
            this.logger.warn(`⚠️ Không thể thêm child URLs nào từ ${url}! Lý do: ${skippedDuplicate > 0 ? `${skippedDuplicate} duplicate URLs` : ''} ${skippedLimit > 0 ? `${skippedLimit} URLs bị giới hạn (visited=${visitedUrls.size}/${maxUrls})` : ''}`);
          }
        }

        return { metadata, isNewUrl };
      } else {
        this.logger.warn(`❌ Metadata không hợp lệ cho URL: ${url} - title: "${metadata?.title}", content length: ${metadata?.content?.length || 0}`);
        return { metadata: null, isNewUrl: false };
      }

    } catch (error) {
      this.logger.error(`Lỗi khi xử lý URL ${url}: ${error.message}`, error.stack);
      return { metadata: null, isNewUrl: false };
    }
  }

  /**
   * Trích xuất và thêm child URLs vào queue
   */
  private async extractAndAddChildUrls(
    url: string,
    depth: number,
    visitedUrls: Set<string>,
    urlsToVisit: Array<{ url: string; depth: number }>,
    maxUrls: number
  ): Promise<void> {
    try {
      const childUrls = await this.extractChildUrls(url);
      let addedCount = 0;
      for (const childUrl of childUrls) {
        if (!visitedUrls.has(childUrl) && visitedUrls.size < maxUrls) {
          urlsToVisit.push({ url: childUrl, depth: depth + 1 });
          addedCount++;
        }
      }

      if (addedCount > 0) {
        this.logger.debug(`Cache: Đã thêm ${addedCount}/${childUrls.length} child URLs vào queue từ ${url}`);
      }
    } catch (childError) {
      this.logger.warn(`Lỗi khi trích xuất child URLs từ cache cho ${url}: ${childError.message}`);
    }
  }

  /**
   * Utility methods
   */
  private normalizeUrl(url: string): string {
    try {
      const urlObj = new URL(url);
      return `${urlObj.protocol}//${urlObj.hostname}${urlObj.pathname}`;
    } catch {
      return url;
    }
  }

  private async delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Trích xuất child URLs từ URL
   */
  private async extractChildUrls(url: string): Promise<string[]> {
    try {
      const html = await this.fetchHtml(url);
      return this.extractChildUrlsFromHtml(html, url);
    } catch (error) {
      this.logger.warn(`Không thể trích xuất child URLs từ ${url}: ${error.message}`);
      return [];
    }
  }

  /**
   * Trích xuất URLs từ HTML
   */
  private async extractChildUrlsFromHtml(html: string, baseUrl: string): Promise<string[]> {
    try {
      const $ = cheerio.load(html);
      const urls = new Set<string>();
      const baseUrlObj = new URL(baseUrl);

      // Trích xuất từ thẻ <a>
      $('a[href]').each((_, element) => {
        const href = $(element).attr('href');
        if (href) {
          try {
            const absoluteUrl = new URL(href, baseUrl).toString();
            // Chỉ lấy URLs cùng domain
            const urlObj = new URL(absoluteUrl);
            if (urlObj.hostname === baseUrlObj.hostname) {
              urls.add(absoluteUrl);
            }
          } catch (e) {
            // Ignore invalid URLs
          }
        }
      });

      // Trích xuất từ thẻ <link> (canonical, alternate, etc.)
      $('link[href]').each((_, element) => {
        const href = $(element).attr('href');
        const rel = $(element).attr('rel');
        if (href && (rel === 'canonical' || rel === 'alternate')) {
          try {
            const absoluteUrl = new URL(href, baseUrl).toString();
            const urlObj = new URL(absoluteUrl);
            if (urlObj.hostname === baseUrlObj.hostname) {
              urls.add(absoluteUrl);
            }
          } catch (e) {
            // Ignore invalid URLs
          }
        }
      });

      const result = Array.from(urls)
        .filter(url => {
          // Lọc bỏ các URLs không mong muốn
          const lowercaseUrl = url.toLowerCase();
          return !lowercaseUrl.includes('#') &&
            !lowercaseUrl.includes('javascript:') &&
            !lowercaseUrl.includes('mailto:') &&
            !lowercaseUrl.includes('tel:') &&
            !lowercaseUrl.endsWith('.pdf') &&
            !lowercaseUrl.endsWith('.jpg') &&
            !lowercaseUrl.endsWith('.png') &&
            !lowercaseUrl.endsWith('.gif');
        })
        .slice(0, 300); // ✅ Tăng giới hạn lên 150 URLs để có đủ child URLs cho crawl

      this.logger.debug(`Trích xuất được ${result.length} child URLs từ ${baseUrl}`);
      return result;

    } catch (error) {
      this.logger.error(`Lỗi khi trích xuất URLs từ HTML: ${error.message}`);
      return [];
    }
  }

  /**
   * Lưu metadata vào database
   * @returns true nếu tạo URL mới, false nếu cập nhật URL đã tồn tại
   */
  private async saveUrlToDatabase(metadata: ExtractedMetadata, userId: number): Promise<boolean> {
    try {
      this.logger.debug(`Đang lưu metadata cho URL: ${metadata.url}`);

      const existingUrl = await this.urlRepository.findOne({
        where: { url: metadata.url }
      });

      const currentTime = Date.now();

      if (existingUrl) {
        // Cập nhật URL đã tồn tại
        existingUrl.title = metadata.title;
        existingUrl.content = metadata.content;
        existingUrl.tags = metadata.tags;
        existingUrl.updatedAt = currentTime;
        await this.urlRepository.save(existingUrl);
        this.logger.debug(`Đã cập nhật URL đã tồn tại: ${metadata.url}`);
        return false; // Không phải URL mới
      } else {
        // Tạo URL mới
        const newUrl = new Url();
        newUrl.url = metadata.url;
        newUrl.title = metadata.title;
        newUrl.content = metadata.content;
        newUrl.tags = metadata.tags;
        newUrl.ownedBy = userId;
        newUrl.ownedByEnum = OwnerType.USER; // Set owner type as USER for user crawl
        newUrl.createdAt = currentTime;
        newUrl.updatedAt = currentTime;
        await this.urlRepository.save(newUrl);
        this.logger.debug(`Đã tạo URL mới: ${metadata.url}`);
        return true; // URL mới được tạo
      }
    } catch (error) {
      this.logger.error(`Lỗi khi lưu URL vào database: ${error.message}`, error.stack);
      throw error;
    }
  }
}

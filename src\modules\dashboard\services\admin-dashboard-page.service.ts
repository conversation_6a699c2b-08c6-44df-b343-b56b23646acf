import { Injectable, Logger } from '@nestjs/common';
import { DashboardPageService } from './dashboard-page.service';
import { DashboardPageRepository } from '../repositories/dashboard-page.repository';
import { DashboardWidgetRepository } from '../repositories/dashboard-widget.repository';
import { DashboardPageType } from '../enums/dashboard-page-type.enum';
import {
  CreateDashboardPageDto,
  UpdateDashboardPageDto,
  QueryDashboardPageDto,
  DashboardPageResponseDto,
} from '../dto/dashboard-page.dto';

/**
 * Service wrapper cho admin dashboard pages với ownerType EMPLOYEE
 */
@Injectable()
export class AdminDashboardPageService {
  private readonly logger = new Logger(AdminDashboardPageService.name);

  constructor(
    private readonly dashboardPageService: DashboardPageService,
    private readonly dashboardPageRepository: DashboardPageRepository,
    private readonly dashboardWidgetRepository: DashboardWidgetRepository,
  ) {}

  /**
   * Tạo dashboard page mới cho admin (ownerType = EMPLOYEE)
   */
  async create(
    dto: CreateDashboardPageDto,
    employeeId: number,
  ): Promise<DashboardPageResponseDto> {
    this.logger.log(
      `Admin creating dashboard page: ${dto.name} for employee ${employeeId}`,
    );

    // Force ownerType = EMPLOYEE và pageType phù hợp
    const adminDto: CreateDashboardPageDto = {
      ...dto,
      ownerType: 'EMPLOYEE',
      pageType: dto.pageType || DashboardPageType.ADMIN_CUSTOM,
    };

    return this.dashboardPageService.create(adminDto, undefined, employeeId);
  }

  /**
   * Lấy danh sách dashboard pages của admin
   */
  async findMany(
    query: QueryDashboardPageDto,
    employeeId: number,
    employeeRoles?: string[],
  ): Promise<{ items: DashboardPageResponseDto[]; total: number }> {
    this.logger.debug(
      `Admin finding dashboard pages for employee ${employeeId}`,
    );

    // Filter chỉ lấy dashboard pages của EMPLOYEE
    const adminQuery: QueryDashboardPageDto = {
      ...query,
      ownerType: 'EMPLOYEE',
      // Nếu không chỉ định pageType, lấy các loại admin
      pageType: query.pageType || undefined,
    };

    return this.dashboardPageService.findMany(
      adminQuery,
      undefined,
      employeeId,
      employeeRoles,
    );
  }

  /**
   * Lấy danh sách dashboard pages có thể truy cập cho admin
   */
  async findAccessiblePages(
    employeeId: number,
    employeeRoles?: string[],
  ): Promise<DashboardPageResponseDto[]> {
    this.logger.debug(
      `Admin finding accessible pages for employee ${employeeId}`,
    );

    return this.dashboardPageService.findAccessiblePages(
      undefined,
      employeeId,
      employeeRoles,
    );
  }

  /**
   * Lấy dashboard page mặc định cho admin
   */
  async findDefaultPage(
    employeeId: number,
    employeeRoles?: string[],
  ): Promise<DashboardPageResponseDto | null> {
    this.logger.debug(`Admin finding default page for employee ${employeeId}`);

    return this.dashboardPageService.findDefaultPage(
      undefined,
      employeeId,
      employeeRoles,
    );
  }

  /**
   * Lấy dashboard page theo slug cho admin
   */
  async findBySlug(
    slug: string,
    employeeId: number,
    employeeRoles?: string[],
    includeWidgets: boolean = true,
  ): Promise<DashboardPageResponseDto> {
    this.logger.debug(
      `Admin finding dashboard page by slug: ${slug} for employee ${employeeId}`,
    );

    return this.dashboardPageService.findBySlug(
      slug,
      undefined,
      employeeId,
      employeeRoles,
      includeWidgets,
    );
  }

  /**
   * Lấy dashboard page theo ID cho admin
   */
  async findById(
    id: string,
    employeeId: number,
    employeeRoles?: string[],
    includeWidgets: boolean = false,
  ): Promise<DashboardPageResponseDto> {
    this.logger.debug(
      `Admin finding dashboard page by ID: ${id} for employee ${employeeId}`,
    );

    return this.dashboardPageService.findById(
      id,
      undefined,
      employeeId,
      employeeRoles,
      includeWidgets,
    );
  }

  /**
   * Cập nhật dashboard page cho admin
   */
  async update(
    id: string,
    dto: UpdateDashboardPageDto,
    employeeId: number,
    employeeRoles?: string[],
  ): Promise<DashboardPageResponseDto> {
    this.logger.log(
      `Admin updating dashboard page: ${id} for employee ${employeeId}`,
    );

    return this.dashboardPageService.update(
      id,
      dto,
      undefined,
      employeeId,
      employeeRoles,
    );
  }

  /**
   * Đặt dashboard page làm mặc định cho admin
   */
  async setAsDefault(
    id: string,
    employeeId: number,
    employeeRoles?: string[],
  ): Promise<DashboardPageResponseDto> {
    this.logger.log(
      `Admin setting dashboard page ${id} as default for employee ${employeeId}`,
    );

    return this.dashboardPageService.setAsDefault(
      id,
      undefined,
      employeeId,
      employeeRoles,
    );
  }

  /**
   * Xóa dashboard page cho admin
   */
  async delete(
    id: string,
    employeeId: number,
    employeeRoles?: string[],
  ): Promise<void> {
    this.logger.log(
      `Admin deleting dashboard page: ${id} for employee ${employeeId}`,
    );

    return this.dashboardPageService.delete(
      id,
      undefined,
      employeeId,
      employeeRoles,
    );
  }

  /**
   * Lấy dashboard analytics overview cho admin
   */
  async getAnalyticsOverview(
    employeeId: number,
    employeeRoles?: string[],
  ): Promise<{
    totalPages: number;
    totalWidgets: number;
    totalActivePages: number;
    totalSharedPages: number;
    pagesByType: Record<string, number>;
    recentPages: DashboardPageResponseDto[];
  }> {
    try {
      this.logger.log(
        `Getting analytics overview for admin employee ${employeeId}`,
      );

      // Lấy tất cả pages của admin
      const allPages = await this.dashboardPageRepository.findAccessiblePages(
        undefined,
        employeeId,
        employeeRoles,
      );

      // Lấy tất cả widgets của admin
      const allWidgets =
        await this.dashboardWidgetRepository.findByEmployeeId(employeeId);

      // Tính toán thống kê
      const totalPages = allPages.length;
      const totalWidgets = allWidgets.length;
      const totalActivePages = allPages.filter((page) => page.isActive).length;
      const totalSharedPages = allPages.filter(
        (page) => page.accessLevel === 'SHARED',
      ).length;

      // Thống kê theo pageType
      const pagesByType = allPages.reduce(
        (acc, page) => {
          acc[page.pageType] = (acc[page.pageType] || 0) + 1;
          return acc;
        },
        {} as Record<string, number>,
      );

      // Lấy 5 pages gần nhất
      const recentPages = allPages
        .sort(
          (a, b) =>
            new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime(),
        )
        .slice(0, 5)
        .map((page) => this.toResponseDto(page));

      return {
        totalPages,
        totalWidgets,
        totalActivePages,
        totalSharedPages,
        pagesByType,
        recentPages,
      };
    } catch (error) {
      this.logger.error(
        `Error getting analytics overview for employee ${employeeId}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Tạo dashboard page mặc định cho admin mới
   */
  async createDefaultAdminDashboard(
    employeeId: number,
  ): Promise<DashboardPageResponseDto> {
    this.logger.log(
      `Creating default admin dashboard for employee ${employeeId}`,
    );

    const defaultDto: CreateDashboardPageDto = {
      name: 'Admin Dashboard',
      slug: 'admin-overview',
      description: 'Dashboard tổng quan dành cho admin',
      icon: 'dashboard',
      isDefault: true,
      ownerType: 'EMPLOYEE',
      pageType: DashboardPageType.ADMIN_CUSTOM,
      accessLevel: 'PRIVATE',
      layoutConfig: {
        breakpoints: {
          lg: 1200,
          md: 996,
          sm: 768,
          xs: 480,
          xxs: 0,
        },
        cols: { lg: 12, md: 10, sm: 6, xs: 4, xxs: 2 },
        rowHeight: 30,
        margin: [10, 10],
        containerPadding: [20, 20],
        compactType: 'vertical',
        preventCollision: false,
      },
      tabsConfig: {
        currentTabId: 'overview',
        tabs: [
          {
            id: 'overview',
            name: 'Tổng quan',
            widgets: [],
            mode: 'view' as 'view' | 'edit',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          },
        ],
      },
      metadata: {
        theme: 'light',
        autoRefresh: true,
        refreshInterval: 300, // 5 minutes
      },
    };

    return this.create(defaultDto, employeeId);
  }

  /**
   * Duplicate dashboard page cho admin
   */
  async duplicatePage(
    sourceId: string,
    newName: string,
    newSlug: string,
    employeeId: number,
    employeeRoles?: string[],
  ): Promise<DashboardPageResponseDto> {
    try {
      this.logger.log(
        `Admin duplicating dashboard page ${sourceId} for employee ${employeeId}`,
      );

      // Lấy source page
      const sourcePage = await this.findById(
        sourceId,
        employeeId,
        employeeRoles,
        true,
      );

      // Tạo dto cho page mới
      const duplicateDto: CreateDashboardPageDto = {
        name: newName || `${sourcePage.name} (Copy)`,
        slug: newSlug || `${sourcePage.slug}-copy`,
        description: sourcePage.description,
        icon: sourcePage.icon,
        layoutConfig: sourcePage.layoutConfig,
        ...(sourcePage.tabsConfig && {
          tabsConfig:
            sourcePage.tabsConfig as CreateDashboardPageDto['tabsConfig'],
        }),
        metadata: sourcePage.metadata,
        ownerType: 'EMPLOYEE',
        pageType: DashboardPageType.ADMIN_CUSTOM,
        accessLevel: 'PRIVATE',
      };

      // Tạo page mới
      const newPage = await this.create(duplicateDto, employeeId);

      this.logger.log(
        `Admin duplicated dashboard page ${sourceId} to ${newPage.id}`,
      );
      return newPage;
    } catch (error) {
      this.logger.error(
        `Error duplicating dashboard page ${sourceId} for employee ${employeeId}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Convert entity to response DTO (helper method)
   */
  private toResponseDto(page: any): DashboardPageResponseDto {
    return {
      id: page.id,
      name: page.name,
      slug: page.slug,
      description: page.description,
      icon: page.icon,
      sortOrder: page.sortOrder,
      isActive: page.isActive,
      isDefault: page.isDefault,
      layoutConfig: page.layoutConfig,
      tabsConfig: page.tabsConfig,
      metadata: page.metadata,
      userId: page.userId,
      employeeId: page.employeeId,
      pageType: page.pageType,
      ownerType: page.ownerType,
      accessLevel: page.accessLevel,
      sharedWith: page.sharedWith,
      createdAt: page.createdAt,
      updatedAt: page.updatedAt,
      dashboardUrl: page.dashboardUrl || `/admin/dashboard/${page.slug}`,
      widgetCount: page.widgets?.length || 0,
      widgets: page.widgets?.map((widget: any) => ({
        id: widget.id,
        name: widget.name,
        widgetKey: widget.widgetKey,
        widgetType: widget.widgetType,
        layout: widget.layout,
        responsiveLayout: widget.responsiveLayout,
        config: widget.config,
        isVisible: widget.isVisible,
        zIndex: widget.zIndex,
        apiEndpoint: widget.apiEndpoint,
        widgetMetadata: widget.widgetMetadata,
      })),
    };
  }
}

import { Processor, WorkerHost } from '@nestjs/bullmq';
import { Logger } from '@nestjs/common';
import { Job } from 'bullmq';
import { QueueName, PageNavigationJobName } from '../queue.constants';
import { PageNavigationJobData } from '../queue.types';

/**
 * Interface cho SSE message format
 */
interface SseMessage {
  id: string;
  event: string;
  data: string;
  retry?: number;
}

/**
 * Interface cho page navigation message data
 */
interface PageNavigationMessageData {
  path: string;
  userId?: number;
  data?: Record<string, any>;
  type?: 'navigate' | 'redirect' | 'replace';
  message?: string;
  timestamp: number;
  trackingId?: string;
  priority?: 'low' | 'normal' | 'high';
}

/**
 * Worker để xử lý page navigation jobs từ queue
 * Worker này sẽ chạy ở backend worker process riêng biệt
 */
@Processor(QueueName.PAGE_NAVIGATION)
export class PageNavigationWorker extends WorkerHost {
  private readonly logger = new Logger(PageNavigationWorker.name);

  // Static observer store để có thể broadcast message đến SSE clients
  private static sseObserverStore: any = null;

  /**
   * Đăng ký SSE Observer Store từ RouterSseController
   * @param observerStore Observer store instance
   */
  static registerSseObserverStore(observerStore: any): void {
    PageNavigationWorker.sseObserverStore = observerStore;
  }

  /**
   * Xử lý job từ Redis queue
   * @param job Job từ Redis
   * @returns Kết quả xử lý
   */
  async process(job: Job): Promise<any> {
    this.logger.log(`Processing job: ${job.name} with ID: ${job.id}`);
    this.logger.debug(`Job data:`, JSON.stringify(job.data, null, 2));

    try {
      switch (job.name) {
        case PageNavigationJobName.NAVIGATE_TO_PAGE:
          return await this.processNavigateToPageJob(job);

        case PageNavigationJobName.PAGE_STATUS_UPDATE:
          return await this.processPageStatusUpdateJob(job);

        default:
          this.logger.warn(`Unknown job name: ${job.name}`);
          return { success: false, message: `Unknown job name: ${job.name}` };
      }
    } catch (error) {
      this.logger.error(`Error processing job ${job.name}:`, error);
      throw error; // Re-throw để BullMQ có thể retry
    }
  }

  /**
   * Xử lý job navigate to page
   * @param job Job navigate to page
   * @returns Kết quả xử lý
   */
  private async processNavigateToPageJob(
    job: Job<PageNavigationJobData>,
  ): Promise<any> {
    const jobData = job.data;

    this.logger.log(
      `Processing navigate to page job - Path: ${jobData.path}, UserId: ${jobData.userId || 'all'}`,
    );

    // Tạo SSE message
    const sseMessage: SseMessage = {
      id: `page-nav-${Date.now()}`,
      event: 'page_navigation',
      data: JSON.stringify({
        path: jobData.path,
        userId: jobData.userId,
        data: jobData.data,
        type: jobData.type || 'navigate',
        message: jobData.message || `Chuyển đến trang: ${jobData.path}`,
        timestamp: jobData.timestamp,
        trackingId: jobData.trackingId,
        priority: jobData.priority || 'normal',
      } as PageNavigationMessageData),
    };

    // Broadcast message đến SSE clients
    await this.broadcastToSseClients(sseMessage, jobData.userId?.toString());

    this.logger.log(
      `Successfully processed navigate to page job - TrackingId: ${jobData.trackingId}`,
    );

    return {
      success: true,
      message: 'Page navigation job processed successfully',
      data: {
        path: jobData.path,
        userId: jobData.userId,
        trackingId: jobData.trackingId,
        timestamp: Date.now(),
      },
    };
  }

  /**
   * Xử lý job page status update
   * @param job Job page status update
   * @returns Kết quả xử lý
   */
  private async processPageStatusUpdateJob(
    job: Job<PageNavigationJobData>,
  ): Promise<any> {
    const jobData = job.data;

    this.logger.log(
      `Processing page status update job - Path: ${jobData.path}, UserId: ${jobData.userId || 'all'}`,
    );

    // Tạo SSE message
    const sseMessage: SseMessage = {
      id: `page-status-${Date.now()}`,
      event: 'page_status_update',
      data: JSON.stringify({
        path: jobData.path,
        userId: jobData.userId,
        data: jobData.data,
        message:
          jobData.message || `Cập nhật trạng thái trang: ${jobData.path}`,
        timestamp: jobData.timestamp,
        trackingId: jobData.trackingId,
        priority: jobData.priority || 'normal',
      } as PageNavigationMessageData),
    };

    // Broadcast message đến SSE clients
    await this.broadcastToSseClients(sseMessage, jobData.userId?.toString());

    this.logger.log(
      `Successfully processed page status update job - TrackingId: ${jobData.trackingId}`,
    );

    return {
      success: true,
      message: 'Page status update job processed successfully',
      data: {
        path: jobData.path,
        userId: jobData.userId,
        trackingId: jobData.trackingId,
        timestamp: Date.now(),
      },
    };
  }

  /**
   * Broadcast message đến SSE clients
   * @param message SSE message
   * @param userId User ID (optional - nếu có thì chỉ gửi cho user cụ thể)
   */
  private async broadcastToSseClients(
    message: SseMessage,
    userId?: string,
  ): Promise<void> {
    try {
      if (!PageNavigationWorker.sseObserverStore) {
        this.logger.warn(
          'SSE Observer Store not registered, cannot broadcast message',
        );
        return;
      }

      // Log message sẽ được broadcast
      this.logger.log('📤 Broadcasting page navigation message:', message);

      if (userId) {
        // Gửi đến user cụ thể (nếu có cách phân biệt user trong observer store)
        // Hiện tại broadcast đến tất cả authenticated clients
        PageNavigationWorker.sseObserverStore.broadcastToAuthenticated(message);
        this.logger.log(
          `📢 Broadcasted to authenticated clients for user: ${userId}`,
        );
      } else {
        // Gửi đến tất cả clients
        PageNavigationWorker.sseObserverStore.broadcastToAll(message);
        this.logger.log('📢 Broadcasted to all connected clients');
      }

      // Log số lượng clients đã nhận message
      const observerCount =
        PageNavigationWorker.sseObserverStore.getObserverCount();
      this.logger.log(
        `📊 Message sent to ${observerCount.total} connected clients`,
      );
    } catch (error) {
      this.logger.error('Error broadcasting to SSE clients:', error);
      // Không throw error để không làm fail job
    }
  }
}

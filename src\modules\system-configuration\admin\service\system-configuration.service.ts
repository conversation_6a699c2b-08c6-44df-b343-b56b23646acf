import { Injectable, Logger } from '@nestjs/common';
import { SystemConfigurationRepository } from '../../repositories';
import { SystemConfiguration } from '@modules/system-configuration';
import {
  UpdateSystemConfigurationDto,
  CreateSystemConfigOnlyDto,
  UpdateSystemConfigOnlyDto,
  SystemConfigOnlyResponseDto,
  UpdateContractKeysBatchDto,
  ContractKeyTypeEnum,
  CreateSystemConfigurationDto,
  SendOtpResponseDto,
  ConfirmSystemConfigOtpDto,
  SystemConfigurationQueryDto,
} from '../../dto';
import { AppException, ErrorCode } from '@common/exceptions';
import { PaginatedResult } from '@common/response/api-response-dto';
import { SYSTEM_CONFIGURATION_ERROR_CODES } from '../../exceptions';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { RedisService } from '@/shared/services/redis.service';
import { SystemConfigEncryptionService } from '../../services/system-config-encryption.service';
import { SystemConfigurationOtpManagerService } from './system-configuration-otp-manager.service';
import { CdnService } from '../../../../shared/services/cdn.service';
import { TimeIntervalEnum } from '../../../../shared/utils';

@Injectable()
export class SystemConfigurationService {
  private readonly logger = new Logger(SystemConfigurationService.name);
  private readonly CACHE_KEY = 'system:configuration';
  private readonly CACHE_TTL = 24 * 60 * 60; // 1 ngày tính bằng giây

  constructor(
    private readonly systemConfigurationRepository: SystemConfigurationRepository,
    @InjectRepository(SystemConfiguration)
    private readonly systemConfigRepository: Repository<SystemConfiguration>,
    private readonly redisService: RedisService,
    private readonly systemConfigEncryptionService: SystemConfigEncryptionService,
    private readonly otpManagerService: SystemConfigurationOtpManagerService,
    private readonly cdnService: CdnService,
  ) {}

  /**
   * Lấy cấu hình đang active
   * @returns Thông tin cấu hình đang active với thông tin ngân hàng đã giải mã và URL CDN cho contract fields
   */
  async getActiveConfiguration(): Promise<SystemConfiguration> {
    try {
      const configuration =
        await this.systemConfigurationRepository.findActive();

      if (!configuration) {
        throw new AppException(
          SYSTEM_CONFIGURATION_ERROR_CODES.ACTIVE_CONFIGURATION_NOT_FOUND,
        );
      }

      // Giải mã thông tin ngân hàng trước khi trả về
      try {
        const decryptedConfig = this.decryptBankAccountInfo(configuration);
        // Gắn URL CDN cho các trường contract
        return this.attachCdnUrlsToContractFields(decryptedConfig);
      } catch (error) {
        this.logger.error(
          `Lỗi khi giải mã thông tin ngân hàng: ${error.message}`,
          error.stack,
        );

        // Trả về cấu hình gốc nếu không thể giải mã
        return configuration;
      }
    } catch (error) {
      this.logger.error(
        `Error getting active configuration: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        SYSTEM_CONFIGURATION_ERROR_CODES.INTERNAL_ERROR,
      );
    }
  }

  /**
   * Lấy cấu hình đang active
   * @returns Thông tin cấu hình đang active với thông tin ngân hàng đã giải mã
   */
  async getActiveConfigurationNotDecrypted(): Promise<SystemConfiguration> {
    try {
      const configuration =
        await this.systemConfigurationRepository.findActive();

      if (!configuration) {
        throw new AppException(
          SYSTEM_CONFIGURATION_ERROR_CODES.ACTIVE_CONFIGURATION_NOT_FOUND,
        );
      }

      return configuration;
    } catch (error) {
      this.logger.error(
        `Error getting active configuration: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Lỗi khi lấy cấu hình hệ thống',
      );
    }
  }



  /**
   * Lấy cấu hình theo ID
   * @param id ID của cấu hình
   * @returns Thông tin cấu hình với thông tin ngân hàng đã giải mã và URL CDN cho contract fields
   */
  async getConfigurationById(id: number): Promise<SystemConfiguration> {
    try {
      const configuration =
        await this.systemConfigurationRepository.findById(id);

      if (!configuration) {
        throw new AppException(
          SYSTEM_CONFIGURATION_ERROR_CODES.CONFIGURATION_WITH_ID_NOT_FOUND,
        );
      }

      // Giải mã thông tin ngân hàng và gắn URL CDN trước khi trả về
      const decryptedConfig = this.decryptBankAccountInfo(configuration);
      return this.attachCdnUrlsToContractFields(decryptedConfig);
    } catch (error) {
      this.logger.error(
        `Error getting configuration by ID: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Lỗi khi lấy cấu hình hệ thống',
      );
    }
  }

  /**
   * Cập nhật cấu hình
   * @param id ID của cấu hình
   * @param updateDto Thông tin cần cập nhật
   * @returns Cấu hình đã cập nhật
   */
  async updateConfiguration(
    id: number,
    updateDto: UpdateSystemConfigurationDto,
  ): Promise<SystemConfiguration> {
    try {
      const updatedConfiguration =
        await this.systemConfigurationRepository.update(id, updateDto);

      if (!updatedConfiguration) {
        throw new AppException(
          ErrorCode.RESOURCE_NOT_FOUND,
          `Không tìm thấy cấu hình hệ thống với ID ${id}`,
        );
      }

      // Nếu cấu hình vừa cập nhật là active, cập nhật lại cache redis
      if (updatedConfiguration.active) {
        // Lưu cấu hình mới vào cache
        await this.redisService.setWithExpiry(
          this.CACHE_KEY,
          JSON.stringify(updatedConfiguration),
          this.CACHE_TTL,
        );
      } else {
        // Nếu không active, xóa cache để lần sau lấy lại từ DB
        await this.redisService.del(this.CACHE_KEY);
      }

      // Giải mã thông tin ngân hàng và gắn URL CDN trước khi trả về
      const decryptedConfig = this.decryptBankAccountInfo(updatedConfiguration);
      return this.attachCdnUrlsToContractFields(decryptedConfig);
    } catch (error) {
      this.logger.error(
        `Error updating configuration: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Lỗi khi cập nhật cấu hình hệ thống',
      );
    }
  }

  /**
   * Tạo cấu hình mới
   * @param configuration Thông tin cấu hình
   * @returns Cấu hình đã tạo
   */
  async createConfiguration(
    configuration: Partial<SystemConfiguration>,
  ): Promise<SystemConfiguration> {
    try {
      const createdConfig =
        await this.systemConfigurationRepository.create(configuration);
      // Nếu cấu hình vừa tạo là active, cập nhật cache redis
      if (createdConfig.active) {
        await this.redisService.setWithExpiry(
          this.CACHE_KEY,
          JSON.stringify(createdConfig),
          this.CACHE_TTL,
        );
      }
      // Giải mã thông tin ngân hàng và gắn URL CDN trước khi trả về
      const decryptedConfig = this.decryptBankAccountInfo(createdConfig);
      return this.attachCdnUrlsToContractFields(decryptedConfig);
    } catch (error) {
      this.logger.error(
        `Error creating configuration: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Lỗi khi tạo cấu hình hệ thống',
      );
    }
  }

  /**
   * Tạo URL QR thanh toán
   * @param amount Số tiền cần thanh toán
   * @param orderId Mã đơn hàng
   * @returns URL QR thanh toán
   */
  async generateQRPaymentUrl(amount: number, orderId: string): Promise<string> {
    try {
      // Lấy cấu hình đang active
      const configuration = await this.getActiveConfiguration();

      if (!configuration.bankCode || !configuration.accountNumber) {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          'Thiếu thông tin ngân hàng trong cấu hình hệ thống',
        );
      }

      // Tạo URL QR thanh toán theo công thức
      return this.generateQRPayment(
        configuration.bankCode,
        configuration.accountNumber,
        amount,
        orderId,
      );
    } catch (error) {
      this.logger.error(
        `Error generating QR payment URL: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Lỗi khi tạo URL QR thanh toán',
      );
    }
  }

  /**
   * Tạo URL QR thanh toán
   * @param amount Số tiền cần thanh toán
   * @param orderId Mã đơn hàng
   * @returns URL QR thanh toán
   */
  async generateQRPaymentUrlForSubcription(
    amount: number,
    orderId: string,
  ): Promise<string> {
    // Lấy cấu hình đang active
    const configuration = await this.getActiveConfiguration();

    if (!configuration.bankCode || !configuration.accountNumber) {
      throw new AppException(
        ErrorCode.VALIDATION_ERROR,
        'Thiếu thông tin ngân hàng trong cấu hình hệ thống',
      );
    }

    // Tạo URL QR thanh toán theo công thức
    return this.generateQRPaymentForSubscription(
      configuration.bankCode,
      configuration.accountNumber,
      amount,
      orderId,
    );
  }

  generateDescription = (orderId: string) => {
    return `REDAI${orderId}SEPAYRPOINT`;
  };

  generateDescriptionForSubscription = (orderId: string) => {
    return `REDAI${orderId}SEPAYSUB`;
  };

  /**
   * Tạo URL QR thanh toán theo công thức
   * @param bankCode Mã ngân hàng
   * @param bankAccount Số tài khoản
   * @param amount Số tiền cần thanh toán
   * @param orderId Mã đơn hàng
   * @returns URL QR thanh toán
   */
  private generateQRPaymentForSubscription(
    bankCode: string,
    bankAccount: string,
    amount: number,
    orderId: string,
  ): string {
    return `https://qr.sepay.vn/img?bank=${bankCode}&acc=${bankAccount}&template=compact&amount=${amount.toFixed(2)}&des=REDAI${orderId}SEPAYSUB`;
  }

  /**
   * Tạo URL QR thanh toán theo công thức
   * @param bankCode Mã ngân hàng
   * @param bankAccount Số tài khoản
   * @param amount Số tiền cần thanh toán
   * @param orderId Mã đơn hàng
   * @returns URL QR thanh toán
   */
  private generateQRPayment(
    bankCode: string,
    bankAccount: string,
    amount: number,
    orderId: string,
  ): string {
    return `https://qr.sepay.vn/img?bank=${bankCode}&acc=${bankAccount}&template=compact&amount=${amount.toFixed(2)}&des=REDAI${orderId}SEPAYRPOINT`;
  }

  /**
   * Lấy cấu hình hệ thống từ cache hoặc database
   * @returns Promise<SystemConfiguration> Cấu hình hệ thống
   */
  async getSystemConfiguration(): Promise<SystemConfiguration> {
    try {
      // Thử lấy từ cache trước
      const cachedConfig = await this.redisService.get(this.CACHE_KEY);
      if (cachedConfig) {
        const parsedConfig = JSON.parse(cachedConfig);
        this.logger.log(
          `[DEBUG] Lấy từ cache - ID: ${parsedConfig.id}, commissionToPointsConversionRate: ${parsedConfig.commissionToPointsConversionRate}`,
        );
        return parsedConfig;
      }

      this.logger.log('[DEBUG] Không có cache, lấy từ database');

      // Nếu không có trong cache, lấy từ database
      const config = await this.systemConfigRepository.findOne({
        where: { active: true },
      });

      if (!config) {
        throw new Error('Không tìm thấy cấu hình hệ thống');
      }

      this.logger.log(
        `[DEBUG] Lấy từ database - ID: ${config.id}, commissionToPointsConversionRate: ${config.commissionToPointsConversionRate}`,
      );

      // Lưu vào cache
      await this.redisService.setWithExpiry(
        this.CACHE_KEY,
        JSON.stringify(config),
        this.CACHE_TTL,
      );

      return config;
    } catch (error) {
      this.logger.error('Lỗi khi lấy cấu hình hệ thống:', error);
      throw error;
    }
  }

  /**
   * Cập nhật cấu hình hệ thống
   * @param config Cấu hình mới
   * @returns Promise<SystemConfiguration> Cấu hình đã cập nhật
   */
  async updateSystemConfiguration(
    config: Partial<SystemConfiguration>,
  ): Promise<SystemConfiguration> {
    try {
      // Cập nhật thời gian
      const now = Math.floor(Date.now() / 1000);
      config.updatedAt = now;

      // Cập nhật vào database
      const existingConfig = await this.systemConfigRepository.findOne({
        where: { active: true },
      });

      if (!existingConfig) {
        throw new Error('Không tìm thấy cấu hình hệ thống để cập nhật');
      }

      const updatedConfig = await this.systemConfigRepository.save({
        ...existingConfig,
        ...config,
      });

      // Xóa cache cũ
      await this.redisService.del(this.CACHE_KEY);

      // Lưu cấu hình mới vào cache
      await this.redisService.setWithExpiry(
        this.CACHE_KEY,
        JSON.stringify(updatedConfig),
        this.CACHE_TTL,
      );

      return updatedConfig;
    } catch (error) {
      this.logger.error('Lỗi khi cập nhật cấu hình hệ thống:', error);
      throw error;
    }
  }

  /**
   * Xóa cache cấu hình hệ thống
   */
  async clearCache(): Promise<void> {
    try {
      await this.redisService.del(this.CACHE_KEY);
      this.logger.log('Đã xóa cache cấu hình hệ thống');
    } catch (error) {
      this.logger.error('Lỗi khi xóa cache cấu hình hệ thống:', error);
      throw error;
    }
  }

  /**
   * Giải mã thông tin tài khoản ngân hàng trong cấu hình
   * @param config Cấu hình hệ thống
   * @returns Cấu hình với thông tin ngân hàng đã giải mã
   */
  private decryptBankAccountInfo(
    config: SystemConfiguration,
  ): SystemConfiguration {
    const decryptedConfig = { ...config };

    // Sử dụng SystemConfigEncryptionService riêng để giải mã
    const decryptedBankInfo = this.systemConfigEncryptionService.decryptBankAccountInfo({
      bankCode: decryptedConfig.bankCode,
      accountNumber: decryptedConfig.accountNumber,
      accountName: decryptedConfig.accountName,
    });

    // Cập nhật thông tin đã giải mã
    if (decryptedBankInfo.bankCode !== undefined) {
      decryptedConfig.bankCode = decryptedBankInfo.bankCode;
    }
    if (decryptedBankInfo.accountNumber !== undefined) {
      decryptedConfig.accountNumber = decryptedBankInfo.accountNumber;
    }
    if (decryptedBankInfo.accountName !== undefined) {
      decryptedConfig.accountName = decryptedBankInfo.accountName;
    }

    return decryptedConfig;
  }

  /**
   * Gắn URL CDN vào các trường contract
   * @param config Cấu hình hệ thống
   * @returns Cấu hình với URL CDN đã được gắn vào các trường contract
   */
  private attachCdnUrlsToContractFields(
    config: SystemConfiguration,
  ): SystemConfiguration {
    const configWithUrls = { ...config };

    try {
      // Gắn URL CDN cho initialRuleContractBusiness
      if (configWithUrls.initialRuleContractBusiness) {
        configWithUrls.initialRuleContractBusiness = this.cdnService.generateUrlView(
          configWithUrls.initialRuleContractBusiness,
          TimeIntervalEnum.ONE_DAY
        ) || configWithUrls.initialRuleContractBusiness;
      }

      // Gắn URL CDN cho initialAffiliateContractBusiness
      if (configWithUrls.initialAffiliateContractBusiness) {
        configWithUrls.initialAffiliateContractBusiness = this.cdnService.generateUrlView(
          configWithUrls.initialAffiliateContractBusiness,
          TimeIntervalEnum.ONE_DAY
        ) || configWithUrls.initialAffiliateContractBusiness;
      }

      // Gắn URL CDN cho initialAffiliateContractCustomer
      if (configWithUrls.initialAffiliateContractCustomer) {
        configWithUrls.initialAffiliateContractCustomer = this.cdnService.generateUrlView(
          configWithUrls.initialAffiliateContractCustomer,
          TimeIntervalEnum.ONE_DAY
        ) || configWithUrls.initialAffiliateContractCustomer;
      }

      // Gắn URL CDN cho initialRuleContractCustomer
      if (configWithUrls.initialRuleContractCustomer) {
        configWithUrls.initialRuleContractCustomer = this.cdnService.generateUrlView(
          configWithUrls.initialRuleContractCustomer,
          TimeIntervalEnum.ONE_DAY
        ) || configWithUrls.initialRuleContractCustomer;
      }

    } catch (error) {
      this.logger.error(
        `Lỗi khi gắn URL CDN cho contract fields: ${error.message}`,
        error.stack,
      );
      // Trả về config gốc nếu có lỗi
      return config;
    }

    return configWithUrls;
  }

  /**
   * Lấy tất cả cấu hình hệ thống (không bao gồm thông tin ngân hàng)
   * @returns Danh sách cấu hình hệ thống với URL CDN cho contract fields
   */
  async getAllSystemConfigurationsOnly(): Promise<
    SystemConfigOnlyResponseDto[]
  > {
    try {
      const configurations = await this.systemConfigurationRepository.findAll();

      return configurations.map((config) => {
        const configWithUrls = this.attachCdnUrlsToContractFields(config);
        return this.mapToSystemConfigOnlyResponse(configWithUrls);
      });
    } catch (error) {
      this.logger.error(
        `Error getting all system configurations: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Lỗi khi lấy danh sách cấu hình hệ thống',
      );
    }
  }

  /**
   * Lấy cấu hình hệ thống theo ID (không bao gồm thông tin ngân hàng)
   * @param id ID của cấu hình
   * @returns Thông tin cấu hình hệ thống với URL CDN cho contract fields
   */
  async getSystemConfigurationByIdOnly(
    id: number,
  ): Promise<SystemConfigOnlyResponseDto> {
    try {
      const configuration =
        await this.systemConfigurationRepository.findById(id);

      if (!configuration) {
        throw new AppException(
          ErrorCode.RESOURCE_NOT_FOUND,
          `Không tìm thấy cấu hình hệ thống với ID ${id}`,
        );
      }

      const configWithUrls = this.attachCdnUrlsToContractFields(configuration);
      return this.mapToSystemConfigOnlyResponse(configWithUrls);
    } catch (error) {
      this.logger.error(
        `Error getting system configuration by ID: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Lỗi khi lấy cấu hình hệ thống',
      );
    }
  }

  /**
   * Lấy cấu hình hệ thống đang active (không bao gồm thông tin ngân hàng)
   * @returns Thông tin cấu hình hệ thống đang active với URL CDN cho contract fields
   */
  async getActiveSystemConfigurationOnly(): Promise<SystemConfigOnlyResponseDto> {
    try {
      const configuration =
        await this.systemConfigurationRepository.findActive();

      if (!configuration) {
        throw new AppException(
          ErrorCode.RESOURCE_NOT_FOUND,
          'Không tìm thấy cấu hình hệ thống đang active',
        );
      }

      const configWithUrls = this.attachCdnUrlsToContractFields(configuration);
      return this.mapToSystemConfigOnlyResponse(configWithUrls);
    } catch (error) {
      this.logger.error(
        `Error getting active system configuration: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Lỗi khi lấy cấu hình hệ thống đang active',
      );
    }
  }

  /**
   * Tạo cấu hình hệ thống mới (không bao gồm thông tin ngân hàng)
   * @param createDto Thông tin cấu hình cần tạo
   * @returns Cấu hình hệ thống đã tạo
   */
  async createSystemConfigurationOnly(
    createDto: CreateSystemConfigOnlyDto,
  ): Promise<SystemConfigOnlyResponseDto> {
    try {
      const now = Math.floor(Date.now() / 1000);

      const configData: Partial<SystemConfiguration> = {
        ...createDto,
        createdAt: now,
        updatedAt: now,
      };

      const createdConfig =
        await this.systemConfigurationRepository.create(configData);

      // Nếu cấu hình vừa tạo là active, cập nhật cache redis
      if (createdConfig.active) {
        await this.redisService.setWithExpiry(
          this.CACHE_KEY,
          JSON.stringify(createdConfig),
          this.CACHE_TTL,
        );
      }

      const configWithUrls = this.attachCdnUrlsToContractFields(createdConfig);
      return this.mapToSystemConfigOnlyResponse(configWithUrls);
    } catch (error) {
      this.logger.error(
        `Error creating system configuration: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Lỗi khi tạo cấu hình hệ thống',
      );
    }
  }

  /**
   * Cập nhật cấu hình hệ thống (không bao gồm thông tin ngân hàng)
   * @param id ID của cấu hình
   * @param updateDto Thông tin cần cập nhật
   * @returns Cấu hình đã cập nhật
   */
  async updateSystemConfigurationOnly(
    id: number,
    updateDto: UpdateSystemConfigOnlyDto,
  ): Promise<SystemConfigOnlyResponseDto> {
    try {
      const existingConfig = await this.systemConfigRepository.findOne({
        where: { id },
      });

      if (!existingConfig) {
        throw new AppException(
          ErrorCode.RESOURCE_NOT_FOUND,
          `Không tìm thấy cấu hình hệ thống với ID ${id}`,
        );
      }

      const now = Math.floor(Date.now() / 1000);

      const updatedConfiguration = await this.systemConfigRepository.save({
        ...existingConfig,
        ...updateDto,
        updatedAt: now,
      });

      // Nếu cấu hình vừa cập nhật là active, cập nhật lại cache redis
      if (updatedConfiguration.active) {
        await this.redisService.setWithExpiry(
          this.CACHE_KEY,
          JSON.stringify(updatedConfiguration),
          this.CACHE_TTL,
        );
      } else {
        // Nếu không active, xóa cache để lần sau lấy lại từ DB
        await this.redisService.del(this.CACHE_KEY);
      }

      const configWithUrls = this.attachCdnUrlsToContractFields(updatedConfiguration);
      return this.mapToSystemConfigOnlyResponse(configWithUrls);
    } catch (error) {
      this.logger.error(
        `Error updating system configuration: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Lỗi khi cập nhật cấu hình hệ thống',
      );
    }
  }

  /**
   * Map SystemConfiguration entity to SystemConfigOnlyResponseDto (loại bỏ bank account fields)
   * @param config SystemConfiguration entity
   * @returns SystemConfigOnlyResponseDto
   */
  private mapToSystemConfigOnlyResponse(
    config: SystemConfiguration,
  ): SystemConfigOnlyResponseDto {
    return {
      id: config.id,
      feePercentage: config.feePercentage,
      commissionToPointsConversionRate: config.commissionToPointsConversionRate,
      purchaseInvoiceTemplate: config.purchaseInvoiceTemplate,
      emailNotificationSystemId: config.emailNotificationSystemId,
      initialAffiliateContractBusiness: config.initialAffiliateContractBusiness,
      initialRuleContractBusiness: config.initialRuleContractBusiness,
      initialAffiliateContractCustomer: config.initialAffiliateContractCustomer,
      initialRuleContractCustomer: config.initialRuleContractCustomer,
      active: config.active,
      createdAt: config.createdAt,
      updatedAt: config.updatedAt,
    };
  }

  /**
   * Cập nhật nhiều contract keys cùng lúc
   * @param updateDto Thông tin các contract keys cần cập nhật
   * @returns Cấu hình đã cập nhật
   */
  async updateContractKeysBatch(
    updateDto: UpdateContractKeysBatchDto,
  ): Promise<SystemConfigOnlyResponseDto> {
    try {
      const existingConfig = await this.systemConfigRepository.findOne({
        where: { active: true },
      });

      if (!existingConfig) {
        throw new AppException(
          ErrorCode.RESOURCE_NOT_FOUND,
          'Không tìm thấy cấu hình hệ thống để cập nhật',
        );
      }

      // Validate không có duplicate types
      const types = updateDto.contractKeys.map((item) => item.type);
      const uniqueTypes = new Set(types);
      if (types.length !== uniqueTypes.size) {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          'Không được cập nhật cùng một loại contract key nhiều lần trong một request',
        );
      }

      const now = Math.floor(Date.now() / 1000);
      const updateData: Partial<SystemConfiguration> = {
        updatedAt: now,
      };

      // Map từng contract key với field tương ứng
      for (const item of updateDto.contractKeys) {
        switch (item.type) {
          case ContractKeyTypeEnum.AFFILIATE_BUSINESS:
            updateData.initialAffiliateContractBusiness = item.value;
            break;
          case ContractKeyTypeEnum.AFFILIATE_CUSTOMER:
            updateData.initialAffiliateContractCustomer = item.value;
            break;
          case ContractKeyTypeEnum.RULE_BUSINESS:
            updateData.initialRuleContractBusiness = item.value;
            break;
          case ContractKeyTypeEnum.RULE_CUSTOMER:
            updateData.initialRuleContractCustomer = item.value;
            break;
          default:
            throw new AppException(
              ErrorCode.VALIDATION_ERROR,
              `Loại contract key không hợp lệ: ${item.type}`,
            );
        }
      }

      const updatedConfiguration = await this.systemConfigRepository.save({
        ...existingConfig,
        ...updateData,
      });

      // Cập nhật cache
      if (updatedConfiguration.active) {
        await this.redisService.setWithExpiry(
          this.CACHE_KEY,
          JSON.stringify(updatedConfiguration),
          this.CACHE_TTL,
        );
      }

      const configWithUrls = this.attachCdnUrlsToContractFields(updatedConfiguration);
      return this.mapToSystemConfigOnlyResponse(configWithUrls);
    } catch (error) {
      this.logger.error(
        `Error updating contract keys batch: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Lỗi khi cập nhật contract keys',
      );
    }
  }

  /**
   * Cập nhật cấu hình hệ thống tổng hợp (bao gồm cả thông tin ngân hàng)
   * @param id ID của cấu hình cần cập nhật
   * @param updateDto Thông tin cần cập nhật
   * @returns Cấu hình đã cập nhật
   */
  async updateSystemConfigurationUnified(
    id: number,
    updateDto: UpdateSystemConfigurationDto,
  ): Promise<SystemConfigOnlyResponseDto> {
    try {
      this.logger.log(
        `[DEBUG] Starting updateSystemConfigurationUnified for ID ${id} with data: ${JSON.stringify(updateDto)}`,
      );

      // Lấy cấu hình theo ID
      const existingConfig = await this.systemConfigRepository.findOne({
        where: { id },
      });

      this.logger.log(
        `[DEBUG] Found existing config: ${existingConfig ? 'YES' : 'NO'}`,
      );

      if (!existingConfig) {
        throw new AppException(
          ErrorCode.RESOURCE_NOT_FOUND,
          'Không tìm thấy cấu hình hệ thống để cập nhật',
        );
      }

      const now = Math.floor(Date.now() / 1000);
      const updateData: Partial<SystemConfiguration> = {
        updatedAt: now,
      };

      // Cập nhật thông tin ngân hàng (với mã hóa cho account number và account name)
      if (updateDto.bankCode !== undefined) {
        this.logger.log(`[DEBUG] Setting bankCode: ${updateDto.bankCode}`);
        updateData.bankCode = updateDto.bankCode; // Bank code không cần mã hóa
      }

      if (updateDto.accountNumber !== undefined) {
        this.logger.log(
          `[DEBUG] Encrypting accountNumber: ${updateDto.accountNumber}`,
        );
        updateData.accountNumber = this.systemConfigEncryptionService.encrypt(
          updateDto.accountNumber,
        );
      }

      if (updateDto.accountName !== undefined) {
        this.logger.log(
          `[DEBUG] Encrypting accountName: ${updateDto.accountName}`,
        );
        updateData.accountName = this.systemConfigEncryptionService.encrypt(
          updateDto.accountName,
        );
      }

      // Cập nhật các thông tin khác của system configuration
      if (updateDto.feePercentage !== undefined) {
        updateData.feePercentage = updateDto.feePercentage;
      }

      if (updateDto.commissionToPointsConversionRate !== undefined) {
        updateData.commissionToPointsConversionRate =
          updateDto.commissionToPointsConversionRate;
      }

      if (updateDto.purchaseInvoiceTemplate !== undefined) {
        updateData.purchaseInvoiceTemplate = updateDto.purchaseInvoiceTemplate;
      }

      if (updateDto.emailNotificationSystemId !== undefined) {
        updateData.emailNotificationSystemId =
          updateDto.emailNotificationSystemId;
      }

      // Cập nhật contract keys nếu có
      if (updateDto.initialAffiliateContractBusiness !== undefined) {
        updateData.initialAffiliateContractBusiness =
          updateDto.initialAffiliateContractBusiness;
      }

      if (updateDto.initialAffiliateContractCustomer !== undefined) {
        updateData.initialAffiliateContractCustomer =
          updateDto.initialAffiliateContractCustomer;
      }

      if (updateDto.initialRuleContractBusiness !== undefined) {
        updateData.initialRuleContractBusiness =
          updateDto.initialRuleContractBusiness;
      }

      if (updateDto.initialRuleContractCustomer !== undefined) {
        updateData.initialRuleContractCustomer =
          updateDto.initialRuleContractCustomer;
      }

      this.logger.log(`[DEBUG] Saving to database with update data`);

      // Cập nhật vào database
      const updatedConfiguration = await this.systemConfigRepository.save({
        ...existingConfig,
        ...updateData,
      });

      this.logger.log(`[DEBUG] Saved to database successfully`);

      // Xóa cache cũ nếu cấu hình này đang active
      if (updatedConfiguration.active) {
        await this.redisService.del(this.CACHE_KEY);
      }

      // Giải mã dữ liệu để trả về cho client
      const decryptedConfig = this.decryptBankAccountInfo(updatedConfiguration);

      // Lưu cấu hình đã mã hóa vào cache nếu đang active (để bảo mật)
      if (updatedConfiguration.active) {
        await this.redisService.setWithExpiry(
          this.CACHE_KEY,
          JSON.stringify(updatedConfiguration),
          this.CACHE_TTL,
        );
        this.logger.log(`[DEBUG] Updated cache successfully`);
      }

      return this.mapToSystemConfigOnlyResponse(decryptedConfig);
    } catch (error) {
      this.logger.error(
        `Lỗi khi cập nhật cấu hình hệ thống: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Lỗi khi cập nhật cấu hình hệ thống',
      );
    }
  }

  /**
   * Lấy cấu hình hệ thống duy nhất đầy đủ (bao gồm thông tin ngân hàng đã giải mã và URL CDN cho contract fields)
   * @returns Cấu hình hệ thống đầy đủ
   */
  async getSystemConfigurationWithoutId(): Promise<SystemConfiguration> {
    try {
      // Tìm cấu hình active duy nhất
      const configuration = await this.systemConfigRepository.findOne({
        where: { active: true },
      });

      if (!configuration) {
        throw new AppException(
          ErrorCode.RESOURCE_NOT_FOUND,
          'Không tìm thấy cấu hình hệ thống',
        );
      }

      // Giải mã thông tin ngân hàng và gắn URL CDN trước khi trả về
      const decryptedConfig = this.decryptBankAccountInfo(configuration);
      return this.attachCdnUrlsToContractFields(decryptedConfig);
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy cấu hình hệ thống: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Lỗi khi lấy cấu hình hệ thống',
      );
    }
  }

  /**
   * Cập nhật cấu hình hệ thống duy nhất (không cần ID)
   * @param updateDto Thông tin cần cập nhật
   * @returns Cấu hình đã cập nhật
   */
  async updateSystemConfigurationWithoutId(
    updateDto: UpdateSystemConfigurationDto,
  ): Promise<SystemConfigOnlyResponseDto> {
    try {
      this.logger.log(
        `[DEBUG] Starting updateSystemConfigurationWithoutId with data: ${JSON.stringify(updateDto)}`,
      );

      // Tìm cấu hình active duy nhất
      const existingConfig = await this.systemConfigRepository.findOne({
        where: { active: true },
      });

      this.logger.log(
        `[DEBUG] Found existing active config: ${existingConfig ? 'YES' : 'NO'}`,
      );

      if (!existingConfig) {
        throw new AppException(
          ErrorCode.RESOURCE_NOT_FOUND,
          'Không tìm thấy cấu hình hệ thống để cập nhật',
        );
      }

      const now = Math.floor(Date.now() / 1000);
      const updateData: Partial<SystemConfiguration> = {
        updatedAt: now,
      };

      // Cập nhật thông tin ngân hàng (với mã hóa cho account number và account name)
      if (updateDto.bankCode !== undefined) {
        this.logger.log(`[DEBUG] Setting bankCode: ${updateDto.bankCode}`);
        updateData.bankCode = updateDto.bankCode; // Bank code không cần mã hóa
      }

      if (updateDto.accountNumber !== undefined) {
        this.logger.log(`[DEBUG] Encrypting and setting accountNumber`);
        updateData.accountNumber = this.systemConfigEncryptionService.encrypt(
          updateDto.accountNumber,
        );
      }

      if (updateDto.accountName !== undefined) {
        this.logger.log(`[DEBUG] Encrypting and setting accountName`);
        updateData.accountName = this.systemConfigEncryptionService.encrypt(
          updateDto.accountName,
        );
      }

      // Cập nhật các thông tin khác
      if (updateDto.active !== undefined) {
        updateData.active = updateDto.active;
      }

      if (updateDto.feePercentage !== undefined) {
        updateData.feePercentage = updateDto.feePercentage;
      }

      if (updateDto.commissionToPointsConversionRate !== undefined) {
        updateData.commissionToPointsConversionRate =
          updateDto.commissionToPointsConversionRate;
      }

      if (updateDto.purchaseInvoiceTemplate !== undefined) {
        updateData.purchaseInvoiceTemplate = updateDto.purchaseInvoiceTemplate;
      }

      if (updateDto.emailNotificationSystemId !== undefined) {
        updateData.emailNotificationSystemId =
          updateDto.emailNotificationSystemId;
      }

      // Contract keys
      if (updateDto.initialAffiliateContractBusiness !== undefined) {
        updateData.initialAffiliateContractBusiness =
          updateDto.initialAffiliateContractBusiness;
      }

      if (updateDto.initialAffiliateContractCustomer !== undefined) {
        updateData.initialAffiliateContractCustomer =
          updateDto.initialAffiliateContractCustomer;
      }

      if (updateDto.initialRuleContractBusiness !== undefined) {
        updateData.initialRuleContractBusiness =
          updateDto.initialRuleContractBusiness;
      }

      if (updateDto.initialRuleContractCustomer !== undefined) {
        updateData.initialRuleContractCustomer =
          updateDto.initialRuleContractCustomer;
      }

      this.logger.log(`[DEBUG] Saving to database with update data`);

      // Cập nhật vào database
      const updatedConfiguration = await this.systemConfigRepository.save({
        ...existingConfig,
        ...updateData,
      });

      this.logger.log(`[DEBUG] Saved to database successfully`);

      // Xóa cache cũ nếu cấu hình này đang active
      if (updatedConfiguration.active) {
        await this.redisService.del(this.CACHE_KEY);
      }

      // Giải mã dữ liệu để trả về cho client
      const decryptedConfig = this.decryptBankAccountInfo(updatedConfiguration);

      // Lưu cấu hình đã mã hóa vào cache nếu đang active (để bảo mật)
      if (updatedConfiguration.active) {
        await this.redisService.setWithExpiry(
          this.CACHE_KEY,
          JSON.stringify(updatedConfiguration),
          this.CACHE_TTL,
        );
        this.logger.log(`[DEBUG] Updated cache successfully`);
      }

      return this.mapToSystemConfigOnlyResponse(decryptedConfig);
    } catch (error) {
      this.logger.error(
        `Lỗi khi cập nhật cấu hình hệ thống: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Lỗi khi cập nhật cấu hình hệ thống',
      );
    }
  }

  /**
   * Gửi OTP cho việc tạo cấu hình hệ thống
   * @param adminEmail Email admin
   * @param adminName Tên admin
   * @param createDto Dữ liệu cấu hình cần tạo
   * @returns Thông tin OTP
   */
  async sendOtpForCreateConfiguration(
    adminEmail: string,
    adminName: string,
    createDto: CreateSystemConfigurationDto,
  ): Promise<SendOtpResponseDto> {
    return await this.otpManagerService.sendOtpForCreate(adminEmail, adminName, createDto);
  }

  /**
   * Gửi OTP cho việc cập nhật cấu hình hệ thống
   * @param adminEmail Email admin
   * @param adminName Tên admin
   * @param updateDto Dữ liệu cấu hình cần cập nhật
   * @returns Thông tin OTP
   */
  async sendOtpForUpdateConfiguration(
    adminEmail: string,
    adminName: string,
    updateDto: UpdateSystemConfigurationDto,
  ): Promise<SendOtpResponseDto> {
    return await this.otpManagerService.sendOtpForUpdate(adminEmail, adminName, updateDto);
  }

  /**
   * Xác nhận OTP và thực hiện tạo/cập nhật cấu hình
   * @param confirmDto Thông tin xác nhận OTP
   * @returns Cấu hình đã tạo/cập nhật
   */
  async confirmOtpAndExecuteAction(
    confirmDto: ConfirmSystemConfigOtpDto,
  ): Promise<SystemConfiguration> {
    try {
      // Xác thực OTP và lấy dữ liệu
      const { action, configData, adminEmail, adminName } =
        await this.otpManagerService.verifyOtpAndGetData(confirmDto.otpToken, confirmDto.otpCode);

      this.logger.log(`Thực hiện ${action} cấu hình hệ thống cho admin ${adminEmail}`);

      if (action === 'CREATE') {
        // Thực hiện tạo cấu hình
        return await this.executeCreateConfiguration(configData as CreateSystemConfigurationDto);
      } else {
        // Thực hiện cập nhật cấu hình
        return await this.executeUpdateConfiguration(configData as UpdateSystemConfigurationDto);
      }
    } catch (error) {
      this.logger.error(
        `Lỗi khi xác nhận OTP và thực hiện action: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Lỗi khi xác nhận OTP và thực hiện thay đổi cấu hình',
      );
    }
  }

  /**
   * Thực hiện tạo cấu hình (logic cũ)
   * @param createDto Dữ liệu cấu hình
   * @returns Cấu hình đã tạo
   */
  private async executeCreateConfiguration(
    createDto: CreateSystemConfigurationDto,
  ): Promise<SystemConfiguration> {
    // Logic tạo cấu hình từ method createSystemConfigurationFull cũ
    const existingConfig = await this.systemConfigRepository.findOne({
      where: { active: true },
    });

    const now = Math.floor(Date.now() / 1000);

    const configData: Partial<SystemConfiguration> = {
      bankCode: createDto.bankCode,
      accountNumber: this.systemConfigEncryptionService.encrypt(createDto.accountNumber),
      accountName: this.systemConfigEncryptionService.encrypt(createDto.accountName),
      feePercentage: createDto.feePercentage,
      commissionToPointsConversionRate: createDto.commissionToPointsConversionRate,
      purchaseInvoiceTemplate: createDto.purchaseInvoiceTemplate,
      emailNotificationSystemId: createDto.emailNotificationSystemId,
      initialAffiliateContractBusiness: createDto.initialAffiliateContractBusiness,
      initialAffiliateContractCustomer: createDto.initialAffiliateContractCustomer,
      initialRuleContractBusiness: createDto.initialRuleContractBusiness,
      initialRuleContractCustomer: createDto.initialRuleContractCustomer,
      active: createDto.active !== undefined ? createDto.active : true,
      updatedAt: now,
    };

    let savedConfiguration: SystemConfiguration;

    if (existingConfig) {
      savedConfiguration = await this.systemConfigRepository.save({
        ...existingConfig,
        ...configData,
      });
    } else {
      configData.createdAt = now;
      savedConfiguration = await this.systemConfigRepository.save(configData);
    }

    if (savedConfiguration.active) {
      await this.redisService.setWithExpiry(
        this.CACHE_KEY,
        JSON.stringify(savedConfiguration),
        this.CACHE_TTL,
      );
    }

    const decryptedConfig = this.decryptBankAccountInfo(savedConfiguration);
    return this.attachCdnUrlsToContractFields(decryptedConfig);
  }

  /**
   * Thực hiện cập nhật cấu hình (logic cũ)
   * @param updateDto Dữ liệu cập nhật
   * @returns Cấu hình đã cập nhật
   */
  private async executeUpdateConfiguration(
    updateDto: UpdateSystemConfigurationDto,
  ): Promise<SystemConfiguration> {
    // Logic cập nhật từ method updateSystemConfigurationWithoutId cũ
    const existingConfig = await this.systemConfigRepository.findOne({
      where: { active: true },
    });

    if (!existingConfig) {
      throw new AppException(
        ErrorCode.RESOURCE_NOT_FOUND,
        'Không tìm thấy cấu hình hệ thống để cập nhật',
      );
    }

    const now = Math.floor(Date.now() / 1000);
    const updateData: Partial<SystemConfiguration> = {
      updatedAt: now,
    };

    if (updateDto.bankCode !== undefined) {
      updateData.bankCode = updateDto.bankCode;
    }
    if (updateDto.accountNumber !== undefined) {
      updateData.accountNumber = this.systemConfigEncryptionService.encrypt(updateDto.accountNumber);
    }
    if (updateDto.accountName !== undefined) {
      updateData.accountName = this.systemConfigEncryptionService.encrypt(updateDto.accountName);
    }
    if (updateDto.active !== undefined) {
      updateData.active = updateDto.active;
    }
    if (updateDto.feePercentage !== undefined) {
      updateData.feePercentage = updateDto.feePercentage;
    }
    if (updateDto.commissionToPointsConversionRate !== undefined) {
      updateData.commissionToPointsConversionRate = updateDto.commissionToPointsConversionRate;
    }
    if (updateDto.purchaseInvoiceTemplate !== undefined) {
      updateData.purchaseInvoiceTemplate = updateDto.purchaseInvoiceTemplate;
    }
    if (updateDto.emailNotificationSystemId !== undefined) {
      updateData.emailNotificationSystemId = updateDto.emailNotificationSystemId;
    }
    if (updateDto.initialAffiliateContractBusiness !== undefined) {
      updateData.initialAffiliateContractBusiness = updateDto.initialAffiliateContractBusiness;
    }
    if (updateDto.initialAffiliateContractCustomer !== undefined) {
      updateData.initialAffiliateContractCustomer = updateDto.initialAffiliateContractCustomer;
    }
    if (updateDto.initialRuleContractBusiness !== undefined) {
      updateData.initialRuleContractBusiness = updateDto.initialRuleContractBusiness;
    }
    if (updateDto.initialRuleContractCustomer !== undefined) {
      updateData.initialRuleContractCustomer = updateDto.initialRuleContractCustomer;
    }

    const updatedConfiguration = await this.systemConfigRepository.save({
      ...existingConfig,
      ...updateData,
    });

    if (updatedConfiguration.active) {
      await this.redisService.del(this.CACHE_KEY);
      await this.redisService.setWithExpiry(
        this.CACHE_KEY,
        JSON.stringify(updatedConfiguration),
        this.CACHE_TTL,
      );
    }

    const decryptedConfig = this.decryptBankAccountInfo(updatedConfiguration);
    return this.attachCdnUrlsToContractFields(decryptedConfig);
  }

  /**
   * Tạo hoặc cập nhật cấu hình hệ thống đầy đủ (bao gồm thông tin ngân hàng)
   * Logic upsert: nếu đã có cấu hình thì cập nhật, nếu chưa có thì tạo mới
   * @param createDto Thông tin cấu hình cần tạo/cập nhật
   * @returns Cấu hình đã tạo/cập nhật
   * @deprecated Sử dụng OTP flow thay thế
   */
  async createSystemConfigurationFull(
    createDto: CreateSystemConfigurationDto,
  ): Promise<SystemConfigOnlyResponseDto> {
    try {
      this.logger.log(
        `[DEBUG] Starting createSystemConfigurationFull with data: ${JSON.stringify(createDto)}`,
      );

      const now = Math.floor(Date.now() / 1000);

      // Kiểm tra xem đã có cấu hình active nào chưa
      const existingConfig = await this.systemConfigRepository.findOne({
        where: { active: true },
      });

      // Tạo dữ liệu cấu hình với mã hóa thông tin ngân hàng
      const configData: Partial<SystemConfiguration> = {
        // Thông tin ngân hàng (mã hóa account number và account name)
        bankCode: createDto.bankCode, // Bank code không mã hóa
        accountNumber: this.systemConfigEncryptionService.encrypt(createDto.accountNumber),
        accountName: this.systemConfigEncryptionService.encrypt(createDto.accountName),

        // Cấu hình hệ thống
        feePercentage: createDto.feePercentage,
        commissionToPointsConversionRate:
          createDto.commissionToPointsConversionRate,
        purchaseInvoiceTemplate: createDto.purchaseInvoiceTemplate,
        emailNotificationSystemId: createDto.emailNotificationSystemId,

        // Contract keys
        initialAffiliateContractBusiness:
          createDto.initialAffiliateContractBusiness,
        initialAffiliateContractCustomer:
          createDto.initialAffiliateContractCustomer,
        initialRuleContractBusiness: createDto.initialRuleContractBusiness,
        initialRuleContractCustomer: createDto.initialRuleContractCustomer,

        // Metadata
        active: createDto.active !== undefined ? createDto.active : true,
        updatedAt: now,
      };

      let savedConfiguration: SystemConfiguration;

      if (existingConfig) {
        // Cập nhật cấu hình hiện có
        this.logger.log(
          `[DEBUG] Updating existing configuration with ID: ${existingConfig.id}`,
        );

        savedConfiguration = await this.systemConfigRepository.save({
          ...existingConfig,
          ...configData,
        });

        this.logger.log(
          `[DEBUG] Updated existing configuration with ID: ${savedConfiguration.id}`,
        );
      } else {
        // Tạo cấu hình mới
        this.logger.log(`[DEBUG] Creating new configuration`);

        configData.createdAt = now;
        savedConfiguration = await this.systemConfigRepository.save(configData);

        this.logger.log(
          `[DEBUG] Created new configuration with ID: ${savedConfiguration.id}`,
        );
      }

      // Nếu cấu hình được set active, cập nhật cache
      if (savedConfiguration.active) {
        await this.redisService.setWithExpiry(
          this.CACHE_KEY,
          JSON.stringify(savedConfiguration),
          this.CACHE_TTL,
        );
        this.logger.log(`[DEBUG] Updated cache with active configuration`);
      }

      // Giải mã dữ liệu và gắn URL CDN để trả về cho client
      const decryptedConfig = this.decryptBankAccountInfo(savedConfiguration);
      const configWithUrls = this.attachCdnUrlsToContractFields(decryptedConfig);

      return this.mapToSystemConfigOnlyResponse(configWithUrls);
    } catch (error) {
      this.logger.error(
        `Lỗi khi tạo/cập nhật cấu hình hệ thống: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Lỗi khi tạo/cập nhật cấu hình hệ thống',
      );
    }
  }

  /**
   * Lấy danh sách tất cả cấu hình hệ thống
   * @returns Danh sách tất cả cấu hình với thông tin ngân hàng đã giải mã và URL CDN cho contract fields
   */
  async getAllConfigurations(): Promise<SystemConfiguration[]> {
    try {
      const configurations = await this.systemConfigRepository.find({
        order: { createdAt: 'DESC' }
      });

      // Giải mã thông tin ngân hàng và gắn URL CDN cho tất cả cấu hình
      return configurations.map(config => {
        try {
          const decryptedConfig = this.decryptBankAccountInfo(config);
          return this.attachCdnUrlsToContractFields(decryptedConfig);
        } catch (error) {
          this.logger.error(
            `Lỗi khi giải mã thông tin ngân hàng cho cấu hình ${config.id}: ${error.message}`,
            error.stack,
          );
          // Trả về cấu hình gốc nếu không thể giải mã
          return config;
        }
      });

    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy danh sách cấu hình hệ thống: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Lỗi khi lấy danh sách cấu hình hệ thống',
      );
    }
  }

  /**
   * Lấy danh sách cấu hình hệ thống có phân trang
   * @param query Tham số query cho phân trang và tìm kiếm
   * @returns Danh sách cấu hình có phân trang với thông tin ngân hàng đã giải mã và URL CDN cho contract fields
   */
  async getAllConfigurationsPaginated(query: SystemConfigurationQueryDto): Promise<PaginatedResult<SystemConfiguration>> {
    try {
      const { page = 1, limit = 10, bankCode, accountNumber, active, minFeePercentage, maxFeePercentage } = query;

      // Tạo query builder
      const queryBuilder = this.systemConfigRepository.createQueryBuilder('config');

      // Thêm điều kiện tìm kiếm
      if (bankCode) {
        queryBuilder.andWhere('config.bankCode ILIKE :bankCode', { bankCode: `%${bankCode}%` });
      }

      if (accountNumber) {
        queryBuilder.andWhere('config.accountNumber ILIKE :accountNumber', { accountNumber: `%${accountNumber}%` });
      }

      if (active !== undefined) {
        queryBuilder.andWhere('config.active = :active', { active });
      }

      if (minFeePercentage !== undefined) {
        queryBuilder.andWhere('config.feePercentage >= :minFeePercentage', { minFeePercentage });
      }

      if (maxFeePercentage !== undefined) {
        queryBuilder.andWhere('config.feePercentage <= :maxFeePercentage', { maxFeePercentage });
      }

      // Sắp xếp theo ngày tạo mới nhất
      queryBuilder.orderBy('config.createdAt', 'DESC');

      // Phân trang
      const offset = (page - 1) * limit;
      queryBuilder.skip(offset).take(limit);

      // Lấy dữ liệu và tổng số bản ghi
      const [configurations, totalItems] = await queryBuilder.getManyAndCount();

      // Giải mã thông tin ngân hàng và gắn URL CDN cho tất cả cấu hình
      const decryptedConfigurations = configurations.map(config => {
        try {
          const decryptedConfig = this.decryptBankAccountInfo(config);
          return this.attachCdnUrlsToContractFields(decryptedConfig);
        } catch (error) {
          this.logger.error(
            `Lỗi khi giải mã thông tin ngân hàng cho cấu hình ${config.id}: ${error.message}`,
            error.stack,
          );
          // Trả về cấu hình gốc nếu không thể giải mã
          return config;
        }
      });

      // Tạo metadata phân trang
      const totalPages = Math.ceil(totalItems / limit);
      const itemCount = decryptedConfigurations.length;

      return {
        items: decryptedConfigurations,
        meta: {
          totalItems,
          itemCount,
          itemsPerPage: limit,
          totalPages,
          currentPage: page,
          hasItems: totalItems > 0
        }
      };

    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy danh sách cấu hình hệ thống có phân trang: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Lỗi khi lấy danh sách cấu hình hệ thống có phân trang',
      );
    }
  }

  /**
   * Active cấu hình hệ thống theo ID
   * Khi active cấu hình này, tất cả các cấu hình khác sẽ bị tắt (active = false)
   * @param id ID của cấu hình cần active
   * @returns Cấu hình đã được active với thông tin ngân hàng đã giải mã
   */
  async activateConfiguration(id: number): Promise<SystemConfiguration> {
    try {
      // Tìm cấu hình theo ID
      const configuration = await this.systemConfigRepository.findOne({
        where: { id }
      });

      if (!configuration) {
        throw new AppException(
          SYSTEM_CONFIGURATION_ERROR_CODES.CONFIGURATION_WITH_ID_NOT_FOUND,
        );
      }

      // Kiểm tra xem cấu hình đã active chưa
      if (configuration.active) {
        throw new AppException(
          SYSTEM_CONFIGURATION_ERROR_CODES.CONFIGURATION_ALREADY_ACTIVE
        );
      }

      // Bắt đầu transaction để đảm bảo tính nhất quán
      await this.systemConfigRepository.manager.transaction(async (manager) => {
        // Tắt tất cả các cấu hình khác
        await manager.update(
          SystemConfiguration,
          {},
          {
            active: false,
            updatedAt: Math.floor(Date.now() / 1000)
          }
        );

        // Active cấu hình được chỉ định
        await manager.update(
          SystemConfiguration,
          { id },
          {
            active: true,
            updatedAt: Math.floor(Date.now() / 1000)
          }
        );
      });

      // Lấy lại cấu hình đã được cập nhật
      const updatedConfiguration = await this.systemConfigRepository.findOne({
        where: { id }
      });

      if (!updatedConfiguration) {
        throw new AppException(
          ErrorCode.INTERNAL_SERVER_ERROR,
          'Lỗi khi lấy cấu hình đã cập nhật'
        );
      }

      // Xóa cache cũ
      await this.clearCache();

      // Cập nhật cache với cấu hình mới
      await this.redisService.setWithExpiry(
        this.CACHE_KEY,
        JSON.stringify(updatedConfiguration),
        this.CACHE_TTL,
      );

      this.logger.log(`[ACTIVATE] Configuration ${id} has been activated successfully`);

      // Giải mã thông tin ngân hàng và gắn URL CDN trước khi trả về
      const decryptedConfig = this.decryptBankAccountInfo(updatedConfiguration);
      return this.attachCdnUrlsToContractFields(decryptedConfig);

    } catch (error) {
      this.logger.error(
        `Lỗi khi active cấu hình hệ thống: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Lỗi khi active cấu hình hệ thống',
      );
    }
  }
}

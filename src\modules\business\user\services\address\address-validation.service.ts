import { Injectable, Logger } from '@nestjs/common';
import { AppException } from '@common/exceptions/app.exception';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';

/**
 * Interface cho địa chỉ đã được parse
 */
export interface ParsedAddress {
  province: string;
  district: string;
  ward: string;
  districtId?: number;
  wardCode?: string;
  provinceId?: number;
  isValid: boolean;
  confidence: number; // Độ tin cậy của việc parse (0-1)
}

/**
 * Interface cho thông tin shipping từ shop
 */
export interface ShopShippingInfo {
  fromDistrictId: number;
  fromWardCode: string;
  fromProvinceName: string;
  fromDistrictName: string;
  fromWardName: string;
  fromAddress: string;
  fromPhone: string;
  fromName: string;
}

/**
 * Service xử lý validation và parsing địa chỉ
 */
@Injectable()
export class AddressValidationService {
  private readonly logger = new Logger(AddressValidationService.name);

  // Mapping cứng cho các tỉnh/thành phố chính (có thể mở rộng từ database)
  private readonly PROVINCE_MAPPING = {
    'hồ chí minh': { id: 79, name: 'Hồ Chí <PERSON>' },
    'tp.hcm': { id: 79, name: 'Hồ Chí Minh' },
    'hcm': { id: 79, name: 'Hồ Chí Minh' },
    'sài gòn': { id: 79, name: 'Hồ Chí Minh' },
    'hà nội': { id: 1, name: 'Hà Nội' },
    'hanoi': { id: 1, name: 'Hà Nội' },
    'đà nẵng': { id: 48, name: 'Đà Nẵng' },
    'da nang': { id: 48, name: 'Đà Nẵng' },
    'cần thơ': { id: 92, name: 'Cần Thơ' },
    'can tho': { id: 92, name: 'Cần Thơ' },
    'hải phòng': { id: 31, name: 'Hải Phòng' },
    'hai phong': { id: 31, name: 'Hải Phòng' }
  };

  // Mapping cứng cho các quận/huyện chính của TP.HCM (có thể mở rộng)
  private readonly DISTRICT_MAPPING_HCM = {
    'quận 1': { id: 1442, wardCodes: ['21211', '21208', '21210'] },
    'quận 2': { id: 1443, wardCodes: ['21308', '21309', '21310'] },
    'quận 3': { id: 1444, wardCodes: ['21408', '21409', '21410'] },
    'quận 4': { id: 1445, wardCodes: ['21508', '21509', '21510'] },
    'quận 5': { id: 1446, wardCodes: ['21608', '21609', '21610'] },
    'quận 6': { id: 1447, wardCodes: ['21708', '21709', '21710'] },
    'quận 7': { id: 1448, wardCodes: ['21808', '21809', '21810'] },
    'quận 8': { id: 1449, wardCodes: ['21908', '21909', '21910'] },
    'quận 9': { id: 1450, wardCodes: ['22008', '22009', '22010'] },
    'quận 10': { id: 1451, wardCodes: ['22108', '22109', '22110'] },
    'quận 11': { id: 1452, wardCodes: ['22208', '22209', '22210'] },
    'quận 12': { id: 1453, wardCodes: ['22308', '22309', '22310'] },
    'quận bình thạnh': { id: 1454, wardCodes: ['22408', '22409', '22410'] },
    'quận gò vấp': { id: 1455, wardCodes: ['22508', '22509', '22510'] },
    'quận phú nhuận': { id: 1456, wardCodes: ['22608', '22609', '22610'] },
    'quận tân bình': { id: 1457, wardCodes: ['22708', '22709', '22710'] },
    'quận tân phú': { id: 1458, wardCodes: ['22808', '22809', '22810'] },
    'quận thủ đức': { id: 1459, wardCodes: ['22908', '22909', '22910'] }
  };

  /**
   * Validate và parse địa chỉ từ chuỗi text
   * @param address Địa chỉ đầy đủ
   * @returns Thông tin địa chỉ đã parse
   */
  async validateAndParseAddress(address: string): Promise<ParsedAddress> {
    try {
      this.logger.log(`Parsing address: ${address}`);

      if (!address || address.trim().length === 0) {
        throw new AppException(
          BUSINESS_ERROR_CODES.ADDRESS_VALIDATION_FAILED,
          'Địa chỉ không được để trống'
        );
      }

      const normalizedAddress = this.normalizeAddress(address);
      const addressParts = normalizedAddress.split(',').map(part => part.trim());

      let province = '';
      let district = '';
      let ward = '';
      let confidence = 0;

      // Parse province
      const provinceInfo = this.extractProvince(addressParts);
      if (provinceInfo) {
        province = provinceInfo.name;
        confidence += 0.4;
      }

      // Parse district
      const districtInfo = this.extractDistrict(addressParts, province);
      if (districtInfo) {
        district = districtInfo.name;
        confidence += 0.3;
      }

      // Parse ward
      const wardInfo = this.extractWard(addressParts);
      if (wardInfo) {
        ward = wardInfo.name;
        confidence += 0.3;
      }

      // Get district ID và ward code
      const { districtId, wardCode } = this.getDistrictAndWardCodes(province, district, ward);

      const result: ParsedAddress = {
        province: province || 'Hồ Chí Minh', // Default
        district: district || 'Quận 1', // Default
        ward: ward || 'Phường Bến Nghé', // Default
        districtId,
        wardCode,
        provinceId: this.getProvinceId(province),
        isValid: confidence > 0.5,
        confidence
      };

      this.logger.log(`Parsed address result:`, result);
      return result;
    } catch (error) {
      this.logger.error(`Error parsing address: ${error.message}`);
      throw error;
    }
  }

  /**
   * Normalize địa chỉ để dễ parse
   */
  private normalizeAddress(address: string): string {
    return address
      .toLowerCase()
      .replace(/\s+/g, ' ')
      .replace(/tp\./g, '')
      .replace(/thành phố/g, '')
      .replace(/tỉnh/g, '')
      .trim();
  }

  /**
   * Extract thông tin tỉnh/thành phố
   */
  private extractProvince(addressParts: string[]): { id: number; name: string } | null {
    for (const part of addressParts) {
      for (const [key, value] of Object.entries(this.PROVINCE_MAPPING)) {
        if (part.includes(key)) {
          return value;
        }
      }
    }
    return null;
  }

  /**
   * Extract thông tin quận/huyện
   */
  private extractDistrict(addressParts: string[], province: string): { name: string } | null {
    for (const part of addressParts) {
      if (part.includes('quận') || part.includes('huyện') || part.includes('thành phố')) {
        // Chuẩn hóa tên quận
        const districtName = part
          .replace(/quận\s*/g, 'quận ')
          .replace(/huyện\s*/g, 'huyện ')
          .replace(/thành phố\s*/g, 'thành phố ')
          .trim();
        
        return { name: this.capitalizeDistrict(districtName) };
      }
    }
    return null;
  }

  /**
   * Extract thông tin phường/xã
   */
  private extractWard(addressParts: string[]): { name: string } | null {
    for (const part of addressParts) {
      if (part.includes('phường') || part.includes('xã') || part.includes('thị trấn')) {
        const wardName = part
          .replace(/phường\s*/g, 'phường ')
          .replace(/xã\s*/g, 'xã ')
          .replace(/thị trấn\s*/g, 'thị trấn ')
          .trim();
        
        return { name: this.capitalizeWard(wardName) };
      }
    }
    return null;
  }

  /**
   * Get district ID và ward code từ mapping
   */
  private getDistrictAndWardCodes(province: string, district: string, ward: string): { districtId: number; wardCode: string } {
    // Mặc định cho TP.HCM
    if (province.toLowerCase().includes('hồ chí minh')) {
      const districtKey = district.toLowerCase();
      const districtInfo = this.DISTRICT_MAPPING_HCM[districtKey];
      
      if (districtInfo) {
        return {
          districtId: districtInfo.id,
          wardCode: districtInfo.wardCodes[0] // Lấy ward code đầu tiên
        };
      }
    }

    // Default fallback
    return {
      districtId: 1442, // Quận 1, TP.HCM
      wardCode: '21211' // Phường Bến Nghé
    };
  }

  /**
   * Get province ID
   */
  private getProvinceId(province: string): number {
    const provinceKey = province.toLowerCase();
    for (const [key, value] of Object.entries(this.PROVINCE_MAPPING)) {
      if (provinceKey.includes(key)) {
        return value.id;
      }
    }
    return 79; // Default TP.HCM
  }

  /**
   * Capitalize district name
   */
  private capitalizeDistrict(district: string): string {
    return district.replace(/\b\w/g, l => l.toUpperCase());
  }

  /**
   * Capitalize ward name
   */
  private capitalizeWard(ward: string): string {
    return ward.replace(/\b\w/g, l => l.toUpperCase());
  }

  /**
   * Get district ID từ tên tỉnh và quận
   */
  async getDistrictId(province: string, district: string): Promise<number> {
    const { districtId } = this.getDistrictAndWardCodes(province, district, '');
    return districtId;
  }

  /**
   * Get ward code từ thông tin địa chỉ
   */
  async getWardCode(province: string, district: string, ward: string): Promise<string> {
    const { wardCode } = this.getDistrictAndWardCodes(province, district, ward);
    return wardCode;
  }
}

import { Processor, WorkerHost, OnWorkerEvent } from '@nestjs/bullmq';
import { Logger } from '@nestjs/common';
import { Job } from 'bullmq';
import { GenericSessionService, GenericWidgetService, GenericLayoutService } from '../services';
import { GenericWebSocketGateway } from '../gateways/generic-websocket.gateway';
import {
  GENERIC_PAGE_QUEUE,
  GenericPageJobType,
  GenericPageJobData,
  GenericPageJobResult,
  AddWidgetJobData,
  RemoveWidgetJobData,
  UpdateWidgetJobData,
  UpdateLayoutJobData,
  SyncStateJobData,
  CleanupExpiredSessionsJobData,
  BroadcastToSessionJobData,
  BroadcastToAllJobData,
} from '../constants/generic-queue.constants';
import { WebSocketEventType } from '../dto/generic-websocket.dto';

/**
 * Worker để xử lý Generic Page queue jobs
 */
@Processor(GENERIC_PAGE_QUEUE)
export class GenericPageWorker extends WorkerHost {
  private readonly logger = new Logger(GenericPageWorker.name);

  constructor(
    private readonly genericSessionService: GenericSessionService,
    private readonly genericWidgetService: GenericWidgetService,
    private readonly genericLayoutService: GenericLayoutService,
    private readonly genericWebSocketGateway: GenericWebSocketGateway,
  ) {
    super();
  }

  /**
   * Process queue jobs
   */
  async process(job: Job<GenericPageJobData>): Promise<GenericPageJobResult> {
    const { name, data } = job;
    
    this.logger.log(`Processing job ${name} with ID ${job.id}`);

    try {
      let result: GenericPageJobResult;

      switch (name) {
        case GenericPageJobType.ADD_WIDGET:
          result = await this.handleAddWidget(data as AddWidgetJobData);
          break;

        case GenericPageJobType.REMOVE_WIDGET:
          result = await this.handleRemoveWidget(data as RemoveWidgetJobData);
          break;

        case GenericPageJobType.UPDATE_WIDGET:
          result = await this.handleUpdateWidget(data as UpdateWidgetJobData);
          break;

        case GenericPageJobType.UPDATE_LAYOUT:
          result = await this.handleUpdateLayout(data as UpdateLayoutJobData);
          break;

        case GenericPageJobType.SYNC_STATE:
          result = await this.handleSyncState(data as SyncStateJobData);
          break;

        case GenericPageJobType.CLEANUP_EXPIRED_SESSIONS:
          result = await this.handleCleanupExpiredSessions(data as CleanupExpiredSessionsJobData);
          break;

        case GenericPageJobType.BROADCAST_TO_SESSION:
          result = await this.handleBroadcastToSession(data as BroadcastToSessionJobData);
          break;

        case GenericPageJobType.BROADCAST_TO_ALL:
          result = await this.handleBroadcastToAll(data as BroadcastToAllJobData);
          break;

        default:
          throw new Error(`Unknown job type: ${name}`);
      }

      this.logger.log(`Job ${name} completed successfully: ${result.message}`);
      return result;

    } catch (error) {
      this.logger.error(`Job ${name} failed: ${error.message}`, error.stack);
      
      const errorResult: GenericPageJobResult = {
        success: false,
        message: `Job ${name} failed`,
        error: error.message,
        timestamp: new Date().toISOString(),
      };

      throw error; // Re-throw để BullMQ có thể retry
    }
  }

  /**
   * Handle add widget job
   */
  private async handleAddWidget(data: AddWidgetJobData): Promise<GenericPageJobResult> {
    try {
      // Thêm widget vào session
      const widget = await this.genericWidgetService.addWidgetToSession(data.sessionId, {
        widgetId: data.widget.widgetId,
        widgetType: data.widget.widgetType,
        widgetData: data.widget.widgetData,
        position: data.widget.position,
        displayOrder: data.widget.displayOrder,
        isVisible: data.widget.isVisible,
        metadata: data.widget.metadata,
      });

      // Broadcast widget added event
      this.genericWebSocketGateway.broadcastToSession(data.sessionId, {
        type: WebSocketEventType.WIDGET_ADDED,
        payload: { widget },
        sessionId: data.sessionId,
        timestamp: new Date().toISOString(),
      });

      return {
        success: true,
        message: `Widget ${data.widget.widgetId} added to session ${data.sessionId}`,
        data: { widget },
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      throw new Error(`Failed to add widget: ${error.message}`);
    }
  }

  /**
   * Handle remove widget job
   */
  private async handleRemoveWidget(data: RemoveWidgetJobData): Promise<GenericPageJobResult> {
    try {
      // Xóa widget khỏi session
      const deletedCount = await this.genericWidgetService.removeWidgetFromSession(
        data.sessionId,
        data.widgetId
      );

      // Broadcast widget removed event
      this.genericWebSocketGateway.broadcastToSession(data.sessionId, {
        type: WebSocketEventType.WIDGET_REMOVED,
        payload: { widgetId: data.widgetId },
        sessionId: data.sessionId,
        timestamp: new Date().toISOString(),
      });

      return {
        success: true,
        message: `Widget ${data.widgetId} removed from session ${data.sessionId}`,
        data: { deletedCount },
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      throw new Error(`Failed to remove widget: ${error.message}`);
    }
  }

  /**
   * Handle update widget job
   */
  private async handleUpdateWidget(data: UpdateWidgetJobData): Promise<GenericPageJobResult> {
    try {
      // Cập nhật widget
      const widget = await this.genericWidgetService.updateWidgetInSession(
        data.sessionId,
        data.widgetId,
        data.updateData
      );

      // Broadcast widget updated event (custom event)
      this.genericWebSocketGateway.broadcastToSession(data.sessionId, {
        type: 'widget_updated',
        payload: { widget },
        sessionId: data.sessionId,
        timestamp: new Date().toISOString(),
      });

      return {
        success: true,
        message: `Widget ${data.widgetId} updated in session ${data.sessionId}`,
        data: { widget },
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      throw new Error(`Failed to update widget: ${error.message}`);
    }
  }

  /**
   * Handle update layout job
   */
  private async handleUpdateLayout(data: UpdateLayoutJobData): Promise<GenericPageJobResult> {
    try {
      // Cập nhật layout
      const layout = await this.genericLayoutService.updateLayoutData(data.sessionId, data.layoutData);

      // Broadcast layout updated event
      this.genericWebSocketGateway.broadcastToSession(data.sessionId, {
        type: WebSocketEventType.LAYOUT_UPDATED,
        payload: { layout: data.layoutData },
        sessionId: data.sessionId,
        timestamp: new Date().toISOString(),
      });

      return {
        success: true,
        message: `Layout updated for session ${data.sessionId}`,
        data: { layout },
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      throw new Error(`Failed to update layout: ${error.message}`);
    }
  }

  /**
   * Handle sync state job
   */
  private async handleSyncState(data: SyncStateJobData): Promise<GenericPageJobResult> {
    try {
      // Lấy current state
      const widgets = await this.genericWidgetService.getWidgetsBySessionId(data.sessionId);
      const layout = await this.genericLayoutService.getLayoutBySessionIdOptional(data.sessionId);

      // Broadcast state synced event
      this.genericWebSocketGateway.broadcastToSession(data.sessionId, {
        type: WebSocketEventType.STATE_SYNCED,
        payload: {
          widgets,
          layout: layout?.layoutData || []
        },
        sessionId: data.sessionId,
        timestamp: new Date().toISOString(),
      });

      return {
        success: true,
        message: `State synced for session ${data.sessionId}`,
        data: { widgets, layout: layout?.layoutData || [] },
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      throw new Error(`Failed to sync state: ${error.message}`);
    }
  }

  /**
   * Handle cleanup expired sessions job
   */
  private async handleCleanupExpiredSessions(data: CleanupExpiredSessionsJobData): Promise<GenericPageJobResult> {
    try {
      // Cleanup expired sessions
      const cleanedCount = await this.genericSessionService.cleanupExpiredSessions();

      return {
        success: true,
        message: `Cleaned up ${cleanedCount} expired sessions`,
        data: { cleanedCount },
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      throw new Error(`Failed to cleanup expired sessions: ${error.message}`);
    }
  }

  /**
   * Handle broadcast to session job
   */
  private async handleBroadcastToSession(data: BroadcastToSessionJobData): Promise<GenericPageJobResult> {
    try {
      // Broadcast event to specific session
      this.genericWebSocketGateway.broadcastToSession(data.sessionId, {
        ...data.event,
        sessionId: data.sessionId,
        timestamp: data.event.timestamp || new Date().toISOString(),
      });

      return {
        success: true,
        message: `Event broadcasted to session ${data.sessionId}`,
        data: { event: data.event },
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      throw new Error(`Failed to broadcast to session: ${error.message}`);
    }
  }

  /**
   * Handle broadcast to all job
   */
  private async handleBroadcastToAll(data: BroadcastToAllJobData): Promise<GenericPageJobResult> {
    try {
      // Broadcast event to all sessions
      this.genericWebSocketGateway.broadcastToAll({
        ...data.event,
        timestamp: data.event.timestamp || new Date().toISOString(),
      });

      return {
        success: true,
        message: 'Event broadcasted to all sessions',
        data: { event: data.event },
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      throw new Error(`Failed to broadcast to all: ${error.message}`);
    }
  }

  /**
   * Event handlers
   */
  @OnWorkerEvent('completed')
  onCompleted(job: Job<GenericPageJobData>, result: GenericPageJobResult) {
    this.logger.log(`Job ${job.name} (${job.id}) completed: ${result.message}`);
  }

  @OnWorkerEvent('failed')
  onFailed(job: Job<GenericPageJobData>, error: Error) {
    this.logger.error(`Job ${job.name} (${job.id}) failed: ${error.message}`, error.stack);
  }

  @OnWorkerEvent('stalled')
  onStalled(job: Job<GenericPageJobData>) {
    this.logger.warn(`Job ${job.name} (${job.id}) stalled`);
  }
}

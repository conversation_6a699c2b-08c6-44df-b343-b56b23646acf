import { ConfigService, ConfigType, EncryptionConfig } from '@/config';
import { EncryptionService } from '@/shared/services/encryption';
import {
  FacebookAuthService,
  FacebookPageService,
  FacebookPersonalService
} from '@/shared/services/facebook';
import {
  FacebookUserInfo
} from '@/shared/services/facebook/interfaces/facebook.interface';
import { AppException, ErrorCode } from '@common/exceptions';
import { PaginatedResult } from '@common/response';
import {
  IntegrationFacebookPageRepository,
  IntegrationProviderRepository,
  IntegrationRepository
} from '@modules/integration/repositories';
import { Injectable, Logger } from '@nestjs/common';
import { CdnService } from '@shared/services/cdn.service';
import { Transactional } from 'typeorm-transactional';
import { ProviderEnum } from '../../constants/provider.enum';
import { Integration } from '../../entities';
import { INTEGRATION_ERROR_CODES } from '../../exceptions/integration-error.code';
import { FacebookPageMetadata, FacebookPersonalMetadata } from '../../interfaces/metadata.interface';
import { FacebookPagePayload, FacebookPersonalPayload } from '../../interfaces/payload_encryption.interface';
import { FacebookAuthResponseDto, FacebookPageResponseDto } from '../dto';
import { CallbackResponseDto } from '../dto/facebook/callback-response.dto';
import { FacebookPageQueryDto } from '../dto/facebook/facebook-page-query.dto';
import { FacebookPageMapper } from '../mappers';

@Injectable()
export class FacebookPageUserService {
  private readonly logger = new Logger(FacebookPageUserService.name);
  private readonly secretKeyPrivate: string;

  constructor(
    private readonly cdnService: CdnService,
    private readonly integrationFacebookPageRepository: IntegrationFacebookPageRepository,
    private readonly integrationRepository: IntegrationRepository,
    private readonly facebookAuthService: FacebookAuthService,
    private readonly facebookPageService: FacebookPageService,
    private readonly facebookPersonalService: FacebookPersonalService,
    private readonly integrationProvider: IntegrationProviderRepository,
    private readonly encryptionService: EncryptionService,
    private readonly configService: ConfigService,
  ) {
    this.secretKeyPrivate = this.configService.getConfig<EncryptionConfig>(
      ConfigType.Encryption,
    ).secretKey;
  }

  /**
   * Lấy danh sách trang Facebook của người dùng với phân trang và lọc
   * @param userId ID của người dùng
   * @param queryDto Tham số truy vấn
   * @returns Danh sách trang Facebook với phân trang
   */
  async findFacebookPages(
    userId: number,
    queryDto: FacebookPageQueryDto,
  ): Promise<PaginatedResult<FacebookPageResponseDto>> {
    try {
      // Sử dụng repository để lấy danh sách trang Facebook chưa bị xóa mềm
      const {
        page = 1,
        limit = 10,
        sortBy,
        isConnectAgent,
        sortDirection,
        search,
      } = queryDto;

      // Gọi repository với các tham số truy vấn
      const result =
        await this.integrationFacebookPageRepository.findPagesByUserId(userId, {
          page,
          limit,
          sortBy,
          search,
          sortDirection,
          isConnectAgent,
        });

      if (!result.items || result.items.length === 0) {
        return {
          items: [],
          meta: {
            totalItems: 0,
            itemCount: 0,
            itemsPerPage: limit,
            totalPages: 0,
            currentPage: page,
          },
        };
      }

      // Sử dụng mapper để chuyển đổi kết quả thành DTO và thêm CDN URL cho avatar
      const items = FacebookPageMapper.toDtoList(result.items, this.cdnService);

      return {
        items,
        meta: result.meta,
      };
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy danh sách trang Facebook: ${error.message}`,
        error.stack,
      );
      throw new AppException(INTEGRATION_ERROR_CODES.FACEBOOK_PAGE_LIST_FAILED);
    }
  }

  /**
   * Tạo URL xác thực Facebook
   * @param userId ID của người dùng
   * @param endpointCallback Endpoint callback sau khi xác thực
   * @returns URL xác thực Facebook
   */
  async createFacebookAuthUrl(
    userId: number,
    endpointCallback: string,
  ): Promise<FacebookAuthResponseDto> {
    try {
      const authUrl = this.facebookAuthService.createAuthUrl(
        endpointCallback,
        userId.toString(),
      );

      return { authUrl };
    } catch (error) {
      this.logger.error(
        `Error creating Facebook auth URL: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        INTEGRATION_ERROR_CODES.FACEBOOK_PAGE_CREATE_FAILED,
        'Lỗi khi tạo URL xác thực Facebook',
      );
    }
  }

  /**
   * Xử lý callback từ Facebook
   * @param userId ID của người dùng
   * @param code
   * @param redirectUri
   * @param state
   * @returns Thông tin tài khoản Facebook cá nhân đã tạo
   */
  @Transactional()
  async handleFacebookCallback(
    userId: number,
    code: string,
    endpointCallback: string,
    state: string,
  ): Promise<CallbackResponseDto> {
    try {
      // Kiểm tra state
      if (state !== userId.toString()) {
        throw new AppException(
          INTEGRATION_ERROR_CODES.FACEBOOK_PAGE_AUTH_INVALID,
          'State không hợp lệ',
        );
      }

      // Kiểm tra xem code có rỗng hoặc quá ngắn không
      if (!code || code.length < 10) {
        throw new AppException(
          INTEGRATION_ERROR_CODES.FACEBOOK_PAGE_AUTH_INVALID,
          'Authorization code không hợp lệ',
        );
      }

      // Đổi code lấy access token
      this.logger.log(
        `[DEBUG] Bắt đầu gọi FacebookService.handleCallback với code và redirectUri`,
      );
      const tokenShort = await this.facebookAuthService.handleCallback(
        code,
        endpointCallback,
      );

      this.logger.log(`[DEBUG] Bắt đầu chuyển đổi sang long-lived token`);
      const longLivedTokenResponse =
        await this.facebookAuthService.getLongLivedToken(tokenShort.access_token);

      const { access_token, expires_in } = longLivedTokenResponse;

      if (!access_token) {
        this.logger.error(
          `[DEBUG] Long-lived token response không có access_token:`,
          longLivedTokenResponse,
        );
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          'Không thể lấy access token từ Facebook',
        );
      }

      // Lấy thông tin người dùng Facebook
      this.logger.log(`[DEBUG] Bắt đầu lấy thông tin người dùng Facebook`);
      const userResponse: FacebookUserInfo =
        await this.facebookPersonalService.getUserInfo(access_token);

      const { id: facebookPersonalId, name } = userResponse;

      if (!name) {
        this.logger.error(
          `[DEBUG] Không thể lấy tên người dùng từ Facebook response:`,
          userResponse,
        );
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          'Không thể lấy tên người dùng từ Facebook',
        );
      }

      // Tính thời gian hết hạn
      // Facebook long-lived token thường có thời hạn 60 ngày nếu không có expires_in
      const defaultExpiresIn = 60 * 24 * 60 * 60; // 60 ngày tính bằng giây
      const actualExpiresIn = expires_in || defaultExpiresIn;

      const expirationDate = new Date();
      expirationDate.setSeconds(expirationDate.getSeconds() + actualExpiresIn);
      const expirationDateUnix = Math.floor(expirationDate.getTime());

      // Lấy IntegrationProvider cho FACEBOOK_PERSONAL
      const facebookPersonalProvider =
        await this.integrationProvider.findByType(
          ProviderEnum.FACEBOOK_PERSONAL,
        );
      if (!facebookPersonalProvider) {
        throw new AppException(
          INTEGRATION_ERROR_CODES.FACEBOOK_PAGE_LIST_FAILED,
        );
      }

      // Kiểm tra xem đã có tài khoản Facebook cá nhân này chưa
      let facebookPersonal = await this.integrationRepository.findOne({
        where: {
          userId,
          typeId: facebookPersonalProvider.id,
          metadata: { personalId: facebookPersonalId } as FacebookPersonalMetadata
        },
      });

      const secretKeyPublic = this.encryptionService.generateSecretKey();

      // Tải avatar từ Facebook và upload lên S3
      let avatarKey: string | null = null;
      try {
        this.logger.log(`[DEBUG] Bắt đầu tải avatar của user Facebook ${facebookPersonalId}`);
        const existingAvatarKey = facebookPersonal?.metadata ?
          (facebookPersonal.metadata as FacebookPersonalMetadata).avatarKey : undefined;

        const avatarResult = await this.facebookPersonalService.getUserAvatarAndUploadToS3(
          facebookPersonalId,
          access_token,
          existingAvatarKey, // Sử dụng key cũ nếu có
        );
        avatarKey = avatarResult.key || null;
        this.logger.log(`[DEBUG] Đã tải avatar thành công với key: ${avatarKey}`);
      } catch (avatarError) {
        this.logger.warn(`[DEBUG] Không thể tải avatar của user Facebook ${facebookPersonalId}: ${avatarError.message}`);
        // Tiếp tục xử lý mà không dừng lại vì lỗi avatar
      }

      if (facebookPersonal && facebookPersonal.encryptedConfig && facebookPersonal.secretKey) {
        // Cập nhật thông tin
        facebookPersonal.encryptedConfig = this.encryptionService.encrypt<FacebookPersonalPayload>(this.secretKeyPrivate, facebookPersonal.secretKey, {
          accessToken: access_token,
        });

        const currentMetadata = facebookPersonal.metadata as FacebookPersonalMetadata;
        facebookPersonal.metadata = {
          ...currentMetadata,
          expirationDateUnix,
          avatarKey: avatarKey || currentMetadata?.avatarKey || null,
        } as FacebookPersonalMetadata;
      } else {
        // Tạo mới Integration cho Facebook Personal
        facebookPersonal = this.integrationRepository.create({
          integrationName: userResponse.name,
          typeId: facebookPersonalProvider.id,
          userId,
          metadata: {
            personalId: facebookPersonalId,
            avatar: avatarKey, // Lưu S3 key
            expirationDateUnix, // Thêm trường bắt buộc
          } as FacebookPersonalMetadata,
          encryptedConfig: this.encryptionService.encrypt<FacebookPersonalPayload>(secretKeyPublic, this.secretKeyPrivate, {
            accessToken: access_token,
          }),
          secretKey: secretKeyPublic,
        });
      }

      // Lưu vào database
      this.logger.log(`[DEBUG] Lưu tài khoản Facebook cá nhân vào database`);
      const facebookPersonalSaved = await this.integrationRepository.save(facebookPersonal);

      // Lấy danh sách trang Facebook
      this.logger.log(`[DEBUG] Bắt đầu đồng bộ danh sách trang Facebook`);
      const result = await this.syncFacebookPages(
        facebookPersonalSaved.id,
        access_token,
        userId,
      );

      return {
        pageCount: result.pageCount,
        pageSuccess: result.pageSuccess,
        pageError: result.pageError,
      };
    } catch (error) {
      this.logger.error(
        `Error handling Facebook callback: ${error.message}`,
        error.stack,
      );

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        `'Lỗi khi xử lý callback từ Facebook:' ${error.message}`,
      );
    }
  }

  // /**
  //  * Đồng bộ danh sách trang Facebook
  //  * @param facebookPersonalId ID của tài khoản Facebook cá nhân
  //  * @param accessToken Access token của tài khoản Facebook
  //  * @returns Thông tin về số lượng trang, trang lỗi và trang thành công
  //  */
  @Transactional()
  private async syncFacebookPages(
    facebookPersonalId: string,
    accessToken: string,
    userId: number,
  ): Promise<{
    pageCount: number;
    pageError?: string[];
    pageSuccess?: string[];
  }> {
    this.logger.log(`[DEBUG] syncFacebookPages started for user ${userId}`);

    try {

      const pageCount = 0;
      const pageError: string[] = [];
      const pageSuccess: string[] = [];

      // Bước 1: Lấy danh sách trang Facebook từ API
      // Lấy danh sách trang Facebook từ API
      this.logger.log(`[DEBUG] Bắt đầu gọi getUserAssignedPages`);
      const pagesResponse = await this.facebookPageService.getUserAssignedPages(accessToken);

      if (!pagesResponse || pagesResponse.length === 0) {
        this.logger.log(`[DEBUG] Không có trang Facebook nào, trả về pageCount: 0`);
        return { pageCount: 0 };
      }

      // Lấy danh sách ID trang Facebook từ API
      const apiPageIds = pagesResponse.map((page) => page.id);

      // Lấy IntegrationProvider cho FACEBOOK_PAGE
      const facebookPageProvider = await this.integrationProvider.findByType(ProviderEnum.FACEBOOK_PAGE);
      if (!facebookPageProvider) {
        this.logger.warn(`[DEBUG] Không tìm thấy provider FACEBOOK_PAGE`);
        return {
          pageCount: pagesResponse.length,
          pageSuccess: pagesResponse.map(page => page.name),
          pageError: [],
        };
      }

      // Lấy tất cả các trang Facebook trong database có pageId nằm trong danh sách từ API
      const allExistingPages = await this.integrationRepository.findFacebookPagesByPageIds(
        apiPageIds,
        facebookPageProvider.id
      );

      // Bước 2: Phân loại các trang Facebook
      // Lọc các trang của người dùng hiện tại
      const currentUserPages = allExistingPages.filter(page => page.userId === userId);

      // Lọc các trang của người dùng khác (để unsubscribe)
      const otherUserPages = allExistingPages.filter(page => page.userId !== userId);

      // Tạo map để dễ dàng tìm kiếm trang hiện có của người dùng hiện tại
      const currentUserPagesMap = new Map<string, Integration>();
      currentUserPages.forEach((page) => {
        const metadata = page.metadata as FacebookPageMetadata;
        if (metadata?.pageId) {
          currentUserPagesMap.set(metadata.pageId, page);
        }
      });

      // Danh sách các ID trang từ API chưa có trong database cho người dùng hiện tại
      const newPageIds = apiPageIds.filter(
        (id) => !currentUserPagesMap.has(id),
      );

      this.logger.log(`Tổng số trang từ API: ${apiPageIds.length}`);
      this.logger.log(`Số trang của người dùng khác: ${otherUserPages.length}`);
      this.logger.log(
        `Số trang của người dùng hiện tại: ${currentUserPages.length}`,
      );
      this.logger.log(`Số trang cần tạo mới: ${newPageIds.length}`);

      // Bước 3: Xử lý từng danh sách

      // Xử lý Danh sách 1: Các trang của người dùng khác
      try {
        // Lọc ra các trang đang active để unsubscribe
        const activePages = otherUserPages.filter((page) => {
          const metadata = page.metadata as any;
          return metadata?.active === true;
        });

        // Unsubscribe các trang đang active
        if (activePages.length > 0) {
          this.logger.log(
            `Cần hủy đăng ký webhook cho ${activePages.length} trang của người dùng khác`,
          );

          // Xử lý unsubscribe (vẫn phải lặp vì cần gọi API riêng cho mỗi trang)
          for (const page of activePages) {
            try {
              const metadata = page.metadata as FacebookPageMetadata;
              const pageId = metadata?.pageId;
              const pageName = page.integrationName;

              // Lấy access token từ encrypted config
              if (page.encryptedConfig && page.secretKey) {
                const decryptedConfig = this.encryptionService.decrypt<FacebookPagePayload>(
                  page.secretKey,
                  this.secretKeyPrivate,
                  page.encryptedConfig
                );
                const accessToken = decryptedConfig?.accessToken;

                if (pageId && accessToken) {
                  await this.facebookPageService.unsubscribeApp(pageId, accessToken);
                  this.logger.log(
                    `Đã hủy đăng ký webhook cho trang ${pageName} (ID: ${pageId}) của người dùng khác`,
                  );
                }
              }
            } catch (unsubError) {
              this.logger.error(
                `Lỗi khi hủy đăng ký webhook cho trang ${page.integrationName}: ${unsubError.message}`,
                unsubError.stack,
              );
            }
          }
        }

        // Cập nhật hàng loạt tất cả các trang của người dùng khác
        if (otherUserPages.length > 0) {
          const pageIds = otherUserPages.map((page) => page.id);

          // Sử dụng một câu lệnh cập nhật hàng loạt
          await this.integrationFacebookPageRepository
            .createQueryBuilder()
            .update(Integration)
            .set({
              metadata: {
                ...otherUserPages[0].metadata,
                error: true,
                active: false,
              },
            })
            .whereInIds(pageIds)
            .execute();

          this.logger.log(
            `Đã cập nhật trạng thái cho ${pageIds.length} trang của người dùng khác`,
          );
        }
      } catch (error) {
        this.logger.error(
          `Lỗi khi xử lý các trang của người dùng khác: ${error.message}`,
          error.stack,
        );
      }

      // Xử lý Danh sách 2 và 3: Cập nhật các trang hiện có và tạo mới các trang chưa có
      // Tạo mảng để lưu trữ các trang cần cập nhật và tạo mới
      const pagesToUpdate: Integration[] = [];
      const pagesToCreate: Integration[] = [];
      const pageAvatarMap = new Map<string, string>(); // Map để lưu trữ avatar cho mỗi trang

      // Xử lý từng trang từ API
      for (const page of pagesResponse) {
        const { id, name, access_token } = page;

        try {
          // Kiểm tra xem trang này có trong danh sách trang hiện có của người dùng không
          const existingPage = currentUserPagesMap.get(id);
          const metadata = existingPage?.metadata as FacebookPageMetadata;

          if (existingPage) {

            // Lấy avatar và upload lên S3
            const { key } = await this.facebookPageService.getPageAvatarAndUploadToS3(
              id,
              access_token,
              metadata.avatar,
            );
            pageAvatarMap.set(id, key);

            if (!existingPage.secretKey) {
              existingPage.secretKey = this.encryptionService.generateSecretKey();
            }

            // Danh sách 2: Cập nhật thông tin
            existingPage.encryptedConfig = this.encryptionService.encrypt<FacebookPagePayload>(existingPage.secretKey, this.secretKeyPrivate, {
              accessToken: access_token,
            });
            existingPage.integrationName = name;
            existingPage.metadata = {
              ...metadata,
              avatar: key,
              active: true,
              error: false,
            } as FacebookPageMetadata;

            pagesToUpdate.push(existingPage);
            pageSuccess.push(name);
          } else {
            // Lấy avatar và upload lên S3
            const { key } =
              await this.facebookPageService.getPageAvatarAndUploadToS3(
                id,
                access_token,
              );
            pageAvatarMap.set(id, key);

            const secretKeyPublic = this.encryptionService.generateSecretKey();

            // Danh sách 3: Tạo mới trang
            const newPage = new Integration();
            newPage.integrationName = name;
            newPage.typeId = facebookPageProvider.id;
            newPage.userId = userId;
            newPage.encryptedConfig = this.encryptionService.encrypt<FacebookPagePayload>(
              secretKeyPublic,
              this.secretKeyPrivate,
              {
                accessToken: access_token,
              });
            newPage.secretKey = secretKeyPublic;
            newPage.metadata = {
              pageId: id,
              avatar: key,
              active: true,
              error: false,
              personalId: facebookPersonalId,
            } as FacebookPageMetadata;

            pagesToCreate.push(newPage);
            pageSuccess.push(name);
          }
        } catch (error) {
          this.logger.error(
            `Lỗi khi xử lý trang ${id}: ${error.message}`,
            error.stack,
          );
          pageError.push(name);
        }
      }

      // Cập nhật hàng loạt các trang hiện có
      if (pagesToUpdate.length > 0) {
        try {
          await this.integrationFacebookPageRepository.save(pagesToUpdate);
          this.logger.log(
            `Đã cập nhật thông tin cho ${pagesToUpdate.length} trang hiện có`,
          );
        } catch (error) {
          this.logger.error(
            `Lỗi khi cập nhật hàng loạt các trang hiện có: ${error.message}`,
            error.stack,
          );
        }
      }

      // Tạo mới hàng loạt các trang
      if (pagesToCreate.length > 0) {
        try {
          await this.integrationRepository.save(pagesToCreate);
          this.logger.log(`Đã tạo mới ${pagesToCreate.length} trang`);
        } catch (error) {
          this.logger.error(
            `Lỗi khi tạo mới hàng loạt các trang: ${error.message}`,
            error.stack,
          );
        }
      }

      return {
        pageCount: pagesResponse.length,
        pageSuccess: pageSuccess.length > 0 ? pageSuccess : undefined,
        pageError: pageError.length > 0 ? pageError : undefined,
      };
    } catch (error) {
      this.logger.error(
        `Lỗi khi đồng bộ danh sách trang Facebook: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        INTEGRATION_ERROR_CODES.FACEBOOK_PAGE_UPDATE_FAILED,
        'Lỗi khi đồng bộ danh sách trang Facebook',
      );
    }
  }

  /**
   * Xóa mềm một trang Facebook
   * @param userId ID của người dùng
   * @param pageId ID của trang Facebook (pageId trong metadata)
   */
  @Transactional()
  async deleteFacebookPage(userId: number, pageId: string): Promise<void> {
    try {
      // Lấy IntegrationProvider cho FACEBOOK_PAGE
      const facebookPageProvider = await this.integrationProvider.findByType(ProviderEnum.FACEBOOK_PAGE);
      if (!facebookPageProvider) {
        throw new AppException(
          INTEGRATION_ERROR_CODES.FACEBOOK_PAGE_NOT_FOUND,
          'Không tìm thấy provider FACEBOOK_PAGE',
        );
      }

      // Tìm trang Facebook thuộc về người dùng theo pageId trong metadata
      const page = await this.integrationRepository.findFacebookPageByPageIdAndUserId(
        pageId,
        facebookPageProvider.id,
        userId
      );

      if (!page) {
        throw new AppException(
          INTEGRATION_ERROR_CODES.FACEBOOK_PAGE_NOT_FOUND,
          `Không tìm thấy trang Facebook với ID ${pageId}`,
        );
      }

      const metadata = page.metadata as FacebookPageMetadata;

      // Nếu trang đang active, thực hiện unsubscribe trước khi xóa
      if (metadata?.active) {
        try {
          // Lấy access token từ encrypted config
          if (page.encryptedConfig && page.secretKey) {
            const decryptedConfig = this.encryptionService.decrypt<FacebookPagePayload>(
              page.secretKey,
              this.secretKeyPrivate,
              page.encryptedConfig
            );
            const accessToken = decryptedConfig?.accessToken;

            if (accessToken) {
              await this.facebookPageService.unsubscribeApp(metadata.pageId, accessToken);
              this.logger.log(
                `Đã hủy đăng ký webhook cho trang ${page.integrationName} (ID: ${pageId})`,
              );
            }
          }
        } catch (unsubError) {
          this.logger.error(
            `Lỗi khi hủy đăng ký webhook cho trang ${page.integrationName}: ${unsubError.message}`,
            unsubError.stack,
          );

          throw new AppException(
            INTEGRATION_ERROR_CODES.FACEBOOK_PAGE_DELETE_FAILED,
            'Lỗi khi xóa trang Facebook',
          );
        }
      }

      // Cập nhật integration
      await this.integrationRepository.delete(page.id);

      this.logger.log(
        `Đã vô hiệu hóa trang Facebook ${page.integrationName} (ID: ${pageId})`,
      );

      // Kiểm tra xem user còn Facebook Page nào active không
      const remainingActivePages = await this.integrationRepository.countActiveFacebookPagesByUserId(
        facebookPageProvider.id,
        userId
      );

      // Nếu không còn Facebook Page nào active, có thể thực hiện logic cleanup khác
      if (remainingActivePages === 0) {
        this.logger.log(`User ${userId} không còn Facebook Page nào active`);
      }
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(
        `Lỗi khi xóa trang Facebook: ${error.message}`,
        error.stack,
      );

      throw new AppException(
        INTEGRATION_ERROR_CODES.FACEBOOK_PAGE_DELETE_FAILED,
        'Lỗi khi xóa trang Facebook',
      );
    }
  }

  /**
   * Bật/tắt trạng thái hoạt động của Facebook Page (đảo ngược giá trị hiện tại)
   * @param userId ID của người dùng
   * @param pageId ID của Facebook Page (pageId trong metadata)
   * @returns Thông tin Facebook Page với trạng thái mới
   */
  @Transactional()
  async toggleFacebookPageStatus(
    userId: number,
    pageId: string,
  ): Promise<{ id: string; active: boolean }> {
    try {
      // Lấy IntegrationProvider cho FACEBOOK_PAGE
      const facebookPageProvider = await this.integrationProvider.findByType(ProviderEnum.FACEBOOK_PAGE);
      if (!facebookPageProvider) {
        throw new AppException(
          INTEGRATION_ERROR_CODES.FACEBOOK_PAGE_NOT_FOUND,
          'Không tìm thấy provider FACEBOOK_PAGE',
        );
      }

      // Toggle trạng thái Facebook Page
      const updatedPage = await this.integrationRepository.toggleFacebookPageActiveStatus(
        pageId,
        facebookPageProvider.id,
        userId
      );

      if (!updatedPage) {
        throw new AppException(INTEGRATION_ERROR_CODES.FACEBOOK_PAGE_NOT_FOUND);
      }

      const metadata = updatedPage.metadata as FacebookPageMetadata;
      const newStatus = metadata?.active || false;

      // Tạo response
      const response = {
        id: updatedPage.id,
        active: newStatus,
        message: newStatus
          ? 'Facebook Page đã được bật thành công'
          : 'Facebook Page đã được tắt thành công'
      };

      this.logger.log(
        `Facebook Page ${updatedPage.id} (${updatedPage.integrationName}) đã được ${newStatus ? 'bật' : 'tắt'} bởi user ${userId}`
      );

      return response;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Lỗi khi bật/tắt trạng thái Facebook Page: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        INTEGRATION_ERROR_CODES.FACEBOOK_PAGE_UPDATE_FAILED,
        'Lỗi khi cập nhật trạng thái Facebook Page',
      );
    }
  }
}

import { Injectable } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { PaginatedResult } from '@common/response';
import {
  SeedingGroup,
  SeedingGroupAccount,
  SeedingGroupStatus,
} from '../entities/seeding-group.entity';
import { SeedingGroupQueryDto } from '../dto/seeding-group/seeding-group.dto';

/**
 * Repository cho Seeding Group
 */
@Injectable()
export class SeedingGroupRepository extends Repository<SeedingGroup> {
  constructor(private dataSource: DataSource) {
    super(SeedingGroup, dataSource.createEntityManager());
  }

  /**
   * Tìm seeding groups với phân trang và filter
   */
  async findWithPagination(
    userId: number,
    queryDto: SeedingGroupQueryDto,
  ): Promise<PaginatedResult<SeedingGroup>> {
    const queryBuilder = this.createQueryBuilder('sg')
      .leftJoinAndSelect('sg.accounts', 'accounts')
      .where('sg.userId = :userId', { userId });

    // Apply filters
    this.applyFilters(queryBuilder, queryDto);

    // Apply sorting
    const sortBy = queryDto.sortBy || 'createdAt';
    const sortOrder = (queryDto.sortBy ? 'ASC' : 'DESC') as 'ASC' | 'DESC';
    queryBuilder.orderBy(`sg.${sortBy}`, sortOrder);

    // Apply pagination
    const page = queryDto.page || 1;
    const limit = Math.min(queryDto.limit || 20, 100);
    const offset = (page - 1) * limit;

    queryBuilder.skip(offset).take(limit);

    const [items, total] = await queryBuilder.getManyAndCount();

    return {
      items,
      meta: {
        totalItems: total,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(total / limit),
        currentPage: page,
      },
    };
  }

  /**
   * Tìm seeding group theo ID và userId
   */
  async findByIdAndUserId(
    id: string,
    userId: number,
  ): Promise<SeedingGroup | null> {
    return this.findOne({
      where: { id, userId },
      relations: ['accounts'],
    });
  }

  /**
   * Tìm seeding groups theo OA Account ID
   */
  async findByOaAccountId(
    oaAccountId: string,
    userId: number,
  ): Promise<SeedingGroup[]> {
    return this.find({
      where: { oaAccountId, userId },
      relations: ['accounts'],
    });
  }

  /**
   * Tìm seeding groups theo Group ID
   */
  async findByGroupId(
    groupId: string,
    userId: number,
  ): Promise<SeedingGroup[]> {
    return this.find({
      where: { groupId, userId },
      relations: ['accounts'],
    });
  }

  /**
   * Tìm seeding groups đang active
   */
  async findActiveGroups(userId?: number): Promise<SeedingGroup[]> {
    const queryBuilder = this.createQueryBuilder('sg')
      .leftJoinAndSelect('sg.accounts', 'accounts')
      .where('sg.status = :status', { status: SeedingGroupStatus.ACTIVE });

    if (userId) {
      queryBuilder.andWhere('sg.userId = :userId', { userId });
    }

    return queryBuilder.getMany();
  }

  /**
   * Cập nhật thống kê cho seeding group
   */
  async updateStats(
    id: string,
    stats: {
      totalAccounts?: number;
      activeAccounts?: number;
      totalMessagesSent?: number;
      lastActivityAt?: number;
    },
  ): Promise<void> {
    const updateData: any = { updatedAt: Date.now() };

    if (stats.totalAccounts !== undefined) {
      updateData.totalAccounts = stats.totalAccounts;
    }
    if (stats.activeAccounts !== undefined) {
      updateData.activeAccounts = stats.activeAccounts;
    }
    if (stats.totalMessagesSent !== undefined) {
      updateData.totalMessagesSent = stats.totalMessagesSent;
    }
    if (stats.lastActivityAt !== undefined) {
      updateData.lastActivityAt = stats.lastActivityAt;
    }

    await this.update(id, updateData);
  }

  /**
   * Bulk delete seeding groups
   */
  async bulkDelete(ids: string[], userId: number): Promise<void> {
    await this.delete({
      id: { $in: ids } as any,
      userId,
    });
  }

  /**
   * Apply filters to query builder
   */
  private applyFilters(
    queryBuilder: SelectQueryBuilder<SeedingGroup>,
    queryDto: SeedingGroupQueryDto,
  ): void {
    if (queryDto.search) {
      queryBuilder.andWhere(
        '(sg.name ILIKE :search OR sg.description ILIKE :search)',
        { search: `%${queryDto.search}%` },
      );
    }

    if (queryDto.status) {
      queryBuilder.andWhere('sg.status = :status', { status: queryDto.status });
    }

    if (queryDto.oaAccountId) {
      queryBuilder.andWhere('sg.oaAccountId = :oaAccountId', {
        oaAccountId: queryDto.oaAccountId,
      });
    }

    if (queryDto.groupId) {
      queryBuilder.andWhere('sg.groupId = :groupId', {
        groupId: queryDto.groupId,
      });
    }
  }
}

/**
 * Repository cho Seeding Group Account
 */
@Injectable()
export class SeedingGroupAccountRepository extends Repository<SeedingGroupAccount> {
  constructor(private dataSource: DataSource) {
    super(SeedingGroupAccount, dataSource.createEntityManager());
  }

  /**
   * Tìm accounts theo seeding group ID
   */
  async findBySeedingGroupId(
    seedingGroupId: string,
  ): Promise<SeedingGroupAccount[]> {
    return this.find({
      where: { seedingGroupId },
      order: { createdAt: 'ASC' },
    });
  }

  /**
   * Tìm account theo personal account ID
   */
  async findByPersonalAccountId(
    personalAccountId: string,
  ): Promise<SeedingGroupAccount[]> {
    return this.find({
      where: { personalAccountId },
      relations: ['seedingGroup'],
    });
  }

  /**
   * Cập nhật thống kê tin nhắn cho account
   */
  async updateMessageStats(
    id: string,
    stats: {
      messagesSentToday?: number;
      totalMessagesSent?: number;
      lastMessageAt?: number;
      lastActiveAt?: number;
    },
  ): Promise<void> {
    const updateData: any = { updatedAt: Date.now() };

    if (stats.messagesSentToday !== undefined) {
      updateData.messagesSentToday = stats.messagesSentToday;
    }
    if (stats.totalMessagesSent !== undefined) {
      updateData.totalMessagesSent = stats.totalMessagesSent;
    }
    if (stats.lastMessageAt !== undefined) {
      updateData.lastMessageAt = stats.lastMessageAt;
    }
    if (stats.lastActiveAt !== undefined) {
      updateData.lastActiveAt = stats.lastActiveAt;
    }

    await this.update(id, updateData);
  }

  /**
   * Cập nhật error cho account
   */
  async updateError(id: string, error: string): Promise<void> {
    await this.update(id, {
      errorCount: () => 'error_count + 1',
      lastError: error,
      updatedAt: Date.now(),
    });
  }

  /**
   * Reset daily message count cho tất cả accounts
   */
  async resetDailyMessageCount(): Promise<void> {
    await this.update(
      {},
      {
        messagesSentToday: 0,
        updatedAt: Date.now(),
      },
    );
  }

  /**
   * Bulk delete accounts theo seeding group ID
   */
  async bulkDeleteBySeedingGroupId(seedingGroupId: string): Promise<void> {
    await this.delete({ seedingGroupId });
  }
}

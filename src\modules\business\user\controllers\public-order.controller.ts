import {
  Controller,
  Post,
  Body,
  HttpCode,
  HttpStatus,
  UseGuards,
  Logger,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiSecurity,
  ApiBody,
} from '@nestjs/swagger';
import { ApiKeyGuard } from '@/common/guards/api-key.guard';
import { ApiResponseDto } from '@/common/response';
import { UserOrderService } from '../services/order/user-order.service';
import { OrderQRPaymentService } from '../services/order/order-qr-payment.service';
import { CreatePublicOrderDto } from '../dto/create-draft-order.dto';
import { UserOrderResponseDto } from '../dto/user-order-response.dto';
import { SWAGGER_API_TAGS } from '@/common/swagger';
import { PaymentMethodEnum } from '../../enums';

/**
 * Controller public cho API tạo đơn hàng (sử dụng API Key)
 */
@ApiTags(SWAGGER_API_TAGS.PUBLIC_ORDERS)
@Controller('public/orders')
@UseGuards(ApiKeyGuard)
@ApiSecurity('api-key')
export class PublicOrderController {
  private readonly logger = new Logger(PublicOrderController.name);

  constructor(
    private readonly userOrderService: UserOrderService,
    private readonly orderQRPaymentService: OrderQRPaymentService,
  ) {}

  /**
   * Tạo đơn hàng public với khả năng set status (Public API với API Key)
   * @param createPublicOrderDto DTO chứa thông tin đơn hàng và status
   * @returns Thông tin đơn hàng đã được tạo
   */
  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'Tạo đơn hàng (Public API)',
    description:
      'API public để tạo đơn hàng với khả năng set trạng thái đơn hàng và trạng thái vận chuyển. ' +
      'Yêu cầu API Key để xác thực. Sử dụng logic tương tự như API /v1/user/orders/draft ' +
      'nhưng cho phép set orderStatus và shippingStatus tùy chỉnh.',
  })
  @ApiBody({
    description: 'Thông tin đơn hàng cần tạo',
    schema: {
      type: 'object',
      required: [
        'userId',
        'userShopAddressId',
        'customerInfo',
        'products',
        'billInfo',
      ],
      properties: {
        userId: {
          type: 'number',
          description: 'ID người dùng sở hữu đơn hàng',
          example: 123,
          minimum: 1,
        },
        orderStatus: {
          type: 'string',
          description: 'Trạng thái đơn hàng',
          enum: [
            'draft',
            'pending',
            'confirmed',
            'processing',
            'completed',
            'cancelled',
            'returned',
          ],
          example: 'pending',
          default: 'pending',
        },
        shippingStatus: {
          type: 'string',
          description: 'Trạng thái vận chuyển',
          enum: [
            'pending',
            'preparing',
            'shipped',
            'delivered',
            'cancelled',
            'returned',
          ],
          example: 'pending',
          default: 'pending',
        },
        agentId: {
          type: 'string',
          description:
            'ID của agent (để lấy payment gateway nếu không truyền paymentGatewayId)',
          example: 'a5541891-75e8-4d55-9185-586813d4bada',
          format: 'uuid',
        },
        userShopAddressId: {
          type: 'number',
          description: 'ID địa chỉ shop của người dùng',
          example: 1,
          minimum: 1,
        },
        customerInfo: {
          type: 'object',
          description: 'Thông tin khách hàng',
          required: ['customerId'],
          properties: {
            customerId: {
              type: 'string',
              description: 'ID khách hàng (UUID)',
              example: 'a5541891-75e8-4d55-9185-586813d4bada',
              format: 'uuid',
            },
          },
        },
        products: {
          type: 'array',
          description: 'Danh sách sản phẩm trong đơn hàng',
          minItems: 1,
          items: {
            type: 'object',
            required: ['productId', 'quantity', 'unitPrice'],
            properties: {
              productId: {
                type: 'string',
                description: 'ID sản phẩm',
                example: 'product-uuid-1',
                format: 'uuid',
              },
              quantity: {
                type: 'number',
                description: 'Số lượng sản phẩm',
                example: 2,
                minimum: 1,
              },
              unitPrice: {
                type: 'number',
                description: 'Giá đơn vị (VND)',
                example: 50000,
                minimum: 0,
              },
              variantId: {
                type: 'string',
                description: 'ID biến thể sản phẩm (nếu có)',
                example: 'variant-uuid-1',
                format: 'uuid',
              },
              customFields: {
                type: 'object',
                description: 'Các trường tùy chỉnh',
                example: {},
              },
            },
          },
        },
        billInfo: {
          type: 'object',
          description: 'Thông tin thanh toán',
          required: ['paymentMethod'],
          properties: {
            subtotal: {
              type: 'number',
              description: 'Tổng tiền hàng (subtotal)',
              example: 100000,
              minimum: 0,
            },
            tax: {
              type: 'number',
              description: 'Thuế (nếu có)',
              example: 10000,
              minimum: 0,
            },
            discount: {
              type: 'number',
              description: 'Giảm giá (nếu có)',
              example: 5000,
              minimum: 0,
            },
            total: {
              type: 'number',
              description:
                'Tổng tiền đơn hàng (sẽ được tính tự động nếu không cung cấp)',
              example: 125000,
              minimum: 0,
            },
            selectedCarrier: {
              type: 'string',
              description: 'Nhà vận chuyển được chọn',
              example: 'GHN',
              enum: ['GHN', 'GHTK', 'AHAMOVE'],
            },
            shippingServiceType: {
              type: 'string',
              description: 'Loại dịch vụ vận chuyển',
              example: 'standard',
              enum: ['standard', 'express'],
            },
            paymentMethod: {
              type: 'string',
              description: 'Phương thức thanh toán',
              example: 'CASH',
              enum: ['CASH', 'BANK_TRANSFER', 'QR_CODE', 'CREDIT_CARD'],
            },
            paymentStatus: {
              type: 'string',
              description:
                'Trạng thái thanh toán (sẽ được set mặc định là PENDING)',
              example: 'PENDING',
              enum: [
                'PENDING',
                'PAID',
                'FAILED',
                'REFUNDED',
                'PARTIAL_PAID',
                'CANCELLED',
                'PROCESSING',
                'COMPLETED',
                'PARTIALLY_REFUNDED',
              ],
            },
            userShippingFee: {
              type: 'number',
              description:
                'Phí vận chuyển do người dùng cung cấp (khi ENABLE_MOCK_ADDRESSES=false)',
              example: 25000,
              minimum: 0,
            },
            paymentInfo: {
              type: 'object',
              description: 'Thông tin thanh toán bổ sung',
              example: {
                note: 'Thanh toán tiền mặt',
                currency: 'VND',
              },
            },
            paymentGatewayId: {
              type: 'string',
              description: 'ID payment gateway (cho QR_CODE)',
              example: 'gateway-uuid-1',
              format: 'uuid',
            },
          },
        },
        logisticInfo: {
          type: 'object',
          description: 'Thông tin vận chuyển (tùy chọn)',
          properties: {
            carrier: {
              type: 'string',
              description: 'Nhà vận chuyển',
              example: 'GHN',
            },
            shippingService: {
              type: 'string',
              description: 'Dịch vụ vận chuyển',
              example: 'standard',
            },
            deliveryAddress: {
              type: 'object',
              description: 'Địa chỉ giao hàng',
              properties: {
                addressId: {
                  type: 'number',
                  description: 'ID địa chỉ',
                  example: 1,
                },
              },
            },
          },
        },
        hasShipping: {
          type: 'boolean',
          description: 'Đơn hàng có yêu cầu vận chuyển hay không',
          example: true,
          default: true,
        },
        note: {
          type: 'string',
          description: 'Ghi chú đơn hàng',
          example: 'Giao hàng trong giờ hành chính',
        },
        tags: {
          type: 'array',
          description: 'Tags đơn hàng',
          items: {
            type: 'string',
          },
          example: ['urgent', 'vip-customer'],
        },
        source: {
          type: 'string',
          description: 'Nguồn đơn hàng',
          example: 'website',
          default: 'website',
        },
      },
    },
    examples: {
      'Đơn hàng cơ bản với thanh toán tiền mặt': {
        summary: 'Đơn hàng CASH cơ bản',
        description: 'Ví dụ tạo đơn hàng với thanh toán tiền mặt',
        value: {
          userId: 123,
          orderStatus: 'pending',
          shippingStatus: 'pending',
          userShopAddressId: 1,
          customerInfo: {
            customerId: 'a5541891-75e8-4d55-9185-586813d4bada',
          },
          products: [
            {
              productId: 'product-uuid-1',
              quantity: 2,
              unitPrice: 50000,
              variantId: 'variant-uuid-1',
              customFields: {},
            },
          ],
          billInfo: {
            subtotal: 100000,
            tax: 0,
            discount: 0,
            total: 125000,
            selectedCarrier: 'GHN',
            shippingServiceType: 'standard',
            paymentMethod: 'CASH',
            paymentStatus: 'PENDING',
            userShippingFee: 25000,
            paymentInfo: {
              note: 'Thanh toán tiền mặt',
              currency: 'VND',
            },
          },
          hasShipping: true,
          note: 'Giao hàng trong giờ hành chính',
          tags: ['urgent'],
          source: 'website',
        },
      },
      'Đơn hàng với QR Code': {
        summary: 'Đơn hàng QR_CODE',
        description: 'Ví dụ tạo đơn hàng với thanh toán QR Code',
        value: {
          userId: 123,
          orderStatus: 'pending',
          shippingStatus: 'pending',
          agentId: 'agent-uuid-1',
          userShopAddressId: 1,
          customerInfo: {
            customerId: 'customer-uuid-1',
          },
          products: [
            {
              productId: 'product-uuid-1',
              quantity: 1,
              unitPrice: 100000,
            },
          ],
          billInfo: {
            subtotal: 100000,
            tax: 10000,
            total: 110000,
            selectedCarrier: 'GHN',
            shippingServiceType: 'express',
            paymentMethod: 'QR_CODE',
            paymentStatus: 'PENDING',
            paymentGatewayId: 'gateway-uuid-1',
            paymentInfo: {
              note: 'Thanh toán QR Code',
              currency: 'VND',
            },
          },
          hasShipping: true,
          source: 'mobile_app',
        },
      },
      'Đơn hàng không vận chuyển': {
        summary: 'Đơn hàng digital/service',
        description: 'Ví dụ tạo đơn hàng không cần vận chuyển',
        value: {
          userId: 123,
          orderStatus: 'confirmed',
          userShopAddressId: 1,
          customerInfo: {
            customerId: 'customer-uuid-1',
          },
          products: [
            {
              productId: 'digital-product-uuid',
              quantity: 1,
              unitPrice: 200000,
            },
          ],
          billInfo: {
            subtotal: 200000,
            total: 200000,
            paymentMethod: 'BANK_TRANSFER',
            paymentStatus: 'PENDING',
            paymentInfo: {
              note: 'Thanh toán chuyển khoản',
              currency: 'VND',
            },
          },
          hasShipping: false,
          source: 'api',
        },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Tạo đơn hàng thành công',
    schema: {
      type: 'object',
      properties: {
        code: { type: 'number', example: 201 },
        message: { type: 'string', example: 'Tạo đơn hàng thành công' },
        result: {
          type: 'object',
          properties: {
            id: {
              type: 'string',
              example: 'a5541891-75e8-4d55-9185-586813d4bada',
            },
            userId: { type: 'number', example: 123 },
            orderStatus: { type: 'string', example: 'pending' },
            shippingStatus: { type: 'string', example: 'pending' },
            hasShipping: { type: 'boolean', example: true },
            source: { type: 'string', example: 'website' },
            productInfo: {
              type: 'object',
              properties: {
                products: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      productId: { type: 'string', example: 'product-uuid-1' },
                      quantity: { type: 'number', example: 2 },
                      unitPrice: { type: 'number', example: 50000 },
                      variantId: { type: 'string', example: 'variant-uuid-1' },
                    },
                  },
                },
              },
            },
            billInfo: {
              type: 'object',
              properties: {
                total: { type: 'number', example: 125000 },
                shippingFee: { type: 'number', example: 25000 },
                paymentMethod: { type: 'string', example: 'CASH' },
                paymentStatus: { type: 'string', example: 'PENDING' },
                selectedCarrier: { type: 'string', example: 'GHN' },
              },
            },
            createdAt: { type: 'number', example: 1704067200000 },
            updatedAt: { type: 'number', example: 1704067200000 },
          },
        },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Dữ liệu đầu vào không hợp lệ',
    schema: {
      type: 'object',
      properties: {
        code: { type: 'number', example: 400 },
        message: { type: 'string', example: 'Dữ liệu đầu vào không hợp lệ' },
        errors: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              field: { type: 'string', example: 'userId' },
              message: {
                type: 'string',
                example: 'userId must be a positive number',
              },
            },
          },
        },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'API Key không hợp lệ hoặc thiếu',
    schema: {
      type: 'object',
      properties: {
        code: { type: 'number', example: 401 },
        message: { type: 'string', example: 'API Key không hợp lệ hoặc thiếu' },
      },
    },
  })
  async createOrder(
    @Body() createPublicOrderDto: CreatePublicOrderDto,
  ): Promise<ApiResponseDto<UserOrderResponseDto>> {
    try {
      const {
        userId,
        orderStatus,
        shippingStatus,
        agentId,
        ...createDraftOrderDto
      } = createPublicOrderDto;

      this.logger.log(
        `Tạo đơn hàng public cho userId=${userId}, orderStatus=${orderStatus}, shippingStatus=${shippingStatus}, agentId=${agentId}`,
      );

      // Tạo đơn hàng sử dụng service hiện có
      const order = await this.userOrderService.createDraftOrderWithStatus(
        userId,
        createDraftOrderDto,
        orderStatus,
        shippingStatus,
      );

      // Tạo QR code nếu phương thức thanh toán là QR_CODE
      if (
        createDraftOrderDto.billInfo.paymentMethod === PaymentMethodEnum.QR_CODE
      ) {
        try {
          const qrCodeUrl =
            await this.orderQRPaymentService.generateOrderQRCode(
              order.id,
              order.billInfo?.total || 0,
              createDraftOrderDto.billInfo.paymentMethod,
              createDraftOrderDto.billInfo.paymentGatewayId,
              agentId,
              userId,
            );

          if (qrCodeUrl) {
            // Thêm QR code URL vào paymentInfo
            order.billInfo = {
              ...order.billInfo,
              paymentInfo: {
                ...order.billInfo?.paymentInfo,
                qrCodeUrl,
              },
            } as any;

            this.logger.log(
              `Generated QR code for order ${order.id}: ${qrCodeUrl}`,
            );
          }
        } catch (qrError) {
          this.logger.warn(
            `Failed to generate QR code for order ${order.id}: ${qrError.message}`,
          );
          // Không throw error, chỉ log warning vì đơn hàng đã được tạo thành công
        }
      }

      return ApiResponseDto.created(order, 'Tạo đơn hàng thành công');
    } catch (error) {
      this.logger.error(
        `Lỗi khi tạo đơn hàng public: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }
}

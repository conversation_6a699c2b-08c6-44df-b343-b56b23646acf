import { 
  Controller, 
  Get, 
  Post, 
  Put, 
  Delete, 
  Body, 
  Param, 
  Query, 
  UseGuards,
  Logger 
} from '@nestjs/common';
import { 
  ApiTags, 
  ApiOperation, 
  ApiResponse, 
  ApiParam, 
  ApiQuery,
  ApiBearerAuth 
} from '@nestjs/swagger';
import { GenericWidgetService, GenericQueueService } from '../services';
import { 
  CreateGenericWidgetDto, 
  UpdateGenericWidgetDto, 
  GenericWidgetResponseDto 
} from '../dto/generic-widget.dto';
import { ApiResponseDto } from '@/common/response';
import { GenericPageJobPriority } from '../constants/generic-queue.constants';

/**
 * Controller cho Generic Widget Management
 * Quản lý widgets trong sessions với queue-based operations
 */
@ApiTags('Generic Widgets')
@Controller('generic/sessions/:sessionId/widgets')
export class GenericWidgetController {
  private readonly logger = new Logger(GenericWidgetController.name);

  constructor(
    private readonly genericWidgetService: GenericWidgetService,
    private readonly genericQueueService: GenericQueueService,
  ) {}

  /**
   * Thêm widget vào session
   */
  @Post()
  @ApiOperation({ 
    summary: 'Thêm widget vào session',
    description: 'Thêm widget mới vào session và broadcast real-time update'
  })
  @ApiParam({
    name: 'sessionId',
    description: 'Session ID',
    example: 'session-123-abc'
  })
  @ApiResponse({
    status: 201,
    description: 'Widget đã được thêm thành công',
    type: ApiResponseDto<GenericWidgetResponseDto>
  })
  @ApiResponse({
    status: 400,
    description: 'Dữ liệu đầu vào không hợp lệ'
  })
  @ApiResponse({
    status: 409,
    description: 'Widget ID đã tồn tại trong session'
  })
  async addWidget(
    @Param('sessionId') sessionId: string,
    @Body() createDto: CreateGenericWidgetDto,
    @Query('useQueue') useQueue?: boolean
  ): Promise<ApiResponseDto<GenericWidgetResponseDto | null>> {
    try {
      if (useQueue) {
        // Sử dụng queue để xử lý async
        await this.genericQueueService.addWidgetJob(
          sessionId,
          {
            widgetId: createDto.widgetId,
            widgetType: createDto.widgetType,
            widgetData: createDto.widgetData,
            position: createDto.position,
            displayOrder: createDto.displayOrder,
            isVisible: createDto.isVisible,
            metadata: createDto.metadata,
          },
          {
            priority: GenericPageJobPriority.HIGH,
            triggeredBy: 'api',
          }
        );

        return ApiResponseDto.success(
          null,
          'Widget job đã được thêm vào queue'
        );
      } else {
        // Xử lý trực tiếp
        const widget = await this.genericWidgetService.addWidgetToSession(sessionId, createDto);
        
        this.logger.log(`Added widget ${createDto.widgetId} to session ${sessionId}`);
        
        return ApiResponseDto.created(
          widget,
          'Widget đã được thêm thành công'
        );
      }
    } catch (error) {
      this.logger.error(`Error adding widget: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Lấy tất cả widgets của session
   */
  @Get()
  @ApiOperation({ 
    summary: 'Lấy danh sách widgets',
    description: 'Lấy tất cả widgets trong session'
  })
  @ApiParam({
    name: 'sessionId',
    description: 'Session ID',
    example: 'session-123-abc'
  })
  @ApiQuery({
    name: 'includeHidden',
    description: 'Có bao gồm widgets ẩn không',
    required: false,
    type: Boolean
  })
  @ApiResponse({
    status: 200,
    description: 'Danh sách widgets',
    type: ApiResponseDto<GenericWidgetResponseDto[]>
  })
  async getWidgets(
    @Param('sessionId') sessionId: string,
    @Query('includeHidden') includeHidden?: boolean
  ): Promise<ApiResponseDto<GenericWidgetResponseDto[]>> {
    try {
      const widgets = await this.genericWidgetService.getWidgetsBySessionId(
        sessionId, 
        includeHidden || false
      );
      
      return ApiResponseDto.success(
        widgets,
        'Lấy danh sách widgets thành công'
      );
    } catch (error) {
      this.logger.error(`Error getting widgets: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Lấy widget theo ID
   */
  @Get(':widgetId')
  @ApiOperation({ 
    summary: 'Lấy widget theo ID',
    description: 'Lấy thông tin chi tiết của widget'
  })
  @ApiParam({
    name: 'sessionId',
    description: 'Session ID',
    example: 'session-123-abc'
  })
  @ApiParam({
    name: 'widgetId',
    description: 'Widget ID',
    example: 'widget-123'
  })
  @ApiResponse({
    status: 200,
    description: 'Thông tin widget',
    type: ApiResponseDto<GenericWidgetResponseDto>
  })
  @ApiResponse({
    status: 404,
    description: 'Không tìm thấy widget'
  })
  async getWidget(
    @Param('sessionId') sessionId: string,
    @Param('widgetId') widgetId: string
  ): Promise<ApiResponseDto<GenericWidgetResponseDto>> {
    try {
      const widget = await this.genericWidgetService.getWidgetBySessionAndWidgetId(sessionId, widgetId);
      
      return ApiResponseDto.success(
        widget,
        'Lấy thông tin widget thành công'
      );
    } catch (error) {
      this.logger.error(`Error getting widget: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Cập nhật widget
   */
  @Put(':widgetId')
  @ApiOperation({ 
    summary: 'Cập nhật widget',
    description: 'Cập nhật thông tin và cấu hình của widget'
  })
  @ApiParam({
    name: 'sessionId',
    description: 'Session ID',
    example: 'session-123-abc'
  })
  @ApiParam({
    name: 'widgetId',
    description: 'Widget ID',
    example: 'widget-123'
  })
  @ApiResponse({
    status: 200,
    description: 'Widget đã được cập nhật thành công',
    type: ApiResponseDto<GenericWidgetResponseDto>
  })
  @ApiResponse({
    status: 404,
    description: 'Không tìm thấy widget'
  })
  async updateWidget(
    @Param('sessionId') sessionId: string,
    @Param('widgetId') widgetId: string,
    @Body() updateDto: UpdateGenericWidgetDto,
    @Query('useQueue') useQueue?: boolean
  ): Promise<ApiResponseDto<GenericWidgetResponseDto | null>> {
    try {
      if (useQueue) {
        // Sử dụng queue để xử lý async
        await this.genericQueueService.updateWidgetJob(
          sessionId,
          widgetId,
          updateDto,
          {
            priority: GenericPageJobPriority.HIGH,
            triggeredBy: 'api',
          }
        );

        return ApiResponseDto.success(
          null,
          'Widget update job đã được thêm vào queue'
        );
      } else {
        // Xử lý trực tiếp
        const widget = await this.genericWidgetService.updateWidgetInSession(sessionId, widgetId, updateDto);
        
        this.logger.log(`Updated widget ${widgetId} in session ${sessionId}`);
        
        return ApiResponseDto.updated(
          widget,
          'Widget đã được cập nhật thành công'
        );
      }
    } catch (error) {
      this.logger.error(`Error updating widget: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Xóa widget
   */
  @Delete(':widgetId')
  @ApiOperation({ 
    summary: 'Xóa widget',
    description: 'Xóa widget khỏi session và broadcast real-time update'
  })
  @ApiParam({
    name: 'sessionId',
    description: 'Session ID',
    example: 'session-123-abc'
  })
  @ApiParam({
    name: 'widgetId',
    description: 'Widget ID',
    example: 'widget-123'
  })
  @ApiResponse({
    status: 200,
    description: 'Widget đã được xóa thành công'
  })
  @ApiResponse({
    status: 404,
    description: 'Không tìm thấy widget'
  })
  async removeWidget(
    @Param('sessionId') sessionId: string,
    @Param('widgetId') widgetId: string,
    @Query('useQueue') useQueue?: boolean
  ): Promise<ApiResponseDto<{ deletedCount: number }>> {
    try {
      if (useQueue) {
        // Sử dụng queue để xử lý async
        await this.genericQueueService.removeWidgetJob(
          sessionId,
          widgetId,
          {
            priority: GenericPageJobPriority.HIGH,
            triggeredBy: 'api',
          }
        );

        return ApiResponseDto.success(
          { deletedCount: 0 },
          'Widget remove job đã được thêm vào queue'
        );
      } else {
        // Xử lý trực tiếp
        const deletedCount = await this.genericWidgetService.removeWidgetFromSession(sessionId, widgetId);
        
        this.logger.log(`Removed widget ${widgetId} from session ${sessionId}`);
        
        return ApiResponseDto.deleted(
          { deletedCount },
          'Widget đã được xóa thành công'
        );
      }
    } catch (error) {
      this.logger.error(`Error removing widget: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Toggle visibility của widget
   */
  @Put(':widgetId/visibility')
  @ApiOperation({ 
    summary: 'Toggle widget visibility',
    description: 'Ẩn/hiện widget'
  })
  @ApiParam({
    name: 'sessionId',
    description: 'Session ID',
    example: 'session-123-abc'
  })
  @ApiParam({
    name: 'widgetId',
    description: 'Widget ID',
    example: 'widget-123'
  })
  @ApiResponse({
    status: 200,
    description: 'Widget visibility đã được cập nhật',
    type: ApiResponseDto<GenericWidgetResponseDto>
  })
  async toggleWidgetVisibility(
    @Param('sessionId') sessionId: string,
    @Param('widgetId') widgetId: string,
    @Body() body: { isVisible: boolean }
  ): Promise<ApiResponseDto<GenericWidgetResponseDto>> {
    try {
      const widget = await this.genericWidgetService.toggleWidgetVisibility(
        sessionId, 
        widgetId, 
        body.isVisible
      );
      
      this.logger.log(`Toggled widget ${widgetId} visibility to ${body.isVisible} in session ${sessionId}`);
      
      return ApiResponseDto.updated(
        widget,
        'Widget visibility đã được cập nhật thành công'
      );
    } catch (error) {
      this.logger.error(`Error toggling widget visibility: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Cập nhật display order của widgets
   */
  @Put('display-orders')
  @ApiOperation({ 
    summary: 'Cập nhật display order',
    description: 'Cập nhật thứ tự hiển thị của widgets'
  })
  @ApiParam({
    name: 'sessionId',
    description: 'Session ID',
    example: 'session-123-abc'
  })
  @ApiResponse({
    status: 200,
    description: 'Display orders đã được cập nhật'
  })
  async updateDisplayOrders(
    @Param('sessionId') sessionId: string,
    @Body() body: { widgetOrders: Array<{ widgetId: string; displayOrder: number }> }
  ): Promise<ApiResponseDto<{ updatedCount: number }>> {
    try {
      const updatedCount = await this.genericWidgetService.updateWidgetDisplayOrders(
        sessionId, 
        body.widgetOrders
      );
      
      this.logger.log(`Updated display orders for ${updatedCount} widgets in session ${sessionId}`);
      
      return ApiResponseDto.updated(
        { updatedCount },
        'Display orders đã được cập nhật thành công'
      );
    } catch (error) {
      this.logger.error(`Error updating display orders: ${error.message}`, error.stack);
      throw error;
    }
  }
}

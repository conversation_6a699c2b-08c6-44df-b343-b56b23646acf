
import { Body, Controller, Get, HttpException, HttpStatus, <PERSON><PERSON>, Param, Post, Res } from '@nestjs/common';
import { Response } from 'express';
import { FacebookDeletionWebhookService } from '../services/facebook-deletion-webhook.service';

@Controller('facebook')
export class FacebookController {
    private readonly logger = new Logger(FacebookController.name);

    constructor(private readonly facebookDeletionWebhookService: FacebookDeletionWebhookService) { }

    @Post('data-deletion')
    async handleDataDeletionWebhook(@Body('signed_request') signedRequest: string, @Res() res: Response) {
        this.logger.log('Nhận được yêu cầu POST tới /facebook/data-deletion');

        if (!signedRequest) {
            this.logger.error('Yêu cầu không chứa signed_request.');
            return res.status(HttpStatus.BAD_REQUEST).send('<PERSON><PERSON>u cầu không hợp lệ.');
        }

        try {
            const responsePayload = await this.facebookDeletionWebhookService.handleDataDeletion(signedRequest);
            return res.status(HttpStatus.OK).json(responsePayload);
        } catch (error) {
            this.logger.error(`Lỗi khi xử lý data deletion: ${error.message}`, error.stack);
            // Ném HttpException để NestJS có thể xử lý lỗi một cách nhất quán
            throw new HttpException('Xác thực yêu cầu thất bại.', HttpStatus.BAD_REQUEST);
        }
    }

    @Get('deletion-status/:code')
    getDeletionStatus(@Param('code') code: string) {
        // Logic để kiểm tra mã và trả về trạng thái
        // Đây là nơi bạn sẽ truy vấn DB để xem trạng thái của yêu cầu xóa
        this.logger.log(`Kiểm tra trạng thái cho mã: ${code}`);
        return {
            message: 'Yêu cầu xóa dữ liệu đã được xử lý thành công.',
            confirmation_code: code,
            status: 'complete',
        };
    }
}
import { QueryDto } from '@common/dto';
import { AppException } from '@common/exceptions';
import { PaginatedResult } from '@common/response/api-response-dto';
import { Injectable, Logger } from '@nestjs/common';
import { Transactional } from 'typeorm-transactional';
import { ApiKeyLocationEnum, HttpMethodEnum, TokenSourceEnum, ToolStatusEnum } from '../../constants';
import { ApiKey, OAuth, UserTool, UserToolVersion, UserToolsCustom } from '../../entities';
import { CUSTOM_TOOLS_ERROR_CODES, TOOLS_ERROR_CODES } from '../../exceptions';
import {
  ApiKeyRepository,
  OAuthRepository,
  UserToolRepository,
  UserToolVersionRepository,
  UserToolsCustomRepository
} from '../../repositories';
import { ToolParameterValidatorService } from '../../services/tool-parameter-validator.service';
import { ApiKeyAuthDto, AuthTypeEnum, OAuthAuthDto } from '../dto/auth-config.dto';
import { IntegrateFromOpenApiDto } from '../dto/integrate-from-openapi.dto';
import { RouteMap, RouteType } from '../dto/integrate-openapi.dto';
import { ToolDetailResponseDto, ToolResponseDto } from '../dto/tool-response.dto';
import { UpdateBaseUrlDto } from '../dto/update-base-url.dto';
import { UpdateToolAuthDto } from '../dto/update-tool-auth.dto';
import { EncryptionService } from '@/shared/services/encryption';
import { ConfigService, ConfigType, EncryptionConfig } from '@/config';

/**
 * Interface cho route HTTP
 */
interface HTTPRoute {
  method: string;              // Phương thức HTTP (GET, POST, v.v.)
  path: string;                // Đường dẫn của route (ví dụ: "/users")
  operation_id?: string;       // ID của operation (tùy chọn)
  description?: string;        // Mô tả của route (tùy chọn)
  summary?: string;            // Tóm tắt của route (tùy chọn)
  parameters?: any[];          // Danh sách tham số (tùy chọn)
  security?: any[];            // Thông tin xác thực (tùy chọn)
  servers?: {                  // Danh sách server (tùy chọn)
    url: string;               // URL của server
    description?: string;      // Mô tả của server (tùy chọn)
  }[];
  responses?: Record<string, any>; // Thông tin responses (tùy chọn)
  openapiSpec?: Record<string, any>; // Đặc tả OpenAPI đầy đủ (để giải quyết tham chiếu)
}

/**
 * Interface cho thông tin xác thực từ OpenAPI
 */
interface SecurityScheme {
  type: string;                // Loại xác thực (apiKey, oauth2, http, openIdConnect)
  name?: string;               // Tên tham số (cho apiKey)
  in?: string;                 // Vị trí (cho apiKey: header, query, cookie)
  scheme?: string;             // Scheme (cho http: bearer, basic)
  bearerFormat?: string;       // Định dạng bearer (cho http với scheme là bearer)
  flows?: any;                 // Flows (cho oauth2)
  openIdConnectUrl?: string;   // URL OpenID Connect (cho openIdConnect)
  description?: string;        // Mô tả
}

/**
 * Service quản lý tích hợp công cụ từ OpenAPI
 */
@Injectable()
export class IntegrationToolsService {
  private readonly logger = new Logger(IntegrationToolsService.name);
  private secretKeyPrivate: string;

  constructor(
    private readonly userToolsCustomRepository: UserToolsCustomRepository,
    private readonly apiKeyRepository: ApiKeyRepository,
    private readonly oAuthRepository: OAuthRepository,
    private readonly toolParameterValidator: ToolParameterValidatorService,
    private readonly encryptionService: EncryptionService,
    private readonly configService: ConfigService,
  ) {
    this.secretKeyPrivate = this.configService.getConfig<EncryptionConfig>(ConfigType.Encryption).secretKey;
  }

  /**
   * Tích hợp công cụ từ đặc tả OpenAPI (phương thức gộp xử lý cả có auth và không auth)
   * @param userId ID của người dùng
   * @param integrateDto Dữ liệu tích hợp (có thể có hoặc không có auth)
   * @returns Kết quả tích hợp
   */
  @Transactional()
  async integrateFromOpenApi(
    userId: number,
    integrateDto: IntegrateFromOpenApiDto
  ): Promise<{
    toolsCreated: number,
    resourcesCreated: number,
    failedEndpoints: Array<{ path: string, method: string, reason: string }>,
    authConfig?: {
      apiKeyCreated: number,
      oauthCreated: number
    }
  }> {
    try {
      // Code cứng quy tắc ánh xạ: Tất cả các phương thức đều là Tool
      const hardcodedMappings: RouteMap[] = [
        { methods: ["GET", "POST", "PUT", "PATCH", "DELETE", "OPTIONS", "HEAD"], pattern: ".*", route_type: RouteType.TOOL }
      ];

      const httpRoutes = this.parseOpenApiToHttpRoutes(integrateDto.openapiSpec);

      // Xác định loại xác thực
      let keyId: string | null = null;
      let authConfig: ApiKeyAuthDto | OAuthAuthDto | any = null;
      if ('authConfig' in integrateDto && integrateDto.authConfig) {
        authConfig = integrateDto.authConfig;

        const secretKey = this.encryptionService.generateSecretKey();

        if (authConfig.authType === AuthTypeEnum.API_KEY) {

          const apiKey = this.apiKeyRepository.create({
            apiKey: this.encryptionService.encrypt(secretKey, this.secretKeyPrivate, authConfig.apiKey),
            apiKeyLocation: authConfig.apiKeyLocation,
            secretKey: secretKey
          });

          const apiKeySaved = await this.apiKeyRepository.save(apiKey);

          keyId = apiKeySaved.id;
        }

      }

      let toolsCreated = 0;
      let resourcesCreated = 0;
      const apiKeyCreated = 0;
      const oauthCreated = 0;
      const failedEndpoints: Array<{ path: string, method: string, reason: string }> = [];

      // Xử lý từng route
      for (const route of httpRoutes) {
        try {
          // Luôn sử dụng hardcodedMappings (cấu hình ngầm)
          const componentType = this.determineComponentType(route, hardcodedMappings);

          if (componentType === RouteType.TOOL) {
            // Truyền baseUrl từ DTO nếu có
            const toolInfo = this.extractToolInfo(route, integrateDto.baseUrl);
            // Không có thông tin xác thực, tạo tool thông thường
            await this.createToolFromOpenApi(userId, toolInfo, keyId);
            toolsCreated++;
          } else if (componentType === RouteType.RESOURCE) {
            const resourceInfo = this.extractResourceInfo(route);
            await this.createResourceFromOpenApi(userId, resourceInfo);
            resourcesCreated++;
          }
        } catch (error) {
          // Ghi log lỗi nhưng không dừng quá trình
          this.logger.error(`Lỗi khi xử lý endpoint ${route.method} ${route.path}: ${error.message}`, error.stack);

          // Thêm vào danh sách endpoint lỗi
          failedEndpoints.push({
            path: route.path,
            method: route.method,
            reason: error.message
          });

          // Tiếp tục với endpoint tiếp theo
          continue;
        }
      }

      // Trả về kết quả phù hợp với loại đầu vào
      const result: any = {
        toolsCreated,
        resourcesCreated,
        failedEndpoints
      };

      if (authConfig) {
        result.authConfig = {
          apiKeyCreated,
          oauthCreated
        };
      }

      return result;
    } catch (error) {
      this.logger.error(`Lỗi khi tích hợp từ OpenAPI: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(TOOLS_ERROR_CODES.TOOL_CREATION_FAILED);
    }
  }

  /**
   * Xác định loại thành phần từ route
   * @param route Route HTTP
   * @param mappings Quy tắc ánh xạ
   * @returns Loại route (TOOL hoặc RESOURCE)
   */
  private determineComponentType(route: HTTPRoute, mappings: RouteMap[]): RouteType {
    for (const routeMap of mappings) {
      if (routeMap.methods.includes(route.method.toUpperCase())) {
        const pattern = routeMap.pattern;
        if (typeof pattern === 'string') {
          if (new RegExp(pattern).test(route.path)) {
            return routeMap.route_type;
          }
        } else if (pattern instanceof RegExp) {
          if (pattern.test(route.path)) {
            return routeMap.route_type;
          }
        }
      }
    }
    return RouteType.TOOL; // Mặc định là TOOL nếu không khớp
  }

  /**
   * Trích xuất thông tin cho Tool
   * @param route Route HTTP
   * @param customBaseUrl Base URL tùy chỉnh từ DTO (nếu có)
   * @returns Thông tin tool
   */
  private extractToolInfo(route: HTTPRoute, customBaseUrl?: string): Record<string, any> {
    const operationId = route.operation_id ||
      `${route.method.toLowerCase()}_${route.path.replace(/\//g, '_').replace(/^{|}$/g, '')}`;
    const description = route.description ||
      route.summary ||
      `Thực thi ${route.method} ${route.path}`;
    // Đảm bảo parameters là một mảng
    const parameters = Array.isArray(route.parameters) ? route.parameters : [];

    // Ưu tiên sử dụng baseUrl từ DTO nếu có
    // Nếu không, trích xuất từ servers trong đặc tả OpenAPI
    const baseUrl = customBaseUrl || (route.servers && route.servers.length > 0
      ? route.servers[0].url
      : null);

    return {
      name: operationId,
      description: description,
      parameters: parameters,
      requestBody: route.openapiSpec?.requestBody, // Thêm requestBody
      method: route.method,
      path: route.path,
      type: "TOOL",
      baseUrl: baseUrl,
      responses: route.responses,
      openapiSpec: route.openapiSpec
    };
  }

  /**
   * Trích xuất thông tin cho Resource
   * @param route Route HTTP
   * @returns Thông tin resource
   */
  private extractResourceInfo(route: HTTPRoute): Record<string, any> {
    const operationId = route.operation_id ||
      `${route.method.toLowerCase()}_${route.path.replace(/\//g, '_').replace(/^{|}$/g, '')}`;
    const uri = `resource://${operationId}`;
    const description = route.description ||
      route.summary ||
      `Đại diện cho ${route.path}`;

    return {
      uri: uri,
      name: operationId,
      description: description,
      method: route.method,
      path: route.path,
      type: "RESOURCE"
    };
  }

  /**
   * Phân tích đặc tả OpenAPI thành danh sách HTTPRoute
   * @param openapiSpec Đặc tả OpenAPI
   * @returns Danh sách route HTTP
   */
  private parseOpenApiToHttpRoutes(openapiSpec: Record<string, any>): HTTPRoute[] {
    const routes: HTTPRoute[] = [];

    // Kiểm tra xem openapiSpec có hợp lệ không
    if (!openapiSpec || !openapiSpec.paths) {
      this.logger.warn('Đặc tả OpenAPI không hợp lệ: thiếu thuộc tính paths');
      return routes;
    }

    try {
      for (const path in openapiSpec.paths) {
        for (const method in openapiSpec.paths[path]) {
          try {
            // Bỏ qua các thuộc tính không phải phương thức HTTP
            if (!['get', 'post', 'put', 'delete', 'patch', 'options', 'head'].includes(method.toLowerCase())) {
              continue;
            }

            const route = openapiSpec.paths[path][method];

            // Tổng hợp tham số từ nhiều nguồn
            let allParameters: any[] = [];

            // Thêm tham số từ parameters ở cấp path
            if (Array.isArray(openapiSpec.paths[path].parameters)) {
              allParameters = [...allParameters, ...openapiSpec.paths[path].parameters];
            }

            // Thêm tham số từ parameters ở cấp operation
            if (Array.isArray(route.parameters)) {
              allParameters = [...allParameters, ...route.parameters];
            }

            // Thêm tham số từ requestBody nếu có
            if (route.requestBody && route.requestBody.content) {
              const contentTypes = Object.keys(route.requestBody.content);
              if (contentTypes.length > 0) {
                // Ưu tiên application/json nếu có
                const preferredContentType = contentTypes.includes('application/json')
                  ? 'application/json'
                  : contentTypes[0];

                const schema = route.requestBody.content[preferredContentType].schema;
                const examples = route.requestBody.content[preferredContentType].examples || {};
                const example = route.requestBody.content[preferredContentType].example;

                if (schema) {
                  // Nếu schema là tham chiếu
                  if (schema.$ref) {
                    // Xử lý tham chiếu để liên kết đúng tới DTO
                    const refPath = schema.$ref.split('/');
                    const refName = refPath.pop();

                    // Tìm schema từ components
                    const refSchema = this.resolveSchemaRef(openapiSpec, schema.$ref);

                    if (refSchema) {
                      const bodyParam = {
                        name: 'body',
                        in: 'body',
                        description: route.requestBody.description || `Request body (${refName})`,
                        required: route.requestBody.required || false,
                        schema: refSchema
                      };

                      // Thêm examples nếu có
                      if (Object.keys(examples).length > 0) {
                        bodyParam['examples'] = examples;
                      } else if (example) {
                        bodyParam['example'] = example;
                      }

                      allParameters.push(bodyParam);
                    } else {
                      // Nếu không tìm thấy schema, sử dụng tham chiếu gốc
                      allParameters.push({
                        name: 'body',
                        in: 'body',
                        description: route.requestBody.description || `Request body (${refName})`,
                        required: route.requestBody.required || false,
                        schema: schema
                      });
                    }
                  }
                  // Nếu schema là object trực tiếp
                  else if (schema.properties) {
                    // Tạo một tham số body duy nhất với toàn bộ schema
                    const bodyParam = {
                      name: 'body',
                      in: 'body',
                      description: route.requestBody.description || 'Request body',
                      required: route.requestBody.required || false,
                      schema: schema
                    };

                    // Thêm examples nếu có
                    if (Object.keys(examples).length > 0) {
                      bodyParam['examples'] = examples;
                    } else if (example) {
                      bodyParam['example'] = example;
                    }

                    allParameters.push(bodyParam);
                  }
                  // Nếu schema là array
                  else if (schema.type === 'array') {
                    const bodyParam = {
                      name: 'body',
                      in: 'body',
                      description: route.requestBody.description || 'Request body (array)',
                      required: route.requestBody.required || false,
                      schema: schema
                    };

                    // Thêm examples nếu có
                    if (Object.keys(examples).length > 0) {
                      bodyParam['examples'] = examples;
                    } else if (example) {
                      bodyParam['example'] = example;
                    }

                    allParameters.push(bodyParam);
                  }
                  // Các loại schema khác
                  else {
                    const bodyParam = {
                      name: 'body',
                      in: 'body',
                      description: route.requestBody.description || 'Request body',
                      required: route.requestBody.required || false,
                      schema: schema
                    };

                    // Thêm examples nếu có
                    if (Object.keys(examples).length > 0) {
                      bodyParam['examples'] = examples;
                    } else if (example) {
                      bodyParam['example'] = example;
                    }

                    allParameters.push(bodyParam);
                  }
                }
              }
            }

            // Lấy thông tin servers từ route hoặc từ openapiSpec
            const routeServers = route.servers || openapiSpec.servers;

            // Lấy thông tin responses để thêm vào schema
            const responses = route.responses || {};

            // Tạo mô tả chi tiết hơn
            let detailedDescription = '';
            if (route.description) {
              detailedDescription += route.description + '\n\n';
            } else if (route.summary) {
              detailedDescription += route.summary + '\n\n';
            }

            // Thêm thông tin về tham số
            if (allParameters.length > 0) {
              detailedDescription += 'Tham số:\n';
              allParameters.forEach(param => {
                detailedDescription += `- ${param.name} (${param.in}): ${param.description || 'Không có mô tả'}\n`;
              });
              detailedDescription += '\n';
            }

            // Thêm thông tin về responses
            if (Object.keys(responses).length > 0) {
              detailedDescription += 'Responses:\n';
              for (const statusCode in responses) {
                const response = responses[statusCode];
                detailedDescription += `- ${statusCode}: ${response.description || 'Không có mô tả'}\n`;
              }
            }

            routes.push({
              method: method.toUpperCase(),
              path: path,
              operation_id: route.operationId,
              description: detailedDescription.trim() || route.description,
              summary: route.summary,
              parameters: allParameters,
              security: route.security || [],
              servers: routeServers,
              responses: responses,
              openapiSpec: openapiSpec // Thêm toàn bộ đặc tả OpenAPI để sử dụng khi giải quyết tham chiếu
            });
          } catch (error) {
            // Ghi log lỗi nhưng tiếp tục xử lý các route khác
            this.logger.error(`Lỗi khi xử lý route ${method.toUpperCase()} ${path}: ${error.message}`, error.stack);
            // Không throw lỗi để tiếp tục xử lý các route khác
          }
        }
      }
    } catch (error) {
      this.logger.error(`Lỗi khi phân tích đặc tả OpenAPI: ${error.message}`, error.stack);
      throw new AppException(CUSTOM_TOOLS_ERROR_CODES.OPENAPI_SCHEMA_EXTRACTION_FAILED);
    }

    return routes;
  }

  /**
   * Giải quyết tham chiếu schema
   * @param openapiSpec Đặc tả OpenAPI
   * @param ref Đường dẫn tham chiếu
   * @returns Schema đã giải quyết hoặc null nếu không tìm thấy
   */
  private resolveSchemaRef(openapiSpec: Record<string, any>, ref: string): any {
    try {
      // Tham chiếu có dạng "#/components/schemas/User"
      const refPath = ref.replace('#/', '').split('/');

      // Duyệt qua các phần của đường dẫn để tìm schema
      let schema = openapiSpec;
      for (const part of refPath) {
        if (!schema[part]) {
          return null;
        }
        schema = schema[part];
      }

      // Nếu schema tìm thấy cũng có $ref, tiếp tục giải quyết
      if (schema.$ref) {
        return this.resolveSchemaRef(openapiSpec, schema.$ref);
      }

      return schema;
    } catch (error) {
      this.logger.warn(`Không thể giải quyết tham chiếu ${ref}: ${error.message}`);
      return null;
    }
  }

  /**
   * Trích xuất thông tin xác thực từ đặc tả OpenAPI
   * @param openapiSpec Đặc tả OpenAPI
   * @returns Đối tượng chứa thông tin xác thực
   */
  private extractSecuritySchemes(openapiSpec: Record<string, any>): Record<string, SecurityScheme> {
    const securitySchemes: Record<string, SecurityScheme> = {};

    // Kiểm tra xem có components và securitySchemes không
    if (!openapiSpec.components || !openapiSpec.components.securitySchemes) {
      return securitySchemes;
    }

    // Trích xuất thông tin từ securitySchemes
    for (const schemeName in openapiSpec.components.securitySchemes) {
      const scheme = openapiSpec.components.securitySchemes[schemeName];
      securitySchemes[schemeName] = scheme;
    }

    return securitySchemes;
  }

  /**
   * Tạo công cụ từ thông tin OpenAPI
   * @param userId ID của người dùng
   * @param toolInfo Thông tin công cụ
   */
  private async createToolFromOpenApi(userId: number, toolInfo: Record<string, any>, apiKeyId: string | null): Promise<void> {
    // Kiểm tra xem tool đã tồn tại chưa
    const existingTool = await this.userToolsCustomRepository.findOne({
      where: {
        userId,
        toolName: toolInfo.name
      }
    });

    if (existingTool) {
      this.logger.log(`Tool ${toolInfo.name} đã tồn tại, bỏ qua`);
      return;
    }

    // Tạo inputSchema từ parameters và requestBody
    const inputSchema = this.convertToInputSchema(
      toolInfo.parameters,
      toolInfo.requestBody,
      toolInfo.openapiSpec
    );

    // Tạo tool mới
    const newTool = new UserToolsCustom();
    newTool.toolName = toolInfo.name;
    newTool.toolDescription = toolInfo.description;
    newTool.endpoint = toolInfo.path;
    newTool.method = toolInfo.method as HttpMethodEnum;
    newTool.baseUrl = toolInfo.baseUrl || 'http://localhost:8080';
    newTool.inputSchema = inputSchema; // Sử dụng inputSchema đã convert
    newTool.userId = userId;
    newTool.apiKeyId = apiKeyId;
    newTool.status = ToolStatusEnum.APPROVED;

    await this.userToolsCustomRepository.save(newTool);
  }

  /**
   * Tạo resource từ thông tin OpenAPI
   * @param userId ID của người dùng
   * @param resourceInfo Thông tin resource
   */
  private async createResourceFromOpenApi(userId: number, resourceInfo: Record<string, any>): Promise<void> {
    // Kiểm tra xem resource đã tồn tại chưa
    const existingTool = await this.userToolsCustomRepository.findOne({
      where: {
        userId,
        toolName: resourceInfo.name
      }
    });

    if (existingTool) {
      this.logger.log(`Resource ${resourceInfo.name} đã tồn tại, bỏ qua`);
      return;
    }

    // Tạo tool mới (resource cũng được lưu dưới dạng tool)
    const newTool = new UserToolsCustom();
    newTool.toolName = resourceInfo.name;
    newTool.toolDescription = resourceInfo.description;
    newTool.endpoint = resourceInfo.uri;
    newTool.method = HttpMethodEnum.GET; // Mặc định là GET cho resource
    newTool.baseUrl = resourceInfo.baseUrl;
    newTool.userId = userId;
    newTool.status = ToolStatusEnum.APPROVED;

    await this.userToolsCustomRepository.save(newTool);
  }

  /**
   * Chuyển đổi dữ liệu JSON Swagger thành inputSchema theo chuẩn JSON Schema
   * @param parameters Tham số từ OpenAPI
   * @param requestBody RequestBody từ OpenAPI
   * @param openapiSpec Đặc tả OpenAPI đầy đủ (để giải quyết tham chiếu)
   * @returns InputSchema chuẩn JSON Schema
   */
  private convertToInputSchema(
    parameters: any,
    requestBody: any,
    openapiSpec?: Record<string, any>
  ): Record<string, unknown> {
    // Khởi tạo inputSchema theo chuẩn JSON Schema
    const inputSchema = {
      type: "object",
      properties: {},
      required: [] as string[]
    };

    // Xử lý parameters từ OpenAPI
    const paramArray = Array.isArray(parameters) ? parameters : [];

    // Phân loại parameters theo loại (query, path, body)
    const queryParams: Record<string, any> = {};
    const pathParams: Record<string, any> = {};
    const bodyParams: Record<string, any> = {};

    const queryRequired: string[] = [];
    const pathRequired: string[] = [];
    const bodyRequired: string[] = [];

    // Xử lý từng parameter
    for (const param of paramArray) {
      if (!param || !param.name) {
        this.logger.warn('Tham số không hợp lệ, thiếu thuộc tính name:', param);
        continue;
      }

      // Tạo schema cho parameter
      const paramSchema = this.createParameterSchema(param, openapiSpec);

      // Phân loại theo param.in
      switch (param.in) {
        case 'query':
          queryParams[param.name] = paramSchema;
          if (param.required === true) {
            queryRequired.push(param.name);
          }
          break;
        case 'path':
          pathParams[param.name] = paramSchema;
          if (param.required === true) {
            pathRequired.push(param.name);
          }
          break;
        case 'body':
        case 'formData':
          bodyParams[param.name] = paramSchema;
          if (param.required === true) {
            bodyRequired.push(param.name);
          }
          break;
        default:
          // Mặc định đưa vào query params
          queryParams[param.name] = paramSchema;
          if (param.required === true) {
            queryRequired.push(param.name);
          }
      }
    }

    // Xử lý requestBody nếu có
    if (requestBody) {
      const bodySchema = this.processRequestBody(requestBody, openapiSpec);
      this.logger.debug('Processed bodySchema:', JSON.stringify(bodySchema, null, 2));
      if (bodySchema) {
        // Luôn merge properties trực tiếp vào bodyParams để tránh nested structure
        if (bodySchema.properties) {
          // Trường hợp bodySchema có properties - merge trực tiếp các properties
          // KHÔNG tạo wrapper "body" nữa
          Object.assign(bodyParams, bodySchema.properties);
          if (bodySchema.required) {
            bodyRequired.push(...bodySchema.required);
          }
          this.logger.debug('Merged bodyParams from properties:', JSON.stringify(bodyParams, null, 2));
        } else {
          // Trường hợp bodySchema không có properties - tạo một property duy nhất
          // Sử dụng tên khác "body" để tránh conflict
          const propertyName = 'requestData';
          bodyParams[propertyName] = {
            type: bodySchema.type || 'object',
            description: bodySchema.description || 'Request body data',
            ...bodySchema
          };
          if (bodySchema.required !== undefined) {
            bodyRequired.push(propertyName);
          }
          this.logger.debug('Added bodyParams as single property:', JSON.stringify(bodyParams, null, 2));
        }
      }
    }

    this.logger.debug('Final bodyParams before creating properties.body:', JSON.stringify(bodyParams, null, 2));

    // Tạo cấu trúc inputSchema cuối cùng
    const properties: Record<string, any> = {};
    const required: string[] = [];

    // Thêm query parameters nếu có
    if (Object.keys(queryParams).length > 0) {
      properties.query_param = {
        type: "object",
        description: "Các tham số truy vấn (query parameters)",
        properties: queryParams,
        ...(queryRequired.length > 0 && { required: queryRequired })
      };
      if (queryRequired.length > 0) {
        required.push('query_param');
      }
    }

    // Thêm path parameters nếu có
    if (Object.keys(pathParams).length > 0) {
      properties.path_param = {
        type: "object",
        description: "Các tham số đường dẫn (path parameters)",
        properties: pathParams,
        ...(pathRequired.length > 0 && { required: pathRequired })
      };
      if (pathRequired.length > 0) {
        required.push('path_param');
      }
    }

    // Thêm body parameters nếu có
    if (Object.keys(bodyParams).length > 0) {
      properties.body = {
        type: "object",
        description: "Các tham số trong body của request",
        properties: bodyParams,
        ...(bodyRequired.length > 0 && { required: bodyRequired })
      };
      if (bodyRequired.length > 0) {
        required.push('body');
      }
    }

    // Cập nhật inputSchema
    inputSchema.properties = properties;
    inputSchema.required = required;

    // Làm sạch schema trước khi validate
    const cleanedSchema = this.cleanSchemaForValidation(inputSchema);

    this.logger.debug('Generated inputSchema:', JSON.stringify(cleanedSchema, null, 2));

    // Validate schema với tool-parameter-validator
    try {
      this.toolParameterValidator.validateToolParameters(cleanedSchema);
      this.logger.debug('InputSchema validation passed');
    } catch (error) {
      this.logger.error(`InputSchema validation failed: ${error.message}`, error.stack);
      this.logger.error('Failed schema:', JSON.stringify(cleanedSchema, null, 2));
      // Trả về schema fallback nếu validation thất bại
      return {
        type: "object",
        properties: {
          input: {
            type: "string",
            description: "Tham số đầu vào chung"
          }
        },
        required: []
      };
    }

    return cleanedSchema;
  }

  /**
   * Tạo schema cho một parameter từ OpenAPI spec
   * @param param Parameter từ OpenAPI
   * @param openapiSpec Đặc tả OpenAPI đầy đủ
   * @returns Schema cho parameter
   */
  private createParameterSchema(param: any, openapiSpec?: Record<string, any>): Record<string, any> {
    let paramSchema: Record<string, any> = {
      type: param.schema?.type || param.type || "string",
      description: param.description || `Tham số ${param.name}`
    };

    // Xử lý schema chi tiết
    if (param.schema) {
      // Kiểm tra tham chiếu
      if (param.schema.$ref && openapiSpec) {
        const resolvedSchema = this.resolveSchemaRef(openapiSpec, param.schema.$ref);
        if (resolvedSchema) {
          paramSchema = this.mergeSchemaProperties(paramSchema, resolvedSchema);
        }
      } else {
        paramSchema = this.mergeSchemaProperties(paramSchema, param.schema);
      }
    }

    // Chỉ giữ lại properties được JSON Schema validator hỗ trợ
    return this.cleanSchemaForValidation(paramSchema);
  }

  /**
   * Làm sạch schema để chỉ giữ lại properties được JSON Schema validator hỗ trợ
   * @param schema Schema cần làm sạch
   * @returns Schema đã được làm sạch
   */
  private cleanSchemaForValidation(schema: any): any {
    if (typeof schema !== 'object' || schema === null) {
      return schema;
    }

    // Danh sách properties được JSON Schema validator hỗ trợ
    const allowedProperties = [
      'type', 'description', 'properties', 'required', 'items',
      'enum', 'minimum', 'maximum', 'minLength', 'maxLength',
      'pattern', 'additionalProperties', 'oneOf', 'anyOf', 'allOf'
    ];

    const cleaned: any = {};

    for (const [key, value] of Object.entries(schema)) {
      if (allowedProperties.includes(key)) {
        // Đệ quy làm sạch cho nested objects
        if (key === 'properties' && typeof value === 'object') {
          cleaned[key] = {};
          for (const [propKey, propValue] of Object.entries(value as any)) {
            cleaned[key][propKey] = this.cleanSchemaForValidation(propValue);
          }
        } else if (key === 'items' && typeof value === 'object') {
          cleaned[key] = this.cleanSchemaForValidation(value);
        } else {
          cleaned[key] = value;
        }
      }
    }

    return cleaned;
  }

  /**
   * Xử lý requestBody từ OpenAPI spec
   * @param requestBody RequestBody từ OpenAPI
   * @param openapiSpec Đặc tả OpenAPI đầy đủ
   * @returns Schema cho requestBody
   */
  private processRequestBody(requestBody: any, openapiSpec?: Record<string, any>): Record<string, any> | null {
    if (!requestBody || !requestBody.content) {
      return null;
    }

    // Ưu tiên application/json, sau đó application/x-www-form-urlencoded
    const contentTypes = ['application/json', 'application/x-www-form-urlencoded', 'multipart/form-data'];
    let selectedContent: any = null;

    for (const contentType of contentTypes) {
      if (requestBody.content[contentType]) {
        selectedContent = requestBody.content[contentType];
        break;
      }
    }

    // Nếu không tìm thấy content type ưu tiên, lấy content type đầu tiên
    if (!selectedContent) {
      const firstContentType = Object.keys(requestBody.content)[0];
      if (firstContentType) {
        selectedContent = requestBody.content[firstContentType];
      }
    }

    if (!selectedContent || !selectedContent.schema) {
      return null;
    }

    let schema = selectedContent.schema;

    // Giải quyết tham chiếu nếu có
    if (schema.$ref && openapiSpec) {
      const resolvedSchema = this.resolveSchemaRef(openapiSpec, schema.$ref);
      if (resolvedSchema) {
        schema = resolvedSchema;
      }
    }

    return schema;
  }

  /**
   * Merge các thuộc tính schema
   * @param baseSchema Schema cơ bản
   * @param sourceSchema Schema nguồn
   * @returns Schema đã merge
   */
  private mergeSchemaProperties(baseSchema: Record<string, any>, sourceSchema: Record<string, any>): Record<string, any> {
    const merged = { ...baseSchema };

    // Copy các thuộc tính cơ bản
    if (sourceSchema.type) merged.type = sourceSchema.type;
    if (sourceSchema.format) merged.format = sourceSchema.format;
    if (sourceSchema.description) merged.description = sourceSchema.description;
    if (sourceSchema.example !== undefined) merged.example = sourceSchema.example;
    if (sourceSchema.enum) merged.enum = sourceSchema.enum;
    if (sourceSchema.minimum !== undefined) merged.minimum = sourceSchema.minimum;
    if (sourceSchema.maximum !== undefined) merged.maximum = sourceSchema.maximum;
    if (sourceSchema.pattern) merged.pattern = sourceSchema.pattern;

    // Xử lý object properties
    if (sourceSchema.type === 'object' && sourceSchema.properties) {
      merged.properties = {};
      merged.required = sourceSchema.required || [];

      for (const propName in sourceSchema.properties) {
        const propSchema = sourceSchema.properties[propName];
        merged.properties[propName] = {
          type: propSchema.type || 'string',
          description: propSchema.description || `${propName} property`,
          ...(propSchema.enum && { enum: propSchema.enum })
        };
      }
    }

    // Xử lý array items
    if (sourceSchema.type === 'array' && sourceSchema.items) {
      merged.items = sourceSchema.items;
    }

    return merged;
  }

  /**
   * Xóa API Key nếu không còn tool nào sử dụng
   * @param apiKeyId ID của API Key cần kiểm tra
   */
  private async deleteApiKeyIfNotUsed(apiKeyId: string): Promise<void> {
    try {
      // Kiểm tra xem còn tool nào khác sử dụng API Key này không
      const toolsUsingApiKey = await this.userToolsCustomRepository.count({
        where: { apiKeyId }
      });

      if (toolsUsingApiKey === 0) {
        // Không còn tool nào sử dụng, có thể xóa API Key
        await this.apiKeyRepository.deleteApiKey(apiKeyId);
        this.logger.debug(`Đã xóa API Key ${apiKeyId} vì không còn tool nào sử dụng`);
      } else {
        this.logger.debug(`Giữ lại API Key ${apiKeyId} vì còn ${toolsUsingApiKey} tool đang sử dụng`);
      }
    } catch (error) {
      this.logger.error(`Lỗi khi kiểm tra và xóa API Key ${apiKeyId}: ${error.message}`);
      // Không throw error để không làm gián đoạn quá trình xóa tool
    }
  }

  /**
   * Xóa OAuth nếu không còn tool nào sử dụng
   * @param oauthId ID của OAuth cần kiểm tra
   */
  private async deleteOAuthIfNotUsed(oauthId: string): Promise<void> {
    try {
      // Kiểm tra xem còn tool nào khác sử dụng OAuth này không
      const toolsUsingOAuth = await this.userToolsCustomRepository.count({
        where: { oauthId }
      });

      if (toolsUsingOAuth === 0) {
        // Không còn tool nào sử dụng, có thể xóa OAuth
        await this.oAuthRepository.deleteOAuth(oauthId);
        this.logger.debug(`Đã xóa OAuth ${oauthId} vì không còn tool nào sử dụng`);
      } else {
        this.logger.debug(`Giữ lại OAuth ${oauthId} vì còn ${toolsUsingOAuth} tool đang sử dụng`);
      }
    } catch (error) {
      this.logger.error(`Lỗi khi kiểm tra và xóa OAuth ${oauthId}: ${error.message}`);
      // Không throw error để không làm gián đoạn quá trình xóa tool
    }
  }

  /**
   * Lấy danh sách công cụ tùy chỉnh của người dùng
   * @param userId ID của người dùng
   * @param query Tham số truy vấn
   * @returns Danh sách công cụ tùy chỉnh phân trang
   */
  async getCustomTools(userId: number, query: QueryDto): Promise<PaginatedResult<ToolResponseDto>> {
    try {
      // Tạo đối tượng tham số truy vấn
      const queryParams = {
        userId,
        page: query.page,
        limit: query.limit,
        search: query.search,
        sortBy: query.sortBy,
        sortDirection: query.sortDirection,
      };

      const result = await this.userToolsCustomRepository.findByUserId(queryParams);

      // Chuyển đổi từ entity sang DTO
      const toolDtos = result.items.map(tool => this.mapCustomToolToDto(tool));

      return {
        items: toolDtos,
        meta: result.meta
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách công cụ tùy chỉnh: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(CUSTOM_TOOLS_ERROR_CODES.CUSTOM_TOOL_NOT_FOUND);
    }
  }

  /**
   * Lấy chi tiết công cụ tùy chỉnh
   * @param userId ID của người dùng
   * @param toolId ID của công cụ
   * @returns Chi tiết công cụ tùy chỉnh
   */
  async getCustomToolDetail(userId: number, toolId: string): Promise<ToolDetailResponseDto> {
    try {
      const tool = await this.userToolsCustomRepository.findById(toolId);

      if (!tool || tool.userId !== userId) {
        throw new AppException(CUSTOM_TOOLS_ERROR_CODES.CUSTOM_TOOL_NOT_FOUND);
      }

      return await this.mapCustomToolToDetailDto(tool);
    } catch (error) {
      this.logger.error(`Lỗi khi lấy chi tiết công cụ tùy chỉnh: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(CUSTOM_TOOLS_ERROR_CODES.CUSTOM_TOOL_NOT_FOUND);
    }
  }

  /**
   * Cập nhật xác thực cho công cụ tùy chỉnh
   * @param userId ID của người dùng
   * @param updateDto Dữ liệu cập nhật
   * @returns Kết quả cập nhật
   */
  @Transactional()
  async updateCustomToolAuth(userId: number, updateDto: UpdateToolAuthDto, toolId: string): Promise<{ message: string }> {
    try {
      // Kiểm tra xem công cụ có tồn tại không
      const tool = await this.userToolsCustomRepository.findById(toolId);

      if (!tool || tool.userId !== userId) {
        throw new AppException(CUSTOM_TOOLS_ERROR_CODES.CUSTOM_TOOL_NOT_FOUND);
      }

      // Lưu lại ID của auth cũ để xử lý sau
      const oldApiKeyId = tool.apiKeyId;
      const oldOauthId = tool.oauthId;

      // Tạo cấu hình xác thực mới
      if (updateDto.authConfig.authType === AuthTypeEnum.API_KEY) {
        const apiKeyDto = updateDto.authConfig as ApiKeyAuthDto;

        const secretKey = this.encryptionService.generateSecretKey();

        // Mã hóa apiKey (Record<string, string>) thành JSON string
        const apiKeyData = JSON.stringify(apiKeyDto.apiKey);

        const apiKey = this.apiKeyRepository.create({
          apiKey: this.encryptionService.encrypt(secretKey, this.secretKeyPrivate, apiKeyData),
          apiKeyLocation: apiKeyDto.apiKeyLocation,
          secretKey: secretKey
        });

        const savedApiKey = await this.apiKeyRepository.save(apiKey);
        tool.apiKeyId = savedApiKey.id;
        tool.oauthId = null;
      } else {
        // Không xác thực
        tool.apiKeyId = null;
        tool.oauthId = null;
      }

      // Cập nhật công cụ
      await this.userToolsCustomRepository.updateCustomTool(tool.id, tool);

      // Xóa auth cũ nếu không còn tool nào sử dụng
      if (oldApiKeyId) {
        await this.deleteApiKeyIfNotUsed(oldApiKeyId);
      }
      if (oldOauthId) {
        await this.deleteOAuthIfNotUsed(oldOauthId);
      }

      return { message: 'Cập nhật xác thực công cụ thành công' };
    } catch (error) {
      this.logger.error(`Lỗi khi cập nhật xác thực công cụ tùy chỉnh: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(CUSTOM_TOOLS_ERROR_CODES.CUSTOM_TOOL_UPDATE_FAILED);
    }
  }

  /**
   * Xóa công cụ tùy chỉnh
   * @param userId ID của người dùng
   * @param toolId ID của công cụ
   * @returns Kết quả xóa
   */
  @Transactional()
  async deleteCustomTool(userId: number, toolId: string): Promise<{ message: string }> {
    try {
      // Kiểm tra xem công cụ có tồn tại không
      const tool = await this.userToolsCustomRepository.findById(toolId);

      if (!tool || tool.userId !== userId) {
        throw new AppException(CUSTOM_TOOLS_ERROR_CODES.CUSTOM_TOOL_NOT_FOUND);
      }

      // Lưu lại ID của API Key và OAuth để xử lý sau
      const apiKeyIdToCheck = tool.apiKeyId;
      const oauthIdToCheck = tool.oauthId;

      // Xóa công cụ trước
      await this.userToolsCustomRepository.deleteCustomTool(toolId);

      // Kiểm tra và xóa API Key nếu không còn tool nào sử dụng
      if (apiKeyIdToCheck) {
        await this.deleteApiKeyIfNotUsed(apiKeyIdToCheck);
      }

      // Kiểm tra và xóa OAuth nếu không còn tool nào sử dụng
      if (oauthIdToCheck) {
        await this.deleteOAuthIfNotUsed(oauthIdToCheck);
      }

      return { message: 'Xóa công cụ tùy chỉnh thành công' };
    } catch (error) {
      this.logger.error(`Lỗi khi xóa công cụ tùy chỉnh: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(CUSTOM_TOOLS_ERROR_CODES.CUSTOM_TOOL_DELETE_FAILED);
    }
  }

  /**
   * Chuyển đổi từ entity sang DTO cơ bản
   * @param tool Entity công cụ tùy chỉnh
   * @returns DTO công cụ tùy chỉnh
   */
  private mapCustomToolToDto(tool: UserToolsCustom): ToolResponseDto {
    const dto = new ToolResponseDto();
    dto.id = tool.id;
    dto.toolName = tool.toolName;
    dto.toolDescription = tool.toolDescription || '';
    dto.endpoint = tool.endpoint;
    dto.method = tool.method;
    dto.active = tool.active;
    dto.createdAt = tool.createdAt;
    dto.updatedAt = tool.updatedAt;

    // Xác định loại xác thực
    if (tool.apiKeyId) {
      dto.authType = AuthTypeEnum.API_KEY;
    } else if (tool.oauthId) {
      dto.authType = AuthTypeEnum.OAUTH;
    } else {
      dto.authType = AuthTypeEnum.NONE;
    }

    return dto;
  }

  /**
   * Chuyển đổi từ entity sang DTO chi tiết
   * @param tool Entity công cụ tùy chỉnh
   * @returns DTO chi tiết công cụ tùy chỉnh
   */
  private async mapCustomToolToDetailDto(tool: UserToolsCustom): Promise<ToolDetailResponseDto> {
    const dto = this.mapCustomToolToDto(tool) as ToolDetailResponseDto;

    // Sử dụng inputSchema từ entity
    const inputSchema = tool.inputSchema || {
      type: "object",
      properties: {},
      required: []
    };

    dto.parameters = {
      name: tool.toolName,
      description: tool.toolDescription || '',
      inputSchema: {
        type: "object",
        properties: {
          query_param: {
            type: "object",
            description: "Các tham số truy vấn",
            properties: (inputSchema as any)?.properties?.query_param?.properties || {}
          },
          path_param: {
            type: "object",
            description: "Các tham số đường dẫn",
            properties: (inputSchema as any)?.properties?.path_param?.properties || {}
          },
          body: {
            type: "object",
            description: "Các tham số trong body",
            properties: (inputSchema as any)?.properties?.body?.properties || {}
          }
        }
      }
    };

    dto.baseUrl = tool.baseUrl; // Thêm baseUrl vào DTO

    // Thêm thông tin extra
    dto.extra = {
      url: `${tool.baseUrl}${tool.endpoint}`,
      method: tool.method,
      headers: {}
    };

    // Thêm thông tin chi tiết về xác thực
    if (tool.apiKeyId) {
      // Lấy thông tin API Key từ repository
      const apiKey = await this.apiKeyRepository.findById(tool.apiKeyId);
      if (apiKey) {
        dto.apiKeyAuth = {
          id: apiKey.id,
          schemeName: 'apiKey',
          apiKeyLocation: apiKey.apiKeyLocation
        };

        // Thêm header xác thực vào extra nếu là API Key
        if (apiKey.apiKeyLocation === 'header') {
          try {
            // Giải mã API Key data
            const decryptedApiKeyData = this.encryptionService.decrypt(
              apiKey.secretKey,
              this.secretKeyPrivate,
              apiKey.apiKey
            );

            // Parse JSON để lấy Record<string, string>
            const apiKeyRecord: Record<string, string> = JSON.parse(decryptedApiKeyData as string);

            // Thêm tất cả API keys vào headers
            Object.entries(apiKeyRecord).forEach(([headerName, headerValue]) => {
              dto.extra.headers[headerName] = headerValue;
            });
          } catch (error) {
            this.logger.error(`Lỗi khi giải mã API Key: ${error.message}`);
            // Fallback: sử dụng giá trị mặc định
            dto.extra.headers['X-API-KEY'] = '****';
          }
        }
      }
    }

    if (tool.oauthId) {
      // Lấy thông tin OAuth từ repository
      const oauth = await this.oAuthRepository.findById(tool.oauthId);
      if (oauth) {
        dto.oauthAuth = {
          id: oauth.id,
          schemeName: oauth.schemeName,
          tokenSource: oauth.tokenSource,
          tokenExpiresAt: oauth.tokenExpiresAt || 0
        };

        // Thêm header xác thực vào extra nếu là OAuth
        // Sử dụng giá trị thực tế của token thay vì placeholder
        dto.extra.headers['Authorization'] = oauth.token ? `Bearer ${oauth.token}` : 'Bearer ';
      }
    }

    return dto;
  }

  /**
   * Cập nhật base URL cho công cụ tùy chỉnh
   * @param userId ID của người dùng
   * @param updateDto Dữ liệu cập nhật base URL
   * @returns Thông báo kết quả
   */
  @Transactional()
  async updateBaseUrl(userId: number, updateDto: UpdateBaseUrlDto, toolId: string): Promise<{ message: string }> {
    try {
      // Kiểm tra xem công cụ có tồn tại không
      const tool = await this.userToolsCustomRepository.findById(toolId);

      if (!tool || tool.userId !== userId) {
        throw new AppException(CUSTOM_TOOLS_ERROR_CODES.CUSTOM_TOOL_NOT_FOUND);
      }

      // Cập nhật base URL
      await this.userToolsCustomRepository.updateCustomTool(tool.id, {
        baseUrl: updateDto.baseUrl
      });

      return { message: 'Cập nhật base URL thành công' };
    } catch (error) {
      this.logger.error(`Lỗi khi cập nhật base URL: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(CUSTOM_TOOLS_ERROR_CODES.CUSTOM_TOOL_UPDATE_FAILED);
    }
  }
}

import { AppException } from '@/common';
import { SortDirection } from '@/common/dto/query.dto';
import { TypeAgentEnum } from '@/modules/agent/constants/type-agents.enum';
import { FeatureEnum, InputModalityEnum } from '@/modules/models/constants';
import { Settings, User } from '@/modules/user/entities';
import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { QueueService } from '@shared/queue/queue.service';
import { InAppJobData } from '@shared/queue/queue.types';
import { RedisService } from '@shared/services/redis.service';
import { RunStatusService } from '@shared/services/run-status.service';
import { CancelReason } from '@shared/run-status';
import * as crypto from 'crypto';
import { Response } from 'express';
import { MoreThan, Not, Repository } from 'typeorm';
import { Transactional } from 'typeorm-transactional';
import { z } from 'zod';
import { Platform, UserAgentRunStatus } from '../../../shared/enums';
import { Agent } from '../../agent/entities/agent.entity';
import { UserMultiAgent } from '../../agent/entities/user-multi-agent.entity';
import {
  AttachmentContentDto,
  AttachmentContentType,
  MessageContentType,
  MessageRequestDto,
  ToolCallDecision,
} from '../dto/message-request.dto';
import { MessageResponseDto } from '../dto/message-response.dto';
import { InternalConversationThread } from '../entities';
import {
  InternalConversationMessage,
  InternalConversationMessageRole,
} from '../entities/internal-conversation-message.entity';
import {
  InternalConversationMessagesAttachment,
  InternalConversationThreadsAttachmentType,
} from '../entities/internal-conversation-messages-attachment.entity';
import { CHAT_ERROR_CODES } from '../exceptions';
import { ContentValidationService } from './content-validation.service';
import { InternalMessageService } from './internal-message.service';
import { InternalMessageValidationService } from './internal-message-validation.service';

/**
 * New Chat Service
 * Clean implementation for handling chat functionality based on the coordination design
 * Replaces the deprecated ChatService with modern patterns
 */
@Injectable()
export class ChatServiceNew {
  private readonly logger = new Logger(ChatServiceNew.name);

  constructor(
    private readonly messageValidation: InternalMessageValidationService,
    private readonly contentValidation: ContentValidationService,
    private readonly redisService: RedisService,
    private readonly runStatusService: RunStatusService,
    private readonly queueService: QueueService,
    private readonly internalMessageService: InternalMessageService,
    @InjectRepository(InternalConversationMessage)
    private readonly messageRepository: Repository<InternalConversationMessage>,
    @InjectRepository(InternalConversationMessagesAttachment)
    private readonly attachmentRepository: Repository<InternalConversationMessagesAttachment>,
    @InjectRepository(Agent)
    private readonly agentRepository: Repository<Agent>,
    @InjectRepository(UserMultiAgent)
    private readonly userMultiAgentRepository: Repository<UserMultiAgent>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(Settings)
    private readonly userSettingRepository: Repository<Settings>,
  ) {}

  /**
   * Process a chat message according to the backend-worker coordination design
   *
   * This method implements the sophisticated coordination strategy:
   * 1. Message persistence in database
   * 2. Debouncing with delayed job scheduling
   * 3. Interruption handling for double-texting
   * 4. Lazy run creation to prevent database waste
   *
   * @param messageRequest - The message request data
   * @param userId - ID of the user sending the message
   * @param jwt - JWT token for worker authentication
   * @param agentId - Optional user agent ID
   * @param threadId - ID of the thread the message belongs to
   * @returns Promise<MessageResponseDto> - Response with run information
   */
  async processMessage(param: {
    messageRequest: MessageRequestDto;
    userId: number;
    jwt: string;
    threadId: string;
    agentId?: string;
  }): Promise<MessageResponseDto> {
    const { messageRequest, userId, jwt, threadId, agentId } = param;
    this.logger.log('Processing message with new chat service', {
      threadId: threadId,
      userId,
      agentId,
    });
    const user = await this.userRepository.findOneBy({ id: userId });
    const userHasEnoughPoints = user?.pointsBalance && user?.pointsBalance >= 1;
    if (!userHasEnoughPoints) {
      throw new AppException(CHAT_ERROR_CODES.NOT_ENOUGH_POINTS);
    }

    this.logger.debug('Starting Phase 1: Validation');

    await this.validateThreadAccess(threadId, userId);

    await this.validateMessageContent(messageRequest, userId, threadId);

    const { inputModalities, features } =
      await this.getAgentModalitiesAndFeatures(agentId, userId);

    if (!inputModalities || !features) {
      throw new AppException(CHAT_ERROR_CODES.AGENT_NOT_FOUND);
    }

    this.validateContentAgainstModelCapabilities(
      inputModalities,
      features,
      messageRequest,
    );

    this.logger.debug('Phase 1 validation completed successfully');

    this.logger.debug('Starting Phase 2: Database Persistence');
    const { messageId, runId, isModification, deletedMessageIds } =
      await this.persistMessage(messageRequest, userId, threadId);

    this.logger.debug('Phase 2 completed', {
      messageId,
      runId,
      isModification,
      deletedMessageIds,
    });
    const userSetting = await this.userSettingRepository.findOneBy({
      userId: userId,
    });

    if (!userSetting) {
      throw new AppException(CHAT_ERROR_CODES.USER_SETTING_NOT_FOUND);
    }

    this.logger.debug('Starting Phase 3: Worker Triggering');
    const jobData = await this.triggerWorkerProcessing({
      runId,
      threadId,
      jwt,
      alwaysApproveToolCall: messageRequest.alwaysApproveToolCall || false,
      toolCallDecision: messageRequest.contentBlocks.toolCallDecision,
      agentId,
      webSearchEnabled: messageRequest.webSearchEnabled,
      user,
      userSetting,
    });

    this.logger.debug('Phase 3 completed', {
      threadId: threadId,
      messageId,
      runId,
    });
    const response = new MessageResponseDto({
      messageId: messageId,
      runId: runId,
      agentName: agentId ? 'User Agent' : 'System Agent',
      status: UserAgentRunStatus.CREATED,
      createdAt: Date.now(),
      deletedMessageIds: deletedMessageIds || undefined,
      debug: {
        jobData: jobData,
        phase3: {
          streamInterruption: `Checked streaming:${Platform.IN_APP}:${threadId}`,
        },
        phase4: {
          platformThreadId: `${Platform.IN_APP}:${threadId}`,
          multiAgent: {
            finalAgentId: jobData.mainAgentId,
            workerAgents: jobData.workerAgents,
          },
        },
      },
    });

    this.logger.log('Phase 2 completed, returning response', {
      messageId: response.messageId,
      runId: response.runId,
      status: response.status,
      isModification,
    });

    return response;
  }

  private async getAgentModalitiesAndFeatures(
    agentId: string | undefined,
    userId: number,
  ): Promise<{
    inputModalities: InputModalityEnum[];
    features: FeatureEnum[];
  }> {
    let modelRegistryRecordOfAgent:
      | {
          inputModalitiesBase: InputModalityEnum[];
          inputModalitiesFineTune: InputModalityEnum[];
          featuresBase: FeatureEnum[];
          featuresFineTune: FeatureEnum[];
          isFineTune: boolean;
          agentConfig: any;
        }
      | null
      | undefined = null;
    if (!agentId) {
      modelRegistryRecordOfAgent = await this.agentRepository
        .createQueryBuilder('agent')
        .innerJoin('models', 'model', 'agent.model_id = model.id')
        .innerJoin(
          'model_registry',
          'registry',
          'model.model_registry_id = registry.id',
        )
        .innerJoin('type_agents', 'typeAgent', 'agent.type_id = typeAgent.id')
        .select([
          'registry.input_modalities_base as "inputModalitiesBase"',
          'registry.input_modalities_fine_tune as "inputModalitiesFineTune"',
          'registry.features_base as "featuresBase"',
          'registry.features_fine_tune as "featuresFineTune"',
          'model.is_fine_tune as "isFineTune"',
          'agent.config as "agentConfig"',
        ])
        .where('agent.deleted_at IS NULL')
        .andWhere('typeAgent.type = :type', { type: TypeAgentEnum.SUPERVISOR })
        .getRawOne();
    } else {
      modelRegistryRecordOfAgent = await this.agentRepository
        .createQueryBuilder('agent')
        .innerJoin('models', 'model', 'agent.model_id = model.id')
        .innerJoin(
          'model_registry',
          'registry',
          'model.model_registry_id = registry.id',
        )
        .select([
          'registry.input_modalities_base as "inputModalitiesBase"',
          'registry.input_modalities_fine_tune as "inputModalitiesFineTune"',
          'registry.features_base as "featuresBase"',
          'registry.features_fine_tune as "featuresFineTune"',
          'model.is_fine_tune as "isFineTune"',
          'agent.config as "agentConfig"',
        ])
        .where('agent.id = :agentId', { agentId })
        .andWhere('agent.user_id = :userId', { userId })
        .andWhere('agent.deleted_at IS NULL')
        .getRawOne();
    }

    if (!modelRegistryRecordOfAgent) {
      throw new AppException(CHAT_ERROR_CODES.AGENT_NOT_FOUND);
    }

    // Check if agent is sold (config.isForSale = true)
    if (modelRegistryRecordOfAgent.agentConfig?.isForSale === true) {
      throw new AppException(CHAT_ERROR_CODES.AGENT_SOLD);
    }

    return {
      inputModalities: modelRegistryRecordOfAgent.isFineTune
        ? modelRegistryRecordOfAgent.inputModalitiesFineTune
        : modelRegistryRecordOfAgent.inputModalitiesBase,
      features: modelRegistryRecordOfAgent.isFineTune
        ? modelRegistryRecordOfAgent.featuresFineTune
        : modelRegistryRecordOfAgent.featuresBase,
    };
  }

  /**
   * Create or update message in database
   * Part of Phase 2: Database Persistence
   */
  @Transactional()
  private async persistMessage(
    messageRequest: MessageRequestDto,
    userId: number,
    threadId: string,
  ): Promise<{
    messageId?: string;
    runId: string;
    isModification: boolean;
    deletedMessageIds: string[];
  }> {
    this.logger.debug('Starting Phase 2: Database Persistence', {
      threadId: threadId,
      userId,
      isModification: !!messageRequest.messageId,
    });

    let messageId: string | undefined = undefined;
    let isModification = false;
    let deletedMessageIds: string[] = [];
    // Handle message modification vs new message creation
    if (messageRequest.messageId) {
      isModification = true;
      messageId = await this.updateExistingMessage(
        messageRequest,
        userId,
        threadId,
      );
      deletedMessageIds = await this.deleteSubsequentMessages(
        messageRequest.messageId,
      );
    } else if (
      messageRequest.contentBlocks.type !==
      MessageContentType.TOOL_CALL_DECISION
    ) {
      messageId = await this.createNewMessage(messageRequest, userId, threadId);
    }

    // remove latest AI message (the message of role assistant that has is_tool_call_confirmed = true)
    if (
      messageRequest.contentBlocks.type ===
      MessageContentType.TOOL_CALL_DECISION
    ) {
      await this.removeLatestAiMessage(threadId, userId);
    }

    // Generate runId using crypto (no database entity creation)
    // Worker will use job data directly, not InternalLlmRun entity
    const runId = crypto.randomUUID();

    this.logger.debug('Phase 2: Database Persistence completed', {
      messageId,
      runId,
      isModification,
      threadId: threadId,
    });

    return {
      messageId,
      runId,
      isModification,
      deletedMessageIds,
    };
  }

  private async deleteSubsequentMessages(messageId: string): Promise<string[]> {
    const targetMessage = await this.messageRepository.findOne({
      where: { id: messageId },
      select: ['threadId', 'createdAt'],
    });

    if (!targetMessage) {
      throw new AppException(CHAT_ERROR_CODES.MESSAGE_NOT_FOUND);
    }

    const subsequentMessages = await this.messageRepository.find({
      where: {
        threadId: targetMessage.threadId,
        createdAt: MoreThan(targetMessage.createdAt),
        id: Not(messageId),
      },
      select: ['id'],
    });

    const messageIdsToDelete = subsequentMessages.map((msg) => msg.id);
    await this.messageRepository.delete(messageIdsToDelete);

    return messageIdsToDelete;
  }

  private async removeLatestAiMessage(threadId: string, userId: number) {
    // 1. Check if there is a pending tool call interrupt in the thread
    const latestMessage = await this.getLatestToolCallInterruptMessage(
      threadId,
      userId,
    );
    if (latestMessage) {
      await this.messageRepository.delete(latestMessage.messageId);
    }
  }

  /**
   * Create a new message in the database
   */
  private async createNewMessage(
    messageRequest: MessageRequestDto,
    userId: number,
    threadId: string,
  ): Promise<string> {
    this.logger.debug('Creating new message', {
      threadId: threadId,
      userId,
    });

    // Create message entity
    const message = this.messageRepository.create({
      threadId: threadId,
      userId: userId,
      text: messageRequest.contentBlocks.text,
      role: InternalConversationMessageRole.USER,
      hasAttachments:
        messageRequest.contentBlocks.type === MessageContentType.ATTACHMENT,
      isToolCallConfirm:
        messageRequest.contentBlocks.type ===
        MessageContentType.TOOL_CALL_DECISION,
      processed: false, // Explicitly set to false for worker processing
      createdAt: `${Date.now()}`,
      replyingToMessageId: messageRequest.replyToMessageId,
    });

    // Save to database
    const savedMessage = await this.messageRepository.save(message);

    // Create attachment records if message has file/image content blocks
    if (messageRequest.contentBlocks.type === MessageContentType.ATTACHMENT) {
      await this.createAttachmentRecords(
        messageRequest.contentBlocks.attachments as AttachmentContentDto[],
        threadId,
        savedMessage.id,
      );
    }

    this.logger.debug('Created new message with attachments', {
      messageId: savedMessage.id,
      threadId: threadId,
      hasAttachments: savedMessage.hasAttachments,
      isToolCallConfirm: savedMessage.isToolCallConfirm,
    });

    return savedMessage.id;
  }

  /**
   * Update an existing message in the database
   */
  private async updateExistingMessage(
    messageRequest: MessageRequestDto,
    userId: number,
    threadId: string,
  ): Promise<string> {
    this.logger.debug('Updating existing message', {
      messageId: messageRequest.messageId,
      threadId: threadId,
      userId,
    });

    await this.messageRepository.update(messageRequest.messageId!, {
      text: messageRequest.contentBlocks.text,
      isToolCallConfirm: false,
      processed: false,
    });

    this.logger.debug('Updated existing message (text only)', {
      messageId: messageRequest.messageId,
      threadId: threadId,
    });

    return messageRequest.messageId!;
  }

  /**
   * Create attachment records for file/image content blocks
   */
  private async createAttachmentRecords(
    contentBlocks: AttachmentContentDto[],
    conversationId: string,
    messageId: string,
  ): Promise<void> {
    const attachmentBlocks = contentBlocks.filter(
      (block) =>
        block.type === AttachmentContentType.IMAGE ||
        block.type === AttachmentContentType.FILE,
    );

    if (attachmentBlocks.length === 0) {
      return; // No attachments to create
    }

    const attachmentRecords = attachmentBlocks.map((block) => {
      return this.attachmentRepository.create({
        conversationId: conversationId,
        messageId: messageId,
        attachmentId: block.fileId, // Extract attachment ID from content block
        mediaType: this.mapContentTypeToAttachmentType(block.type),
      });
    });

    await this.attachmentRepository.save(attachmentRecords);

    this.logger.debug('Created attachment records', {
      conversationId,
      messageId,
      attachmentCount: attachmentRecords.length,
    });
  }

  /**
   * Map content block type to attachment type enum
   */
  private mapContentTypeToAttachmentType(
    contentType: string,
  ): InternalConversationThreadsAttachmentType {
    switch (contentType) {
      case 'image':
        return InternalConversationThreadsAttachmentType.IMAGE;
      case 'file':
        return InternalConversationThreadsAttachmentType.KNOWLEDGE_FILE;
      default:
        throw new Error(
          `Unsupported content type for attachment: ${contentType}`,
        );
    }
  }

  /**
   * Handle stream interruption via Redis pub/sub
   * Part of Phase 3: Worker Triggering
   *
   * Sends MESSAGE_INTERRUPT cancellation when user sends new message during processing
   */
  private async handleStreamInterruption(
    threadId: string,
    newRunId: string,
  ): Promise<void> {
    const platformThreadId = `in_app:${threadId}`;
    const currentStatus =
      await this.runStatusService.getRunStatus(platformThreadId);

    this.logger.debug('Checking for active run during interruption', {
      threadId,
      newRunId,
      currentStatus: currentStatus?.status,
      currentRunId: currentStatus?.metadata.runId,
      hasActiveRun: currentStatus?.status === 'active',
    });

    if (currentStatus && currentStatus.status === 'active') {
      // Cancel the current run with MESSAGE_INTERRUPT reason
      const cancelSuccess = await this.runStatusService.cancelRun(
        platformThreadId,
        CancelReason.MESSAGE_INTERRUPT,
        currentStatus.metadata.runId,
      );

      if (cancelSuccess) {
        this.logger.debug('Sent message interrupt cancellation', {
          threadId,
          cancelledRunId: currentStatus.metadata.runId,
          newRunId,
          reason: CancelReason.MESSAGE_INTERRUPT,
        });
      } else {
        this.logger.error('Failed to cancel active run during interruption', {
          threadId,
          currentRunId: currentStatus.metadata.runId,
          newRunId,
        });
      }
    } else {
      this.logger.debug('No active run found, skipping interruption', {
        threadId,
        newRunId,
        currentStatus: currentStatus?.status || 'none',
      });
    }
  }

  /**
   * Validate thread access and ownership
   */
  private async validateThreadAccess(
    threadId: string,
    userId: number,
  ): Promise<InternalConversationThread> {
    this.logger.debug('Validating thread access', { threadId, userId });

    // Use MessageValidationService to validate thread exists and user has access
    const thread = await this.messageValidation.validateThreadExistsAndAccess(
      threadId,
      { userId },
    );

    this.logger.debug('Thread access validation successful', {
      threadId,
      threadTitle: thread.title,
    });

    return thread;
  }

  /**
   * Validate message content blocks
   */
  private async validateMessageContent(
    messageRequest: MessageRequestDto,
    userId: number,
    threadId: string,
  ): Promise<void> {
    this.logger.debug('Validating message content', {
      threadId: threadId,
    });

    if (messageRequest.contentBlocks.type === MessageContentType.TEXT) {
      return;
    }

    if (messageRequest.contentBlocks.type === MessageContentType.ATTACHMENT) {
      await this.contentValidation.validateFileExistenceInContentBlocks(
        messageRequest.contentBlocks.attachments as AttachmentContentDto[],
        { userId },
      );
      return;
    }

    if (
      messageRequest.contentBlocks.type ===
      MessageContentType.TOOL_CALL_DECISION
    ) {
      await this.validateToolCallDecision(
        messageRequest.contentBlocks.toolCallDecision as ToolCallDecision,
        threadId,
        userId,
      );
      return;
    }

    // Validate reply message references if present
    if (messageRequest.replyToMessageId) {
      await this.messageValidation.validateMessageExists(
        messageRequest.replyToMessageId,
        threadId,
        { userId },
      );
    }

    if (messageRequest.messageId) {
      await this.messageValidation.validateModifyLastTextBlock(
        messageRequest.messageId,
        threadId,
        { userId },
      );
    }

    this.logger.debug('Message content validation completed successfully');
  }

  /**
   * Validate tool call decision
   */
  private async validateToolCallDecision(
    toolCallBlock: ToolCallDecision,
    threadId: string,
    userId: number,
  ): Promise<void> {
    this.logger.debug('Validating tool call decision', {
      decision: toolCallBlock,
      threadId,
      userId,
    });

    // 1. Check if there is a pending tool call interrupt in the thread
    const latestMessage = await this.getLatestToolCallInterruptMessage(
      threadId,
      userId,
    );

    if (!latestMessage) {
      this.logger.warn(
        'Tool call decision validation failed: latest message is not a tool call interrupt',
        {
          threadId,
          userId,
          decision: toolCallBlock,
        },
      );
      throw new AppException(
        CHAT_ERROR_CODES.TOOL_CALL_DECISION_REQUIRES_INTERRUPT,
        'tool_call_decision requires the latest message in the thread to be a tool_call_interrupt, but it is not',
      );
    }

    // 3. User permission is implicitly validated by thread ownership (already checked in validateThreadAccess)

    this.logger.debug('Tool call decision validation completed successfully', {
      threadId,
      userId,
      decision: toolCallBlock,
      interruptMessageId: latestMessage.messageId,
    });
  }

  /**
   * Validate content against model capabilities
   */
  private validateContentAgainstModelCapabilities(
    inputModalities: InputModalityEnum[],
    features: FeatureEnum[],
    messageRequest: MessageRequestDto,
  ): void {
    this.logger.debug('Validating content against model capabilities', {
      inputModalities,
      features,
    });

    if (
      messageRequest.contentBlocks.type === MessageContentType.TEXT &&
      !inputModalities.includes(InputModalityEnum.TEXT)
    ) {
      throw new AppException(
        CHAT_ERROR_CODES.UNSUPPORTED_CONTENT_TYPE,
        `Current agent does not support text input. Supported modalities: ${inputModalities.join(', ')}`,
      );
    }

    if (
      messageRequest.contentBlocks.type === MessageContentType.ATTACHMENT &&
      messageRequest.contentBlocks.attachments?.filter(
        (block) => block.type === 'image',
      ).length &&
      !inputModalities.includes(InputModalityEnum.IMAGE)
    ) {
      throw new AppException(
        CHAT_ERROR_CODES.UNSUPPORTED_CONTENT_TYPE,
        `Current agent does not support image input. Supported modalities: ${inputModalities.join(', ')}`,
      );
    }
  }

  /**
   * Get the latest tool call interrupt message in a thread
   * The tool call confirm message MUST be the latest message in the thread
   */
  private async getLatestToolCallInterruptMessage(
    threadId: string,
    userId: number,
  ): Promise<{ messageId: string; role: string; createdAt: number } | null> {
    this.logger.debug('Getting latest tool call interrupt message', {
      threadId,
      userId,
    });

    // Get only the latest message in the thread
    const latestMessages =
      await this.internalMessageService.getUserThreadMessages(
        userId,
        threadId,
        {
          page: 1,
          limit: 1,
          sortBy: 'createdAt',
          sortDirection: SortDirection.DESC,
        },
      );

    // Check if there are any messages
    if (!latestMessages.items || latestMessages.items.length === 0) {
      this.logger.debug('No messages found in thread', {
        threadId,
        userId,
      });
      return null;
    }

    const latestMessage = latestMessages.items[0];

    // The latest message MUST be a tool call confirm message
    if (!latestMessage.isToolCallConfirm) {
      this.logger.debug('Latest message is not a tool call confirm message', {
        threadId,
        userId,
        latestMessageId: latestMessage.messageId,
        isToolCallConfirm: latestMessage.isToolCallConfirm,
      });
      return null;
    }

    this.logger.debug('Found latest tool call interrupt message', {
      threadId,
      userId,
      messageId: latestMessage.messageId,
      createdAt: latestMessage.messageCreatedAt,
    });

    return {
      messageId: latestMessage.messageId,
      role: latestMessage.role,
      createdAt: latestMessage.messageCreatedAt,
    };
  }

  /**
   * Trigger worker processing via BullMQ
   * Part of Phase 3: Worker Triggering
   */
  private async triggerWorkerProcessing(param: {
    runId: string;
    threadId: string;
    jwt: string;
    alwaysApproveToolCall: boolean;
    toolCallDecision?: ToolCallDecision | undefined;
    agentId?: string | undefined;
    webSearchEnabled?: boolean;
    user: User;
    userSetting: Settings;
  }): Promise<InAppJobData> {
    const {
      runId,
      threadId,
      jwt,
      alwaysApproveToolCall,
      toolCallDecision,
      agentId,
      webSearchEnabled,
      user,
      userSetting,
    } = param;
    const delay = toolCallDecision ? 0 : 2500;
    const platformThreadId = `${Platform.IN_APP}:${threadId}`;
    const keys = {
      platformThreadId,
      runStatusKey: `run_status:${Platform.IN_APP}:${threadId}`,
      streamKey: `${Platform.IN_APP}:agent_stream:${threadId}:${runId}`,
    };

    // 🔒 VALIDATE KEYS BEFORE USING THEM
    this.validateRedisKeys(keys, { threadId, runId });

    // Resolve agent and worker IDs based on the multi-agent logic
    const { finalAgentId, workerAgents } =
      await this.resolveMultiAgentConfiguration(agentId);

    if (!finalAgentId) {
      this.logger.error('no final agent id');
      throw new AppException(
        CHAT_ERROR_CODES.AGENT_NOT_FOUND,
        'Agent not found',
      );
    }

    const jobData: InAppJobData = {
      runId: runId,
      threadId,
      mainAgentId: finalAgentId,
      keys,
      humanInfo: {
        user: {
          userId: user.id,
          fullName: user.fullName,
          email: user.email,
          gender: user.gender,
          dateOfBirth: user.dateOfBirth,
          type: user.type,
          countryCode: user.countryCode,
          pointsBalance: user.pointsBalance,
          isVerifyPhone: user.isVerifyPhone,
          address: user.address,
          timezone: userSetting.timezone,
          currency: userSetting.currency,
        },
      },
      jwt,
      chatWithSystem: !agentId,
      alwaysApproveToolCall,
      toolCallDecision,
      workerAgents,
      platform: Platform.IN_APP,
      webSearchEnabled,
    };

    // Check if worker is already actively processing this conversation
    const isRunActive =
      await this.runStatusService.isRunActive(platformThreadId);

    if (isRunActive) {
      this.logger.debug('Worker already processing, sending interruption', {
        threadId,
        platformThreadId,
        currentRunStatus:
          await this.runStatusService.getRunStatus(platformThreadId),
        newRunId: runId,
      });
      await this.handleStreamInterruption(threadId, runId);
    }

    await this.queueService.addInAppAiJob(jobData, {
      delay,
      jobId: platformThreadId,
      attempts: 1,
      removeOnComplete: true,
      removeOnFail: true,
    });

    this.logger.debug('Triggered worker processing', {
      runId,
      threadId,
      platformThreadId,
      agentId: finalAgentId,
      workerAgents,
    });

    return jobData;
  }

  /**
   * Resolve multi-agent configuration based on the request
   * - If no agentId provided: Use SUPERVISOR agent
   * - If agentId provided: Use user-built agent + its workers from user_multi_agent table
   */
  private async resolveMultiAgentConfiguration(
    agentId: string | undefined,
  ): Promise<{
    finalAgentId: string;
    workerAgents: Array<{ id: string; prompt?: string }>;
  }> {
    if (!agentId) {
      // get all agents in system, both supervisor and worker
      const systemAgents = await this.agentRepository
        .createQueryBuilder('agents')
        .innerJoin('type_agents', 'ta', 'agents.type_id = ta.id')
        .select([
          'agents.id as "agentId"',
          'ta.type AS "taType"',
          'agents.config as "agentConfig"',
        ])
        .where('ta.type IN (:...types)', {
          types: [TypeAgentEnum.SUPERVISOR, TypeAgentEnum.SYSTEM],
        })
        .andWhere('agents.active = :active', { active: true })
        .andWhere('agents.deleted_at IS NULL')
        .getRawMany();

      this.logger.debug('Found system agents', JSON.stringify(systemAgents));

      // pick out the supervisor to see how many there are
      const supervisors = systemAgents.filter(
        (agent) => agent.taType === TypeAgentEnum.SUPERVISOR,
      );

      this.logger.debug('Found supervisors', JSON.stringify(supervisors));

      if (supervisors.length === 0) {
        this.logger.error('No supervisor agents found');
        throw new AppException(
          CHAT_ERROR_CODES.AGENT_NOT_FOUND,
          'No supervisor agents found',
        );
      }

      if (supervisors.length > 1) {
        this.logger.error('Multiple supervisor agents found');
        throw new AppException(
          CHAT_ERROR_CODES.TOO_MANY_SUPERVISORS,
          'Multiple supervisor agents found',
        );
      }

      const finalAgentId = supervisors[0].agentId;
      const workerSystemAgents = systemAgents.filter(
        (agent) => agent.agentId !== finalAgentId,
      );

      return {
        finalAgentId: finalAgentId,
        workerAgents: workerSystemAgents.map((agent) => ({
          id: agent.agentId,
          prompt: agent.agentConfig?.description || undefined,
        })),
      };
    } else {
      // get the agent and its typeId
      const userAgent = await this.agentRepository
        .createQueryBuilder('agents')
        .innerJoin('type_agents', 'ta', 'agents.type_id = ta.id')
        .where('agents.id = :agentId', { agentId })
        .andWhere('ta.type NOT IN (:...systemTypes)', {
          systemTypes: [TypeAgentEnum.SUPERVISOR, TypeAgentEnum.SYSTEM],
        })
        // .andWhere('agents.active = :active', { active: true }) 
        .andWhere('agents.deleted_at IS NULL')
        .select(['agents.id', 'ta.id AS "typeId"'])
        .getRawOne();

      if (!userAgent) {
        this.logger.error('User agent not found or is system agent');
        throw new AppException(
          CHAT_ERROR_CODES.AGENT_NOT_FOUND,
          'User agent not found or is system agent',
        );
      }

      // get the worker agents - only include active ones
      const workerRelations = await this.userMultiAgentRepository
        .createQueryBuilder('uma')
        .innerJoin(
          'agents',
          'child_agent',
          'uma.child_agent_id = child_agent.id',
        )
        .select([
          'uma.child_agent_id as "childAgentId"',
          'uma.prompt as prompt',
        ])
        .where('uma.parent_agent_id = :agentId', { agentId })
        // .andWhere('child_agent.active = :active', { active: true }) 
        .andWhere('child_agent.deleted_at IS NULL')
        .getRawMany();

      const workerAgents =
        workerRelations
          ?.map((relation) => ({
            id: relation?.childAgentId,
            prompt: relation?.prompt || undefined,
          }))
          ?.filter((id) => id != null) || [];

      // Optimized query: Use typeId from above to avoid redundant joins
      const agentSystemIds = await this.agentRepository
        .createQueryBuilder('system_agents')
        .innerJoin(
          'type_agent_agent_system',
          'taas',
          'taas.agent_id = system_agents.id',
        )
        .select(['system_agents.id', 'system_agents.config'])
        .where('taas.type_id = :typeId', { typeId: userAgent.typeId })
        .andWhere('system_agents.active = :active', { active: true })
        .andWhere('system_agents.deleted_at IS NULL')
        .getMany();

      if (agentSystemIds.length > 0) {
        workerAgents.push(
          ...agentSystemIds.map((agent) => ({
            id: agent.id,
            prompt: agent.config?.description || undefined,
          })),
        );
      }

      return {
        finalAgentId: agentId,
        workerAgents: workerAgents,
      };
    }
  }

  /**
   * Cancel an active run for a conversation thread
   * Sends USER_ABORT cancellation when user explicitly requests cancellation
   * 🔧 UPDATED: Now uses RunStatusService for unified status management
   */
  async cancelRun(
    threadId: string,
    userId: number,
  ): Promise<{ success: boolean; message: string }> {
    this.logger.debug('Starting run cancellation', {
      threadId,
      userId,
    });

    await this.validateThreadAccess(threadId, userId);

    // Check if there's an active run using RunStatusService
    const platformThreadId = `in_app:${threadId}`;
    const isRunActive =
      await this.runStatusService.isRunActive(platformThreadId);
    const currentStatus =
      await this.runStatusService.getRunStatus(platformThreadId);

    this.logger.debug('Checking for active run during cancellation', {
      threadId,
      userId,
      isRunActive,
      currentStatus: currentStatus?.status,
      currentRunId: currentStatus?.metadata.runId,
    });

    if (!isRunActive || !currentStatus) {
      this.logger.debug('No active run found for cancellation', {
        threadId,
        userId,
        currentStatus: currentStatus?.status || 'none',
      });

      return {
        success: false,
        message: 'No active processing found for this conversation',
      };
    }

    // Cancel the run using RunStatusService with USER_ABORT reason
    const cancelSuccess = await this.runStatusService.cancelRun(
      platformThreadId,
      CancelReason.USER_ABORT,
      currentStatus.metadata.runId,
    );

    if (cancelSuccess) {
      this.logger.log('Sent user abort cancellation', {
        threadId,
        userId,
        cancelledRunId: currentStatus.metadata.runId,
        reason: CancelReason.USER_ABORT,
      });

      return {
        success: true,
        message: `Cancellation sent for run ${currentStatus.metadata.runId}`,
      };
    } else {
      this.logger.error('Failed to cancel active run', {
        threadId,
        userId,
        currentRunId: currentStatus.metadata.runId,
      });

      return {
        success: false,
        message: 'Failed to cancel the active run',
      };
    }
  }

  async streamChatEvents(
    res: Response,
    threadId: string,
    runId: string,
    userId: number,
    fromMessageId?: string,
  ): Promise<void> {
    try {
      await this.validateThreadAccess(threadId, userId);

      this.logger.log(
        `🔥 Starting chat SSE stream for thread ${threadId}, run ${runId}`,
      );

      res.set({
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache, no-transform',
        Connection: 'keep-alive',
        'X-Accel-Buffering': 'no',
      });

      res.flushHeaders();

      const streamKey = `${Platform.IN_APP}:agent_stream:${threadId}:${runId}`;
      await this.consumeStreamMessages(res, streamKey, threadId, fromMessageId);
    } catch (error) {
      this.logger.error(`💥 Stream failed for thread ${threadId}:`, error);
      throw error;
    } finally {
      // GUARANTEED CLEANUP: This block ensures the response stream is always closed.
      if (!res.writableEnded) {
        res.end();
      }
      this.logger.log(
        `🔚 SSE stream session ended for thread ${threadId}, run ${runId}`,
      );
    }
  }

  /**
   * Consume messages from Redis stream and send via SSE.
   * This method no longer closes the response; it relies on the caller's finally block.
   */
  private async consumeStreamMessages(
    res: Response,
    streamKey: string,
    threadId: string,
    fromMessageId?: string,
  ): Promise<void> {
    const client = this.redisService.getDuplicateClient();
    let lastId = fromMessageId || '0-0';

    // This flag is used to gracefully exit the consumption loops.
    let streamShouldEnd = false;

    const sendMessage = (id: string, fields: string[]): void => {
      // This function now only sends data and sets a flag on terminal events.
      // It no longer manages the response stream itself.
      const payload = this.parseStreamFields(fields);

      res.write(`id: ${id}\n`);
      res.write(`event: ${payload.type}\n`);
      res.write(`data: ${JSON.stringify(payload)}\n\n`);

      this.logger.debug(`-> Sent event ${payload.type} for thread ${threadId}`);

      if (
        ['run_complete', 'run_error', 'run_cancelled'].includes(payload.type)
      ) {
        this.logger.log(
          `🏁 Stream termination event received: ${payload.type}`,
        );
        streamShouldEnd = true;
      }
    };

    try {
      this.logger.log(
        `Starting consumption for ${threadId}. Initial position: ${lastId}`,
      );

      // 1. CATCH-UP READ: Read any messages missed before connecting.
      const catchUpResponse = await client.xread('STREAMS', streamKey, lastId);
      if (catchUpResponse) {
        const [[, messages]] = catchUpResponse;
        this.logger.log(
          `Replaying ${messages.length} unconsumed messages for ${threadId}.`,
        );
        for (const [id, fields] of messages) {
          sendMessage(id, fields);
          lastId = id;
          if (streamShouldEnd) break; // Exit if a terminal event is found
        }
      }
      res.on('close', () => {
        this.logger.warn(
          `📴 Client disconnected from SSE for thread ${threadId}`,
        );
        streamShouldEnd = true;
      });

      // 2. LIVE READ: Block and wait for new messages.
      while (!res.writableEnded && !streamShouldEnd) {
        try {
          const liveResponse = await client.xread(
            'BLOCK',
            5000, // 5-second timeout
            'STREAMS',
            streamKey,
            lastId,
          );

          if (liveResponse) {
            const [[, messages]] = liveResponse;
            for (const [id, fields] of messages) {
              sendMessage(id, fields);
              lastId = id;
              if (streamShouldEnd) break; // Exit inner loop
            }
          }
        } catch (error) {
          this.logger.error(
            `💥 Error during live stream read for ${threadId}:`,
            error.message,
          );
          // On read error, break the loop and allow finally to clean up.
          break;
        }
      }
    } catch (error) {
      // This catches errors from the initial catch-up read or other unexpected issues.
      this.logger.error(
        `💥 Fatal error in stream consumption for ${threadId}:`,
        error.message,
      );
      // Propagate the error to the calling method to be handled there.
      throw error;
    } finally {
      // GUARANTEED CLEANUP: This block ensures the Redis client is always closed.
      try {
        await client.quit();
        this.logger.debug(`✅ Redis client cleaned up for thread ${threadId}`);
      } catch (cleanupError) {
        this.logger.error(
          `⚠️ Error cleaning up Redis client for thread ${threadId}:`,
          cleanupError.message,
        );
      }
      this.logger.log(`🛑 SSE consumption loop ended for thread ${threadId}`);
    }
  }

  /**
   * Parse Redis stream fields to extract worker events
   * Worker stores events as: ['event', '{"type":"text_message_start","timestamp":...}']
   */
  private parseStreamFields(fields: string[]): Record<string, any> {
    for (let i = 0; i < fields.length; i += 2) {
      const key = fields[i];
      const value = fields[i + 1];

      if (key === 'event') {
        try {
          return JSON.parse(value);
        } catch (error) {
          this.logger.error('Failed to parse event JSON:', error);
          return { type: 'parse_error', raw: value };
        }
      }
    }

    return { type: 'unknown', fields };
  }

  /**
   * Validate Redis keys format and content using Zod schema
   * Ensures all keys follow expected patterns before job creation
   * 🔒 SECURITY: Prevents malformed keys from reaching workers
   * 🔧 UPDATED: Now validates new runStatusKey pattern
   */
  private validateRedisKeys(
    keys: InAppJobData['keys'],
    context: { threadId: string; runId: string },
  ): void {
    const { threadId, runId } = context;

    const RedisKeysSchema = z
      .object({
        platformThreadId: z.literal(`in_app:${threadId}`),
        runStatusKey: z.literal(`run_status:in_app:${threadId}`),
        streamKey: z.literal(`in_app:agent_stream:${threadId}:${runId}`),
      })
      .refine((keys) => Object.values(keys).every((key) => key.length <= 200), {
        message: 'All keys must be 200 characters or less',
      });

    try {
      RedisKeysSchema.parse(keys);
      this.logger.debug('Redis keys validation passed', {
        threadId,
        runId,
        keys,
      });
    } catch (error) {
      this.logger.error('Redis keys validation failed', {
        threadId,
        runId,
        keys,
        error: error.message,
      });
      throw new AppException(
        CHAT_ERROR_CODES.THREAD_VALIDATION_FAILED,
        `Invalid Redis key format: ${error.message}`,
      );
    }
  }
}

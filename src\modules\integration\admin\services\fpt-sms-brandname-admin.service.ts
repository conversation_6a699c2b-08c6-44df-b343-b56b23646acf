import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';
import { catchError } from 'rxjs/operators';
import {
  TestFptSmsConnectionDto,
  TestFptSmsConnectionResponseDto,
} from '../dto/fpt-sms-brandname-admin.dto';
import { CreateFptSmsBrandnameAdminDto } from '../dto/create-fpt-sms-brandname-admin.dto';
import {
  AccessTokenRequest,
  AccessTokenResponse,
} from '@/shared/services/sms/fpt-sms-brandname.service';
import { IntegrationRepository } from '../../repositories';
import { KeyPairEncryptionService } from '@/shared/services/encryption';
import { OwnedTypeEnum } from '../../enums';
import { FptSmsBrandnameResponseDto } from '../../user/dto/sms';
import { AppException } from '@/common/exceptions';
import { INTEGRATION_ERROR_CODES } from '../../exceptions/integration-error.code';

@Injectable()
export class FptSmsBrandnameAdminService {
  private readonly logger = new Logger(FptSmsBrandnameAdminService.name);
  private readonly apiUrl = 'https://api01.sms.fpt.net';

  constructor(
    private readonly httpService: HttpService,
    private readonly integrationRepository: IntegrationRepository,
    private readonly keyPairEncryptionService: KeyPairEncryptionService,
  ) {}

  /**
   * Tạo session ID ngẫu nhiên (tối đa 32 ký tự)
   */
  private generateSessionId(): string {
    const timestamp = Date.now().toString(36);
    const randomStr = Math.random().toString(36).substring(2);
    const sessionId = `${timestamp}_${randomStr}`;
    return sessionId.length > 32 ? sessionId.substring(0, 32) : sessionId;
  }

  /**
   * Lấy FPT SMS provider từ database
   */
  private async getFptSmsProvider() {
    const provider = await this.integrationRepository.query(`
      SELECT * FROM integration_providers WHERE type = 'SMS_FPT' LIMIT 1
    `);

    if (!provider || provider.length === 0) {
      throw new AppException(
        INTEGRATION_ERROR_CODES.PROVIDER_NOT_FOUND,
        'Không tìm thấy FPT SMS provider trong hệ thống'
      );
    }

    return provider[0];
  }

  /**
   * Chuyển đổi Integration entity sang FptSmsBrandnameResponseDto
   */
  private toResponseDto(integration: any): FptSmsBrandnameResponseDto {
    const metadata = integration.metadata || {};

    return {
      id: integration.id,
      integration_name: integration.integrationName,
      brandName: metadata.brandName || '',
      endpoint: metadata.apiUrl || this.apiUrl,
      createdAt: integration.createdAt,
    };
  }

  /**
   * Tạo FPT SMS integration mới cho admin
   */
  async createFptSmsIntegration(
    createDto: CreateFptSmsBrandnameAdminDto,
    employeeId: number,
  ): Promise<FptSmsBrandnameResponseDto> {
    try {
      this.logger.log(`Tạo FPT SMS integration cho admin bởi employee ${employeeId}`);

      // Lấy provider SMS_FPT
      const provider = await this.getFptSmsProvider();

      // Tách dữ liệu nhạy cảm để mã hóa
      const encryptedConfigData = {
        FPT_SMS_CLIENT_ID: createDto.FPT_SMS_CLIENT_ID,
        FPT_SMS_CLIENT_SECRET: createDto.FPT_SMS_CLIENT_SECRET,
      };

      // Mã hóa dữ liệu nhạy cảm
      const encryptionResult = this.keyPairEncryptionService.encrypt(encryptedConfigData);

      // Tạo metadata cho các trường không nhạy cảm
      const metadata = {
        brandName: createDto.brandName,
        apiUrl: this.apiUrl,
        createdByAdmin: true,
        adminEmployeeId: employeeId,
      };

      // Đây là admin integration, không cần kiểm tra user integration
      const existingIntegration: any = null;

      let savedIntegration: any;

      if (existingIntegration) {
        // Cập nhật integration hiện có
        await this.integrationRepository.update(existingIntegration.id, {
          integrationName: createDto.integration_name,
          encryptedConfig: encryptionResult.encryptedData,
          secretKey: encryptionResult.publicKey,
          metadata: metadata as any,
          employeeId,
        });

        savedIntegration = await this.integrationRepository.findOne({
          where: { id: existingIntegration.id },
        });

        this.logger.log(`Đã cập nhật FPT SMS integration ${existingIntegration.id}`);
      } else {
        // Tạo integration mới cho admin (không có userId)
        const newIntegration = this.integrationRepository.create({
          integrationName: createDto.integration_name,
          typeId: provider.id,
          userId: null, // Admin integration không có userId
          ownedType: OwnedTypeEnum.ADMIN,
          employeeId,
          encryptedConfig: encryptionResult.encryptedData,
          secretKey: encryptionResult.publicKey,
          metadata: metadata as any,
        });

        savedIntegration = await this.integrationRepository.save(newIntegration);
        this.logger.log(`Đã tạo FPT SMS integration mới ${savedIntegration.id}`);
      }

      return this.toResponseDto(savedIntegration);
    } catch (error) {
      this.logger.error(`Lỗi khi tạo FPT SMS integration: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Test kết nối FPT SMS Brandname
   */
  async testConnection(
    testConnectionDto: TestFptSmsConnectionDto,
    adminId: number,
  ): Promise<TestFptSmsConnectionResponseDto> {
    const startTime = Date.now();
    const testedAt = new Date().toISOString();
    const sessionId = testConnectionDto.sessionId || this.generateSessionId();

    this.logger.log(
      `Admin ${adminId} đang test kết nối FPT SMS Brandname với session_id: ${sessionId}`,
    );

    try {
      // Chuẩn bị request để lấy access token
      const tokenRequest: AccessTokenRequest = {
        grant_type: 'client_credentials',
        client_id: testConnectionDto.clientId,
        client_secret: testConnectionDto.clientSecret,
        scope: 'send_brandname send_brandname_otp',
        session_id: sessionId,
      };

      this.logger.debug(
        `Gửi request lấy access token với config: ${JSON.stringify({
          apiUrl: testConnectionDto.apiUrl,
          clientId: testConnectionDto.clientId,
          brandName: testConnectionDto.brandName,
          sessionId,
        })}`,
      );

      // Gọi API lấy access token
      const response = await firstValueFrom(
        this.httpService
          .post<AccessTokenResponse>(
            `${testConnectionDto.apiUrl}/oauth2/token`,
            tokenRequest,
            {
              headers: {
                'Content-Type': 'application/json',
              },
              timeout: 10000, // 10 seconds timeout
            },
          )
          .pipe(
            catchError((error) => {
              this.logger.error(
                `Lỗi khi test kết nối FPT SMS: ${error.message}`,
                error.stack,
              );
              throw error;
            }),
          ),
      );

      const responseTime = Date.now() - startTime;

      // Kiểm tra response có lỗi không
      if (response.data.error) {
        this.logger.warn(
          `Test kết nối thất bại với error code: ${response.data.error}`,
        );
        
        return {
          success: false,
          message: 'Test kết nối FPT SMS Brandname thất bại',
          responseTime,
          errorDetails: {
            errorCode: response.data.error.toString(),
            errorMessage: response.data.error_description || 'Lỗi không xác định từ FPT SMS API',
            httpStatus: response.status,
          },
          configInfo: {
            apiUrl: testConnectionDto.apiUrl,
            clientId: testConnectionDto.clientId,
            brandName: testConnectionDto.brandName,
            sessionId,
          },
          testedAt,
          testedBy: adminId,
        };
      }

      // Test thành công
      this.logger.log(
        `Test kết nối FPT SMS thành công cho admin ${adminId}, response time: ${responseTime}ms`,
      );

      return {
        success: true,
        message: 'Test kết nối FPT SMS Brandname thành công',
        responseTime,
        tokenInfo: {
          access_token: response.data.access_token,
          expires_in: response.data.expires_in,
          token_type: response.data.token_type,
          scope: response.data.scope,
        },
        configInfo: {
          apiUrl: testConnectionDto.apiUrl,
          clientId: testConnectionDto.clientId,
          brandName: testConnectionDto.brandName,
          sessionId,
        },
        testedAt,
        testedBy: adminId,
      };
    } catch (error) {
      const responseTime = Date.now() - startTime;
      
      this.logger.error(
        `Test kết nối FPT SMS thất bại cho admin ${adminId}: ${error.message}`,
        error.stack,
      );

      // Xử lý các loại lỗi khác nhau
      let errorCode = 'UNKNOWN_ERROR';
      let errorMessage = error.message || 'Lỗi không xác định';
      let httpStatus: number | undefined;

      if (error.response) {
        // Lỗi HTTP response
        httpStatus = error.response.status;
        if (error.response.status === 401) {
          errorCode = 'INVALID_CREDENTIALS';
          errorMessage = 'Client ID hoặc Client Secret không hợp lệ';
        } else if (error.response.status === 403) {
          errorCode = 'ACCESS_FORBIDDEN';
          errorMessage = 'Không có quyền truy cập FPT SMS API';
        } else if (error.response.status === 404) {
          errorCode = 'API_NOT_FOUND';
          errorMessage = 'Không tìm thấy endpoint FPT SMS API';
        } else if (error.response.status >= 500) {
          errorCode = 'SERVER_ERROR';
          errorMessage = 'Lỗi server FPT SMS';
        }
        
        // Nếu có error response data
        if (error.response.data?.error_description) {
          errorMessage = error.response.data.error_description;
        }
      } else if (error.code === 'ECONNREFUSED') {
        errorCode = 'CONNECTION_REFUSED';
        errorMessage = 'Không thể kết nối đến FPT SMS API';
      } else if (error.code === 'ENOTFOUND') {
        errorCode = 'DNS_ERROR';
        errorMessage = 'Không thể phân giải tên miền FPT SMS API';
      } else if (error.code === 'ETIMEDOUT') {
        errorCode = 'TIMEOUT';
        errorMessage = 'Timeout khi kết nối đến FPT SMS API';
      }

      return {
        success: false,
        message: 'Test kết nối FPT SMS Brandname thất bại',
        responseTime,
        errorDetails: {
          errorCode,
          errorMessage,
          httpStatus,
        },
        configInfo: {
          apiUrl: testConnectionDto.apiUrl,
          clientId: testConnectionDto.clientId,
          brandName: testConnectionDto.brandName,
          sessionId,
        },
        testedAt,
        testedBy: adminId,
      };
    }
  }
}

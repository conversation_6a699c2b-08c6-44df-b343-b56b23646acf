// Export các service chính từ shared/services
export { EncryptionService } from './encryption.service';
export { S3Service } from './s3.service';
export { RedisService } from './redis.service';
export { RunStatusService } from './run-status.service';
export { RecaptchaService } from './recaptcha.service';
export { PdfEditService } from './pdf/pdf-edit.service';
export { CdnService } from './cdn.service';
export { EmailTrackingService } from './email-tracking.service';

// Export modules
export { ServicesModule } from './services.module';

// Export các module chính (chỉ export module, không export tất cả để tránh xung đột)
export { EmailModule } from './email';
export { SmsModule } from './sms';
export { ZaloModule } from './zalo';
export { ZaloPersonalModule } from './zalo-personal';
export { GoogleApiModule } from './google';
export { PayPalModule } from './paypal';
export { StripeModule } from './stripe';
export { SepayHubModule } from './sepay-hub';
export { MatBaoModule } from './matbao';
export { MarketingAiModule } from './marketing-ai';
// export { TikTokModule } from './tiktok'; // TODO: TikTok module not implemented yet
export { AhamoveService } from './shipment/ahamove';
export { AutomationWebModule } from './automation-web';

// Export các service chính từ sub-modules (chọn lọc để tránh xung đột)
export { EmailService } from './email';
export { SmsService } from './sms';
export { ZaloService } from './zalo';
export { ZaloPersonalService } from './zalo-personal';
export { TelegramService } from './telegram/telegram.service';
export { PayPalService } from './paypal';
export {
  StripeService,
  StripeWebhookService,
  StripeSubscriptionService,
} from './stripe';
export { SepayHubService } from './sepay-hub';
export { MatBaoCAService } from './matbao';
// export { TikTokAuthService, TikTokContentService } from './tiktok'; // TODO: TikTok services not implemented yet
// AhamoveOrderService removed - using AhamoveService directly
export { GHNService } from './shipment/ghn';
export { GHTKService } from './shipment/ghtk';
export { AutomationWebService } from './automation-web';

import { Entity, Column, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn, Index } from 'typeorm';

/**
 * Entity đại diện cho bảng generic_sessions trong cơ sở dữ liệu
 * Bảng lưu trữ thông tin sessions cho trang generic với WebSocket real-time control
 */
@Entity('generic_sessions')
@Index(['sessionId'], { unique: true })
@Index(['userId'])
@Index(['isActive'])
@Index(['lastActivity'])
export class GenericSession {
  /**
   * ID duy nhất của session, dạng UUID
   */
  @PrimaryGeneratedColumn('uuid')
  id: string;

  /**
   * Session ID duy nhất để track từ frontend, dạng string
   */
  @Column({ name: 'session_id', length: 255, unique: true })
  sessionId: string;

  /**
   * ID của user (optional, cho authenticated users)
   */
  @Column({ name: 'user_id', type: 'int', nullable: true })
  userId?: number;

  /**
   * <PERSON><PERSON>u hình trang dạng JSON, bao gồm theme, layout settings, etc.
   */
  @Column({ name: 'page_config', type: 'jsonb', default: '{}' })
  pageConfig: Record<string, any>;

  /**
   * Trạng thái active của session
   */
  @Column({ name: 'is_active', type: 'boolean', default: true })
  isActive: boolean;

  /**
   * Thời gian hoạt động cuối cùng của session
   */
  @Column({ name: 'last_activity', type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  lastActivity: Date;

  /**
   * Thời gian tạo session
   */
  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  /**
   * Thời gian cập nhật session
   */
  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  /**
   * Metadata bổ sung cho session (IP, User Agent, etc.)
   */
  @Column({ type: 'jsonb', nullable: true })
  metadata?: Record<string, any>;

  /**
   * Thời gian hết hạn của session (optional)
   */
  @Column({ name: 'expires_at', type: 'timestamp', nullable: true })
  expiresAt?: Date;
}

import { Injectable, Logger } from '@nestjs/common';

/**
 * Enum định nghĩa trạng thái gửi tin QC
 */
export enum FptQcCampaignStatus {
  ALLOWED = 'ALLOWED',
  BLOCKED_TIME = 'BLOCKED_TIME',
  BLOCKED_WEEKEND_SUBMISSION = 'BLOCKED_WEEKEND_SUBMISSION',
  INSUFFICIENT_RECIPIENTS = 'INSUFFICIENT_RECIPIENTS'
}

/**
 * Enum định nghĩa các nhà mạng
 */
export enum TelcoProvider {
  MOBIFONE = 'MOBIFONE',
  VINAPHONE = 'VINAPHONE',
  VIETTEL = 'VIETTEL',
  VNM_GTEL = 'VNM_GTEL'
}

/**
 * Interface cho khung giờ gửi tin
 */
interface TimeSlot {
  start: number;
  end: number;
}

/**
 * Interface cho quy định khung giờ gửi tin của từng nhà mạng
 */
interface TelcoSendTimeRules {
  morning: TimeSlot;
  afternoon?: TimeSlot;
  evening?: TimeSlot;
}

/**
 * Interface cho kết quả kiểm tra rule gửi tin QC
 */
export interface FptQcCampaignRuleResult {
  status: FptQcCampaignStatus;
  message: string;
  allowedSendTime?: Date;
  nextAllowedSubmissionTime?: Date;
  suggestedMinRecipients?: number;
}

/**
 * Service xử lý các rule gửi tin nhắn quảng cáo (QC) cho FPT SMS
 */
@Injectable()
export class FptQcCampaignRuleService {
  private readonly logger = new Logger(FptQcCampaignRuleService.name);

  // Quy định số ký tự cho từng nhà mạng
  private readonly CHARACTER_LIMITS = {
    [TelcoProvider.MOBIFONE]: {
      message1: 122,
      message2: { min: 123, max: 268 },
      message3: { min: 269, max: 421 }
    },
    [TelcoProvider.VINAPHONE]: {
      message1: 122,
      message2: { min: 123, max: 268 },
      message3: { min: 269, max: 421 }
    },
    [TelcoProvider.VIETTEL]: {
      message1: 160,
      message2: { min: 161, max: 306 },
      message3: { min: 307, max: 444 }
    },
    [TelcoProvider.VNM_GTEL]: {
      message1: 122,
      message2: { min: 123, max: 268 },
      message3: { min: 269, max: 421 }
    }
  };

  // Quy định khung giờ gửi tin cho từng nhà mạng
  private readonly SEND_TIME_RULES: Record<TelcoProvider, TelcoSendTimeRules> = {
    [TelcoProvider.MOBIFONE]: {
      morning: { start: 8 * 60, end: 11 * 60 + 30 }, // 8:00-11:30
      afternoon: { start: 13 * 60 + 30, end: 20 * 60 }, // 13:30-20:00
      evening: { start: 20 * 60, end: 21 * 60 } // 20:00-21:00
    },
    [TelcoProvider.VINAPHONE]: {
      morning: { start: 8 * 60, end: 11 * 60 + 30 }, // 8:00-11:30
      afternoon: { start: 13 * 60 + 30, end: 18 * 60 + 30 } // 13:30-18:30
    },
    [TelcoProvider.VIETTEL]: {
      morning: { start: 8 * 60, end: 11 * 60 + 30 }, // 8:00-11:30
      afternoon: { start: 13 * 60 + 30, end: 20 * 60 } // 13:30-20:00
    },
    [TelcoProvider.VNM_GTEL]: {
      morning: { start: 8 * 60, end: 21 * 60 } // 8:00-21:00
    }
  };

  // Số lượng thuê bao tối thiểu cho 1 đơn hàng QC
  private readonly MIN_RECIPIENTS_PER_TELCO = 150;

  /**
   * Kiểm tra số ký tự tin nhắn có phù hợp với quy định nhà mạng không
   * @param message Nội dung tin nhắn
   * @param telco Nhà mạng
   * @returns Kết quả kiểm tra
   */
  checkMessageLength(message: string, telco: TelcoProvider): FptQcCampaignRuleResult {
    const messageLength = message.length;
    const limits = this.CHARACTER_LIMITS[telco];

    if (messageLength <= limits.message1) {
      return {
        status: FptQcCampaignStatus.ALLOWED,
        message: `Tin nhắn 1 segment (${messageLength}/${limits.message1} ký tự)`
      };
    }

    if (messageLength >= limits.message2.min && messageLength <= limits.message2.max) {
      return {
        status: FptQcCampaignStatus.ALLOWED,
        message: `Tin nhắn 2 segments (${messageLength}/${limits.message2.max} ký tự)`
      };
    }

    if (messageLength >= limits.message3.min && messageLength <= limits.message3.max) {
      return {
        status: FptQcCampaignStatus.ALLOWED,
        message: `Tin nhắn 3 segments (${messageLength}/${limits.message3.max} ký tự)`
      };
    }

    return {
      status: FptQcCampaignStatus.BLOCKED_TIME,
      message: `Tin nhắn vượt quá giới hạn ký tự cho ${telco}. Tối đa ${limits.message3.max} ký tự.`
    };
  }

  /**
   * Kiểm tra thời gian có được phép gửi tin QC không theo từng nhà mạng
   * @param sendTime Thời gian dự định gửi tin
   * @param telco Nhà mạng (mặc định kiểm tra chung)
   * @returns Kết quả kiểm tra
   */
  checkSendTimeRule(sendTime: Date, telco?: TelcoProvider): FptQcCampaignRuleResult {
    const hour = sendTime.getHours();
    const minute = sendTime.getMinutes();
    const timeInMinutes = hour * 60 + minute;

    // Nếu không chỉ định nhà mạng, kiểm tra theo rule chung (strictest)
    if (!telco) {
      return this.checkGeneralSendTimeRule(sendTime);
    }

    const rules = this.SEND_TIME_RULES[telco];
    let isAllowed = false;
    const allowedSlots: string[] = [];

    // Kiểm tra khung giờ sáng
    if (rules.morning && timeInMinutes >= rules.morning.start && timeInMinutes <= rules.morning.end) {
      isAllowed = true;
    } else if (rules.morning) {
      allowedSlots.push(`${this.formatTime(rules.morning.start)}-${this.formatTime(rules.morning.end)}`);
    }

    // Kiểm tra khung giờ chiều
    if (rules.afternoon && timeInMinutes >= rules.afternoon.start && timeInMinutes <= rules.afternoon.end) {
      isAllowed = true;
    } else if (rules.afternoon) {
      allowedSlots.push(`${this.formatTime(rules.afternoon.start)}-${this.formatTime(rules.afternoon.end)}`);
    }

    // Kiểm tra khung giờ tối (chỉ Mobifone)
    if (rules.evening && timeInMinutes >= rules.evening.start && timeInMinutes <= rules.evening.end) {
      isAllowed = true;
    } else if (rules.evening) {
      allowedSlots.push(`${this.formatTime(rules.evening.start)}-${this.formatTime(rules.evening.end)}`);
    }

    if (isAllowed) {
      return {
        status: FptQcCampaignStatus.ALLOWED,
        message: `Thời gian gửi tin QC hợp lệ cho ${telco}`,
        allowedSendTime: sendTime
      };
    }

    const nextAllowedTime = this.calculateNextAllowedSendTime(sendTime, telco);

    return {
      status: FptQcCampaignStatus.BLOCKED_TIME,
      message: `Ngoài khung giờ được phép gửi tin QC cho ${telco}. Khung giờ cho phép: ${allowedSlots.join(', ')}`,
      allowedSendTime: nextAllowedTime
    };
  }

  /**
   * Kiểm tra số lượng thuê bao có đủ tối thiểu không
   * @param recipientCount Số lượng thuê bao
   * @param telcoBreakdown Phân bổ theo nhà mạng (optional)
   * @returns Kết quả kiểm tra
   */
  checkRecipientCountRule(recipientCount: number, telcoBreakdown?: Record<TelcoProvider, number>): FptQcCampaignRuleResult {
    if (telcoBreakdown) {
      // Kiểm tra từng nhà mạng
      for (const [telco, count] of Object.entries(telcoBreakdown)) {
        if (count > 0 && count < this.MIN_RECIPIENTS_PER_TELCO) {
          return {
            status: FptQcCampaignStatus.INSUFFICIENT_RECIPIENTS,
            message: `Số lượng thuê bao ${telco} không đủ tối thiểu (${count}/${this.MIN_RECIPIENTS_PER_TELCO}). Đề xuất sử dụng dịch vụ chăm sóc khách hàng.`,
            suggestedMinRecipients: this.MIN_RECIPIENTS_PER_TELCO
          };
        }
      }
    } else {
      // Kiểm tra tổng số
      if (recipientCount < this.MIN_RECIPIENTS_PER_TELCO) {
        return {
          status: FptQcCampaignStatus.INSUFFICIENT_RECIPIENTS,
          message: `Số lượng thuê bao không đủ tối thiểu (${recipientCount}/${this.MIN_RECIPIENTS_PER_TELCO}). Đề xuất sử dụng dịch vụ chăm sóc khách hàng.`,
          suggestedMinRecipients: this.MIN_RECIPIENTS_PER_TELCO
        };
      }
    }

    return {
      status: FptQcCampaignStatus.ALLOWED,
      message: 'Số lượng thuê bao đạt yêu cầu tối thiểu'
    };
  }

  /**
   * Kiểm tra thời gian có được phép tiếp nhận đơn hàng không
   * Thời gian tiếp nhận: 08h30 đến 16h30 các ngày làm việc (thứ 2 - thứ 6)
   * Thời gian duyệt: 2 giờ
   * @param submissionTime Thời gian nộp đơn hàng
   * @returns Kết quả kiểm tra
   */
  checkSubmissionTimeRule(submissionTime: Date): FptQcCampaignRuleResult {
    const dayOfWeek = submissionTime.getDay(); // 0 = Chủ nhật, 1 = Thứ 2, ..., 6 = Thứ 7
    const hour = submissionTime.getHours();
    const minute = submissionTime.getMinutes();
    const timeInMinutes = hour * 60 + minute;

    // Kiểm tra ngày làm việc (thứ 2 - thứ 6)
    const isWeekday = dayOfWeek >= 1 && dayOfWeek <= 5;

    if (!isWeekday) {
      const nextMonday = this.getNextMonday(submissionTime);
      nextMonday.setHours(8, 30, 0, 0);

      return {
        status: FptQcCampaignStatus.BLOCKED_WEEKEND_SUBMISSION,
        message: 'Chỉ tiếp nhận đơn hàng vào các ngày làm việc (thứ 2 - thứ 6)',
        nextAllowedSubmissionTime: nextMonday
      };
    }

    // Khung giờ tiếp nhận: 08:30 - 16:30
    const submissionStart = 8 * 60 + 30; // 08:30
    const submissionEnd = 16 * 60 + 30; // 16:30

    const isInSubmissionTime = timeInMinutes >= submissionStart && timeInMinutes <= submissionEnd;

    if (isInSubmissionTime) {
      return {
        status: FptQcCampaignStatus.ALLOWED,
        message: 'Thời gian tiếp nhận đơn hàng hợp lệ',
        allowedSendTime: submissionTime
      };
    }

    // Tính thời gian tiếp nhận tiếp theo
    const nextAllowedSubmission = this.calculateNextAllowedSubmissionTime(submissionTime);

    return {
      status: FptQcCampaignStatus.BLOCKED_TIME,
      message: 'Ngoài giờ tiếp nhận đơn hàng. Giờ tiếp nhận: 08:30-16:30 các ngày làm việc. Thời gian duyệt: 2 giờ.',
      nextAllowedSubmissionTime: nextAllowedSubmission
    };
  }

  /**
   * Kiểm tra toàn bộ rule cho việc gửi tin QC
   * @param submissionTime Thời gian nộp đơn hàng
   * @param sendTime Thời gian dự định gửi tin
   * @param recipientCount Số lượng thuê bao
   * @param message Nội dung tin nhắn (optional)
   * @param telco Nhà mạng (optional)
   * @param telcoBreakdown Phân bổ theo nhà mạng (optional)
   * @returns Kết quả kiểm tra tổng hợp
   */
  validateQcCampaignRules(
    submissionTime: Date, 
    sendTime: Date, 
    recipientCount: number,
    message?: string,
    telco?: TelcoProvider,
    telcoBreakdown?: Record<TelcoProvider, number>
  ): FptQcCampaignRuleResult {
    // Kiểm tra rule tiếp nhận đơn hàng trước
    const submissionResult = this.checkSubmissionTimeRule(submissionTime);
    if (submissionResult.status !== FptQcCampaignStatus.ALLOWED) {
      return submissionResult;
    }

    // Kiểm tra số lượng thuê bao
    const recipientResult = this.checkRecipientCountRule(recipientCount, telcoBreakdown);
    if (recipientResult.status !== FptQcCampaignStatus.ALLOWED) {
      return recipientResult;
    }

    // Kiểm tra độ dài tin nhắn nếu có
    if (message && telco) {
      const messageLengthResult = this.checkMessageLength(message, telco);
      if (messageLengthResult.status !== FptQcCampaignStatus.ALLOWED) {
        return messageLengthResult;
      }
    }

    // Kiểm tra rule thời gian gửi tin
    const sendTimeResult = this.checkSendTimeRule(sendTime, telco);
    return sendTimeResult;
  }

  /**
   * Kiểm tra thời gian gửi tin theo rule chung (strictest - áp dụng cho tất cả nhà mạng)
   */
  private checkGeneralSendTimeRule(sendTime: Date): FptQcCampaignRuleResult {
    const hour = sendTime.getHours();
    const minute = sendTime.getMinutes();
    const timeInMinutes = hour * 60 + minute;

    // Rule chung: 8:00-11:30 và 13:30-18:30 (strictest)
    const morningStart = 8 * 60; // 08:00
    const morningEnd = 11 * 60 + 30; // 11:30
    const afternoonStart = 13 * 60 + 30; // 13:30
    const afternoonEnd = 18 * 60 + 30; // 18:30

    const isInMorningSlot = timeInMinutes >= morningStart && timeInMinutes <= morningEnd;
    const isInAfternoonSlot = timeInMinutes >= afternoonStart && timeInMinutes <= afternoonEnd;

    if (isInMorningSlot || isInAfternoonSlot) {
      return {
        status: FptQcCampaignStatus.ALLOWED,
        message: 'Thời gian gửi tin QC hợp lệ (rule chung)',
        allowedSendTime: sendTime
      };
    }

    const nextAllowedTime = this.calculateNextAllowedSendTime(sendTime);

    return {
      status: FptQcCampaignStatus.BLOCKED_TIME,
      message: 'Ngoài khung giờ được phép gửi tin QC. Khung giờ chung: 08:00-11:30 và 13:30-18:30',
      allowedSendTime: nextAllowedTime
    };
  }

  /**
   * Format thời gian từ phút sang HH:MM
   */
  private formatTime(minutes: number): string {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`;
  }

  /**
   * Tính thời gian gửi tin tiếp theo được phép
   */
  private calculateNextAllowedSendTime(currentTime: Date, telco?: TelcoProvider): Date {
    const nextTime = new Date(currentTime);
    const hour = currentTime.getHours();
    const minute = currentTime.getMinutes();
    const timeInMinutes = hour * 60 + minute;

    if (!telco) {
      // Rule chung
      if (timeInMinutes < 8 * 60) {
        nextTime.setHours(8, 0, 0, 0);
        return nextTime;
      }
      if (timeInMinutes > 11 * 60 + 30 && timeInMinutes < 13 * 60 + 30) {
        nextTime.setHours(13, 30, 0, 0);
        return nextTime;
      }
      if (timeInMinutes > 18 * 60 + 30) {
        nextTime.setDate(nextTime.getDate() + 1);
        nextTime.setHours(8, 0, 0, 0);
        return nextTime;
      }
      return nextTime;
    }

    const rules = this.SEND_TIME_RULES[telco];

    // Tìm slot tiếp theo cho nhà mạng cụ thể
    if (rules.morning && timeInMinutes < rules.morning.start) {
      nextTime.setHours(Math.floor(rules.morning.start / 60), rules.morning.start % 60, 0, 0);
      return nextTime;
    }

    if (rules.afternoon && timeInMinutes < rules.afternoon.start) {
      nextTime.setHours(Math.floor(rules.afternoon.start / 60), rules.afternoon.start % 60, 0, 0);
      return nextTime;
    }

    if (rules.evening && timeInMinutes < rules.evening.start) {
      nextTime.setHours(Math.floor(rules.evening.start / 60), rules.evening.start % 60, 0, 0);
      return nextTime;
    }

    // Nếu đã qua tất cả slot trong ngày, chuyển sang ngày hôm sau
    nextTime.setDate(nextTime.getDate() + 1);
    if (rules.morning) {
      nextTime.setHours(Math.floor(rules.morning.start / 60), rules.morning.start % 60, 0, 0);
    } else {
      nextTime.setHours(8, 0, 0, 0); // Fallback
    }

    return nextTime;
  }

  /**
   * Tính thời gian tiếp nhận đơn hàng tiếp theo được phép
   */
  private calculateNextAllowedSubmissionTime(currentTime: Date): Date {
    const nextTime = new Date(currentTime);
    const dayOfWeek = currentTime.getDay();
    const hour = currentTime.getHours();
    const minute = currentTime.getMinutes();
    const timeInMinutes = hour * 60 + minute;

    // Nếu là ngày làm việc
    if (dayOfWeek >= 1 && dayOfWeek <= 5) {
      // Nếu trước 08:30, chuyển về 08:30 cùng ngày
      if (timeInMinutes < 8 * 60 + 30) {
        nextTime.setHours(8, 30, 0, 0);
        return nextTime;
      }

      // Nếu sau 16:30, chuyển về 08:30 ngày làm việc tiếp theo
      if (timeInMinutes > 16 * 60 + 30) {
        if (dayOfWeek === 5) { // Thứ 6
          nextTime.setDate(nextTime.getDate() + 3); // Chuyển về thứ 2
        } else {
          nextTime.setDate(nextTime.getDate() + 1); // Ngày hôm sau
        }
        nextTime.setHours(8, 30, 0, 0);
        return nextTime;
      }
    } else {
      // Nếu là cuối tuần, chuyển về thứ 2 tuần sau
      const nextMonday = this.getNextMonday(currentTime);
      nextMonday.setHours(8, 30, 0, 0);
      return nextMonday;
    }

    return nextTime;
  }

  /**
   * Lấy ngày thứ 2 tiếp theo
   */
  private getNextMonday(date: Date): Date {
    const nextMonday = new Date(date);
    const dayOfWeek = date.getDay();
    const daysUntilMonday = dayOfWeek === 0 ? 1 : 8 - dayOfWeek; // 0 = Chủ nhật
    nextMonday.setDate(date.getDate() + daysUntilMonday);
    return nextMonday;
  }

  /**
   * Ước tính thời gian duyệt tin nhắn QC (2 giờ theo quy định mới)
   * @param submissionTime Thời gian nộp đơn
   * @returns Thời gian ước tính hoàn thành duyệt
   */
  estimateApprovalTime(submissionTime: Date): { minTime: Date; maxTime: Date } {
    const approvalTime = new Date(submissionTime.getTime() + 2 * 60 * 60 * 1000); // +2 giờ
    
    return { 
      minTime: approvalTime, 
      maxTime: approvalTime // Quy định mới: chính xác 2 giờ
    };
  }

  /**
   * Tính số segment tin nhắn dựa trên độ dài
   * @param messageLength Độ dài tin nhắn
   * @param telco Nhà mạng
   * @returns Số segment
   */
  calculateMessageSegments(messageLength: number, telco: TelcoProvider): number {
    const limits = this.CHARACTER_LIMITS[telco];

    if (messageLength <= limits.message1) return 1;
    if (messageLength <= limits.message2.max) return 2;
    if (messageLength <= limits.message3.max) return 3;
    
    return Math.ceil(messageLength / limits.message3.max); // Vượt quá giới hạn
  }

  /**
   * Lấy thông tin giới hạn ký tự cho nhà mạng
   * @param telco Nhà mạng
   * @returns Thông tin giới hạn
   */
  getCharacterLimits(telco: TelcoProvider) {
    return this.CHARACTER_LIMITS[telco];
  }

  /**
   * Lấy thông tin khung giờ gửi tin cho nhà mạng
   * @param telco Nhà mạng
   * @returns Thông tin khung giờ
   */
  getSendTimeRules(telco: TelcoProvider) {
    return this.SEND_TIME_RULES[telco];
  }

  /**
   * Lấy số lượng thuê bao tối thiểu
   * @returns Số lượng tối thiểu
   */
  getMinRecipientsPerTelco(): number {
    return this.MIN_RECIPIENTS_PER_TELCO;
  }
}

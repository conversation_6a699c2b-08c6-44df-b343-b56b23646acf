import {
  registerDecorator,
  ValidationOptions,
  ValidatorConstraint,
  ValidatorConstraintInterface,
  ValidationArguments,
} from 'class-validator';
import { Injectable } from '@nestjs/common';
import { UserAudienceCustomFieldDefinitionRepository } from '../repositories/user-audience-custom-field-definition.repository';
import { CustomFieldDataType } from '@modules/marketing/common/enums/custom-field-data-type.enum';

/**
 * Validator để kiểm tra tính hợp lệ của custom field
 */
@ValidatorConstraint({ name: 'isValidCustomField', async: true })
@Injectable()
export class IsValidCustomFieldConstraint implements ValidatorConstraintInterface {
  constructor(
    private readonly customFieldDefinitionRepository: UserAudienceCustomFieldDefinitionRepository,
  ) {}

  async validate(value: any, args: ValidationArguments): Promise<boolean> {
    const [userId] = args.constraints;
    const object = args.object as any;
    const fieldId = object.fieldId;
    const fieldValue = object.fieldValue;

    if (!fieldId || !userId) {
      return false;
    }

    try {
      // Kiểm tra field definition có tồn tại không
      const fieldDefinition = await this.customFieldDefinitionRepository.findOne({
        where: { id: fieldId, userId },
      });

      if (!fieldDefinition) {
        return false;
      }

      // Kiểm tra giá trị có phù hợp với kiểu dữ liệu không
      return this.validateFieldValue(fieldValue, fieldDefinition.dataType, fieldDefinition.config);
    } catch (error) {
      return false;
    }
  }

  defaultMessage(args: ValidationArguments): string {
    return 'Trường tùy chỉnh không hợp lệ hoặc không tồn tại';
  }

  /**
   * Kiểm tra giá trị có phù hợp với kiểu dữ liệu không
   */
  private validateFieldValue(value: any, dataType: string, config: Record<string, any>): boolean {
    if (value === null || value === undefined) {
      // Kiểm tra trường có bắt buộc không
      return !config?.required;
    }

    switch (dataType) {
      case CustomFieldDataType.TEXT:
        if (typeof value !== 'string') return false;
        // Kiểm tra độ dài nếu có cấu hình
        if (config?.maxLength && value.length > config.maxLength) return false;
        if (config?.minLength && value.length < config.minLength) return false;
        // Kiểm tra pattern nếu có
        if (config?.pattern && !new RegExp(config.pattern).test(value)) return false;
        return true;

      case CustomFieldDataType.NUMBER:
        if (!Number.isInteger(Number(value))) return false;
        // Kiểm tra min/max nếu có
        const numValue = Number(value);
        if (config?.min !== undefined && numValue < config.min) return false;
        if (config?.max !== undefined && numValue > config.max) return false;
        return true;

      case CustomFieldDataType.DATE:
        // Kiểm tra định dạng ngày
        const date = new Date(value);
        return !isNaN(date.getTime());

      case CustomFieldDataType.BOOLEAN:
        return typeof value === 'boolean' || value === 'true' || value === 'false' || value === 0 || value === 1;

      case CustomFieldDataType.OBJECT:
        try {
          if (typeof value === 'string') {
            JSON.parse(value);
          } 
          return true;
        } catch {
          return typeof value === 'object';
        }

      default:
        return true;
    }
  }
}

/**
 * Decorator để validate custom field
 */
export function IsValidCustomField(userId: () => number, validationOptions?: ValidationOptions) {
  return function (object: object, propertyName: string) {
    registerDecorator({
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      constraints: [userId],
      validator: IsValidCustomFieldConstraint,
    });
  };
}

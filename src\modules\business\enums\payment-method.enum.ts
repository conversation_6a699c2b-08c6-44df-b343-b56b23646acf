/**
 * Enum định nghĩa các phương thức thanh toán
 */
export enum PaymentMethodEnum {
  /**
   * Tiền mặt
   */
  CASH = 'CASH',

  /**
   * Thẻ tín dụng
   */
  CREDIT_CARD = 'CREDIT_CARD',

  /**
   * <PERSON><PERSON>ể<PERSON> khoản
   */
  BANKING = 'BANKING',

  /**
   * <PERSON><PERSON> điện tử
   */
  E_WALLET = 'E_WALLET',

  /**
   * Chuyển khoản ngân hàng
   */
  BANK_TRANSFER = 'BANK_TRANSFER',

  /**
   * <PERSON>h toán khi nhận hàng (COD)
   */
  COD = 'COD',

  /**
   * Trả góp
   */
  INSTALLMENT = 'INSTALLMENT',

  /**
   * Thanh toán QR Code
   */
  QR_CODE = 'QR_CODE',
}

/**
 * Mô tả phương thức thanh toán bằng tiếng Việt
 */
export const PAYMENT_METHOD_DESCRIPTIONS = {
  [PaymentMethodEnum.CASH]: 'Tiền mặt',
  [PaymentMethodEnum.CREDIT_CARD]: 'Thẻ tín dụng',
  [PaymentMethodEnum.BANKING]: 'Chuyển khoản',
  [PaymentMethodEnum.E_WALLET]: 'Ví điện tử',
  [PaymentMethodEnum.BANK_TRANSFER]: 'Chuyển khoản ngân hàng',
  [PaymentMethodEnum.COD]: 'Thanh toán khi nhận hàng',
  [PaymentMethodEnum.INSTALLMENT]: 'Trả góp',
  [PaymentMethodEnum.QR_CODE]: 'Thanh toán QR Code',
};

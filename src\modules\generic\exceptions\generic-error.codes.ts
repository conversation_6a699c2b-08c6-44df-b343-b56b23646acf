import { HttpStatus } from '@nestjs/common';
import { ErrorCode } from '@/common';

/**
 * Mã lỗi cho module Generic (50000-50199)
 */
export const GENERIC_ERROR_CODES = {
  // ===== SESSION ERRORS (50000-50029) =====
  GENERIC_SESSION_NOT_FOUND: new ErrorCode(
    50000,
    'Không tìm thấy phiên làm việc',
    HttpStatus.NOT_FOUND,
  ),

  GENERIC_SESSION_ALREADY_EXISTS: new ErrorCode(
    50001,
    'Phiên làm việc đã tồn tại',
    HttpStatus.CONFLICT,
  ),

  GENERIC_SESSION_CREATE_ERROR: new ErrorCode(
    50002,
    'Lỗi khi tạo phiên làm việc',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  GENERIC_SESSION_UPDATE_ERROR: new ErrorCode(
    50003,
    'Lỗi khi cập nhật phiên làm việc',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  GENERIC_SESSION_DELETE_ERROR: new ErrorCode(
    50004,
    'Lỗi khi xóa phiên làm việc',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  GENERIC_SESSION_QUERY_ERROR: new ErrorCode(
    50005,
    'Lỗi khi truy vấn phiên làm việc',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  GENERIC_SESSION_CHECK_ERROR: new ErrorCode(
    50006,
    'Lỗi khi kiểm tra phiên làm việc',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  GENERIC_SESSION_CLEANUP_ERROR: new ErrorCode(
    50007,
    'Lỗi khi dọn dẹp phiên làm việc',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  // ===== WIDGET ERRORS (50030-50059) =====
  GENERIC_WIDGET_NOT_FOUND: new ErrorCode(
    50030,
    'Không tìm thấy widget',
    HttpStatus.NOT_FOUND,
  ),

  GENERIC_WIDGET_ALREADY_EXISTS: new ErrorCode(
    50031,
    'Widget đã tồn tại',
    HttpStatus.CONFLICT,
  ),

  GENERIC_WIDGET_CREATE_ERROR: new ErrorCode(
    50032,
    'Lỗi khi tạo widget',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  GENERIC_WIDGET_UPDATE_ERROR: new ErrorCode(
    50033,
    'Lỗi khi cập nhật widget',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  GENERIC_WIDGET_DELETE_ERROR: new ErrorCode(
    50034,
    'Lỗi khi xóa widget',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  GENERIC_WIDGET_QUERY_ERROR: new ErrorCode(
    50035,
    'Lỗi khi truy vấn widget',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  GENERIC_WIDGET_CHECK_ERROR: new ErrorCode(
    50036,
    'Lỗi khi kiểm tra widget',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  // ===== LAYOUT ERRORS (50060-50089) =====
  GENERIC_LAYOUT_NOT_FOUND: new ErrorCode(
    50060,
    'Không tìm thấy layout',
    HttpStatus.NOT_FOUND,
  ),

  GENERIC_LAYOUT_CREATE_ERROR: new ErrorCode(
    50061,
    'Lỗi khi tạo layout',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  GENERIC_LAYOUT_UPDATE_ERROR: new ErrorCode(
    50062,
    'Lỗi khi cập nhật layout',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  GENERIC_LAYOUT_DELETE_ERROR: new ErrorCode(
    50063,
    'Lỗi khi xóa layout',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  GENERIC_LAYOUT_QUERY_ERROR: new ErrorCode(
    50064,
    'Lỗi khi truy vấn layout',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  // ===== COLLABORATION ERRORS (50090-50119) =====
  SESSION_SHARE_ERROR: new ErrorCode(
    50090,
    'Lỗi khi chia sẻ phiên làm việc',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  SESSION_UNSHARE_ERROR: new ErrorCode(
    50091,
    'Lỗi khi hủy chia sẻ phiên làm việc',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  GET_ACCESSIBLE_SESSIONS_ERROR: new ErrorCode(
    50092,
    'Lỗi khi lấy danh sách phiên làm việc có thể truy cập',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  // ===== RECOVERY ERRORS (50120-50149) =====
  SESSION_RECOVERY_ERROR: new ErrorCode(
    50120,
    'Lỗi khi khôi phục phiên làm việc',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  FORCE_SYNC_ERROR: new ErrorCode(
    50121,
    'Lỗi khi đồng bộ hóa bắt buộc',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  // ===== WEBSOCKET ERRORS (50150-50179) =====
  WEBSOCKET_CONNECTION_ERROR: new ErrorCode(
    50150,
    'Lỗi kết nối WebSocket',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  WEBSOCKET_MESSAGE_ERROR: new ErrorCode(
    50151,
    'Lỗi xử lý tin nhắn WebSocket',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  // ===== GENERAL ERRORS (50180-50199) =====
  GENERIC_VALIDATION_ERROR: new ErrorCode(
    50180,
    'Lỗi validation dữ liệu',
    HttpStatus.BAD_REQUEST,
  ),

  GENERIC_PERMISSION_DENIED: new ErrorCode(
    50181,
    'Không có quyền truy cập',
    HttpStatus.FORBIDDEN,
  ),

  GENERIC_INTERNAL_ERROR: new ErrorCode(
    50182,
    'Lỗi hệ thống nội bộ',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
};

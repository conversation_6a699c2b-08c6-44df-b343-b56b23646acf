import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AppException, ErrorCode } from '@/common';
import { SubscriptionPaymentRepository } from '../../repositories/subscription-payment.repository';
import { PlanPricingRepository } from '../../repositories/plan-pricing.repository';
import { PackageOptionAddonRepository } from '../../repositories/package-option-addon.repository';
import { PlanRepository } from '../../repositories/plan.repository';
import { AddonRepository } from '../../repositories/addon.repository';
import { SystemConfigSharedService } from '@shared/services/system-config-shared.service';
import { Invoice } from '@modules/r-point/entities';
import { SubscriptionPaymentRedisService } from './subscription-payment-redis.service';
import { SubscriptionCreationService } from '../../services';
import { SubscriptionInvoiceService } from '../../services/subscription-invoice.service';
import { InvoiceRepository } from '@modules/r-point/repositories';
import {
  CreateSubscriptionPaymentDto,
  SubscriptionPaymentResponseDto,
  SubscriptionPaymentStatusDto,
  SubscriptionPaymentDetailWithBankDto,
  PaymentBankInfoDto,
  SubscriptionPaymentHistoryDto,
  SubscriptionPaymentHistoryQueryDto,
} from '../dto/subscription-payment.dto';
import { SubscriptionPaymentInfoDetails } from '../../interfaces';
import { BankService } from '@modules/user/user/service/bank.service';
import { PaginatedResult } from '@common/response/api-response-dto';

/**
 * Service xử lý thanh toán subscription
 */
@Injectable()
export class SubscriptionPaymentService {
  private readonly logger = new Logger(SubscriptionPaymentService.name);

  constructor(
    private readonly subscriptionPaymentRepository: SubscriptionPaymentRepository,
    private readonly planPricingRepository: PlanPricingRepository,
    private readonly packageOptionAddonRepository: PackageOptionAddonRepository,
    private readonly planRepository: PlanRepository,
    private readonly addonRepository: AddonRepository,
    private readonly systemConfigSharedService: SystemConfigSharedService,
    private readonly subscriptionPaymentRedisService: SubscriptionPaymentRedisService,
    private readonly subscriptionCreationService: SubscriptionCreationService,
    private readonly subscriptionInvoiceService: SubscriptionInvoiceService,
    private readonly invoiceRepository: InvoiceRepository,
    private readonly bankService: BankService,
  ) {}

  /**
   * Lấy thông tin đầy đủ của plan pricing và package option addons để lưu vào info_details
   * @param planPricingId ID của plan pricing
   * @returns Thông tin đầy đủ để lưu vào info_details
   */
  private async getPaymentInfoDetails(
    planPricingId: number,
  ): Promise<SubscriptionPaymentInfoDetails> {
    try {
      // 1. Lấy thông tin plan pricing
      const planPricing = await this.planPricingRepository.findOne({
        where: { id: planPricingId },
      });

      if (!planPricing) {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          'Plan pricing không tồn tại',
        );
      }

      // 2. Lấy thông tin plan
      const plan = await this.planRepository.findOne({
        where: { id: planPricing.planId },
      });

      // 3. Lấy tất cả package option addons của plan pricing
      const packageOptionAddons = await this.packageOptionAddonRepository.find({
        where: { planPricingId },
      });

      // 4. Lấy thông tin chi tiết của các addons
      const addonIds = packageOptionAddons.map((poa) => poa.addonId);
      const addons =
        addonIds.length > 0
          ? await this.addonRepository.findByIds(addonIds)
          : [];

      // 5. Tạo map addon để dễ lookup
      const addonMap = new Map(addons.map((addon) => [addon.id, addon]));

      // 6. Tạo cấu trúc dữ liệu info_details
      const infoDetails: SubscriptionPaymentInfoDetails = {
        planPricing: {
          id: planPricing.id,
          planId: planPricing.planId,
          billingCycle: planPricing.billingCycle,
          price: planPricing.price,
          isActive: planPricing.isActive,
          description: planPricing.description,
          currency: planPricing.currency,
          name: planPricing.name,
          plan: plan
            ? {
                id: plan.id,
                name: plan.name,
                description: plan.description,
                type: plan.type,
                isActive: plan.isActive,
              }
            : undefined,
        },
        packageOptionAddons: packageOptionAddons.map((poa) => {
          const addon = addonMap.get(poa.addonId);
          return {
            id: poa.id,
            addonId: poa.addonId,
            unit: poa.unit,
            durationInDays: poa.durationInDays,
            quantity: poa.quantity,
            addon: addon
              ? {
                  id: addon.id,
                  name: addon.name,
                  description: addon.description,
                  billingType: addon.billingType,
                  // monthlyPrice: addon.monthlyPrice,
                  volumeUnit: addon.volumeUnit,
                  // pricePerUnit: addon.pricePerUnit,
                  isActive: addon.isActive,
                  type: addon.type,
                }
              : undefined,
          };
        }),
        capturedAt: Math.floor(Date.now() / 1000),
      };

      return infoDetails;
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy thông tin info_details cho plan pricing ${planPricingId}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Tạo thanh toán cho subscription
   * @param userId ID của user
   * @param createPaymentDto DTO tạo thanh toán
   * @returns Thông tin thanh toán và QR code
   */
  async createPayment(
    userId: number,
    createPaymentDto: CreateSubscriptionPaymentDto,
  ): Promise<SubscriptionPaymentResponseDto> {
    try {
      this.logger.log(
        `Tạo thanh toán subscription cho user ${userId}, plan pricing ${createPaymentDto.planPricingId}`,
      );

      const planPricing = await this.planPricingRepository.findOne({
        where: { id: createPaymentDto.planPricingId },
      });
      if (!planPricing || !planPricing.isActive) {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          'Gói dịch vụ không tồn tại hoặc đã ngừng hoạt động',
        );
      }

      // 3. Tính toán giá cuối cùng (có thể áp dụng coupon sau này)
      // Convert price từ string sang number vì PostgreSQL numeric type trả về string
      const priceAsNumber =
        typeof planPricing.price === 'string'
          ? parseFloat(planPricing.price)
          : planPricing.price;
      const finalAmount = priceAsNumber;
      const originalAmount = priceAsNumber;

      // 4. Lấy thông tin đầy đủ để lưu vào info_details
      this.logger.log(
        `Lấy thông tin info_details cho plan pricing ${createPaymentDto.planPricingId}`,
      );
      const infoDetails = await this.getPaymentInfoDetails(
        createPaymentDto.planPricingId,
      );
      this.logger.log(
        `Đã lấy được info_details: ${JSON.stringify(infoDetails, null, 2)}`,
      );

      // 5. Tạo giao dịch thanh toán
      const now = Math.floor(Date.now() / 1000);
      this.logger.log(`Tạo payment record với data:`, {
        userId,
        planPricingId: createPaymentDto.planPricingId,
        amount: finalAmount,
        originalAmount,
        status: 'PENDING',
        paymentMethod: 'BANK_TRANSFER',
        currency: 'VND',
        description:
          this.systemConfigSharedService.generateDescriptionForSubscription(
            userId.toString(),
          ),
        createdAt: now,
        updatedAt: now,
        infoDetails: !!infoDetails,
      });

      const payment = await this.subscriptionPaymentRepository.create({
        userId,
        amount: finalAmount,
        originalAmount,
        status: 'PENDING',
        paymentMethod: 'BANK_TRANSFER',
        currency: 'VND',
        description: this.systemConfigSharedService.generateDescriptionForSubscription(
          userId.toString(),
        ),
        createdAt: now,
        updatedAt: now,
        infoDetails,
        invoiceInfo: createPaymentDto.invoiceInfo
          ? this.convertInvoiceInfoDtoToInterface(createPaymentDto.invoiceInfo)
          : undefined, // Lưu thông tin hóa đơn vào trường riêng
      });

      this.logger.log(`Đã tạo payment thành công với ID: ${payment.id}`);

      // 6. Tạo QR code thanh toán
      const qrCodeUrl =
        await this.systemConfigSharedService.generateQRPaymentUrlForSubscription(
          finalAmount,
          payment.id.toString(),
        );

      // 6. Lưu thông tin hóa đơn nếu có
      if (createPaymentDto.invoiceInfo) {
        try {
          const invoiceData: any = {
            orderId: payment.id,
            paymentMethod: 'BANK_TRANSFER',
            currency: 'VND',
            productName: `Subscription Plan - ${planPricing.name || 'Plan'}`,
            productDescription: `Thanh toán subscription plan pricing ID: ${createPaymentDto.planPricingId}`,
            quantity: 1,
            unitPrice: finalAmount,
            amount: finalAmount,
            vatRate: 0, // Có thể cấu hình sau
            vatAmount: 0, // Có thể cấu hình sau
            totalAmount: finalAmount,
            invoiceType: createPaymentDto.invoiceInfo.type,
            email: createPaymentDto.invoiceInfo.email,
            createdAt: now,
            updatedAt: now,
          };

          // Xử lý theo loại hóa đơn
          if (createPaymentDto.invoiceInfo.type === 'BUSINESS') {
            // Hóa đơn doanh nghiệp
            invoiceData.buyerFullName =
              createPaymentDto.invoiceInfo.representativeName || '';
            invoiceData.companyName =
              createPaymentDto.invoiceInfo.companyName || '';
            invoiceData.taxCode = createPaymentDto.invoiceInfo.taxCode || '';
            invoiceData.address =
              createPaymentDto.invoiceInfo.companyAddress || '';
            invoiceData.representativeName =
              createPaymentDto.invoiceInfo.representativeName || '';
            invoiceData.representativePosition =
              createPaymentDto.invoiceInfo.representativePosition || '';
          } else if (createPaymentDto.invoiceInfo.type === 'PERSONAL') {
            // Hóa đơn cá nhân
            invoiceData.buyerFullName =
              createPaymentDto.invoiceInfo.fullName || '';
            invoiceData.address = createPaymentDto.invoiceInfo.address || '';

            // Xử lý ngày sinh nếu có
            if (createPaymentDto.invoiceInfo.dateOfBirth) {
              invoiceData.dateOfBirth = new Date(
                createPaymentDto.invoiceInfo.dateOfBirth,
              );
            }

            // Xử lý giới tính nếu có
            if (createPaymentDto.invoiceInfo.gender) {
              invoiceData.gender = createPaymentDto.invoiceInfo.gender;
            }
          }

          await this.invoiceRepository.save(invoiceData);
          this.logger.log(
            `Đã lưu thông tin hóa đơn cho giao dịch subscription ${payment.id}`,
          );
        } catch (invoiceError) {
          this.logger.error(
            `Lỗi khi lưu thông tin hóa đơn cho giao dịch ${payment.id}: ${invoiceError.message}`,
            invoiceError.stack,
          );
          // Không throw error để không ảnh hưởng đến flow chính
        }
      }

      // 7. Lưu thông tin vào Redis
      const paymentStatusData: SubscriptionPaymentStatusDto = {
        id: payment.id,
        userId: payment.userId,
        planPricingId: payment.infoDetails?.planPricing?.id || 0, // Lấy từ infoDetails
        amount:
          typeof payment.amount === 'string'
            ? parseFloat(payment.amount)
            : payment.amount,
        status: payment.status,
        qrCodeUrl,
        description: payment.description || '',
        createdAt: payment.createdAt,
        updatedAt: payment.updatedAt || payment.createdAt,
      };

      await this.subscriptionPaymentRedisService.savePaymentStatus(
        payment.id,
        paymentStatusData,
      );

      this.logger.log(
        `Đã tạo thanh toán subscription thành công: ${payment.id}`,
      );

      return {
        transactionId: payment.id,
        planPricingId: payment.infoDetails?.planPricing?.id || 0, // Lấy từ infoDetails
        amount:
          typeof payment.amount === 'string'
            ? parseFloat(payment.amount)
            : payment.amount,
        originalAmount:
          typeof payment.originalAmount === 'string'
            ? parseFloat(payment.originalAmount)
            : payment.originalAmount,
        qrCodeUrl,
        status: payment.status,
        description: payment.description || '',
        createdAt: payment.createdAt,
        updatedAt: payment.updatedAt || payment.createdAt,
      };
    } catch (error) {
      this.logger.error(
        `Lỗi khi tạo thanh toán subscription: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Lấy trạng thái thanh toán
   * @param userId ID của user
   * @param paymentId ID của thanh toán
   * @returns Trạng thái thanh toán
   */
  async getPaymentStatus(
    userId: number,
    paymentId: number,
  ): Promise<SubscriptionPaymentStatusDto> {
    try {
      // 1. Thử lấy từ Redis trước
      const cachedStatus =
        await this.subscriptionPaymentRedisService.getPaymentStatus(paymentId);
      if (cachedStatus && cachedStatus.userId === userId) {
        return cachedStatus;
      }

      // 2. Nếu không có trong Redis, lấy từ database
      const payment =
        await this.subscriptionPaymentRepository.findByIdAndUserId(
          paymentId,
          userId,
        );
      if (!payment) {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          'Giao dịch không tồn tại',
        );
      }

      // 3. Tạo lại QR code URL
      // Convert amount từ string sang number nếu cần
      const amountAsNumber =
        typeof payment.amount === 'string'
          ? parseFloat(payment.amount)
          : payment.amount;
      const qrCodeUrl =
        await this.systemConfigSharedService.generateQRPaymentUrl(
          amountAsNumber,
          payment.id.toString(),
        );

      const paymentStatus: SubscriptionPaymentStatusDto = {
        id: payment.id,
        userId: payment.userId,
        planPricingId: payment.infoDetails?.planPricing?.id || 0, // Lấy từ infoDetails
        amount:
          typeof payment.amount === 'string'
            ? parseFloat(payment.amount)
            : payment.amount,
        status: payment.status,
        qrCodeUrl,
        description: payment.description || '',
        createdAt: payment.createdAt,
        updatedAt: payment.updatedAt || payment.createdAt,
        invoiceStatus: payment.invoiceStatus, // Thêm trạng thái xuất hóa đơn
      };

      // 4. Lưu lại vào Redis nếu giao dịch vẫn đang chờ
      if (payment.status === 'PENDING') {
        await this.subscriptionPaymentRedisService.savePaymentStatus(
          payment.id,
          paymentStatus,
        );
      }

      return paymentStatus;
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy trạng thái thanh toán: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Lấy chi tiết đơn hàng subscription payment với thông tin ngân hàng đầy đủ
   * @param userId ID của user
   * @param paymentId ID của thanh toán
   * @returns Chi tiết đơn hàng với thông tin ngân hàng
   */
  async getPaymentDetail(
    userId: number,
    paymentId: number,
  ): Promise<SubscriptionPaymentDetailWithBankDto> {
    try {
      this.logger.log(
        `Lấy chi tiết đơn hàng: ${paymentId} cho user: ${userId}`,
      );

      // 1. Lấy thông tin payment từ database
      const payment =
        await this.subscriptionPaymentRepository.findByIdAndUserId(
          paymentId,
          userId,
        );

      if (!payment) {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          'Đơn hàng không tồn tại hoặc không thuộc về bạn',
        );
      }

      // 2. Lấy thông tin cấu hình hệ thống để có bankCode và accountNumber
      const systemConfig =
        await this.systemConfigSharedService.getActiveConfiguration();

      if (
        !systemConfig.bankCode ||
        !systemConfig.accountNumber ||
        !systemConfig.accountName
      ) {
        throw new AppException(
          ErrorCode.INTERNAL_SERVER_ERROR,
          'Thiếu thông tin ngân hàng trong cấu hình hệ thống',
        );
      }

      // 3. Lấy thông tin ngân hàng từ Bank entity
      const bankInfo = await this.bankService.findByCode(systemConfig.bankCode);

      if (!bankInfo) {
        throw new AppException(
          ErrorCode.INTERNAL_SERVER_ERROR,
          `Không tìm thấy thông tin ngân hàng với mã: ${systemConfig.bankCode}`,
        );
      }

      // 4. Tạo lại QR code URL
      const amountAsNumber =
        typeof payment.amount === 'string'
          ? parseFloat(payment.amount)
          : payment.amount;

      const qrCodeUrl =
        await this.systemConfigSharedService.generateQRPaymentUrlForSubscription(
          amountAsNumber,
          payment.id.toString(),
        );

      // 5. Tạo PaymentBankInfoDto
      const paymentBankInfo: PaymentBankInfoDto = {
        bankCode: systemConfig.bankCode,
        bankName: bankInfo.bankName,
        accountNumber: systemConfig.accountNumber,
        accountName: systemConfig.accountName,
        logoPath: bankInfo.logoPath,
      };

      // 6. Tạo SubscriptionPaymentDetailWithBankDto
      const paymentDetail: SubscriptionPaymentDetailWithBankDto = {
        id: payment.id,
        userId: payment.userId,
        planPricingId: payment.infoDetails?.planPricing?.id || 0, // Lấy từ infoDetails
        amount:
          typeof payment.amount === 'string'
            ? parseFloat(payment.amount)
            : payment.amount,
        originalAmount:
          typeof payment.originalAmount === 'string'
            ? parseFloat(payment.originalAmount)
            : payment.originalAmount,
        status: payment.status,
        paymentMethod: payment.paymentMethod,
        currency: payment.currency,
        description: payment.description || '',
        qrCodeUrl,
        bankInfo: paymentBankInfo,
        createdAt: payment.createdAt,
        updatedAt: payment.updatedAt || payment.createdAt,
        referenceCode: payment.referenceCode,
        confirmedAt: payment.confirmedAt,
        failureReason: payment.failureReason,
        couponId: payment.couponId,
        infoDetails: payment.infoDetails, // Thêm thông tin chi tiết plan pricing và addons
        invoiceStatus: payment.invoiceStatus, // Thêm trạng thái xuất hóa đơn
      };

      this.logger.log(`Đã lấy chi tiết đơn hàng thành công: ${paymentId}`);
      return paymentDetail;
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy chi tiết đơn hàng: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Xử lý xác nhận thanh toán từ webhook
   * @param paymentId ID của thanh toán
   * @param referenceCode Mã tham chiếu từ ngân hàng
   * @returns Thanh toán đã được cập nhật
   */
  async confirmPayment(
    paymentId: number,
    referenceCode?: string,
  ): Promise<void> {
    try {
      this.logger.log(`Xác nhận thanh toán subscription: ${paymentId}`);

      // 1. Cập nhật trạng thái trong database
      const updatedPayment =
        await this.subscriptionPaymentRepository.updateStatus(
          paymentId,
          'CONFIRMED',
          { referenceCode },
        );

      if (!updatedPayment) {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          'Không thể cập nhật trạng thái thanh toán',
        );
      }

      // 2. Tạo QR code URL cho Redis (để đảm bảo tính nhất quán)
      const qrCodeUrl =
        await this.systemConfigSharedService.generateQRPaymentUrl(
          updatedPayment.amount,
          updatedPayment.id.toString(),
        );

      // 3. Cập nhật trạng thái trong Redis với đầy đủ thông tin
      await this.subscriptionPaymentRedisService.updatePaymentStatus(
        paymentId,
        'CONFIRMED',
        {
          referenceCode,
          qrCodeUrl,
          updatedAt: Math.floor(Date.now() / 1000),
        },
      );

      // 3.1. Xóa payment khỏi Redis sau khi CONFIRMED (tùy chọn - có thể giữ lại để tracking)
      // await this.subscriptionPaymentRedisService.deletePaymentStatus(paymentId);

      // 4. Tạo subscription cho user
      try {
        const subscriptionResult =
          await this.subscriptionCreationService.createSubscriptionFromPayment(
            updatedPayment,
          );
        this.logger.log(
          `Đã tạo subscription cho user ${updatedPayment.userId}: ${subscriptionResult.userAddonUsages.length} addon usages, total capacity: ${subscriptionResult.totalCapacityAdded}`,
        );
        this.logger.debug(
          `Payment info: ${JSON.stringify(subscriptionResult.paymentInfo)}`,
        );
      } catch (subscriptionError) {
        this.logger.error(
          `Lỗi khi tạo subscription cho payment ${paymentId}: ${subscriptionError.message}`,
          subscriptionError.stack,
        );
        // Không throw error để không ảnh hưởng đến việc xác nhận thanh toán
        // Có thể implement retry mechanism hoặc manual intervention
      }

      this.logger.log(
        `Đã xác nhận thanh toán subscription thành công: ${paymentId}`,
      );
    } catch (error) {
      this.logger.error(
        `Lỗi khi xác nhận thanh toán subscription: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Lấy danh sách lịch sử thanh toán subscription của user
   * @param userId ID của user
   * @param queryDto Tham số phân trang và tìm kiếm
   * @returns Kết quả phân trang lịch sử thanh toán
   */
  async getPaymentHistory(
    userId: number,
    queryDto: SubscriptionPaymentHistoryQueryDto,
  ): Promise<PaginatedResult<SubscriptionPaymentHistoryDto>> {
    try {
      this.logger.log(`Lấy lịch sử thanh toán cho user: ${userId}`);

      // 1. Lấy dữ liệu từ repository với phân trang
      const result =
        await this.subscriptionPaymentRepository.findPaymentHistoryByUser(
          userId,
          queryDto,
          queryDto.status,
        );

      // 2. Chuyển đổi entity thành DTO
      const historyDtos: SubscriptionPaymentHistoryDto[] = result.items.map(
        (payment) => ({
          id: payment.id,
          userId: payment.userId,
          amount:
            typeof payment.amount === 'string'
              ? parseFloat(payment.amount)
              : payment.amount,
          originalAmount:
            typeof payment.originalAmount === 'string'
              ? parseFloat(payment.originalAmount)
              : payment.originalAmount,
          status: payment.status,
          paymentMethod: payment.paymentMethod,
          currency: payment.currency,
          description: payment.description || '',
          createdAt: payment.createdAt,
          updatedAt: payment.updatedAt || payment.createdAt,
          referenceCode: payment.referenceCode,
          confirmedAt: payment.confirmedAt,
          failureReason: payment.failureReason,
          couponId: payment.couponId,
          infoDetails: payment.infoDetails,
          invoiceStatus: payment.invoiceStatus, // Thêm trạng thái xuất hóa đơn
        }),
      );

      // 3. Trả về kết quả với metadata phân trang
      return {
        items: historyDtos,
        meta: result.meta,
      };
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy lịch sử thanh toán cho user ${userId}: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Lỗi khi lấy lịch sử thanh toán',
      );
    }
  }

  /**
   * Convert InvoiceInfoDto to PaymentInvoiceInfo interface
   * @param dto InvoiceInfoDto from request
   * @returns PaymentInvoiceInfo interface
   */
  private convertInvoiceInfoDtoToInterface(dto: any): any {
    return {
      type: dto.type,
      email: dto.email || '', // Đảm bảo email không undefined
      companyName: dto.companyName,
      taxCode: dto.taxCode,
      companyAddress: dto.companyAddress,
      representativeName: dto.representativeName,
      representativePosition: dto.representativePosition,
      fullName: dto.fullName,
      address: dto.address,
      phoneNumber: dto.phoneNumber,
      dateOfBirth: dto.dateOfBirth,
      gender: dto.gender,
    };
  }
}

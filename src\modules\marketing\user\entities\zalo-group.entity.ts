import {
  <PERSON>ti<PERSON>,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  JoinColumn,
  OneToMany,
  Index,
  BeforeUpdate,
} from 'typeorm';
import { User } from '@modules/user/entities';
// ZaloOfficialAccount đã được migrate sang Integration entity
import { ZaloGroupMember } from './zalo-group-member.entity';

export enum ZaloGroupStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  DELETED = 'deleted',
}

@Entity('zalo_groups')
@Index(['userId', 'zaloOfficialAccountId'])
@Index(['integrationId'])
@Index(['groupId'])
@Index(['status'])
export class ZaloGroup {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'user_id', type: 'int' })
  userId: number;

  @Column({ name: 'zalo_official_account_id', type: 'uuid' })
  zaloOfficialAccountId: string;

  /**
   * ID của integration (UUID) - l<PERSON><PERSON><PERSON> kết với bảng integrations
   * <PERSON><PERSON> thế cho zalo_official_account_id trong hệ thống integration mới
   */
  @Column({ name: 'integration_id', type: 'uuid', nullable: true })
  integrationId: string;

  @Column({ name: 'group_id', type: 'varchar', length: 255 })
  groupId: string;

  @Column({ name: 'group_name', type: 'varchar', length: 255 })
  groupName: string;

  @Column({ name: 'description', type: 'text', nullable: true })
  description: string;

  @Column({ name: 'avatar_url', type: 'varchar', length: 500, nullable: true })
  avatarUrl: string;

  @Column({ name: 'member_count', type: 'int', default: 0 })
  memberCount: number;

  @Column({ name: 'admin_count', type: 'int', default: 1 })
  adminCount: number;

  @Column({
    name: 'status',
    type: 'varchar',
    length: 50,
    enum: ZaloGroupStatus,
    default: ZaloGroupStatus.ACTIVE,
  })
  status: ZaloGroupStatus;

  @Column({ name: 'is_oa_admin', type: 'boolean', default: true })
  isOaAdmin: boolean;

  @Column({ name: 'created_by_oa', type: 'boolean', default: false })
  createdByOa: boolean;

  @Column({ name: 'last_activity_at', type: 'bigint', nullable: true })
  lastActivityAt: number;

  @Column({ name: 'metadata', type: 'jsonb', nullable: true })
  metadata: Record<string, any>;

  // === Thông tin từ Zalo API group_info ===
  @Column({ name: 'group_link', type: 'varchar', length: 500, nullable: true })
  groupLink: string;

  @Column({ name: 'zalo_status', type: 'varchar', length: 50, nullable: true })
  zaloStatus: string; // enabled/disabled

  @Column({ name: 'total_member', type: 'int', nullable: true })
  totalMember: number;

  @Column({ name: 'max_member', type: 'varchar', length: 10, nullable: true })
  maxMember: string;

  @Column({
    name: 'auto_delete_date',
    type: 'varchar',
    length: 20,
    nullable: true,
  })
  autoDeleteDate: string;

  // === Thông tin từ Zalo API asset_info ===
  @Column({ name: 'asset_type', type: 'varchar', length: 20, nullable: true })
  assetType: string; // gmf10/gmf50/gmf100

  @Column({ name: 'asset_id', type: 'varchar', length: 100, nullable: true })
  assetId: string;

  @Column({
    name: 'valid_through',
    type: 'varchar',
    length: 20,
    nullable: true,
  })
  validThrough: string;

  @Column({ name: 'auto_renew', type: 'boolean', nullable: true })
  autoRenew: boolean;

  // === Thông tin từ Zalo API group_setting ===
  @Column({ name: 'lock_send_msg', type: 'boolean', nullable: true })
  lockSendMsg: boolean;

  @Column({ name: 'join_appr', type: 'boolean', nullable: true })
  joinAppr: boolean;

  @Column({ name: 'enable_msg_history', type: 'boolean', nullable: true })
  enableMsgHistory: boolean;

  @Column({ name: 'enable_link_join', type: 'boolean', nullable: true })
  enableLinkJoin: boolean;

  // === Thông tin đồng bộ ===
  @Column({ name: 'last_sync_at', type: 'bigint', nullable: true })
  lastSyncAt: number;

  @Column({ name: 'created_at', type: 'bigint' })
  createdAt: number;

  @Column({ name: 'updated_at', type: 'bigint' })
  updatedAt: number;

  // Relations
  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'user_id' })
  user: User;

  // @ManyToOne(() => ZaloOfficialAccount, { onDelete: 'CASCADE' })
  // @JoinColumn({ name: 'zalo_official_account_id' })
  // zaloOfficialAccount: ZaloOfficialAccount;

  @OneToMany(() => ZaloGroupMember, (member) => member.zaloGroup)
  members: ZaloGroupMember[];
}

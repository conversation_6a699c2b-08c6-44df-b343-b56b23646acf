import {
  <PERSON>,
  Post,
  Body,
  HttpStatus,
  UseGuards,
  Logger,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBody } from '@nestjs/swagger';
import { ApiResponseDto } from '@common/response/api-response-dto';
import { CalendarNotificationService } from '../../user/services/calendar-notification.service';
import { TaskStatus, NotificationChannelType } from '../../constants/calendar.constants';
import { InternalRequestGuard } from '@/common/guards/internal-request.guard';

/**
 * Internal API Controller cho Calendar
 * Chỉ được gọi từ worker, không expose ra public API
 */
@ApiTags('Calendar Internal')
@UseGuards(InternalRequestGuard)
@Controller('calendar/internal')
export class CalendarInternalController {
  private readonly logger = new Logger(CalendarInternalController.name);

  constructor(
    private readonly calendarNotificationService: CalendarNotificationService,
  ) {}

  @Post('send-notification')
  @ApiOperation({ summary: 'Gửi thông báo calendar (Internal)' })
  @ApiBody({
    description: 'Dữ liệu gửi thông báo',
    schema: {
      type: 'object',
      properties: {
        reminderId: { type: 'string' },
        eventId: { type: 'string' },
        userId: { type: 'number' },
        channelType: {
          type: 'string',
          enum: Object.values(NotificationChannelType),
        },
        channelConfig: { type: 'object' },
        title: { type: 'string' },
        message: { type: 'string' },
        messageTemplate: { type: 'string' },
        metadata: { type: 'object' },
      },
      required: [
        'reminderId',
        'eventId',
        'userId',
        'channelType',
        'channelConfig',
        'title',
        'message',
      ],
    },
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Thông báo đã được gửi thành công',
  })
  async sendNotification(
    @Body()
    notificationData: {
      reminderId: string;
      eventId: string;
      userId: number;
      channelType: string;
      channelConfig: any;
      title: string;
      message: string;
      messageTemplate?: string;
      metadata?: any;
    },
  ): Promise<ApiResponseDto<any>> {
    this.logger.log(
      `Processing send notification request: ${notificationData.reminderId} via ${notificationData.channelType}`,
    );

    try {
      const result = await this.calendarNotificationService.sendNotification(
        notificationData.channelType as NotificationChannelType,
        notificationData.channelConfig,
        notificationData.title,
        notificationData.message,
        notificationData.messageTemplate,
      );

      // TODO: Cập nhật trạng thái reminder thành SENT
      // Repository không còn tồn tại dalam version tối ưu
      this.logger.log(`Reminder status updated to COMPLETED: ${notificationData.reminderId}`);

      this.logger.log(
        `Notification sent successfully: ${notificationData.reminderId}`,
      );

      return ApiResponseDto.success(result, 'Thông báo đã được gửi thành công');
    } catch (error) {
      this.logger.error(
        `Failed to send notification: ${notificationData.reminderId} - Error: ${error.message}`,
        error.stack,
      );

      // TODO: Cập nhật trạng thái reminder thành FAILED
      // Repository không còn tồn tại trong version tối ưu
      this.logger.error(`Reminder status updated to FAILED: ${notificationData.reminderId}`);

      throw error;
    }
  }

  @Post('execute-task')
  @ApiOperation({ summary: 'Thực thi nhiệm vụ calendar (Internal)' })
  @ApiBody({
    description: 'Dữ liệu thực thi nhiệm vụ',
    schema: {
      type: 'object',
      properties: {
        taskId: { type: 'string' },
        eventId: { type: 'string' },
        userId: { type: 'number' },
        agentId: { type: 'string' },
        workflowTaskId: { type: 'string' },
        taskName: { type: 'string' },
        taskDescription: { type: 'string' },
        resources: { type: 'object' },
        executionConfig: { type: 'object' },
        executionParams: { type: 'object' },
        metadata: { type: 'object' },
      },
      required: ['taskId', 'eventId', 'userId', 'agentId', 'taskName'],
    },
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Nhiệm vụ đã được thực thi thành công',
  })
  async executeTask(
    @Body()
    taskData: {
      taskId: string;
      eventId: string;
      userId: number;
      agentId: string;
      workflowTaskId: string;
      taskName: string;
      taskDescription: string;
      resources?: any;
      executionConfig?: any;
      executionParams?: any;
      metadata?: any;
    },
  ): Promise<ApiResponseDto<any>> {
    this.logger.log(
      `Processing execute task request: ${taskData.taskId} - Agent: ${taskData.agentId}`,
    );

    try {
      // TODO: Service không còn tồn tại trong version tối ưu
      const result = { success: false, message: 'Task execution service not implemented in optimized version' };

      this.logger.log(`Task execution not implemented: ${taskData.taskId}`);

      return ApiResponseDto.success(
        result,
        'Nhiệm vụ không được hỗ trợ trong phiên bản tối ưu',
      );
    } catch (error) {
      this.logger.error(
        `Failed to execute task: ${taskData.taskId} - Error: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  @Post('generate-report')
  @ApiOperation({ summary: 'Tạo báo cáo calendar (Internal)' })
  @ApiBody({
    description: 'Dữ liệu tạo báo cáo',
    schema: {
      type: 'object',
      properties: {
        reportId: { type: 'string' },
        eventId: { type: 'string' },
        userId: { type: 'number' },
        reportType: { type: 'string' },
        reportConfig: { type: 'object' },
        metadata: { type: 'object' },
      },
      required: ['reportId', 'eventId', 'userId', 'reportType', 'reportConfig'],
    },
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Báo cáo đã được tạo thành công',
  })
  async generateReport(
    @Body()
    reportData: {
      reportId: string;
      eventId: string;
      userId: number;
      reportType: string;
      reportConfig: any;
      metadata?: any;
    },
  ): Promise<ApiResponseDto<any>> {
    this.logger.log(
      `Processing generate report request: ${reportData.reportId} - Type: ${reportData.reportType}`,
    );

    try {
      // TODO: Service không còn tồn tại trong version tối ưu
      const result = { success: false, message: 'Report generation service not implemented in optimized version' };

      this.logger.log(`Report generation not implemented: ${reportData.reportId}`);

      return ApiResponseDto.success(result, 'Báo cáo không được hỗ trợ trong phiên bản tối ưu');
    } catch (error) {
      this.logger.error(
        `Failed to generate report: ${reportData.reportId} - Error: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  @Post('create-recurrence-instances')
  @ApiOperation({ summary: 'Tạo recurrence instances (Internal)' })
  @ApiBody({
    description: 'Dữ liệu tạo recurrence instances',
    schema: {
      type: 'object',
      properties: {
        recurrenceId: { type: 'string' },
        maxInstances: { type: 'number' },
      },
      required: ['recurrenceId'],
    },
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Recurrence instances đã được tạo thành công',
  })
  async createRecurrenceInstances(
    @Body() recurrenceData: { recurrenceId: string; maxInstances?: number },
  ): Promise<ApiResponseDto<any>> {
    this.logger.log(
      `Processing create recurrence instances request: ${recurrenceData.recurrenceId}`,
    );

    try {
      // TODO: Service không còn tồn tại trong version tối ưu
      const result = { success: false, message: 'Recurrence engine service not implemented in optimized version' };

      this.logger.log(
        `Recurrence engine not implemented: ${recurrenceData.recurrenceId}`,
      );

      return ApiResponseDto.success(
        result,
        'Recurrence không được hỗ trợ trong phiên bản tối ưu',
      );
    } catch (error) {
      this.logger.error(
        `Failed to create recurrence instances: ${recurrenceData.recurrenceId} - Error: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Helper method để cập nhật trạng thái reminder
   */
  private async updateReminderStatus(
    reminderId: string,
    status: string,
    metadata: any,
  ): Promise<void> {
    try {
      // TODO: Implement update reminder status logic
      this.logger.log(`Updated reminder status: ${reminderId} -> ${status}`);
    } catch (error) {
      this.logger.error(
        `Failed to update reminder status: ${reminderId}`,
        error.stack,
      );
    }
  }
}

import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsOptional, IsObject, IsBoolean, IsNumber, IsArray, IsUUID, IsDateString } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * DTO cho tạo session mới
 */
export class CreateGenericSessionDto {
  @ApiPropertyOptional({
    description: 'Session ID tùy chỉnh (nếu không cung cấp sẽ auto-generate)',
    example: 'session-123-abc'
  })
  @IsOptional()
  @IsString()
  sessionId?: string;

  @ApiPropertyOptional({
    description: 'User ID cho authenticated sessions',
    example: 123
  })
  @IsOptional()
  @IsNumber()
  userId?: number;

  @ApiPropertyOptional({
    description: 'Cấu hình trang dạng JSON',
    example: { theme: 'dark', autoLayout: true }
  })
  @IsOptional()
  @IsObject()
  pageConfig?: Record<string, any>;

  @ApiPropertyOptional({
    description: 'Metadata bổ sung cho session',
    example: { userAgent: 'Mozilla/5.0...', ip: '***********' }
  })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;

  @ApiPropertyOptional({
    description: 'Thời gian hết hạn session (ISO string)',
    example: '2024-12-31T23:59:59Z'
  })
  @IsOptional()
  @IsDateString()
  expiresAt?: string;
}

/**
 * DTO cho cập nhật session
 */
export class UpdateGenericSessionDto {
  @ApiPropertyOptional({
    description: 'Cấu hình trang dạng JSON',
    example: { theme: 'light', autoLayout: false }
  })
  @IsOptional()
  @IsObject()
  pageConfig?: Record<string, any>;

  @ApiPropertyOptional({
    description: 'Trạng thái active của session',
    example: true
  })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @ApiPropertyOptional({
    description: 'Metadata bổ sung cho session',
    example: { lastPage: '/dashboard', sessionDuration: 3600 }
  })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;

  @ApiPropertyOptional({
    description: 'Thời gian hết hạn session (ISO string)',
    example: '2024-12-31T23:59:59Z'
  })
  @IsOptional()
  @IsDateString()
  expiresAt?: string;
}

/**
 * DTO cho response session
 */
export class GenericSessionResponseDto {
  @ApiProperty({
    description: 'ID của session',
    example: 'uuid-123-456'
  })
  id: string;

  @ApiProperty({
    description: 'Session ID',
    example: 'session-123-abc'
  })
  sessionId: string;

  @ApiPropertyOptional({
    description: 'User ID',
    example: 123
  })
  userId?: number;

  @ApiProperty({
    description: 'Cấu hình trang',
    example: { theme: 'dark', autoLayout: true }
  })
  pageConfig: Record<string, any>;

  @ApiProperty({
    description: 'Trạng thái active',
    example: true
  })
  isActive: boolean;

  @ApiProperty({
    description: 'Thời gian hoạt động cuối',
    example: '2024-01-08T10:30:00Z'
  })
  lastActivity: Date;

  @ApiProperty({
    description: 'Thời gian tạo',
    example: '2024-01-08T09:00:00Z'
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Thời gian cập nhật',
    example: '2024-01-08T10:30:00Z'
  })
  updatedAt: Date;

  @ApiPropertyOptional({
    description: 'Metadata bổ sung',
    example: { userAgent: 'Mozilla/5.0...', ip: '***********' }
  })
  metadata?: Record<string, any>;

  @ApiPropertyOptional({
    description: 'Thời gian hết hạn',
    example: '2024-12-31T23:59:59Z'
  })
  expiresAt?: Date;
}

/**
 * DTO cho query sessions
 */
export class QueryGenericSessionDto {
  @ApiPropertyOptional({
    description: 'User ID để filter',
    example: 123
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  userId?: number;

  @ApiPropertyOptional({
    description: 'Trạng thái active để filter',
    example: true
  })
  @IsOptional()
  @IsBoolean()
  @Type(() => Boolean)
  isActive?: boolean;

  @ApiPropertyOptional({
    description: 'Page number cho pagination',
    example: 1
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  page?: number;

  @ApiPropertyOptional({
    description: 'Limit số lượng records',
    example: 10
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  limit?: number;
}

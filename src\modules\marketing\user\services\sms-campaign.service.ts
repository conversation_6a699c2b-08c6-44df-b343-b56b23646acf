import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { AppException, ErrorCode } from '@/common';
import { InjectQueue } from '@nestjs/bullmq';
import { Queue } from 'bullmq';
import { QueueName, SmsJobName } from '@/shared/queue/queue.constants';
import { SmsCampaignUserRepository } from '../repositories/sms-campaign-user.repository';
import { UserTemplateSmsService } from './user-template-sms.service';
import { UserAudienceRepository } from '../repositories/user-audience.repository';
import { UserSegmentService } from './user-segment.service';
import { UserSegmentRepository } from '../repositories/user-segment.repository';
import {
  SmsCampaignUser,
  SmsCampaignStatus,
} from '../entities/sms-campaign-user.entity';
import { CreateSmsCampaignDto } from '../dto/sms-campaign/create-sms-campaign.dto';
import { IntegrationRepository } from '@/modules/integration/repositories';
import {
  FptSmsBrandnameService,
  FptSmsBrandnameConfig,
} from '@/shared/services/sms/fpt-sms-brandname.service';
import { KeyPairEncryptionService } from '@/shared/services/encryption/key-pair-encryption.service';
import { SmsCampaignType } from '../../enums/sms-campaign-type.enum';
import { HttpService } from '@nestjs/axios';
import { RedisService } from '@/shared/services/redis.service';
import {
  CreateSmsCampaignResponseDto,
  SmsCampaignItemDto,
  SmsCampaignQueryDto,
  SmsCampaignOverviewDto,
} from '../dto/sms-campaign/sms-campaign-response.dto';
import { SmsCampaignOverviewFilterDto } from '../dto/sms-campaign/sms-campaign-overview-filter.dto';
import {
  SmsMarketingJobDto,
  SmsRecipientDto,
} from '../dto/sms-campaign/sms-marketing-job.dto';
import { PaginatedResult } from '@/common/response';
import { SenderTypeEnum } from '@/shared/enums/sender-type.enum';
import { BulkDeleteResponseDto } from '@/modules/marketing/common/dto/bulk-delete.dto';

import { Transactional } from 'typeorm-transactional';

/**
 * Service xử lý SMS campaign với queue integration
 */
@Injectable()
export class SmsCampaignService {
  private readonly logger = new Logger(SmsCampaignService.name);

  constructor(
    @InjectQueue(QueueName.SMS_MARKETING)
    private readonly smsQueue: Queue,
    private readonly smsCampaignRepository: SmsCampaignUserRepository,
    private readonly userTemplateSmsService: UserTemplateSmsService,
    private readonly userAudienceRepository: UserAudienceRepository,
    private readonly userSegmentService: UserSegmentService,
    private readonly userSegmentRepository: UserSegmentRepository,
    private readonly integrationRepository: IntegrationRepository,
    private readonly keyPairEncryptionService: KeyPairEncryptionService,
    private readonly httpService: HttpService,
    private readonly redisService: RedisService,
  ) {}

  /**
   * Tạo SMS campaign với template
   */
  @Transactional()
  async createSmsCampaignWithTemplate(
    userId: number,
    createDto: CreateSmsCampaignDto,
  ): Promise<CreateSmsCampaignResponseDto> {
    this.logger.log(
      `Creating SMS campaign with template for user ${userId}: ${createDto.name}`,
    );

    // Kiểm tra tên campaign đã tồn tại chưa
    const existingCampaign =
      await this.smsCampaignRepository.findByNameAndUserId(
        createDto.name,
        userId,
      );
    if (existingCampaign) {
      throw new AppException(
        ErrorCode.VALIDATION_ERROR,
        `Campaign với tên "${createDto.name}" đã tồn tại`,
      );
    }

    // Kiểm tra template có tồn tại không
    const template = await this.userTemplateSmsService.getTemplateForCampaign(
      userId,
      createDto.templateId,
    );

    // Tạo templateConfig từ template đã lấy từ DB
    const templateConfig = {
      id: template.id,
      name: template.customName || `Template ${template.id}`,
      content: template.customContent || '',
      customContent: template.customContent || undefined,
      variables: template.placeholders || {},
      isActive: true, // Mặc định là active vì template đã tồn tại
    };

    // Validation cho ADS campaign
    if (
      createDto.campaignType === SmsCampaignType.ADS &&
      !createDto.scheduledAt
    ) {
      throw new AppException(
        ErrorCode.VALIDATION_ERROR,
        'ADS campaign bắt buộc phải có scheduledAt',
      );
    }

    // Lấy SMS integration config
    const smsIntegrationConfig = await this.getSmsIntegrationConfig(
      userId,
      createDto.serverId,
    );

    // Lấy danh sách audiences
    let audiences: any[] = [];

    // Xử lý phoneNumbers nếu có
    if (createDto.phoneNumbers && createDto.phoneNumbers.length > 0) {
      const phoneAudiences = await this.createMockAudiencesForPhones(
        userId,
        createDto.phoneNumbers,
      );
      audiences = audiences.concat(phoneAudiences);
    }

    // Xử lý segmentIds và audienceIds
    if (
      (createDto.segmentIds && createDto.segmentIds.length > 0) ||
      (createDto.audienceIds && createDto.audienceIds.length > 0)
    ) {
      const existingAudiences = await this.getAudiencesFromInput(
        userId,
        createDto.segmentIds,
        createDto.audienceIds,
      );
      audiences = audiences.concat(existingAudiences);
    }

    if (audiences.length === 0) {
      throw new AppException(
        ErrorCode.VALIDATION_ERROR,
        'Không tìm thấy audience nào để gửi SMS',
      );
    }

    // Logic phân nhánh theo campaignType
    if (createDto.campaignType === SmsCampaignType.OTP) {
      // Tạo campaign trong database cho OTP
      const savedCampaign = await this.smsCampaignRepository.createCampaign({
        userId,
        name: createDto.name,
        description: createDto.description || null,
        content: template.customContent || '',
        campaignType: createDto.campaignType,
        smsIntegrationConfig: smsIntegrationConfig,
        segmentConfig: null, // Sẽ lưu segmentIds riêng
        audiences: null, // Sẽ lưu audienceIds riêng
        templateConfig: templateConfig,
        templateVariables: createDto.templateVariables || null,
        status: createDto.scheduledAt
          ? SmsCampaignStatus.SCHEDULED
          : SmsCampaignStatus.SENDING,
        scheduledAt: createDto.scheduledAt || null,
        totalRecipients: audiences.length,
      });
      this.logger.log(`OTP Campaign saved with ID: ${savedCampaign.id}`);

      // Tạo job cho queue với template
      const jobIds = await this.createSmsJobs(
        savedCampaign,
        audiences,
        createDto.templateId,
        createDto.templateVariables || {},
      );

      // Lưu job IDs vào campaign
      savedCampaign.jobIds = jobIds;
      await this.smsCampaignRepository.save(savedCampaign);

      this.logger.log(
        `Created OTP SMS campaign ${savedCampaign.id} with ${audiences.length} recipients`,
      );

      return {
        campaignId: savedCampaign.id,
        jobCount: jobIds.length,
        jobIds,
        scheduledAt: savedCampaign.scheduledAt || undefined,
        status: savedCampaign.status,
        totalRecipients: savedCampaign.totalRecipients,
      };
    } else {
      // Gửi SMS campaign ads (brandname) - chỉ cho FPT SMS
      if (!createDto.scheduledAt) {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          'ADS campaign bắt buộc phải có scheduledAt',
        );
      }

      // SMS integration config đã được lấy ở trên
      if (!smsIntegrationConfig || !smsIntegrationConfig.id) {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          'Cấu hình SMS integration không hợp lệ',
        );
      }

      // Verify SMS integration exists and belongs to user
      const smsIntegration = await this.integrationRepository.findOne({
        where: { id: createDto.serverId, userId },
      });

      if (!smsIntegration) {
        throw new AppException(
          ErrorCode.NOT_FOUND,
          'Không tìm thấy cấu hình SMS integration',
        );
      }

      // Get SMS config from integration (đã giải mã)
      const smsConfig = await this.getSmsConfigFromIntegration(
        createDto.serverId,
      );

      // Phân nhánh xử lý theo provider
      const providerType = smsConfig.provider || smsConfig.metadata?.provider;

      this.logger.log(`Processing SMS campaign with provider: ${providerType}`);

      if (providerType === 'FPT_SMS') {
        // Xử lý FPT SMS brandname (logic cũ)
        return await this.handleFptSmsBrandnameCampaign(
          createDto,
          userId,
          audiences,
          template,
          smsConfig,
          templateConfig,
        );
      } else if (providerType === 'TWILIO') {
        // Xử lý Twilio campaign
        return await this.handleTwilioCampaign(
          createDto,
          userId,
          audiences,
          template,
          smsConfig,
          templateConfig,
        );
      } else {
        // Xử lý các provider khác (queue-based)
        return await this.handleGenericProviderCampaign(
          createDto,
          userId,
          audiences,
          template,
          smsConfig,
          templateConfig,
        );
      }

      // Logic cũ đã được di chuyển vào method riêng
    }
  }

  /**
   * Lấy danh sách SMS campaigns với phân trang
   */
  async getCampaigns(
    userId: number,
    queryDto: SmsCampaignQueryDto,
  ): Promise<PaginatedResult<SmsCampaignItemDto>> {
    const page = queryDto.page || 1;
    const limit = queryDto.limit || 20;

    this.logger.log(
      `Getting SMS campaigns for user ${userId}, page: ${page}, limit: ${limit}`,
    );

    const { campaigns, total } =
      await this.smsCampaignRepository.findByUserIdWithPagination(
        userId,
        page,
        limit,
        queryDto.search,
        queryDto.status,
        queryDto.sortBy,
        queryDto.sortDirection,
      );

    const items = campaigns.map((campaign) => this.mapToItemDto(campaign));

    return {
      items,
      meta: {
        totalItems: total,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(total / limit),
        currentPage: page,
      },
    };
  }

  /**
   * Lấy thống kê tổng quan SMS campaigns với filter
   */
  async getOverview(
    userId: number,
    filter?: SmsCampaignOverviewFilterDto,
  ): Promise<SmsCampaignOverviewDto> {
    this.logger.log(`Getting SMS campaign overview for user ${userId}`, {
      filter,
    });

    const stats = await this.smsCampaignRepository.getOverviewStats(
      userId,
      filter,
    );
    const overallSuccessRate =
      stats.totalSent + stats.totalFailed > 0
        ? (stats.totalSent / (stats.totalSent + stats.totalFailed)) * 100
        : 0;

    // Tính chi phí trung bình mỗi tin nhắn
    const averageCostPerMessage =
      stats.totalSent > 0 ? stats.totalCost / stats.totalSent : 0;

    return {
      totalCampaigns: stats.totalCampaigns,
      draftCampaigns: stats.draftCampaigns,
      scheduledCampaigns: stats.scheduledCampaigns,
      sendingCampaigns: stats.sendingCampaigns,
      sentCampaigns: stats.sentCampaigns,
      failedCampaigns: stats.failedCampaigns,
      totalSent: stats.totalSent,
      totalFailed: stats.totalFailed,
      overallSuccessRate: Math.round(overallSuccessRate * 100) / 100,
      activeProviders: stats.activeCampaigns,
      totalCost: stats.totalCost,
      averageCostPerMessage: Math.round(averageCostPerMessage * 100) / 100,
    };
  }

  /**
   * Xóa SMS campaign
   */
  async deleteCampaign(userId: number, campaignId: number): Promise<void> {
    this.logger.log(`Deleting SMS campaign ${campaignId} for user ${userId}`);

    const campaign = await this.smsCampaignRepository.findByIdAndUserId(
      campaignId,
      userId,
    );
    if (!campaign) {
      throw new NotFoundException(
        `Campaign với ID ${campaignId} không tồn tại`,
      );
    }

    // TODO: Hủy jobs trong queue nếu campaign đang SCHEDULED hoặc SENDING

    const deleted = await this.smsCampaignRepository.deleteCampaign(
      campaignId,
      userId,
    );
    if (!deleted) {
      throw new AppException(
        ErrorCode.NOT_FOUND,
        `Không thể xóa campaign với ID ${campaignId}`,
      );
    }

    this.logger.log(`SMS campaign ${campaignId} deleted successfully`);
  }

  /**
   * Tính quota cần thiết cho SMS campaign
   * Công thức: số điện thoại × (độ dài message / 160) làm tròn lên
   */
  private calculateSmsQuota(phoneCount: number, messageLength: number): number {
    const messagesPerPhone = Math.ceil(messageLength / 160);
    return phoneCount * messagesPerPhone;
  }

  /**
   * Tạo instance của FptSmsBrandnameService với cấu hình từ database
   */
  private createFptSmsBrandnameService(
    serverConfig: any,
  ): FptSmsBrandnameService {
    const config: FptSmsBrandnameConfig = {
      apiUrl: serverConfig.endpoint || 'https://api01.sms.fpt.net',
      clientId: serverConfig.apiKey,
      clientSecret: (serverConfig.additionalSettings as any)?.clientSecret,
      brandName: (serverConfig.additionalSettings as any)?.brandName || 'REDAI',
    };

    return new FptSmsBrandnameService(
      this.httpService,
      config,
      this.redisService.getClient(),
    );
  }

  /**
   * Lấy SMS integration config từ serverId (đã giải mã)
   */
  private async getSmsIntegrationConfig(
    userId: number,
    serverId: string,
  ): Promise<any> {
    // Sử dụng method getSmsConfigFromIntegration để lấy config đã giải mã
    const decryptedConfig = await this.getSmsConfigFromIntegration(serverId);

    // Verify ownership
    const integration = await this.integrationRepository.findById(
      serverId,
      userId,
    );
    if (!integration) {
      throw new AppException(
        ErrorCode.VALIDATION_ERROR,
        `Không tìm thấy SMS server với ID ${serverId} hoặc không thuộc về user`,
      );
    }

    // Kiểm tra xem có phải SMS integration không
    const provider =
      decryptedConfig.provider || decryptedConfig.metadata?.provider;
    if (
      !provider ||
      !['FPT_SMS', 'TWILIO', 'VONAGE', 'SPEED_SMS'].includes(provider)
    ) {
      throw new AppException(
        ErrorCode.VALIDATION_ERROR,
        'Integration không phải là SMS server',
      );
    }

    this.logger.log(
      `Lấy SMS integration config thành công cho provider: ${provider}`,
    );

    // Trả về config đã giải mã với format chuẩn cho campaign
    return {
      id: decryptedConfig.id,
      integrationName:
        decryptedConfig.integrationName || `${provider} Integration`,
      typeId: decryptedConfig.typeId,
      provider: provider,
      providerName: provider, // Thêm providerName để worker validation pass
      metadata: decryptedConfig.metadata,
      // Config đã giải mã
      apiKey: decryptedConfig.apiKey,
      endpoint: decryptedConfig.endpoint,
      additionalSettings: decryptedConfig.additionalSettings || {},
      fallback: decryptedConfig.fallback || false,
    };
  }

  /**
   * Validate phone number format
   */
  private isValidPhone(phone: string): boolean {
    // Regex cho số điện thoại Việt Nam
    const phoneRegex = /^(\+84|84|0)(3|5|7|8|9)[0-9]{8}$/;
    return phoneRegex.test(phone.replace(/\s/g, ''));
  }

  /**
   * Parse phone number để tách countryCode và phoneNumber
   */
  private parsePhoneNumber(phone: string): {
    countryCode: number;
    phoneNumber: string;
  } {
    // Remove all non-digit characters
    const cleanPhone = phone.replace(/[^\d]/g, '');

    // If starts with 84, keep as is
    if (cleanPhone.startsWith('84') && cleanPhone.length >= 11) {
      return {
        countryCode: 84,
        phoneNumber: cleanPhone.substring(2),
      };
    }
    // If starts with 0, replace with 84
    else if (cleanPhone.startsWith('0') && cleanPhone.length >= 10) {
      return {
        countryCode: 84,
        phoneNumber: cleanPhone.substring(1),
      };
    }
    // If just digits and length >= 9, assume Vietnam number
    else if (cleanPhone.length >= 9 && cleanPhone.length <= 10) {
      return {
        countryCode: 84,
        phoneNumber: cleanPhone,
      };
    }

    // Default fallback
    return {
      countryCode: 84,
      phoneNumber: cleanPhone,
    };
  }

  /**
   * Tạo user_audience cho những số điện thoại chưa có
   */
  private async createMockAudiencesForPhones(
    userId: number,
    phoneNumbers: string[],
  ): Promise<any[]> {
    const audiences: any[] = [];
    const now = Date.now();

    for (const phone of phoneNumbers) {
      // Validate phone number
      if (!this.isValidPhone(phone)) {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          `Số điện thoại không hợp lệ: ${phone}`,
        );
      }

      // Parse phone number để tách countryCode và phoneNumber
      const { countryCode, phoneNumber } = this.parsePhoneNumber(phone);

      // Kiểm tra xem số điện thoại đã có user_audience chưa
      const existingAudience = await this.userAudienceRepository.findOne({
        where: { userId, countryCode, phoneNumber },
      });

      if (existingAudience) {
        audiences.push(existingAudience);
      } else {
        // Tạo user_audience mới
        const savedAudience = await this.userAudienceRepository.create({
          userId,
          name: `SMS_${countryCode}${phoneNumber}`,
          countryCode,
          phoneNumber,
          email: undefined,
          avatar: null,
          avatarsExternal: null,
          importResource: 'MANUAL' as any,
          zaloOfficialAccountId: null,
          zaloSocialId: null,
          zaloUserIsFollower: null,
          userLastInteractionDate: null,
          createdAt: now,
          updatedAt: now,
        });

        audiences.push(savedAudience);
      }
    }

    return audiences;
  }

  /**
   * Lấy audiences từ segmentIds hoặc audienceIds
   */
  private async getAudiencesFromInput(
    userId: number,
    segmentIds?: number[],
    audienceIds?: number[],
  ): Promise<any[]> {
    let allAudiences: any[] = [];

    // Xử lý segmentIds
    if (segmentIds && segmentIds.length > 0) {
      for (const segmentId of segmentIds) {
        try {
          // Kiểm tra segment tồn tại thông qua findOne
          await this.userSegmentService.findOne(userId, segmentId);

          // Lấy segment entity để sử dụng với getAudiencesInSegment
          const segmentEntity = await this.userSegmentRepository.findOne({
            where: { id: segmentId, userId },
          });

          if (!segmentEntity) {
            throw new NotFoundException(
              `Segment với ID ${segmentId} không tồn tại`,
            );
          }

          const segmentAudiences =
            await this.userSegmentService.getAudiencesInSegment(
              userId,
              segmentEntity,
            );
          allAudiences = allAudiences.concat(segmentAudiences);
        } catch (error) {
          if (error instanceof NotFoundException) {
            throw error;
          }
          throw new NotFoundException(
            `Segment với ID ${segmentId} không tồn tại`,
          );
        }
      }
    }

    // Xử lý audienceIds
    if (audienceIds && audienceIds.length > 0) {
      this.logger.log(
        `Looking for audiences with IDs: ${audienceIds.join(', ')} for user ${userId}`,
      );
      const audiences = await this.userAudienceRepository.findByIdsAndUserId(
        audienceIds,
        userId,
      );
      this.logger.log(
        `Found ${audiences.length} audiences out of ${audienceIds.length} requested`,
      );
      if (audiences.length !== audienceIds.length) {
        const foundIds = audiences.map((a) => a.id);
        const missingIds = audienceIds.filter((id) => !foundIds.includes(id));
        this.logger.error(
          `Missing audience IDs: ${missingIds.join(', ')} for user ${userId}`,
        );
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          `Không tìm thấy audience với IDs: ${missingIds.join(', ')}`,
        );
      }
      allAudiences = allAudiences.concat(audiences);
    }

    if (allAudiences.length === 0) {
      throw new AppException(
        ErrorCode.VALIDATION_ERROR,
        'Phải cung cấp segmentIds hoặc audienceIds',
      );
    }

    // Loại bỏ duplicate audiences (theo ID)
    const uniqueAudiences = allAudiences.filter(
      (audience, index, self) =>
        index === self.findIndex((a) => a.id === audience.id),
    );

    return uniqueAudiences;
  }

  /**
   * Tạo SMS jobs và đẩy vào queue
   */
  private async createSmsJobs(
    campaign: SmsCampaignUser,
    audiences: any[],
    templateId: number,
    templateVariables: Record<string, any>,
  ): Promise<string[]> {
    const now = Date.now();

    // Tạo danh sách recipients
    const recipients: SmsRecipientDto[] = audiences.map((audience) => ({
      audienceId: audience.id,
      phone:
        audience.countryCode && audience.phoneNumber
          ? `${audience.countryCode}${audience.phoneNumber}`
          : audience.phoneNumber || '',
      customFields: audience.customFields || {},
    }));

    // Xử lý content: nếu có templateId thì xử lý template, nếu không thì dùng content trực tiếp
    let processedContent: string;

    if (templateId === 0) {
      // Không sử dụng template, dùng content trực tiếp
      processedContent = campaign.content || '';
    } else {
      // Sử dụng template, cần xử lý template với variables
      const template = await this.userTemplateSmsService.getTemplateForCampaign(
        campaign.userId,
        templateId,
      );
      if (!template || !template.customContent) {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          `Template ${templateId} không có nội dung`,
        );
      }

      // Xử lý template với templateVariables
      processedContent = this.processTemplateContent(
        template.customContent,
        templateVariables,
      );
    }

    // Tạo job data với content đã được xử lý sẵn
    const jobData: SmsMarketingJobDto = {
      campaignId: campaign.id,
      userId: campaign.userId,
      campaignType: campaign.campaignType,
      templateId,
      templateVariables,
      recipients,
      smsIntegrationConfig: campaign.smsIntegrationConfig!,
      content: processedContent, // Sử dụng content đã được xử lý
      campaignName: campaign.name,
      createdAt: now,
      scheduledAt: campaign.scheduledAt || undefined,
      senderType: SenderTypeEnum.USER, // SMS được gửi bởi user
      senderId: campaign.userId, // ID của user
    };

    // Đối với ADS campaign, không delay job - gửi ngay và để FPT API xử lý theo scheduledAt
    // Đối với OTP campaign, có thể delay nếu cần
    const delay =
      campaign.campaignType === SmsCampaignType.OTP && campaign.scheduledAt
        ? Math.max(0, campaign.scheduledAt * 1000 - now)
        : 0;

    // Thêm job vào queue
    const job = await this.smsQueue.add(SmsJobName.SMS_MARKETING, jobData, {
      delay,
      attempts: 3,
      backoff: {
        type: 'exponential',
        delay: 2000,
      },
      removeOnComplete: 100,
      removeOnFail: 50,
    });

    return [job.id?.toString() || 'unknown'];
  }

  /**
   * Xử lý template content với variables
   * @param templateContent Nội dung template gốc
   * @param templateVariables Variables để thay thế
   * @returns Nội dung đã được xử lý
   */
  private processTemplateContent(
    templateContent: string,
    templateVariables: Record<string, any>,
  ): string {
    if (!templateContent) {
      return '';
    }

    let processedContent = templateContent;

    // Thay thế các placeholder theo format {{key}}
    Object.keys(templateVariables).forEach((key) => {
      const placeholder = `{{${key}}}`;
      const value = templateVariables[key]?.toString() || '';
      processedContent = processedContent.replace(
        new RegExp(placeholder, 'g'),
        value,
      );
    });

    this.logger.debug(
      `Template processed: ${templateContent} -> ${processedContent}`,
    );

    return processedContent;
  }

  /**
   * Retry SMS campaign đã failed
   */
  @Transactional()
  async retryCampaign(
    userId: number,
    campaignId: number,
  ): Promise<CreateSmsCampaignResponseDto> {
    this.logger.log(`Retrying SMS campaign ${campaignId} for user ${userId}`);

    // Lấy campaign
    const campaign = await this.smsCampaignRepository.findByIdAndUserId(
      campaignId,
      userId,
    );
    if (!campaign) {
      throw new AppException(
        ErrorCode.NOT_FOUND,
        `Campaign với ID ${campaignId} không tồn tại`,
      );
    }

    // Kiểm tra trạng thái campaign
    if (campaign.status !== SmsCampaignStatus.FAILED) {
      throw new AppException(
        ErrorCode.VALIDATION_ERROR,
        `Campaign phải ở trạng thái FAILED để có thể retry. Trạng thái hiện tại: ${campaign.status}`,
      );
    }

    // Reset trạng thái campaign
    campaign.status = SmsCampaignStatus.PENDING;
    campaign.startedAt = null;
    campaign.completedAt = null;
    campaign.sentCount = 0;
    campaign.failedCount = 0;
    campaign.updatedAt = Date.now();

    await this.smsCampaignRepository.save(campaign);

    // Lấy lại audiences từ campaign config
    let audiences: any[] = [];

    // Xử lý phoneNumbers nếu có trong audiences
    if (campaign.audiences && Array.isArray(campaign.audiences)) {
      audiences = campaign.audiences.map((audience, index) => ({
        id: index + 1,
        phone: audience.phoneNumber,
        name: audience.name || `Phone ${audience.phoneNumber}`,
        customFields: {}, // Default empty object vì audience structure không có customFields
      }));
    }

    // Nếu không có audiences, thử lấy từ segment
    if (audiences.length === 0 && campaign.segmentConfig?.id) {
      const existingAudiences = await this.getAudiencesFromInput(
        userId,
        [campaign.segmentConfig.id],
        undefined,
      );
      audiences = existingAudiences;
    }

    if (audiences.length === 0) {
      throw new AppException(
        ErrorCode.VALIDATION_ERROR,
        'Không tìm thấy audience nào để retry campaign',
      );
    }

    // Tạo job mới
    const templateId = campaign.templateConfig?.id || 0;
    const templateVariables = campaign.templateVariables || {};

    const jobIds = await this.createSmsJobs(
      campaign,
      audiences,
      templateId,
      templateVariables,
    );

    // Cập nhật job IDs
    campaign.jobIds = jobIds;
    campaign.status = SmsCampaignStatus.SENDING;
    await this.smsCampaignRepository.save(campaign);

    this.logger.log(
      `Retried SMS campaign ${campaignId} with ${audiences.length} recipients`,
    );

    return {
      campaignId: campaign.id,
      jobCount: jobIds.length,
      jobIds,
      scheduledAt: campaign.scheduledAt || undefined,
      status: campaign.status,
      totalRecipients: campaign.totalRecipients,
    };
  }

  /**
   * Xóa nhiều SMS campaign
   */
  async bulkDeleteCampaigns(
    userId: number,
    ids: number[],
  ): Promise<BulkDeleteResponseDto> {
    this.logger.log(
      `Bulk deleting SMS campaigns for user ${userId}: ${ids.join(', ')}`,
    );

    const deletedIds: number[] = [];
    const failedIds: number[] = [];

    for (const id of ids) {
      try {
        // Kiểm tra campaign có tồn tại và thuộc về user không
        const existingCampaign =
          await this.smsCampaignRepository.findByIdAndUserId(id, userId);
        if (!existingCampaign) {
          failedIds.push(id);
          continue;
        }

        // TODO: Hủy jobs trong queue nếu campaign đang SCHEDULED hoặc SENDING
        // if (existingCampaign.status === SmsCampaignStatus.SCHEDULED || existingCampaign.status === SmsCampaignStatus.SENDING) {
        //   // Cancel jobs in queue
        // }

        // Xóa campaign
        const deleted = await this.smsCampaignRepository.deleteCampaign(
          id,
          userId,
        );
        if (deleted) {
          deletedIds.push(id);
        } else {
          failedIds.push(id);
        }
      } catch (error) {
        this.logger.error(
          `Error deleting SMS campaign ${id}: ${error.message}`,
          error.stack,
        );
        failedIds.push(id);
      }
    }

    const deletedCount = deletedIds.length;
    const failedCount = failedIds.length;
    const message = `Đã xóa ${deletedCount} SMS campaign thành công${failedCount > 0 ? `, ${failedCount} campaign không thể xóa` : ''}`;

    this.logger.log(
      `Bulk delete SMS campaigns completed: ${deletedCount} deleted, ${failedCount} failed`,
    );

    return {
      deletedCount,
      failedCount,
      deletedIds,
      failedIds,
      message,
    };
  }

  /**
   * Map entity sang item DTO
   */
  private mapToItemDto(campaign: SmsCampaignUser): SmsCampaignItemDto {
    const successRate =
      campaign.totalRecipients > 0
        ? (campaign.sentCount / campaign.totalRecipients) * 100
        : 0;

    return {
      id: campaign.id,
      name: campaign.name,
      description: campaign.description || undefined,
      campaignType: campaign.campaignType,
      status: campaign.status,
      totalRecipients: campaign.totalRecipients,
      sentCount: campaign.sentCount,
      failedCount: campaign.failedCount,
      successRate: Math.round(successRate * 100) / 100,
      scheduledAt: campaign.scheduledAt || undefined,
      startedAt: campaign.startedAt || undefined,
      completedAt: campaign.completedAt || undefined,
      createdAt: campaign.createdAt,
      updatedAt: campaign.updatedAt,
    };
  }

  /**
   * Xử lý campaign cho Twilio provider
   */
  private async handleTwilioCampaign(
    createDto: CreateSmsCampaignDto,
    userId: number,
    audiences: any[],
    template: any,
    serverConfig: any,
    templateConfig: any,
  ): Promise<CreateSmsCampaignResponseDto> {
    this.logger.log(`Handling Twilio campaign: ${createDto.name}`);

    // Tạo smsIntegrationConfig từ serverConfig
    const smsIntegrationConfig = {
      id: serverConfig.id || 'unknown',
      integrationName: serverConfig.integrationName || 'Twilio SMS',
      typeId: serverConfig.typeId || 0,
      metadata: serverConfig.metadata || {},
    };

    // Tạo campaign trong database
    const savedCampaign = await this.smsCampaignRepository.createCampaign({
      userId,
      name: createDto.name,
      description: createDto.description || null,
      content: template.customContent || '',
      campaignType: createDto.campaignType,
      smsIntegrationConfig: smsIntegrationConfig,
      segmentConfig: null,
      templateConfig: templateConfig,
      audiences: null,
      templateVariables: createDto.templateVariables || null,
      status: createDto.scheduledAt
        ? SmsCampaignStatus.SCHEDULED
        : SmsCampaignStatus.SENDING,
      scheduledAt: createDto.scheduledAt || null,
      totalRecipients: audiences.length,
    });

    // Tạo jobs cho queue với Twilio-specific options
    const jobIds = await this.createTwilioSmsJobs(
      savedCampaign,
      audiences,
      createDto.templateId,
      createDto.templateVariables || {},
      serverConfig,
    );

    // Lưu job IDs vào campaign
    savedCampaign.jobIds = jobIds;
    await this.smsCampaignRepository.save(savedCampaign);

    this.logger.log(
      `Created Twilio SMS campaign ${savedCampaign.id} with ${audiences.length} recipients`,
    );

    return {
      campaignId: savedCampaign.id,
      jobCount: jobIds.length,
      jobIds,
      scheduledAt: savedCampaign.scheduledAt || undefined,
      status: savedCampaign.status,
      totalRecipients: savedCampaign.totalRecipients,
    };
  }

  /**
   * Xử lý campaign cho các provider khác (queue-based)
   */
  private async handleGenericProviderCampaign(
    createDto: CreateSmsCampaignDto,
    userId: number,
    audiences: any[],
    template: any,
    _smsConfig: any, // Renamed to indicate it's intentionally unused
    templateConfig: any,
  ): Promise<CreateSmsCampaignResponseDto> {
    this.logger.log(`Handling generic provider campaign: ${createDto.name}`);

    // Tạo smsIntegrationConfig từ _smsConfig
    const smsIntegrationConfig = {
      id: _smsConfig.id || 'unknown',
      integrationName: _smsConfig.integrationName || 'Generic SMS',
      typeId: _smsConfig.typeId || 0,
      metadata: _smsConfig.metadata || {},
    };

    // Tạo campaign trong database
    const savedCampaign = await this.smsCampaignRepository.createCampaign({
      userId,
      name: createDto.name,
      description: createDto.description || null,
      content: template.customContent || '',
      campaignType: createDto.campaignType,
      smsIntegrationConfig: smsIntegrationConfig,
      segmentConfig: null,
      templateConfig: templateConfig,
      audiences: null,
      templateVariables: createDto.templateVariables || null,
      status: createDto.scheduledAt
        ? SmsCampaignStatus.SCHEDULED
        : SmsCampaignStatus.SENDING,
      scheduledAt: createDto.scheduledAt || null,
      totalRecipients: audiences.length,
    });

    const jobIds = await this.createSmsJobs(
      savedCampaign,
      audiences,
      createDto.templateId,
      createDto.templateVariables || {},
    );
    savedCampaign.jobIds = jobIds;
    await this.smsCampaignRepository.save(savedCampaign);

    return {
      campaignId: savedCampaign.id,
      jobCount: jobIds.length,
      jobIds,
      scheduledAt: savedCampaign.scheduledAt || undefined,
      status: savedCampaign.status,
      totalRecipients: savedCampaign.totalRecipients,
    };
  }

  /**
   * Xử lý FPT SMS brandname campaign
   */
  private async handleFptSmsBrandnameCampaign(
    createDto: CreateSmsCampaignDto,
    userId: number,
    audiences: any[],
    template: any,
    serverConfig: any,
    templateConfig: any,
  ): Promise<CreateSmsCampaignResponseDto> {
    this.logger.log(`Handling FPT SMS brandname campaign: ${createDto.name}`);

    const content = template.customContent || '';
    const phoneList = audiences.map((audience) => audience.phone);
    const quota = this.calculateSmsQuota(phoneList.length, content.length);

    // Tạo instance FptSmsBrandnameService với cấu hình từ database
    const fptSmsService = this.createFptSmsBrandnameService(serverConfig);

    try {
      // Bước 1: Tạo campaign
      const campaignResult = await fptSmsService.createCampaign({
        CampaignName: createDto.name,
        BrandName:
          (serverConfig.additionalSettings as any)?.brandName || 'REDAI',
        Message: content,
        ScheduleTime: createDto.scheduledAt
          ? new Date(createDto.scheduledAt * 1000)
              .toISOString()
              .slice(0, 16)
              .replace('T', ' ')
          : new Date().toISOString().slice(0, 16).replace('T', ' '),
        Quota: quota,
      });

      this.logger.debug(
        `Campaign created successfully: ${campaignResult.CampaignCode}`,
      );

      // Bước 2: Gửi ads
      const adsResult = await fptSmsService.sendAds({
        CampaignCode: campaignResult.CampaignCode,
        PhoneList: phoneList.join(','),
      });

      // Kiểm tra kết quả gửi ads
      if (adsResult.FailureCount > 0) {
        const failedDetail = adsResult.Details.find(
          (detail) => detail.Status !== 'SUCCESS',
        );
        throw new Error(
          `Failed to send ads: ${failedDetail?.ErrorMessage || 'Unknown error'}`,
        );
      }

      this.logger.debug(
        `Brandname SMS sent successfully. Total sent: ${adsResult.TotalSent}, Success: ${adsResult.SuccessCount}`,
      );

      // Tạo smsIntegrationConfig từ serverConfig
      const smsIntegrationConfig = {
        id: serverConfig.id || 'unknown',
        integrationName: serverConfig.integrationName || 'FPT SMS',
        typeId: serverConfig.typeId || 0,
        metadata: serverConfig.metadata || {},
      };

      // Tạo campaign trong database với CampaignCode
      const savedCampaign = await this.smsCampaignRepository.createCampaign({
        userId,
        name: createDto.name,
        description: createDto.description || null,
        content,
        campaignType: createDto.campaignType,
        smsIntegrationConfig: smsIntegrationConfig,
        segmentConfig: null,
        templateConfig: templateConfig,
        audiences: null,
        templateVariables: createDto.templateVariables || null,
        status: SmsCampaignStatus.PENDING,
        scheduledAt: createDto.scheduledAt,
        totalRecipients: audiences.length,
        externalCampaignCode: campaignResult.CampaignCode,
      });

      this.logger.log(
        `ADS Campaign saved with ID: ${savedCampaign.id}, External Code: ${campaignResult.CampaignCode}`,
      );

      return {
        campaignId: savedCampaign.id,
        jobCount: 0, // Không có job vì đã gửi trực tiếp
        jobIds: [],
        scheduledAt: savedCampaign.scheduledAt || undefined,
        status: savedCampaign.status,
        totalRecipients: savedCampaign.totalRecipients,
      };
    } catch (error) {
      // Xử lý lỗi tên campaign từ FPT API
      if (error.response?.data?.error === 1014) {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          'Tên campaign đã tồn tại trên hệ thống FPT SMS',
        );
      }

      this.logger.error(`Error creating FPT SMS campaign: ${error.message}`);
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        `Lỗi khi tạo campaign FPT SMS: ${error.message}`,
      );
    }
  }

  /**
   * Tạo SMS jobs cho Twilio với specific options
   */
  private async createTwilioSmsJobs(
    campaign: any,
    audiences: any[],
    templateId: number,
    templateVariables: Record<string, any>,
    serverConfig: any,
  ): Promise<string[]> {
    const now = Date.now();
    const jobIds: string[] = [];

    // Chuẩn bị Twilio-specific options
    const twilioOptions = this.prepareTwilioJobOptions(serverConfig);

    // Xử lý content: nếu có templateId thì xử lý template, nếu không thì dùng content trực tiếp
    let processedContent: string;

    if (!templateId || templateId === 0) {
      // Không sử dụng template, dùng content trực tiếp
      processedContent = campaign.content || '';
    } else {
      // Sử dụng template, cần xử lý template với variables
      const template = await this.userTemplateSmsService.getTemplateForCampaign(
        campaign.userId,
        templateId,
      );
      if (!template || !template.customContent) {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          `Template ${templateId} không có nội dung`,
        );
      }

      // Xử lý template với templateVariables
      processedContent = this.processTemplateContent(
        template.customContent,
        templateVariables || {},
      );
    }

    // Tạo job data với Twilio options
    const jobData: SmsMarketingJobDto = {
      campaignId: campaign.id,
      userId: campaign.userId,
      campaignType: campaign.campaignType,
      smsIntegrationConfig: campaign.smsIntegrationConfig!,
      templateId: templateId || null,
      templateVariables: templateVariables || {},
      recipients: audiences.map((audience) => ({
        phone:
          audience.countryCode && audience.phoneNumber
            ? `${audience.countryCode}${audience.phoneNumber}`
            : audience.phoneNumber || '',
        audienceId: audience.id,
        customFields: audience.customFields || {},
      })),
      content: processedContent, // Sử dụng content đã được xử lý
      createdAt: Date.now(),
      // Thêm Twilio-specific options
      ...twilioOptions,
      scheduledAt: campaign.scheduledAt || undefined,
      senderType: SenderTypeEnum.USER, // SMS được gửi bởi user
      senderId: campaign.userId, // ID của user
    };

    // Đối với ADS campaign, không delay job - gửi ngay và để FPT API xử lý theo scheduledAt
    // Đối với OTP campaign, có thể delay nếu cần
    const delay =
      campaign.campaignType === SmsCampaignType.OTP && campaign.scheduledAt
        ? Math.max(0, campaign.scheduledAt * 1000 - now)
        : 0;

    // Thêm job vào queue
    const job = await this.smsQueue.add(SmsJobName.SMS_MARKETING, jobData, {
      delay,
      attempts: 3,
      backoff: {
        type: 'exponential',
        delay: 2000,
      },
      removeOnComplete: 100,
      removeOnFail: 50,
    });

    jobIds.push(job.id?.toString() || 'unknown');

    return jobIds;
  }

  /**
   * Chuẩn bị Twilio job options từ server config
   */
  private prepareTwilioJobOptions(serverConfig: any): any {
    const settings = serverConfig.additionalSettings as any;

    const options: any = {
      accountSid: settings?.accountSid || serverConfig.apiKey,
      authToken: settings?.authToken,
    };

    // Thêm phone number hoặc messaging service SID
    if (settings?.messagingServiceSid) {
      options.messagingServiceSid = settings.messagingServiceSid;
    } else if (settings?.phoneNumber) {
      options.from = settings.phoneNumber;
    }

    // Thêm status callback URL nếu có
    if (settings?.statusCallbackUrl) {
      options.statusCallback = settings.statusCallbackUrl;
    }

    return options;
  }

  /**
   * Lấy và giải mã SMS configuration từ Integration
   */
  private async getSmsConfigFromIntegration(
    integrationId: string,
  ): Promise<any> {
    try {
      this.logger.log(`Lấy SMS config từ integration: ${integrationId}`);

      const integration = await this.integrationRepository.findOne({
        where: { id: integrationId },
      });

      if (!integration) {
        throw new AppException(
          ErrorCode.NOT_FOUND,
          `Không tìm thấy SMS integration với ID: ${integrationId}`,
        );
      }

      // Lấy provider type từ integration_providers table thông qua type_id
      const providerResult = await this.integrationRepository.query(
        `
        SELECT type FROM integration_providers WHERE id = $1 LIMIT 1
      `,
        [integration.typeId],
      );

      if (!providerResult || providerResult.length === 0) {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          `Không tìm thấy provider type cho integration ${integrationId}`,
        );
      }

      const providerType = providerResult[0].type;

      // Kiểm tra xem có phải SMS provider không
      const smsProviderTypes = [
        'SMS_FPT',
        'SMS_TWILIO',
        'SMS_VONAGE',
        'SMS_SPEED',
      ];
      if (!smsProviderTypes.includes(providerType)) {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          `Integration ${integrationId} không phải là SMS provider. Provider type: ${providerType}`,
        );
      }

      // Convert provider type sang format cũ để tương thích
      const providerMapping: Record<string, string> = {
        SMS_FPT: 'FPT_SMS',
        SMS_TWILIO: 'TWILIO',
        SMS_VONAGE: 'VONAGE',
        SMS_SPEED: 'SPEED_SMS',
      };

      const legacyProviderName = providerMapping[providerType] || providerType;
      const metadata = integration.metadata as any;

      // Nếu không có encryptedConfig hoặc secretKey, trả về config cơ bản
      if (!integration.encryptedConfig || !integration.secretKey) {
        this.logger.warn(
          `Integration ${integrationId} không có encrypted config, sử dụng metadata`,
        );
        return {
          id: integration.id,
          integrationName: integration.integrationName,
          typeId: integration.typeId,
          provider: legacyProviderName,
          metadata: metadata,
          // Fallback config từ metadata
          apiKey: metadata.apiKey || null,
          endpoint: metadata.apiUrl || metadata.endpoint || null,
          additionalSettings: metadata.additionalSettings || {},
          fallback: true,
        };
      }

      // Giải mã cấu hình
      this.logger.debug(`Giải mã config cho integration: ${integrationId}`);
      const decryptionResult = this.keyPairEncryptionService.decrypt(
        integration.encryptedConfig,
        integration.secretKey,
      );

      if (!decryptionResult.success) {
        this.logger.warn(
          `Không thể giải mã SMS config cho integration ${integrationId}`,
        );
        // Fallback: sử dụng metadata
        return {
          id: integration.id,
          integrationName: integration.integrationName,
          typeId: integration.typeId,
          provider: legacyProviderName,
          metadata: metadata,
          apiKey: metadata.apiKey || null,
          endpoint: metadata.apiUrl || metadata.endpoint || null,
          additionalSettings: metadata.additionalSettings || {},
          fallback: true,
        };
      }

      // Parse config đã giải mã
      let decryptedConfig: any;
      try {
        decryptedConfig = JSON.parse(decryptionResult.decryptedData);
      } catch (parseError: any) {
        this.logger.error(`Lỗi parse JSON config: ${parseError.message}`);
        throw new AppException(
          ErrorCode.INTERNAL_SERVER_ERROR,
          'Lỗi parse cấu hình SMS đã giải mã',
        );
      }

      // Trả về config đầy đủ cho worker
      const finalConfig = {
        id: integration.id,
        integrationName: integration.integrationName,
        typeId: integration.typeId,
        provider: legacyProviderName,
        metadata: metadata,
        // Config đã giải mã
        ...decryptedConfig,
        // Đảm bảo có các trường cần thiết
        apiKey:
          decryptedConfig.apiKey || decryptedConfig.clientId || metadata.apiKey,
        endpoint:
          decryptedConfig.endpoint || decryptedConfig.apiUrl || metadata.apiUrl,
        additionalSettings: {
          ...metadata.additionalSettings,
          ...decryptedConfig.additionalSettings,
        },
        fallback: false,
      };

      this.logger.debug(
        `SMS config đã được giải mã thành công cho provider: ${legacyProviderName}`,
      );
      return finalConfig;
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy SMS config từ integration ${integrationId}: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        `Lỗi khi lấy cấu hình SMS: ${error.message}`,
      );
    }
  }
}

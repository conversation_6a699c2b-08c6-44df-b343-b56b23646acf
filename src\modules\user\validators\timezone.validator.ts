import { registerDecorator, ValidationOptions, ValidationArguments } from 'class-validator';

/**
 * <PERSON>h sách timezone được hỗ trợ
 */
const SUPPORTED_TIMEZONES = [
  'UTC',
  'Asia/Ho_Chi_Minh',
  'Asia/Shanghai',
  'Asia/Tokyo',
  'Asia/Seoul',
  'Asia/Singapore',
  'Asia/Bangkok',
  'Asia/Jakarta',
  'Asia/Manila',
  'Asia/Kuala_Lumpur',
  'Asia/Dubai',
  'Asia/Kolkata',
  'Europe/London',
  'Europe/Paris',
  'Europe/Berlin',
  'Europe/Rome',
  'Europe/Madrid',
  'Europe/Moscow',
  'America/New_York',
  'America/Los_Angeles',
  'America/Chicago',
  'America/Denver',
  'America/Sao_Paulo',
  'Australia/Sydney',
  'Australia/Melbourne',
  'Pacific/Auckland',
];

/**
 * Kiểm tra xem timezone có hợp lệ không bằng cách sử dụng Intl.DateTimeFormat
 * @param timezone Timezone string cần kiểm tra
 * @returns True nếu hợp lệ, false nếu không
 */
function isValidTimezone(timezone: string): boolean {
  try {
    // Kiểm tra xem timezone có trong danh sách được hỗ trợ không
    if (!SUPPORTED_TIMEZONES.includes(timezone)) {
      return false;
    }
    
    // Kiểm tra xem timezone có thể được sử dụng với Intl.DateTimeFormat không
    Intl.DateTimeFormat(undefined, { timeZone: timezone });
    return true;
  } catch {
    return false;
  }
}

/**
 * Custom validator decorator để kiểm tra timezone hợp lệ
 * @param validationOptions Tùy chọn validation
 * @returns Decorator function
 */
export function IsValidTimezone(validationOptions?: ValidationOptions) {
  return function (object: object, propertyName: string) {
    registerDecorator({
      name: 'isValidTimezone',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: {
        validate(value: any, args: ValidationArguments) {
          if (typeof value !== 'string') {
            return false;
          }
          return isValidTimezone(value);
        },
        defaultMessage(args: ValidationArguments) {
          return `${args.property} phải là một timezone hợp lệ. Các timezone được hỗ trợ: ${SUPPORTED_TIMEZONES.join(', ')}`;
        },
      },
    });
  };
}

/**
 * Lấy danh sách timezone được hỗ trợ
 * @returns Array của timezone strings
 */
export function getSupportedTimezones(): string[] {
  return [...SUPPORTED_TIMEZONES];
}

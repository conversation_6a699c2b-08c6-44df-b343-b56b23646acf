import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsOptional, IsObject, IsBoolean, IsNumber, IsArray, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * DTO cho widget position
 */
export class WidgetPositionDto {
  @ApiProperty({
    description: 'X coordinate trong grid',
    example: 0
  })
  @IsNumber()
  x: number;

  @ApiProperty({
    description: 'Y coordinate trong grid',
    example: 0
  })
  @IsNumber()
  y: number;

  @ApiProperty({
    description: 'Width trong grid units',
    example: 4
  })
  @IsNumber()
  w: number;

  @ApiProperty({
    description: 'Height trong grid units',
    example: 3
  })
  @IsNumber()
  h: number;

  @ApiPropertyOptional({
    description: 'Minimum width',
    example: 2
  })
  @IsOptional()
  @IsNumber()
  minW?: number;

  @ApiPropertyOptional({
    description: 'Minimum height',
    example: 2
  })
  @IsOptional()
  @IsNumber()
  minH?: number;

  @ApiPropertyOptional({
    description: 'Maximum width',
    example: 8
  })
  @IsOptional()
  @IsNumber()
  maxW?: number;

  @ApiPropertyOptional({
    description: 'Maximum height',
    example: 6
  })
  @IsOptional()
  @IsNumber()
  maxH?: number;
}

/**
 * DTO cho tạo widget mới
 */
export class CreateGenericWidgetDto {
  @ApiProperty({
    description: 'Widget ID duy nhất trong session',
    example: 'widget-123'
  })
  @IsString()
  widgetId: string;

  @ApiProperty({
    description: 'Loại widget',
    example: 'data-count'
  })
  @IsString()
  widgetType: string;

  @ApiProperty({
    description: 'Dữ liệu cấu hình widget',
    example: { title: 'Total Users', value: 1234, icon: 'users' }
  })
  @IsObject()
  widgetData: Record<string, any>;

  @ApiProperty({
    description: 'Vị trí và kích thước widget',
    type: WidgetPositionDto
  })
  @ValidateNested()
  @Type(() => WidgetPositionDto)
  position: WidgetPositionDto;

  @ApiPropertyOptional({
    description: 'Thứ tự hiển thị',
    example: 1
  })
  @IsOptional()
  @IsNumber()
  displayOrder?: number;

  @ApiPropertyOptional({
    description: 'Trạng thái hiển thị',
    example: true
  })
  @IsOptional()
  @IsBoolean()
  isVisible?: boolean;

  @ApiPropertyOptional({
    description: 'Metadata bổ sung',
    example: { source: 'api', refreshInterval: 30000 }
  })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}

/**
 * DTO cho cập nhật widget
 */
export class UpdateGenericWidgetDto {
  @ApiPropertyOptional({
    description: 'Dữ liệu cấu hình widget',
    example: { title: 'Updated Title', value: 5678 }
  })
  @IsOptional()
  @IsObject()
  widgetData?: Record<string, any>;

  @ApiPropertyOptional({
    description: 'Vị trí và kích thước widget',
    type: WidgetPositionDto
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => WidgetPositionDto)
  position?: WidgetPositionDto;

  @ApiPropertyOptional({
    description: 'Thứ tự hiển thị',
    example: 2
  })
  @IsOptional()
  @IsNumber()
  displayOrder?: number;

  @ApiPropertyOptional({
    description: 'Trạng thái hiển thị',
    example: false
  })
  @IsOptional()
  @IsBoolean()
  isVisible?: boolean;

  @ApiPropertyOptional({
    description: 'Metadata bổ sung',
    example: { lastUpdated: '2024-01-08T10:30:00Z' }
  })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}

/**
 * DTO cho response widget
 */
export class GenericWidgetResponseDto {
  @ApiProperty({
    description: 'ID của widget record',
    example: 'uuid-123-456'
  })
  id: string;

  @ApiProperty({
    description: 'Session ID',
    example: 'session-123-abc'
  })
  sessionId: string;

  @ApiProperty({
    description: 'Widget ID',
    example: 'widget-123'
  })
  widgetId: string;

  @ApiProperty({
    description: 'Loại widget',
    example: 'data-count'
  })
  widgetType: string;

  @ApiProperty({
    description: 'Dữ liệu cấu hình widget',
    example: { title: 'Total Users', value: 1234, icon: 'users' }
  })
  widgetData: Record<string, any>;

  @ApiProperty({
    description: 'Vị trí và kích thước widget',
    type: WidgetPositionDto
  })
  position: WidgetPositionDto;

  @ApiProperty({
    description: 'Thứ tự hiển thị',
    example: 1
  })
  displayOrder: number;

  @ApiProperty({
    description: 'Trạng thái hiển thị',
    example: true
  })
  isVisible: boolean;

  @ApiProperty({
    description: 'Thời gian tạo',
    example: '2024-01-08T09:00:00Z'
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Thời gian cập nhật',
    example: '2024-01-08T10:30:00Z'
  })
  updatedAt: Date;

  @ApiPropertyOptional({
    description: 'Metadata bổ sung',
    example: { source: 'api', refreshInterval: 30000 }
  })
  metadata?: Record<string, any>;
}

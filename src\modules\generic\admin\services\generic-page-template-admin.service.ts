import { Injectable, Logger } from '@nestjs/common';
import { PaginatedResult } from '@/common/response';
import { AppException } from '@/common';
import { GENERIC_PAGE_ERROR_CODES } from '../../exceptions/generic-page-error.code';
import { GenericPageTemplateRepository } from '../../repositories/generic-page-template.repository';
import { GenericPageTemplate } from '../../entities/generic-page-template.entity';
import {
  CreateGenericPageTemplateDto,
  GenericPageTemplateResponseDto,
  QueryGenericPageTemplateDto,
  UpdateGenericPageTemplateDto,
} from '../dto';

@Injectable()
export class GenericPageTemplateAdminService {
  private readonly logger = new Logger(GenericPageTemplateAdminService.name);

  constructor(
    private readonly genericPageTemplateRepository: GenericPageTemplateRepository,
  ) {}

  /**
   * Tạo mẫu trang mới
   * @param createGenericPageTemplateDto Thông tin mẫu trang mới
   * @param employeeId ID của nhân viên tạo mẫu trang
   * @returns Thông tin mẫu trang đã tạo
   */
  async createGenericPageTemplate(
    createGenericPageTemplateDto: CreateGenericPageTemplateDto,
    employeeId: string,
  ): Promise<GenericPageTemplateResponseDto> {
    try {
      // Tạo entity mới
      const genericPageTemplate = new GenericPageTemplate();
      genericPageTemplate.name = createGenericPageTemplateDto.name;
      genericPageTemplate.description =
        createGenericPageTemplateDto.description || '';
      genericPageTemplate.category =
        createGenericPageTemplateDto.category || '';
      genericPageTemplate.thumbnail =
        createGenericPageTemplateDto.thumbnail || '';
      genericPageTemplate.config = createGenericPageTemplateDto.config;
      genericPageTemplate.createdAt = Date.now();
      genericPageTemplate.updatedAt = Date.now();
      genericPageTemplate.createdBy = employeeId;
      genericPageTemplate.updatedBy = employeeId;

      // Lưu vào cơ sở dữ liệu
      const savedGenericPageTemplate =
        await this.genericPageTemplateRepository.save(genericPageTemplate);

      // Chuyển đổi sang DTO
      return this.mapToResponseDto(savedGenericPageTemplate);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Error creating generic page template: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        GENERIC_PAGE_ERROR_CODES.GENERIC_PAGE_TEMPLATE_CREATE_ERROR,
        'Lỗi khi tạo mẫu trang',
      );
    }
  }

  /**
   * Cập nhật mẫu trang
   * @param id ID của mẫu trang
   * @param updateGenericPageTemplateDto Thông tin cập nhật
   * @param employeeId ID của nhân viên cập nhật
   * @returns Thông tin mẫu trang đã cập nhật
   */
  async updateGenericPageTemplate(
    id: string,
    updateGenericPageTemplateDto: UpdateGenericPageTemplateDto,
    employeeId: string,
  ): Promise<GenericPageTemplateResponseDto> {
    try {
      // Tìm mẫu trang theo ID
      const genericPageTemplate =
        await this.genericPageTemplateRepository.findById(id);

      // Cập nhật thông tin
      if (updateGenericPageTemplateDto.name) {
        genericPageTemplate.name = updateGenericPageTemplateDto.name;
      }
      if (updateGenericPageTemplateDto.description !== undefined) {
        genericPageTemplate.description =
          updateGenericPageTemplateDto.description;
      }
      if (updateGenericPageTemplateDto.category !== undefined) {
        genericPageTemplate.category = updateGenericPageTemplateDto.category;
      }
      if (updateGenericPageTemplateDto.thumbnail !== undefined) {
        genericPageTemplate.thumbnail = updateGenericPageTemplateDto.thumbnail;
      }
      if (updateGenericPageTemplateDto.config) {
        genericPageTemplate.config = updateGenericPageTemplateDto.config;
      }
      genericPageTemplate.updatedAt = Date.now();
      genericPageTemplate.updatedBy = employeeId;

      // Lưu vào cơ sở dữ liệu
      const savedGenericPageTemplate =
        await this.genericPageTemplateRepository.save(genericPageTemplate);

      // Chuyển đổi sang DTO
      return this.mapToResponseDto(savedGenericPageTemplate);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Error updating generic page template: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        GENERIC_PAGE_ERROR_CODES.GENERIC_PAGE_TEMPLATE_UPDATE_ERROR,
        `Lỗi khi cập nhật mẫu trang với ID ${id}`,
      );
    }
  }

  /**
   * Lấy thông tin mẫu trang theo ID
   * @param id ID của mẫu trang
   * @returns Thông tin mẫu trang
   */
  async getGenericPageTemplateById(
    id: string,
  ): Promise<GenericPageTemplateResponseDto> {
    try {
      const genericPageTemplate =
        await this.genericPageTemplateRepository.findById(id);
      return this.mapToResponseDto(genericPageTemplate);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Error getting generic page template by ID: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        GENERIC_PAGE_ERROR_CODES.GENERIC_PAGE_TEMPLATE_NOT_FOUND,
        `Lỗi khi lấy thông tin mẫu trang với ID ${id}`,
      );
    }
  }

  /**
   * Xóa mẫu trang
   * @param id ID của mẫu trang
   */
  async deleteGenericPageTemplate(id: string): Promise<void> {
    try {
      // Tìm mẫu trang theo ID
      const genericPageTemplate =
        await this.genericPageTemplateRepository.findById(id);

      // Xóa mẫu trang
      await this.genericPageTemplateRepository.remove(genericPageTemplate);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Error deleting generic page template: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        GENERIC_PAGE_ERROR_CODES.GENERIC_PAGE_TEMPLATE_DELETE_ERROR,
        `Lỗi khi xóa mẫu trang với ID ${id}`,
      );
    }
  }

  /**
   * Chuyển đổi entity sang DTO
   * @param genericPageTemplate Entity GenericPageTemplate
   * @param tags Danh sách tag
   * @returns DTO GenericPageTemplateResponseDto
   */
  private mapToResponseDto(
    genericPageTemplate: GenericPageTemplate,
  ): GenericPageTemplateResponseDto {
    const responseDto = new GenericPageTemplateResponseDto();
    responseDto.id = genericPageTemplate.id;
    responseDto.name = genericPageTemplate.name;
    responseDto.description = genericPageTemplate.description;
    responseDto.category = genericPageTemplate.category;
    responseDto.thumbnail = genericPageTemplate.thumbnail;
    responseDto.config = genericPageTemplate.config;
    responseDto.createdAt = genericPageTemplate.createdAt;
    responseDto.updatedAt = genericPageTemplate.updatedAt;
    responseDto.createdBy = genericPageTemplate.createdBy;
    responseDto.updatedBy = genericPageTemplate.updatedBy;
    return responseDto;
  }
}

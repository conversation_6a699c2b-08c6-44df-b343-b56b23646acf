{"extends": "./tsconfig.json", "compilerOptions": {"target": "ES2022", "module": "commonjs", "baseUrl": "./", "outDir": "./dist", "incremental": false, "tsBuildInfoFile": null, "skipLibCheck": true, "skipDefaultLibCheck": true, "isolatedModules": false, "declaration": false, "declarationMap": false, "sourceMap": false, "removeComments": true, "assumeChangesOnlyAffectDirectDependencies": true, "preserveSymlinks": false, "preserveWatchOutput": false, "pretty": false, "listFiles": false, "listEmittedFiles": false, "traceResolution": false, "diagnostics": false, "extendedDiagnostics": false, "noEmitOnError": true, "noImplicitReturns": false, "noImplicitThis": false, "noImplicitAny": false, "strictNullChecks": false, "strict": false}, "exclude": ["node_modules", "test", "dist", "**/*spec.ts", "**/*.test.ts", "coverage", "docs", "scripts", "migrations", "**/*.d.ts"], "include": ["src/**/*"]}
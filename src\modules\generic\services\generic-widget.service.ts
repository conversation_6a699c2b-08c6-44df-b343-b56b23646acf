import { Injectable, Logger } from '@nestjs/common';
import { GenericSessionWidgetRepository, GenericSessionLayoutRepository } from '../repositories';
import { GenericSessionWidget } from '../entities';
import { CreateGenericWidgetDto, UpdateGenericWidgetDto, GenericWidgetResponseDto } from '../dto/generic-widget.dto';
import { LayoutItemDto } from '../dto/generic-layout.dto';
import { AppException } from '@/common';
import { GENERIC_ERROR_CODES } from '../exceptions';

@Injectable()
export class GenericWidgetService {
  private readonly logger = new Logger(GenericWidgetService.name);

  constructor(
    private readonly genericSessionWidgetRepository: GenericSessionWidgetRepository,
    private readonly genericSessionLayoutRepository: GenericSessionLayoutRepository,
  ) {}

  /**
   * Thêm widget vào session
   * @param sessionId Session ID
   * @param createDto Data để tạo widget
   * @returns Widget đã được tạo
   */
  async addWidgetToSession(sessionId: string, createDto: CreateGenericWidgetDto): Promise<GenericWidgetResponseDto> {
    try {
      // Kiểm tra widgetId đã tồn tại trong session chưa
      const existingWidget = await this.genericSessionWidgetRepository.findBySessionAndWidgetIdOptional(
        sessionId, 
        createDto.widgetId
      );
      
      if (existingWidget) {
        throw new AppException(
          GENERIC_ERROR_CODES.GENERIC_WIDGET_ALREADY_EXISTS,
          `Widget ${createDto.widgetId} đã tồn tại trong session ${sessionId}`,
        );
      }

      // Tạo widget mới
      const widget = this.genericSessionWidgetRepository.create({
        sessionId,
        widgetId: createDto.widgetId,
        widgetType: createDto.widgetType,
        widgetData: createDto.widgetData,
        position: createDto.position,
        displayOrder: createDto.displayOrder || 0,
        isVisible: createDto.isVisible !== undefined ? createDto.isVisible : true,
        metadata: createDto.metadata,
      });

      const savedWidget = await this.genericSessionWidgetRepository.save(widget);

      // Cập nhật layout để include widget mới
      await this.updateLayoutWithNewWidget(sessionId, savedWidget);

      this.logger.log(`Added widget ${createDto.widgetId} to session ${sessionId}`);
      return this.mapToResponseDto(savedWidget);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error adding widget to session: ${error.message}`, error.stack);
      throw new AppException(
        GENERIC_ERROR_CODES.GENERIC_WIDGET_CREATE_ERROR,
        `Lỗi khi thêm widget ${createDto.widgetId} vào session ${sessionId}`,
      );
    }
  }

  /**
   * Cập nhật widget trong session
   * @param sessionId Session ID
   * @param widgetId Widget ID
   * @param updateDto Data để cập nhật
   * @returns Widget đã được cập nhật
   */
  async updateWidgetInSession(
    sessionId: string, 
    widgetId: string, 
    updateDto: UpdateGenericWidgetDto
  ): Promise<GenericWidgetResponseDto> {
    try {
      const widget = await this.genericSessionWidgetRepository.findBySessionAndWidgetId(sessionId, widgetId);

      // Cập nhật các trường
      if (updateDto.widgetData !== undefined) {
        widget.widgetData = updateDto.widgetData;
      }
      if (updateDto.position !== undefined) {
        widget.position = updateDto.position;
      }
      if (updateDto.displayOrder !== undefined) {
        widget.displayOrder = updateDto.displayOrder;
      }
      if (updateDto.isVisible !== undefined) {
        widget.isVisible = updateDto.isVisible;
      }
      if (updateDto.metadata !== undefined) {
        widget.metadata = updateDto.metadata;
      }

      const updatedWidget = await this.genericSessionWidgetRepository.save(widget);

      // Cập nhật layout nếu position thay đổi
      if (updateDto.position !== undefined) {
        await this.updateLayoutWithWidgetPosition(sessionId, updatedWidget);
      }

      this.logger.log(`Updated widget ${widgetId} in session ${sessionId}`);
      return this.mapToResponseDto(updatedWidget);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error updating widget: ${error.message}`, error.stack);
      throw new AppException(
        GENERIC_ERROR_CODES.GENERIC_WIDGET_UPDATE_ERROR,
        `Lỗi khi cập nhật widget ${widgetId} trong session ${sessionId}`,
      );
    }
  }

  /**
   * Xóa widget khỏi session
   * @param sessionId Session ID
   * @param widgetId Widget ID
   * @returns Số lượng widgets đã xóa
   */
  async removeWidgetFromSession(sessionId: string, widgetId: string): Promise<number> {
    try {
      // Kiểm tra widget có tồn tại không
      await this.genericSessionWidgetRepository.findBySessionAndWidgetId(sessionId, widgetId);

      // Xóa widget
      const deletedCount = await this.genericSessionWidgetRepository.removeBySessionAndWidgetId(sessionId, widgetId);

      // Cập nhật layout để remove widget
      await this.updateLayoutRemoveWidget(sessionId, widgetId);

      this.logger.log(`Removed widget ${widgetId} from session ${sessionId}`);
      return deletedCount;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error removing widget: ${error.message}`, error.stack);
      throw new AppException(
        GENERIC_ERROR_CODES.GENERIC_WIDGET_DELETE_ERROR,
        `Lỗi khi xóa widget ${widgetId} khỏi session ${sessionId}`,
      );
    }
  }

  /**
   * Lấy tất cả widgets của session
   * @param sessionId Session ID
   * @param includeHidden Có bao gồm widgets ẩn không
   * @returns Danh sách widgets
   */
  async getWidgetsBySessionId(sessionId: string, includeHidden: boolean = false): Promise<GenericWidgetResponseDto[]> {
    try {
      const widgets = await this.genericSessionWidgetRepository.findBySessionId(sessionId, includeHidden);
      return widgets.map(widget => this.mapToResponseDto(widget));
    } catch (error) {
      this.logger.error(`Error getting widgets by session ID: ${error.message}`, error.stack);
      throw new AppException(
        GENERIC_ERROR_CODES.GENERIC_WIDGET_QUERY_ERROR,
        `Lỗi khi lấy widgets của session ${sessionId}`,
      );
    }
  }

  /**
   * Lấy widget theo sessionId và widgetId
   * @param sessionId Session ID
   * @param widgetId Widget ID
   * @returns Widget nếu tìm thấy
   */
  async getWidgetBySessionAndWidgetId(sessionId: string, widgetId: string): Promise<GenericWidgetResponseDto> {
    try {
      const widget = await this.genericSessionWidgetRepository.findBySessionAndWidgetId(sessionId, widgetId);
      return this.mapToResponseDto(widget);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error getting widget: ${error.message}`, error.stack);
      throw new AppException(
        GENERIC_ERROR_CODES.GENERIC_WIDGET_NOT_FOUND,
        `Lỗi khi lấy widget ${widgetId} trong session ${sessionId}`,
      );
    }
  }

  /**
   * Ẩn/hiện widget
   * @param sessionId Session ID
   * @param widgetId Widget ID
   * @param isVisible Trạng thái hiển thị
   * @returns Widget đã được cập nhật
   */
  async toggleWidgetVisibility(sessionId: string, widgetId: string, isVisible: boolean): Promise<GenericWidgetResponseDto> {
    try {
      const widget = await this.genericSessionWidgetRepository.updateVisibility(sessionId, widgetId, isVisible);
      
      this.logger.log(`Toggled widget ${widgetId} visibility to ${isVisible} in session ${sessionId}`);
      return this.mapToResponseDto(widget);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error toggling widget visibility: ${error.message}`, error.stack);
      throw new AppException(
        GENERIC_ERROR_CODES.GENERIC_WIDGET_UPDATE_ERROR,
        `Lỗi khi thay đổi visibility của widget ${widgetId}`,
      );
    }
  }

  /**
   * Cập nhật display order của widgets
   * @param sessionId Session ID
   * @param widgetOrders Array of { widgetId, displayOrder }
   * @returns Số lượng widgets đã cập nhật
   */
  async updateWidgetDisplayOrders(
    sessionId: string, 
    widgetOrders: Array<{ widgetId: string; displayOrder: number }>
  ): Promise<number> {
    try {
      const updatedCount = await this.genericSessionWidgetRepository.updateDisplayOrders(sessionId, widgetOrders);
      
      this.logger.log(`Updated display orders for ${updatedCount} widgets in session ${sessionId}`);
      return updatedCount;
    } catch (error) {
      this.logger.error(`Error updating widget display orders: ${error.message}`, error.stack);
      throw new AppException(
        GENERIC_ERROR_CODES.GENERIC_WIDGET_UPDATE_ERROR,
        `Lỗi khi cập nhật display orders trong session ${sessionId}`,
      );
    }
  }

  /**
   * Cập nhật layout khi thêm widget mới
   * @param sessionId Session ID
   * @param widget Widget đã được thêm
   */
  private async updateLayoutWithNewWidget(sessionId: string, widget: GenericSessionWidget): Promise<void> {
    try {
      const layout = await this.genericSessionLayoutRepository.findBySessionIdOptional(sessionId);
      
      if (layout) {
        // Thêm widget vào layout data
        const newLayoutItem: LayoutItemDto = {
          i: widget.widgetId,
          x: widget.position.x,
          y: widget.position.y,
          w: widget.position.w,
          h: widget.position.h,
          minW: widget.position.minW,
          minH: widget.position.minH,
          maxW: widget.position.maxW,
          maxH: widget.position.maxH,
        };

        const updatedLayoutData = [...layout.layoutData, newLayoutItem];
        await this.genericSessionLayoutRepository.updateLayoutData(sessionId, updatedLayoutData);
      }
    } catch (error) {
      this.logger.warn(`Failed to update layout with new widget: ${error.message}`);
      // Không throw error vì đây không phải critical operation
    }
  }

  /**
   * Cập nhật layout khi thay đổi position của widget
   * @param sessionId Session ID
   * @param widget Widget đã được cập nhật
   */
  private async updateLayoutWithWidgetPosition(sessionId: string, widget: GenericSessionWidget): Promise<void> {
    try {
      const layout = await this.genericSessionLayoutRepository.findBySessionIdOptional(sessionId);
      
      if (layout) {
        // Cập nhật position của widget trong layout data
        const updatedLayoutData = layout.layoutData.map(item => {
          if (item.i === widget.widgetId) {
            return {
              ...item,
              x: widget.position.x,
              y: widget.position.y,
              w: widget.position.w,
              h: widget.position.h,
              minW: widget.position.minW,
              minH: widget.position.minH,
              maxW: widget.position.maxW,
              maxH: widget.position.maxH,
            };
          }
          return item;
        });

        await this.genericSessionLayoutRepository.updateLayoutData(sessionId, updatedLayoutData);
      }
    } catch (error) {
      this.logger.warn(`Failed to update layout with widget position: ${error.message}`);
      // Không throw error vì đây không phải critical operation
    }
  }

  /**
   * Cập nhật layout khi xóa widget
   * @param sessionId Session ID
   * @param widgetId Widget ID đã được xóa
   */
  private async updateLayoutRemoveWidget(sessionId: string, widgetId: string): Promise<void> {
    try {
      const layout = await this.genericSessionLayoutRepository.findBySessionIdOptional(sessionId);
      
      if (layout) {
        // Xóa widget khỏi layout data
        const updatedLayoutData = layout.layoutData.filter(item => item.i !== widgetId);
        await this.genericSessionLayoutRepository.updateLayoutData(sessionId, updatedLayoutData);
      }
    } catch (error) {
      this.logger.warn(`Failed to update layout after removing widget: ${error.message}`);
      // Không throw error vì đây không phải critical operation
    }
  }

  /**
   * Map entity to response DTO
   * @param widget GenericSessionWidget entity
   * @returns GenericWidgetResponseDto
   */
  private mapToResponseDto(widget: GenericSessionWidget): GenericWidgetResponseDto {
    return {
      id: widget.id,
      sessionId: widget.sessionId,
      widgetId: widget.widgetId,
      widgetType: widget.widgetType,
      widgetData: widget.widgetData,
      position: widget.position,
      displayOrder: widget.displayOrder,
      isVisible: widget.isVisible,
      createdAt: widget.createdAt,
      updatedAt: widget.updatedAt,
      metadata: widget.metadata,
    };
  }
}

import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { GenericPageUserController } from './controllers';
import { GenericPageUserService } from './services';
import { GenericPage, GenericPageTemplate } from '../entities';
import {
  GenericPageRepository,
  GenericPageTemplateRepository,
} from '../repositories';

@Module({
  imports: [TypeOrmModule.forFeature([GenericPage, GenericPageTemplate])],
  controllers: [GenericPageUserController],
  providers: [
    GenericPageRepository,
    GenericPageTemplateRepository,
    GenericPageUserService,
  ],
  exports: [GenericPageUserService],
})
export class GenericUserModule {}

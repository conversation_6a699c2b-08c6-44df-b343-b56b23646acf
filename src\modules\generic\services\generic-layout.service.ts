import { Injectable, Logger } from '@nestjs/common';
import { GenericSessionLayoutRepository } from '../repositories';
import { GenericSessionLayout } from '../entities';
import { UpdateGenericLayoutDto, GenericLayoutResponseDto, LayoutItemDto } from '../dto/generic-layout.dto';
import { AppException, ErrorCode } from '@/common';

@Injectable()
export class GenericLayoutService {
  private readonly logger = new Logger(GenericLayoutService.name);

  constructor(
    private readonly genericSessionLayoutRepository: GenericSessionLayoutRepository,
  ) {}

  /**
   * Lấy layout của session
   * @param sessionId Session ID
   * @returns Layout nếu tìm thấy
   */
  async getLayoutBySessionId(sessionId: string): Promise<GenericLayoutResponseDto> {
    try {
      const layout = await this.genericSessionLayoutRepository.findBySessionId(sessionId);
      return this.mapToResponseDto(layout);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error getting layout by session ID: ${error.message}`, error.stack);
      throw new AppException(
        ErrorCode.RESOURCE_NOT_FOUND,
        `Lỗi khi lấy layout của session ${sessionId}`,
      );
    }
  }

  /**
   * Lấy layout của session (optional - không throw error)
   * @param sessionId Session ID
   * @returns Layout nếu tìm thấy, null nếu không tìm thấy
   */
  async getLayoutBySessionIdOptional(sessionId: string): Promise<GenericLayoutResponseDto | null> {
    try {
      const layout = await this.genericSessionLayoutRepository.findBySessionIdOptional(sessionId);
      if (!layout) {
        return null;
      }
      return this.mapToResponseDto(layout);
    } catch (error) {
      this.logger.error(`Error getting layout by session ID: ${error.message}`, error.stack);
      return null;
    }
  }

  /**
   * Cập nhật layout của session
   * @param sessionId Session ID
   * @param updateDto Data để cập nhật layout
   * @returns Layout đã được cập nhật
   */
  async updateLayout(sessionId: string, updateDto: UpdateGenericLayoutDto): Promise<GenericLayoutResponseDto> {
    try {
      const layout = await this.genericSessionLayoutRepository.upsertLayout(
        sessionId,
        updateDto.layoutData,
        updateDto.breakpoints,
        updateDto.gridConfig,
        updateDto.metadata
      );

      this.logger.log(`Updated layout for session ${sessionId}`);
      return this.mapToResponseDto(layout);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error updating layout: ${error.message}`, error.stack);
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        `Lỗi khi cập nhật layout cho session ${sessionId}`,
      );
    }
  }

  /**
   * Cập nhật chỉ layout data (positions của widgets)
   * @param sessionId Session ID
   * @param layoutData Layout data mới
   * @returns Layout đã được cập nhật
   */
  async updateLayoutData(sessionId: string, layoutData: LayoutItemDto[]): Promise<GenericLayoutResponseDto> {
    try {
      const layout = await this.genericSessionLayoutRepository.updateLayoutData(sessionId, layoutData);
      
      this.logger.log(`Updated layout data for session ${sessionId}`);
      return this.mapToResponseDto(layout);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error updating layout data: ${error.message}`, error.stack);
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        `Lỗi khi cập nhật layout data cho session ${sessionId}`,
      );
    }
  }

  /**
   * Cập nhật grid configuration
   * @param sessionId Session ID
   * @param gridConfig Grid configuration mới
   * @returns Layout đã được cập nhật
   */
  async updateGridConfig(sessionId: string, gridConfig: any): Promise<GenericLayoutResponseDto> {
    try {
      const layout = await this.genericSessionLayoutRepository.updateGridConfig(sessionId, gridConfig);
      
      this.logger.log(`Updated grid config for session ${sessionId}`);
      return this.mapToResponseDto(layout);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error updating grid config: ${error.message}`, error.stack);
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        `Lỗi khi cập nhật grid config cho session ${sessionId}`,
      );
    }
  }

  /**
   * Cập nhật breakpoints configuration
   * @param sessionId Session ID
   * @param breakpoints Breakpoints configuration mới
   * @returns Layout đã được cập nhật
   */
  async updateBreakpoints(sessionId: string, breakpoints: any): Promise<GenericLayoutResponseDto> {
    try {
      const layout = await this.genericSessionLayoutRepository.updateBreakpoints(sessionId, breakpoints);
      
      this.logger.log(`Updated breakpoints for session ${sessionId}`);
      return this.mapToResponseDto(layout);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error updating breakpoints: ${error.message}`, error.stack);
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        `Lỗi khi cập nhật breakpoints cho session ${sessionId}`,
      );
    }
  }

  /**
   * Tạo layout mặc định cho session
   * @param sessionId Session ID
   * @returns Layout mặc định đã được tạo
   */
  async createDefaultLayout(sessionId: string): Promise<GenericLayoutResponseDto> {
    try {
      const layout = await this.genericSessionLayoutRepository.createDefaultLayout(sessionId);
      
      this.logger.log(`Created default layout for session ${sessionId}`);
      return this.mapToResponseDto(layout);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error creating default layout: ${error.message}`, error.stack);
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        `Lỗi khi tạo layout mặc định cho session ${sessionId}`,
      );
    }
  }

  /**
   * Xóa layout của session
   * @param sessionId Session ID
   * @returns Số lượng layouts đã xóa
   */
  async deleteLayout(sessionId: string): Promise<number> {
    try {
      const deletedCount = await this.genericSessionLayoutRepository.removeBySessionId(sessionId);
      
      this.logger.log(`Deleted layout for session ${sessionId}`);
      return deletedCount;
    } catch (error) {
      this.logger.error(`Error deleting layout: ${error.message}`, error.stack);
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        `Lỗi khi xóa layout của session ${sessionId}`,
      );
    }
  }

  /**
   * Compact layout (tự động sắp xếp lại widgets)
   * @param sessionId Session ID
   * @param compactType Loại compact ('vertical' | 'horizontal')
   * @returns Layout đã được compact
   */
  async compactLayout(sessionId: string, compactType: 'vertical' | 'horizontal' = 'vertical'): Promise<GenericLayoutResponseDto> {
    try {
      const layout = await this.genericSessionLayoutRepository.findBySessionId(sessionId);
      
      // Implement compact logic
      const compactedLayoutData = this.performCompact(layout.layoutData, compactType);
      
      const updatedLayout = await this.genericSessionLayoutRepository.updateLayoutData(sessionId, compactedLayoutData);
      
      this.logger.log(`Compacted layout for session ${sessionId} with type ${compactType}`);
      return this.mapToResponseDto(updatedLayout);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error compacting layout: ${error.message}`, error.stack);
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        `Lỗi khi compact layout cho session ${sessionId}`,
      );
    }
  }

  /**
   * Reset layout về trạng thái mặc định
   * @param sessionId Session ID
   * @returns Layout đã được reset
   */
  async resetLayout(sessionId: string): Promise<GenericLayoutResponseDto> {
    try {
      // Xóa layout hiện tại
      await this.genericSessionLayoutRepository.removeBySessionId(sessionId);
      
      // Tạo layout mặc định mới
      const layout = await this.genericSessionLayoutRepository.createDefaultLayout(sessionId);
      
      this.logger.log(`Reset layout for session ${sessionId}`);
      return this.mapToResponseDto(layout);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error resetting layout: ${error.message}`, error.stack);
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        `Lỗi khi reset layout cho session ${sessionId}`,
      );
    }
  }

  /**
   * Thực hiện compact layout
   * @param layoutData Layout data hiện tại
   * @param compactType Loại compact
   * @returns Layout data đã được compact
   */
  private performCompact(layoutData: any[], compactType: 'vertical' | 'horizontal'): any[] {
    if (compactType === 'vertical') {
      // Sắp xếp theo y, sau đó theo x
      const sorted = [...layoutData].sort((a, b) => {
        if (a.y !== b.y) return a.y - b.y;
        return a.x - b.x;
      });

      // Compact vertical - di chuyển widgets lên trên
      let currentY = 0;
      return sorted.map(item => {
        const newItem = { ...item, y: currentY };
        currentY += item.h;
        return newItem;
      });
    } else {
      // Sắp xếp theo x, sau đó theo y
      const sorted = [...layoutData].sort((a, b) => {
        if (a.x !== b.x) return a.x - b.x;
        return a.y - b.y;
      });

      // Compact horizontal - di chuyển widgets sang trái
      let currentX = 0;
      return sorted.map(item => {
        const newItem = { ...item, x: currentX };
        currentX += item.w;
        return newItem;
      });
    }
  }

  /**
   * Map entity to response DTO
   * @param layout GenericSessionLayout entity
   * @returns GenericLayoutResponseDto
   */
  private mapToResponseDto(layout: GenericSessionLayout): GenericLayoutResponseDto {
    return {
      id: layout.id,
      sessionId: layout.sessionId,
      layoutData: layout.layoutData,
      breakpoints: layout.breakpoints,
      gridConfig: layout.gridConfig,
      updatedAt: layout.updatedAt,
      metadata: layout.metadata,
    };
  }
}

/*
----------------------------------------------------------------
File: facebook-deletion-webhook.service.ts
----------------------------------------------------------------
- Ch<PERSON>a logic cốt lõi để xác thực signed_request.
- Tương tác với cơ sở dữ liệu để xóa dữ liệu người dùng.
*/
import { ConfigService, ConfigType, WebhookConfig } from '@/config';
import { FacebookWebhookService } from '@/shared/services/facebook/webhook/facebook-webhook.service';
import { Injectable, Logger } from '@nestjs/common';


@Injectable()
export class FacebookDeletionWebhookService {
    private readonly logger = new Logger(FacebookDeletionWebhookService.name);
    private readonly webhookConfig: WebhookConfig;


    constructor(
        private configService: ConfigService,
        private readonly facebookWebhookService: FacebookWebhookService,
    ) {
        this.webhookConfig = this.configService.getConfig<WebhookConfig>(
            ConfigType.Webhook,
        );
    }

    /**
     * Xử lý yêu cầu xóa dữ liệu từ webhook của Facebook.
     * @param signedRequest Chuỗi signed_request nhận được.
     * @returns Một đối tượng chứa URL theo dõi và mã xác nhận.
     */
    async handleDataDeletion(signedRequest: string): Promise<{ url: string; confirmation_code: string }> {
        this.logger.log('Bắt đầu xác thực signed_request...');

        const payload = await this.facebookWebhookService.parseAndVerifySignedRequest(signedRequest);
        const userId = payload.user_id;

        this.logger.log(`Xác thực thành công. Bắt đầu xử lý xóa dữ liệu cho user_id: ${userId}`);

        // ===================================================================
        // === THỰC HIỆN LOGIC XÓA DỮ LIỆU CỦA BẠN TẠI ĐÂY ===
        //
        // Ví dụ:
        // await this.userService.deleteByFacebookId(userId);
        // await this.pageService.disconnectAllFromUser(userId);
        //
        // ===================================================================
        this.logger.log(`Đã xử lý xong việc xóa dữ liệu cho user_id: ${userId}`);

        // Tạo URL và mã xác nhận để trả về cho Facebook
        const confirmationCode = `${userId}-${Date.now()}`;
        const statusUrl = `${this.webhookConfig.baseUrl}/facebook/deletion-status/${confirmationCode}`;

        return {
            url: statusUrl,
            confirmation_code: confirmationCode,
        };
    }
}
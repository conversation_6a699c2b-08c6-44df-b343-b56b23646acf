{"name": "redai-backend-p01", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "prebuild:docker": "<PERSON><PERSON><PERSON> dist", "start:dev:check": "concurrently \"nest start --watch\" \"tsc --noEmit --watch\"", "type-check": "tsc --noEmit", "type-check:watch": "tsc --noEmit --watch", "type-check:docker": "tsc -p tsconfig.docker.json --noEmit", "build": "npm run type-check && tsc -p tsconfig.build.json", "build:nest": "npm run type-check && nest build", "build:docker": "npm run prebuild:docker && tsc -p tsconfig.docker.json --noEmitOnError && npx tsc-alias -p tsconfig.docker.json", "clean": "rimraf dist .tsbuildinfo", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "start:localhost": "cross-env NODE_ENV=localhost nest start --watch", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "zalo-oa-migration": "ts-node src/modules/integration/scripts/run-zalo-oa-migration.ts", "test:employee": "jest --config ./src/modules/employee/jest.config.js", "test:strategy": "jest --config ./src/modules/strategy/jest.config.js", "test:affiliate": "jest --config ./src/modules/affiliate/jest.config.js", "test:marketplace-admin": "node --max-old-space-size=5120 ./node_modules/jest/bin/jest.js --config ./src/modules/marketplace/admin/test/jest.config.ts", "test:queue": "jest src/shared/queue/tests/queue.test.ts --forceExit", "test:sms": "jest --config ./src/modules/sms/tests/jest.config.js", "test:marketing": "jest --config ./src/modules/marketing/tests/jest.config.js", "test:twilio": "jest src/modules/sms/tests/twilio-sms.service.spec.ts src/modules/marketing/admin/tests/admin-twilio-sms.controller.spec.ts src/modules/marketing/user/tests/user-twilio-sms.controller.spec.ts --verbose", "test:twilio-unit": "jest src/modules/sms/tests/ --config ./src/modules/sms/tests/jest.config.js --verbose", "test:twilio-integration": "jest src/modules/marketing/admin/tests/admin-twilio-sms.controller.spec.ts src/modules/marketing/user/tests/user-twilio-sms.controller.spec.ts --config ./src/modules/marketing/tests/jest.config.js --verbose", "encrypt-email-integrations": "ts-node scripts/encrypt-email-integrations.ts", "test:twilio-coverage": "jest src/modules/sms/tests/ src/modules/marketing/admin/tests/admin-twilio-sms.controller.spec.ts src/modules/marketing/user/tests/user-twilio-sms.controller.spec.ts --coverage --verbose", "queue:test": "ts-node -r tsconfig-paths/register src/scripts/queue-test.ts", "migration:generate": "typeorm-ts-node-commonjs migration:generate -d src/data-source.ts", "migration:run": "typeorm-ts-node-commonjs migration:run -d src/data-source.ts", "migration:revert": "typeorm-ts-node-commonjs migration:revert -d src/data-source.ts", "migration:show": "typeorm-ts-node-commonjs migration:show -d src/data-source.ts"}, "dependencies": {"@anthropic-ai/sdk": "^0.40.1", "@aws-sdk/client-s3": "^3.787.0", "@aws-sdk/lib-storage": "^3.856.0", "@aws-sdk/s3-request-presigner": "^3.787.0", "@geersch/nestjs-retry": "^1.0.0", "@google-cloud/storage": "^7.16.0", "@google-cloud/translate": "^9.0.1", "@google-cloud/vision": "^5.1.0", "@google/genai": "^0.13.0", "@google/generative-ai": "^0.24.1", "@googleapis/analytics": "^8.0.1", "@googleapis/calendar": "^10.0.1", "@googleapis/docs": "^4.0.1", "@googleapis/drive": "^13.0.1", "@googleapis/gmail": "^13.0.1", "@googleapis/sheets": "^10.0.1", "@modelcontextprotocol/sdk": "^1.15.1", "@nestjs/axios": "^4.0.0", "@nestjs/bull": "^11.0.2", "@nestjs/bullmq": "^11.0.2", "@nestjs/common": "^11.0.1", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.0.1", "@nestjs/event-emitter": "^3.0.1", "@nestjs/jwt": "^11.0.0", "@nestjs/microservices": "^11.1.3", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.0.1", "@nestjs/platform-socket.io": "^11.1.5", "@nestjs/schedule": "^6.0.0", "@nestjs/swagger": "^11.1.3", "@nestjs/typeorm": "^11.0.0", "@nestjs/websockets": "^11.1.5", "@paypal/paypal-server-sdk": "^1.1.0", "@pdf-lib/fontkit": "^1.1.1", "@types/connect-redis": "^0.0.23", "@types/express-session": "^1.18.2", "@types/node-cron": "^3.0.11", "@types/ua-parser-js": "^0.7.39", "@types/uuid": "^10.0.0", "@warriorteam/messenger-sdk": "^1.3.0", "@xstate/fsm": "^2.1.0", "ajv": "^8.17.1", "ajv-formats": "^3.0.1", "bcrypt": "^5.1.1", "buffer-crc32": "^1.0.0", "bullmq": "^5.53.0", "cheerio": "^1.0.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "connect-redis": "^6.1.3", "cron-parser": "^5.3.0", "csv-parser": "^3.2.0", "currency-codes": "^2.2.0", "eventsource": "^4.0.0", "exponential-backoff": "^3.1.2", "express-session": "^1.18.2", "facebook-nodejs-business-sdk": "^23.0.0", "google-ads-api": "^19.0.4-rest-beta", "googleapis": "^148.0.0", "helmet": "^8.1.0", "ioredis": "^5.6.1", "joi": "^17.13.3", "jsonschema": "^1.5.0", "libphonenumber-js": "^1.12.9", "luxon": "^3.7.1", "mammoth": "^1.9.1", "nanoid": "^5.1.5", "nestjs-i18n": "^10.5.1", "node-cron": "^4.2.1", "node-fetch": "^3.3.2", "nodemailer": "^6.10.1", "npm": "^11.3.0", "openai": "^4.94.0", "opossum": "^8.4.0", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "pdf-lib": "^1.17.1", "pg": "^8.14.1", "puppeteer": "^24.9.0", "qrcode": "^1.5.4", "react-grid-layout": "^1.5.1", "redis": "^4.7.1", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "sharp": "^0.34.1", "socket.io": "^4.8.1", "speakeasy": "^2.0.0", "string-similarity": "^4.0.4", "stripe": "^18.3.0", "swagger-ui-express": "^5.0.1", "tiktoken": "^1.0.21", "tsconfig-paths": "^4.2.0", "typeorm": "^0.3.22", "typeorm-naming-strategies": "^4.1.0", "typeorm-transactional": "^0.5.0", "ua-parser-js": "^2.0.4", "uuid": "^11.1.0", "xlsx": "^0.18.5", "xstate": "^5.19.2"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.18.0", "@nestjs/cli": "^11.0.7", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.1", "@swc/cli": "^0.6.0", "@swc/core": "^1.10.7", "@types/bcrypt": "^5.0.2", "@types/bull": "^3.15.9", "@types/eventsource": "^1.1.15", "@types/express": "^5.0.0", "@types/jest": "^29.5.14", "@types/jsonschema": "^0.0.5", "@types/multer": "^2.0.0", "@types/node": "^22.10.7", "@types/nodemailer": "^6.4.17", "@types/opossum": "^8.1.8", "@types/passport-jwt": "^4.0.1", "@types/selenium-webdriver": "^4.1.28", "@types/supertest": "^6.0.2", "axios": "^1.10.0", "concurrently": "^9.2.0", "cross-env": "^7.0.3", "eslint": "^9.25.1", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.2", "globals": "^16.0.0", "jest": "^29.7.0", "prettier": "^3.4.2", "rimraf": "^6.0.1", "shx": "^0.4.0", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.3.4", "ts-loader": "^9.5.2", "ts-migrate": "^0.1.35", "ts-migrate-plugins": "^0.1.35", "ts-migrate-server": "^0.1.33", "ts-node": "^10.9.2", "tsc-alias": "^1.8.10", "typescript": "^5.8.3", "typescript-eslint": "^8.20.0"}}
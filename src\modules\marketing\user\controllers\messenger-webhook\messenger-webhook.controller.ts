import {
  Body,
  Controller,
  Get,
  HttpException,
  HttpStatus,
  Logger,
  Post,
  Query,
  SetMetadata,
  UseGuards,
} from '@nestjs/common';
import { InjectQueue } from '@nestjs/bullmq';
import { Queue } from 'bullmq';
import { ConfigService } from '@/config';
import { QueueName } from '@/shared/queue/queue.constants';
import { Public } from '@/common/decorators/public.decorator';
import {
  getWebhookEventType,
  WebhookEventType,
} from '@warriorteam/messenger-sdk';
import { FacebookSignatureGuard } from './guards/facebook-signature.guard';
@Controller('facebook')
@Public()
@SetMetadata('isPublic', true)
export class FacebookController {
  private readonly logger = new Logger(FacebookController.name);
  private readonly verifyToken: string;
  private readonly routableEventTypes: WebhookEventType[] = [
    WebhookEventType.MESSAGE,
    WebhookEventType.MESSAGE_EDIT,
    WebhookEventType.MESSAGE_REACTION,
    WebhookEventType.MESSAGE_READ,
    WebhookEventType.MESSAGING_FEEDBACK,
    WebhookEventType.MESSAGING_POSTBACK,
  ];
  constructor(
    private readonly config: ConfigService,
    @InjectQueue(QueueName.MESSENGER_AI_INTERCEPT)
    private readonly messengerQueue: Queue,
  ) {
    this.verifyToken = this.config.facebook.webhookVerifyToken;
  }

  @Get('/webhook') getWebhook(@Query() query: any): string {
    const isValid =
      query['hub.mode'] === 'subscribe' &&
      query['hub.verify_token'] === this.verifyToken;
    if (!isValid) {
      throw new HttpException('Forbidden', HttpStatus.FORBIDDEN);
    }
    this.logger.log('WEBHOOK_VERIFIED');
    return query['hub.challenge'];
  }

  @Post('/webhook')
  @UseGuards(FacebookSignatureGuard)
  async postWebhook(@Body() body: any): Promise<string> {
    if (body.object !== 'page') {
      throw new HttpException('Not a page event', HttpStatus.NOT_FOUND);
    }
    try {
      this.logger.log('Webhook received and signature verified', {
        eventCount: body.entry?.length || 0,
        object: body.object,
      });

      const eventType = getWebhookEventType(body);
      if (eventType && this.isRoutableEvent(eventType)) {
        this.logger.log(`Routable event detected: ${eventType}`);

        // Enqueue the event for processing
        await this.messengerQueue.add(QueueName.MESSENGER_AI_INTERCEPT, body, {
          removeOnComplete: true,
          removeOnFail: true,
        });

        this.logger.debug('Event successfully enqueued for processing');
      } else {
        this.logger.debug('Event not routable, skipping queue', { eventType });
      }

      return 'EVENT_RECEIVED';
    } catch (error) {
      this.logger.error('Error processing webhook event:', {
        error: error.message,
        stack: error.stack,
      });
      throw new HttpException(
        'Internal Server Error',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  private isRoutableEvent(eventType: WebhookEventType): boolean {
    return this.routableEventTypes.includes(eventType);
  }
}

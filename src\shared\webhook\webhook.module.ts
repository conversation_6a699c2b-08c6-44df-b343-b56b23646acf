import { Module, Global } from '@nestjs/common';
import { APP_INTERCEPTOR } from '@nestjs/core';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { BullModule } from '@nestjs/bullmq';
import { QueueName } from '@shared/queue/queue.constants';

// Interceptors
import { WebhookInterceptor } from './interceptors/webhook.interceptor';

// Handlers
import { WebhookHandler } from './handlers/webhook.handler';

// Services
import { WebhookEventService } from './services/webhook-event.service';
import { WebhookLogService } from './services/webhook-log.service';
import { WebhookAnalyticsService } from './services/webhook-analytics.service';
import { WebhookQueueService } from './services/webhook-queue.service';

/**
 * Webhook Module - Global module cho webhook system
 * Cung cấp webhook functionality cho toàn bộ ứng dụng
 */
@Global()
@Module({
  imports: [
    // EventEmitter đã được import ở app.module.ts
    // EventEmitterModule.forRoot(),

    // Register webhook queues
    BullModule.registerQueue(
      { name: QueueName.WEBHOOK },
      { name: QueueName.EXTERNAL_WEBHOOK },
    ),
  ],
  controllers: [],
  providers: [
    // Services
    WebhookEventService,
    WebhookLogService,
    WebhookAnalyticsService,
    WebhookQueueService,

    // Handlers
    WebhookHandler,

    // Global Interceptor
    {
      provide: APP_INTERCEPTOR,
      useClass: WebhookInterceptor,
    },
  ],
  exports: [
    // Export services để các modules khác có thể sử dụng
    WebhookEventService,
    WebhookLogService,
    WebhookAnalyticsService,
    WebhookQueueService,
    WebhookHandler,
  ],
})
export class WebhookModule {}

import { Body, Controller, Get, Param } from '@nestjs/common';
import {
  ApiExtraModels,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { ApiResponseDto } from '@common/response';
import { GenericPageUserService } from '../services';
import {
  GenericPageResponseDto,
  SubmissionResponseDto,
  SubmitFormDto,
} from '../dto';
import { Request } from 'express';
// TODO: Fix import path or create this decorator
// import { Public } from '@modules/auth/decorators/public.decorator';

@ApiTags('User Generic Page')
@Controller('user/generic-pages')
@ApiExtraModels(ApiResponseDto, GenericPageResponseDto, SubmissionResponseDto)
export class GenericPageUserController {
  constructor(
    private readonly genericPageUserService: GenericPageUserService,
  ) {}

  /**
   * Lấy thông tin trang đã xuất bản theo đường dẫn
   */
  @Get('by-path/:path')
  // @Public()
  @ApiOperation({ summary: 'L<PERSON>y thông tin trang đã xuất bản theo đường dẫn' })
  @ApiParam({ name: 'path', description: 'Đường dẫn của trang' })
  @ApiResponse({
    status: 200,
    description: 'Thông tin trang',
    schema: ApiResponseDto.getSchema(GenericPageResponseDto),
  })
  async getPublishedGenericPageByPath(
    @Param('path') path: string,
  ): Promise<ApiResponseDto<GenericPageResponseDto>> {
    const result =
      await this.genericPageUserService.getPublishedGenericPageByPath(path);
    return ApiResponseDto.success(result);
  }
}

import { registerDecorator, ValidationOptions, ValidationArguments } from 'class-validator';

/**
 * Custom validator để kiểm tra logic model:
 * Bắt buộc phải có modelId
 */
export function IsValidModelConfiguration(validationOptions?: ValidationOptions) {
  return function (object: object, propertyName: string) {
    registerDecorator({
      name: 'isValidModelConfiguration',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: {
        validate(value: any, args: ValidationArguments) {
          const obj = args.object as any;

          // Kiểm tra modelId
          const hasModel = obj.modelId && obj.modelId.trim().length > 0;

          return hasModel;
        },
        defaultMessage(args: ValidationArguments) {
          return 'Bắt buộc phải có modelId';
        },
      },
    });
  };
}

/**
 * Custom validator để kiểm tra khối user model phải đ<PERSON><PERSON> đủ
 */
export function IsCompleteUserModelBlock(validationOptions?: ValidationOptions) {
  return function (object: object, propertyName: string) {
    registerDecorator({
      name: 'isCompleteUserModelBlock',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: {
        validate(value: any, args: ValidationArguments) {
          const obj = args.object as any;
          
          // Nếu có userModelId thì phải có keyLlmId
          if (obj.userModelId && obj.userModelId.trim().length > 0) {
            return obj.keyLlmId && obj.keyLlmId.trim().length > 0;
          }
          
          // Nếu có keyLlmId thì phải có userModelId
          if (obj.keyLlmId && obj.keyLlmId.trim().length > 0) {
            return obj.userModelId && obj.userModelId.trim().length > 0;
          }
          
          return true; // Nếu không có gì thì OK
        },
        defaultMessage(args: ValidationArguments) {
          return 'Nếu sử dụng user model, phải có đầy đủ userModelId và keyLlmId';
        },
      },
    });
  };
}

import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import {
  GenericPageAdminController,
  GenericPageTemplateAdminController,
} from './controllers';
import {
  GenericPageAdminService,
  GenericPageTemplateAdminService,
} from './services';
import { GenericPage, GenericPageTemplate } from '../entities';
import {
  GenericPageRepository,
  GenericPageTemplateRepository,
} from '../repositories';

@Module({
  imports: [TypeOrmModule.forFeature([GenericPage, GenericPageTemplate])],
  controllers: [GenericPageAdminController, GenericPageTemplateAdminController],
  providers: [
    GenericPageRepository,
    GenericPageTemplateRepository,
    GenericPageAdminService,
    GenericPageTemplateAdminService,
  ],
  exports: [GenericPageAdminService, GenericPageTemplateAdminService],
})
export class GenericAdminModule {}

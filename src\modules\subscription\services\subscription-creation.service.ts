import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Transactional } from 'typeorm-transactional';
import { SubscriptionPayment, UserAddonUsage } from '@modules/subscription/entities';
import { AddonType } from '@modules/subscription/enums/addon-type.enum';
import { UserRepository } from '@modules/user/repositories/user.repository';
import { AppException, ErrorCode } from '@common/exceptions';

/**
 * Interface cho kết quả tạo subscription (đã đơn giản hóa)
 */
export interface SubscriptionCreationResult {
  userAddonUsages: UserAddonUsage[];
  totalCapacityAdded: number;
  paymentInfo: {
    paymentId: number;
    userId: number;
    planName: string;
  };
}

/**
 * Service xử lý tạo subscription sau khi thanh toán thành công
 * ĐÃ ĐƯỢC REFACTOR: Bỏ UserSubscription, chỉ tạo UserAddonUsage trực tiếp từ payment.infoDetails
 */
@Injectable()
export class SubscriptionCreationService {
  private readonly logger = new Logger(SubscriptionCreationService.name);

  constructor(
    @InjectRepository(UserAddonUsage)
    private readonly userAddonUsageRepository: Repository<UserAddonUsage>,
    private readonly userRepository: UserRepository,
  ) {}

  /**
   * Tạo subscription từ payment đã confirmed
   * REFACTORED: Bây giờ chỉ tạo UserAddonUsage trực tiếp từ payment.infoDetails
   * @param payment Payment đã được confirmed
   * @returns Kết quả tạo subscription
   */
  @Transactional()
  async createSubscriptionFromPayment(payment: SubscriptionPayment): Promise<SubscriptionCreationResult> {
    try {
      this.logger.log(`Bắt đầu tạo subscription cho payment ${payment.id}, user ${payment.userId}`);

      // 1. Validate payment status
      if (payment.status !== 'CONFIRMED') {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          'Payment chưa được confirmed, không thể tạo subscription'
        );
      }

      // 2. Validate payment có infoDetails không
      if (!payment.infoDetails || !payment.infoDetails.packageOptionAddons) {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          `Payment ${payment.id} không có thông tin infoDetails hoặc packageOptionAddons`
        );
      }

      // 3. Lấy thông tin addon từ payment.infoDetails
      const packageOptionAddons = payment.infoDetails.packageOptionAddons;
      const planInfo = payment.infoDetails.planPricing;

      // 4. Tính toán thời gian sử dụng
      const now = Date.now();
      const billingCycleDays = planInfo.billingCycle === 'yearly' ? 365 : 30;
      const usagePeriodStart = now;
      const usagePeriodEnd = now + billingCycleDays * 24 * 60 * 60 * 1000;

      // 5. Tạo hoặc cập nhật UserAddonUsage cho từng addon
      const userAddonUsages: UserAddonUsage[] = [];
      let totalCapacityAdded = 0;

      for (const packageAddon of packageOptionAddons) {
        const capacityAdded = Number(packageAddon.quantity) || 1;
        const addonInfo = packageAddon.addon;

        // Validate capacity value
        if (isNaN(capacityAdded) || capacityAdded <= 0) {
          throw new AppException(
            ErrorCode.VALIDATION_ERROR,
            `Invalid quantity for addon ${packageAddon.addonId}: ${packageAddon.quantity}`
          );
        }

        // Validate capacity không quá lớn (PostgreSQL bigint max: 9223372036854775807)
        if (capacityAdded > 9223372036854775807) {
          throw new AppException(
            ErrorCode.VALIDATION_ERROR,
            `Quantity too large for addon ${packageAddon.addonId}: ${capacityAdded}`
          );
        }

        // ✅ Xử lý R-Point addon - cộng vào pointsBalance
        if (addonInfo?.type === AddonType.RPOINT) {
          this.logger.log(`Phát hiện R-Point addon ${packageAddon.addonId}, cộng ${capacityAdded} points cho user ${payment.userId}`);

          try {
            await this.userRepository.updateUserBalance(payment.userId, capacityAdded);
            this.logger.log(`Đã cộng ${capacityAdded} R-Point cho user ${payment.userId} từ addon ${packageAddon.addonId}`);
          } catch (error) {
            this.logger.error(`Lỗi khi cộng R-Point cho user ${payment.userId}: ${error.message}`, error.stack);
            throw new AppException(
              ErrorCode.INTERNAL_SERVER_ERROR,
              `Lỗi khi cộng R-Point: ${error.message}`
            );
          }
        }

        // Tìm existing user addon usage
        const existingUsage = await this.userAddonUsageRepository.findOne({
          where: {
            userId: payment.userId,
            addonId: packageAddon.addonId,
          },
        });

        let savedUsage: UserAddonUsage;

        if (existingUsage) {
          // Cập nhật existing usage: cộng dồn quantity
          // NOTE: Logic này đơn giản hóa, không phân biệt MONTHLY vs VOLUME billing type
          // Để có logic phức tạp hơn, sử dụng UserAddonUsageCapacityService
          const currentValue = Number(existingUsage.remainingValue) || 0;
          existingUsage.remainingValue = currentValue + capacityAdded;

          // Cập nhật usageLimit nếu cần
          const currentLimit = Number(existingUsage.usageLimit) || 0;
          existingUsage.usageLimit = currentLimit + capacityAdded;

          existingUsage.lastUpdatedAt = now;

          savedUsage = await this.userAddonUsageRepository.save(existingUsage);

          this.logger.log(
            `Đã cập nhật user addon usage: user ${payment.userId}, addon ${packageAddon.addonId}, ` +
            `old: ${currentValue}, add: ${capacityAdded}, new: ${savedUsage.remainingValue}`
          );
        } else {
          // Tạo mới user addon usage
          const userAddonUsage = this.userAddonUsageRepository.create({
            userId: payment.userId,
            addonId: packageAddon.addonId,
            remainingValue: capacityAdded,
            usageLimit: capacityAdded, // Set usageLimit = quantity ban đầu
            currentUsage: 0, // Bắt đầu với 0 usage
            usageUnit: packageAddon.unit || addonInfo?.volumeUnit || 'units',
            usagePeriodStart: usagePeriodStart,
            usagePeriodEnd: usagePeriodEnd,
            status: 'ACTIVE',
            lastUpdatedAt: now,
          });

          savedUsage = await this.userAddonUsageRepository.save(userAddonUsage);

          this.logger.log(
            `Đã tạo mới user addon usage: user ${payment.userId}, addon ${packageAddon.addonId}, quantity: ${capacityAdded}`
          );
        }

        userAddonUsages.push(savedUsage);
        totalCapacityAdded += capacityAdded;
      }

      this.logger.log(
        `Đã tạo subscription thành công cho user ${payment.userId} với ${userAddonUsages.length} addon usages, tổng capacity: ${totalCapacityAdded}`
      );

      return {
        userAddonUsages,
        totalCapacityAdded,
        paymentInfo: {
          paymentId: payment.id,
          userId: payment.userId,
          planName: planInfo.name || planInfo.plan?.name || 'Unknown Plan',
        },
      };

    } catch (error) {
      this.logger.error(`Lỗi khi tạo subscription cho payment ${payment.id}: ${error.message}`, error.stack);
      throw error;
    }
  }
}

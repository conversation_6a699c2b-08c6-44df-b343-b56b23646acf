import { EncryptionService as SimpleEncryptionService } from '@/shared/services/encryption.service';
import { KeyPairEncryptionService } from '@/shared/services/encryption/key-pair-encryption.service';
import { Agent } from '@modules/agent/entities';
import { AgentRepository } from '@modules/agent/repositories/agent.repository';
import { MarketingUserModule } from '@modules/marketing/user';
import { GoogleAdsAccount } from '@modules/marketing/user/entities/google-ads-account.entity';
import { ModelIntegration } from '@modules/models/entities/model-integration.entity';
import { ModelRegistry } from '@modules/models/entities/model-registry.entity';
import { Models } from '@modules/models/entities/models.entity';
import { Addon, UserAddonUsage } from '@modules/subscription/entities';
import {
  AddonRepository,
  UserAddonUsageRepository,
} from '@modules/subscription/repositories';
import { Bank } from '@modules/user/entities/bank.entity';
import { User } from '@modules/user/entities/user.entity';
import { BankRepository } from '@modules/user/repositories/bank.repository';
import { HttpModule } from '@nestjs/axios';
import { BullModule } from '@nestjs/bullmq';
import { forwardRef, Global, Module } from '@nestjs/common';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { TypeOrmModule } from '@nestjs/typeorm';
import { QueueName } from '@shared/queue/queue.constants';
import { GoogleApiModule } from '@shared/services/google';
import { SepayHubModule } from '@shared/services/sepay-hub';
import { ServicesModule } from '@shared/services/services.module';
import { SmsModule } from '@shared/services/sms/sms.module';
import * as entities from '../entities';
import * as repositories from '../repositories';
import { GmailIntegrationRepository } from '../repositories/gmail-integration.repository';
import { GoogleAdsAccountRepository } from '../repositories/google-ads-account.repository';
import { IntegrationProviderRepository } from '../repositories/integration-provider.repository';
import { ZaloOAIntegrationRepository } from '../repositories/zalo-oa-integration.repository';
import { ZaloPersonalIntegrationRepository } from '../repositories/zalo-personal-integration.repository';
import { EmailServerConfigurationHelper } from '../services';
import { GmailIntegrationService } from '../services/gmail-integration.service';
import { HttpClientService } from '../services/http-client.service';
import { IntegrationProviderService } from '../services/integration-provider.service';
import { AhamoveValidationService } from '../services/providers/ahamove-validation.service';
import { GHNValidationService } from '../services/providers/ghn-validation.service';
import { GHTKValidationService } from '../services/providers/ghtk-validation.service';
import { JTValidationService } from '../services/providers/jt-validation.service';
import { SmsServerConfigurationMigrationService } from '../services/sms-server-configuration-migration.service';
import { ZaloOAAdapterService } from '../services/zalo-oa-adapter.service';
import { ZaloOAIntegrationService } from '../services/zalo-oa-integration.service';
import { ZaloOALegacyWrapperService } from '../services/zalo-oa-legacy-wrapper.service';
import { ZaloPersonalIntegrationService } from '../services/zalo-personal-integration.service';
import { ZaloPersonalWebhookService } from '../services/zalo-personal-webhook.service';
import * as controllers from './controllers';
import { ShippingIntegrationValidationHelper } from './helpers';
import { ShippingIntegrationRepository } from './repositories';
import { FptSmsIntegrationRepository } from './repositories/fpt-sms-integration.repository';
import * as services from './services';

// Import LLM Key Model Crawler services
import { Media } from '@/modules/data/media/entities';
import { MediaRepository } from '@/modules/data/media/repositories/media.repository';
import { DataSource } from 'typeorm';
import { IntegrationKeyCreatedListener } from '../services/llm-key/events/integration-key-created.listener';
import { LlmKeyModelCrawlerService } from '../services/llm-key/llm-key-model-crawler.service';
import { ModelSyncService } from '../services/llm-key/model-sync.service';
import { ProviderModelFetcherService } from '../services/llm-key/provider-model-fetcher.service';

// Google integrations
import { GoogleCalendarApi } from './api/google-calendar.api';
import { GoogleDocsApi } from './api/google-docs.api';
import { GoogleSheetsApi } from './api/google-sheets.api';
import { FacebookDeletionWebhookService } from './services/facebook-deletion-webhook.service';
import { GoogleCalendarIntegrationService } from './services/google-calendar-integration.service';
import { GoogleDocsIntegrationService } from './services/google-docs-integration.service';
import { GoogleSheetsIntegrationService } from './services/google-sheets-integration.service';

// No need to filter since enums are not exported from the index
const entityClasses = Object.values(entities);

/**
 * Module quản lý tích hợp cho người dùng
 * Được đặt là Global để providers có thể được inject ở mọi nơi
 */
@Global()
@Module({
  imports: [
    TypeOrmModule.forFeature([
      ...entityClasses,
      Agent,
      GoogleAdsAccount,
      UserAddonUsage,
      Addon,
      User,
      Bank,
      Models,
      ModelIntegration,
      ModelRegistry,
      Media,
    ]),
    HttpModule,
    ServicesModule, // Required for EncryptionService
    SepayHubModule,
    GoogleApiModule,
    SmsModule, // Required for FptSmsBrandnameService
    EventEmitterModule, // Required for event handling
    HttpModule, // Required for HTTP requests
    BullModule.registerQueue({
      name: QueueName.ZALO_ZNS,
    }),
    BullModule.registerQueue({
      name: QueueName.INTEGRATION,
    }),
    forwardRef(() => MarketingUserModule),
  ],
  controllers: Object.values(controllers),
  providers: [
    ...Object.values(repositories),
    ...Object.values(services),
    // Local services
    {
      provide: 'SimpleEncryptionService',
      useClass: SimpleEncryptionService,
    },
    EmailServerConfigurationHelper,
    HttpClientService,
    GHNValidationService,
    GHTKValidationService,
    AhamoveValidationService,
    JTValidationService,
    AgentRepository,
    GoogleAdsAccountRepository,
    UserAddonUsageRepository,
    AddonRepository,
    BankRepository,
    IntegrationProviderRepository,
    IntegrationProviderService,
    ShippingIntegrationRepository,
    FptSmsIntegrationRepository,
    ShippingIntegrationValidationHelper,

    // LLM Key Model Crawler services
    SimpleEncryptionService,
    LlmKeyModelCrawlerService,
    ProviderModelFetcherService,
    ModelSyncService,
    IntegrationKeyCreatedListener,
    // Zalo OA Integration services
    ZaloOAIntegrationService,
    ZaloOAIntegrationRepository,
    ZaloOAAdapterService,
    ZaloOALegacyWrapperService,
    // Zalo Personal Integration services
    ZaloPersonalIntegrationService,
    ZaloPersonalIntegrationRepository,
    ZaloPersonalWebhookService,
    KeyPairEncryptionService,
    SmsServerConfigurationMigrationService,
    // Gmail Integration services (new)
    GmailIntegrationService,
    GmailIntegrationRepository,

    // Google Calendar & Sheets & Docs Integration services
    GoogleCalendarIntegrationService,
    GoogleSheetsIntegrationService,
    GoogleDocsIntegrationService,
    GoogleCalendarApi,
    GoogleSheetsApi,
    GoogleDocsApi,

    FacebookDeletionWebhookService,

    // MediaRepository provider
    {
      provide: MediaRepository,
      useFactory: (dataSource: DataSource) => {
        return new MediaRepository(dataSource);
      },
      inject: [DataSource],
    },
  ],
  exports: [
    ...Object.values(repositories),
    ...Object.values(services),
    HttpClientService,
    GHNValidationService,
    GHTKValidationService,
    AhamoveValidationService,
    JTValidationService,
    'SimpleEncryptionService',
    EmailServerConfigurationHelper,
    IntegrationProviderService,
    ShippingIntegrationRepository,
    FptSmsIntegrationRepository,
    ShippingIntegrationValidationHelper,

    // Zalo OA Integration services
    ZaloOAIntegrationService,
    ZaloOAIntegrationRepository,
    ZaloOAAdapterService,
    ZaloOALegacyWrapperService,
    // Zalo Personal Integration services
    ZaloPersonalIntegrationService,
    ZaloPersonalIntegrationRepository,
    ZaloPersonalWebhookService,
    KeyPairEncryptionService,
    SmsServerConfigurationMigrationService,
    // Gmail Integration services (new)
    GmailIntegrationService,
    GmailIntegrationRepository,

    // Google Calendar & Sheets & Docs Integration services
    GoogleCalendarIntegrationService,
    GoogleSheetsIntegrationService,
    GoogleDocsIntegrationService,
    GoogleCalendarApi,
    GoogleSheetsApi,
    GoogleDocsApi,
  ],
})
export class IntegrationUserModule { }

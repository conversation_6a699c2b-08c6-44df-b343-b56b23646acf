import { Injectable, Logger } from '@nestjs/common';
import { AppException } from '@/common';
import { GENERIC_PAGE_ERROR_CODES } from '../../exceptions/generic-page-error.code';
import { GenericPageRepository } from '../../repositories/generic-page.repository';
import { GenericPageStatusEnum } from '../../constants/generic-page.enum';
import {
  GenericPageResponseDto,
  SubmissionResponseDto,
  SubmitFormDto,
} from '../dto';

@Injectable()
export class GenericPageUserService {
  private readonly logger = new Logger(GenericPageUserService.name);

  constructor(private readonly genericPageRepository: GenericPageRepository) {}

  /**
   * Lấy thông tin trang đã xuất bản theo đường dẫn
   * @param path Đường dẫn của trang
   * @returns Thông tin trang
   */
  async getPublishedGenericPageByPath(
    path: string,
  ): Promise<GenericPageResponseDto> {
    try {
      const genericPage =
        await this.genericPageRepository.findPublishedByPath(path);
      return this.mapToResponseDto(genericPage);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Error getting published generic page by path: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        GENERIC_PAGE_ERROR_CODES.GENERIC_PAGE_NOT_FOUND,
        `Không tìm thấy trang với đường dẫn ${path}`,
      );
    }
  }

  /**
   * Chuyển đổi entity sang DTO
   * @param genericPage Entity GenericPage
   * @returns DTO GenericPageResponseDto
   */
  private mapToResponseDto(genericPage: any): GenericPageResponseDto {
    const responseDto = new GenericPageResponseDto();
    responseDto.id = genericPage.id;
    responseDto.name = genericPage.name;
    responseDto.description = genericPage.description;
    responseDto.path = genericPage.path;
    responseDto.config = genericPage.config;
    responseDto.publishedAt = genericPage.publishedAt;
    return responseDto;
  }
}

import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsArray, IsEnum, IsNotEmpty, IsOptional, IsString, ValidateNested, Allow, IsObject, registerDecorator, ValidationOptions, ValidationArguments } from 'class-validator';
import { Type, Transform } from 'class-transformer';

/**
 * Enum cho loại template ZNS theo Zalo API
 */
export enum ZnsTemplateType {
  CUSTOM = 1,           // ZNS tùy chỉnh
  AUTHENTICATION = 2,   // ZNS xác thực
  PAYMENT_REQUEST = 3,  // ZNS yêu cầu thanh toán
  VOUCHER = 4,         // ZNS voucher
  SERVICE_RATING = 5,  // ZNS Đánh giá dịch vụ
}

/**
 * Enum cho tag template ZNS - theo tài liệu Zalo API tag phải là string
 */
export enum ZnsTemplateTag {
  TRANSACTION = '1',      // Transaction
  CUSTOMER_CARE = '2',    // Customer care
  PROMOTION = '3',        // Promotion
}

/**
 * Enum cho param type
 */
export enum ZnsParamType {
  CUSTOMER_NAME = 1,        // Tên khách hàng (30)
  PHONE_NUMBER = 2,         // Số điện thoại (15)
  ADDRESS = 3,              // Địa chỉ (200)
  CODE = 4,                 // Mã số (30)
  CUSTOM_LABEL = 5,         // Nhãn tùy chỉnh (30)
  TRANSACTION_STATUS = 6,   // Trạng thái giao dịch (30)
  CONTACT_INFO = 7,         // Thông tin liên hệ (50)
  GENDER_TITLE = 8,         // Giới tính / Danh xưng (5)
  PRODUCT_BRAND = 9,        // Tên sản phẩm / Thương hiệu (200)
  QUANTITY_AMOUNT = 10,     // Số lượng / Số tiền (20)
  TIME = 11,                // Thời gian (20)
  OTP = 12,                 // OTP (10)
  URL = 13,                 // URL (200)
  CURRENCY_VND = 14,        // Tiền tệ (VNĐ) (12)
  BANK_TRANSFER_NOTE = 15,  // Bank transfer note (90)
}

/**
 * DTO cho param template ZNS
 */
export class ZnsTemplateParamDto {
  @ApiProperty({
    description: 'Tên của param',
    example: 'customer_name',
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    description: 'Loại param',
    enum: ZnsParamType,
    example: ZnsParamType.CUSTOMER_NAME,
  })
  @IsEnum(ZnsParamType)
  type: ZnsParamType;

  @ApiProperty({
    description: 'Dữ liệu mẫu của param',
    example: 'Nguyễn Văn A',
  })
  @IsString()
  @IsNotEmpty()
  sample_value: string;
}

/**
 * DTO cho component layout - sử dụng cấu trúc dynamic
 * Mỗi component sẽ có cấu trúc: { "COMPONENT_TYPE": { ...data } }
 *
 * @example
 * { "TITLE": { "value": "Xác nhận đơn hàng" } }
 * { "PARAGRAPH": { "value": "Cảm ơn <name> đã mua hàng" } }
 * { "BUTTONS": { "items": [{ "content": "https://example.com", "type": 1, "title": "Xem chi tiết" }] } }
 */
export class ZnsLayoutComponentDto {
  [key: string]: any;
}

/**
 * DTO cho layout section (header, body, footer)
 * Sử dụng index signature để cho phép các property động
 *
 * @example
 * {
 *   "components": [
 *     { "TITLE": { "value": "Xác nhận đơn hàng" } },
 *     { "PARAGRAPH": { "value": "Cảm ơn <name> đã mua hàng" } }
 *   ]
 * }
 */
export class ZnsLayoutSectionDto {
  [key: string]: any;
}

/**
 * Custom validator cho ZNS template layout khi template_type = 1
 */
function IsValidZnsLayout(validationOptions?: ValidationOptions) {
  return function (object: object, propertyName: string) {
    registerDecorator({
      name: 'isValidZnsLayout',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: {
        validate(value: any, args: ValidationArguments) {
          const dto = args.object as RegisterZnsTemplateDto;

          // Chỉ validate khi template_type = 1
          if (dto.template_type !== 1) {
            return true;
          }

          if (!value || typeof value !== 'object') {
            return false;
          }

          const layout = value;
          const errors: string[] = [];

          // Validate header
          if (!layout.header) {
            errors.push('Header là bắt buộc khi template_type = 1');
          } else {
            const headerComponents = layout.header.components;
            if (!Array.isArray(headerComponents) || headerComponents.length !== 1) {
              errors.push('Header phải có đúng 1 component');
            } else {
              const component = headerComponents[0];
              const componentKeys = Object.keys(component);
              if (componentKeys.length !== 1) {
                errors.push('Header component phải có đúng 1 loại');
              } else {
                const componentType = componentKeys[0];
                if (componentType !== 'LOGO' && componentType !== 'IMAGES') {
                  errors.push('Header component chỉ được là LOGO hoặc IMAGES');
                }
              }
            }
          }

          // Validate body
          if (!layout.body) {
            errors.push('Body là bắt buộc khi template_type = 1');
          } else {
            const bodyComponents = layout.body.components;
            if (!Array.isArray(bodyComponents)) {
              errors.push('Body components phải là array');
            } else {
              let titleCount = 0;
              let paragraphCount = 0;
              let tableCount = 0;

              bodyComponents.forEach((component, index) => {
                const componentKeys = Object.keys(component);
                if (componentKeys.length !== 1) {
                  errors.push(`Body component ${index + 1} phải có đúng 1 loại`);
                } else {
                  const componentType = componentKeys[0];
                  switch (componentType) {
                    case 'TITLE':
                      titleCount++;
                      break;
                    case 'PARAGRAPH':
                      paragraphCount++;
                      break;
                    case 'TABLE':
                      tableCount++;
                      break;
                    case 'OTP':
                    case 'VOUCHER':
                    case 'PAYMENT':
                      // Các component khác được phép
                      break;
                    default:
                      // Không báo lỗi cho các component khác, chỉ đếm TITLE, PARAGRAPH, TABLE
                      break;
                  }
                }
              });

              // Validate counts
              if (titleCount !== 1) {
                errors.push('Body phải có đúng 1 component TITLE');
              }
              if (paragraphCount > 4) {
                errors.push('Body chỉ được có tối đa 4 component PARAGRAPH');
              }
              if (tableCount > 1) {
                errors.push('Body chỉ được có tối đa 1 component TABLE');
              }
            }
          }

          // Validate footer (optional)
          if (layout.footer) {
            const footerComponents = layout.footer.components;
            if (Array.isArray(footerComponents)) {
              if (footerComponents.length > 2) {
                errors.push('Footer chỉ được có tối đa 2 components');
              }

              // Auto-fix BUTTONS components URL
              footerComponents.forEach((component) => {
                const componentKeys = Object.keys(component);
                if (componentKeys.length === 1) {
                  const componentType = componentKeys[0];
                  if (componentType === 'BUTTONS') {
                    const buttonsData = component.BUTTONS;
                    if (buttonsData && Array.isArray(buttonsData.items)) {
                      buttonsData.items.forEach((button: any) => {
                        if (button.content && typeof button.content === 'string') {
                          // Tự động thêm "/" vào cuối URL nếu chưa có
                          if (!button.content.endsWith('/')) {
                            button.content = button.content + '/';
                          }
                        }
                      });
                    }
                  }
                }
              });
            }
          }

          // Store errors for custom message
          if (errors.length > 0) {
            (args.object as any)._layoutValidationErrors = errors;
            return false;
          }

          return true;
        },
        defaultMessage(args: ValidationArguments) {
          const errors = (args.object as any)._layoutValidationErrors || ['Layout không hợp lệ'];
          return errors.join('; ');
        },
      },
    });
  };
}

/**
 * DTO cho layout template ZNS
 * Sử dụng index signature để cho phép các property động (header, body, footer)
 *
 * @example
 * {
 *   "header": { "components": [...] },
 *   "body": { "components": [...] },
 *   "footer": { "components": [...] }
 * }
 */
export class ZnsTemplateLayoutDto {
  [key: string]: any;
}

/**
 * DTO cho việc đăng ký template ZNS theo Zalo API
 */
export class RegisterZnsTemplateDto {
  @ApiProperty({
    description: 'Tên mẫu tin (10-60 ký tự)',
    example: 'Mẫu chăm sóc khách hàng',
    minLength: 10,
    maxLength: 60,
  })
  @IsString()
  @IsNotEmpty()
  template_name: string;

  @ApiProperty({
    description: 'Loại mẫu tin (có thể là số hoặc chuỗi số)',
    enum: ZnsTemplateType,
    example: ZnsTemplateType.CUSTOM,
  })
  @Transform(({ value }) => {
    // Chuyển đổi string thành number nếu cần
    if (typeof value === 'string' && !isNaN(Number(value))) {
      return Number(value);
    }
    return value;
  })
  @IsEnum(ZnsTemplateType)
  template_type: ZnsTemplateType;

  @ApiProperty({
    description: 'Tag mẫu tin (string theo tài liệu Zalo API)',
    enum: ZnsTemplateTag,
    example: ZnsTemplateTag.TRANSACTION,
  })
  @IsEnum(ZnsTemplateTag)
  tag: ZnsTemplateTag;

  @ApiProperty({
    description: 'Layout template bao gồm header, body, footer',
    example: {
      "header": {
        "components": [
          { "LOGO": { "light": { "type": "IMAGE", "media_id": "1ashjkdbkj" }, "dark": { "type": "IMAGE", "media_id": "1ashjkdbkj" } } }
        ]
      },
      "body": {
        "components": [
          { "TITLE": { "value": "Xác nhận đơn hàng" } },
          { "PARAGRAPH": { "value": "Cảm ơn <name> đã mua hàng" } }
        ]
      }
    }
  })
  @IsObject()
  @IsValidZnsLayout({
    message: 'Layout không hợp lệ cho template_type = 1'
  })
  layout: any;

  @ApiProperty({
    description: 'Mã tracking do đối tác tự định nghĩa',
    example: 'abc123',
  })
  @IsString()
  @IsNotEmpty()
  tracking_id: string;

  @ApiPropertyOptional({
    description: 'Thông tin về param (nếu có)',
    type: [ZnsTemplateParamDto],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ZnsTemplateParamDto)
  params?: ZnsTemplateParamDto[];

  @ApiPropertyOptional({
    description: 'Ghi chú kiểm duyệt (1-400 ký tự)',
    example: 'Ghi chú kiểm duyệt',
    minLength: 1,
    maxLength: 400,
  })
  @IsOptional()
  @IsString()
  note?: string;
}

/**
 * DTO cho việc tạo template ZNS chỉ vào database (không đăng ký lên Zalo)
 */
export class CreateZnsTemplateDraftDto {
  @ApiProperty({
    description: 'Tên mẫu tin (10-60 ký tự)',
    example: 'Mẫu chăm sóc khách hàng',
    minLength: 10,
    maxLength: 60,
  })
  @IsString()
  @IsNotEmpty()
  template_name: string;

  @ApiProperty({
    description: 'Loại mẫu tin (có thể là số hoặc chuỗi số)',
    enum: ZnsTemplateType,
    example: ZnsTemplateType.CUSTOM,
  })
  @Transform(({ value }) => {
    // Chuyển đổi string thành number nếu cần
    if (typeof value === 'string' && !isNaN(Number(value))) {
      return Number(value);
    }
    return value;
  })
  @IsEnum(ZnsTemplateType)
  template_type: ZnsTemplateType;

  @ApiProperty({
    description: 'Tag mẫu tin (string theo tài liệu Zalo API)',
    enum: ZnsTemplateTag,
    example: ZnsTemplateTag.TRANSACTION,
  })
  @IsEnum(ZnsTemplateTag)
  tag: ZnsTemplateTag;

  @ApiProperty({
    description: 'Layout template bao gồm header, body, footer',
    example: {
      "header": {
        "components": [
          { "LOGO": { "light": { "type": "IMAGE", "media_id": "1ashjkdbkj" }, "dark": { "type": "IMAGE", "media_id": "1ashjkdbkj" } } }
        ]
      },
      "body": {
        "components": [
          { "TITLE": { "value": "Xác nhận đơn hàng" } },
          { "PARAGRAPH": { "value": "Cảm ơn <n> đã mua hàng" } }
        ]
      }
    }
  })
  @IsObject()
  @IsValidZnsLayout({
    message: 'Layout không hợp lệ cho template_type = 1'
  })
  layout: any;

  @ApiProperty({
    description: 'Mã tracking do đối tác tự định nghĩa',
    example: 'abc123',
  })
  @IsString()
  @IsNotEmpty()
  tracking_id: string;

  @ApiPropertyOptional({
    description: 'Thông tin về param (nếu có)',
    type: [ZnsTemplateParamDto],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ZnsTemplateParamDto)
  params?: ZnsTemplateParamDto[];

  @ApiPropertyOptional({
    description: 'Ghi chú kiểm duyệt (1-400 ký tự)',
    example: 'Ghi chú kiểm duyệt',
    minLength: 1,
    maxLength: 400,
  })
  @IsOptional()
  @IsString()
  note?: string;
}

import { PaymentMethodEnum, PaymentStatusEnum } from '../enums';
import { PaymentInfo } from './payment-info.interface';

/**
 * Interface cho thông tin hóa đơn đơn hàng
 */
export interface BillInfo {
  /**
   * Tổng tiền hàng (subtotal) - trư<PERSON><PERSON> khi tính thuế, phí vận chuyển
   */
  subtotal?: number;

  /**
   * Thuế (nếu có)
   */
  tax?: number;

  /**
   * Gi<PERSON>m giá (nếu có)
   */
  discount?: number;

  /**
   * Tổng tiền đơn hàng (bao gồm tất cả phí)
   */
  total: number;

  /**
   * Phí vận chuyển
   */
  shippingFee?: number;

  /**
   * Phương thức thanh toán
   */
  paymentMethod: PaymentMethodEnum | string;

  /**
   * Trạng thái thanh toán
   */
  paymentStatus?: PaymentStatusEnum | string;

  /**
   * Nhà vận chuyển đượ<PERSON> chọn
   */
  selectedCarrier?: string;

  /**
   * <PERSON>ạ<PERSON> dịch vụ vận chuyển
   */
  shippingServiceType?: string;

  /**
   * Thông tin thanh toán chi tiết
   */
  paymentInfo?: PaymentInfo;

  /**
   * ID của payment gateway (cho QR_CODE)
   */
  paymentGatewayId?: string;

  /**
   * Các thông tin bổ sung khác
   */
  [key: string]: any;
}

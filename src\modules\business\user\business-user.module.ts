import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ServicesModule } from '@shared/services/services.module';
import { APP_INTERCEPTOR } from '@nestjs/core';
import { IntegrationUserModule } from '@modules/integration/user/integration-user.module';
import { LocationModule } from '@modules/location/location.module';
import { AgentUserModule } from '@modules/agent/user/agent-user.module';
import {
  CustomField,
  UserOrder,
  Inventory,
  UserConvert,
  UserConvertCustomer,
  UserConvertCustomerMergeRecommendation,
  UserAddressV2,
  ProductAdvancedInfo,
  UserShopAddress,
  UserShopAddressV2,
  // New entities
  CustomerProduct,
  EntityHasMedia,
  DigitalProduct,
  DigitalProductVersion,
  PhysicalProduct,
  PhysicalProductVariant,
  EventProduct,
  EventProductTicket,
  ServiceProduct,
  ServicePackageOption,
  ComboProduct,
  ProductInventory,
  // External conversation entities for website chat
  ExternalConversationMessage,
  ExternalConversationMessageAttachment,
  ExternalCustomerPlatformData,
} from '../entities';
import { Product } from '@modules/marketplace/entities';
import { Agent } from '@modules/agent/entities/agent.entity';

import {
  UserOrderRepository,
  InventoryRepository,
  CustomFieldRepository,
  UserConvertRepository,
  UserConvertCustomerRepository,
  UserConvertCustomerMergeRecommendationRepository,
  UserAddressV2Repository,
  ProductAdvancedInfoRepository,
  UserShopAddressRepository,
  UserShopAddressV2Repository,
  BusinessReportRepository,

  // New repositories
  CustomerProductRepository,
  EntityHasMediaRepository,
  DigitalProductRepository,
  DigitalProductVersionRepository,
  PhysicalProductRepository,
  PhysicalProductVariantRepository,
  EventProductRepository,
  EventProductTicketRepository,
  ComboProductRepository,
  ServiceProductRepository,
  ServicePackageOptionRepository,
  ProductInventoryRepository,
} from '../repositories';
import { ProductRepository } from '@modules/marketplace/repositories';

import {
  UserOrderController,
  UserOrderTrackingController,
  UserShopAddressV2Controller,
  UserAddressV2Controller,
  CustomFieldController,
  UserConvertController,
  UserConvertCustomerController,
  UserConvertCustomerMergeRecommendationController,
  BusinessReportController,
  PaymentQRController,

  // New controllers
  CustomerProductController,
  EntityHasMediaController,
  SimpleCustomerProductController,
  CompletePhysicalProductController,
  CompleteDigitalProductController,
  CompleteEventProductController,
  CompleteServiceProductController,
  CompleteComboProductController,
  AddressController,
  WebhookSepayHubController,
  PublicOrderController,
} from './controllers';

import { ShippingWebhookController } from '@modules/business/user/controllers';

import {
  UserOrderService,
  CustomFieldService,
  UserConvertService,
  UserConvertCustomerService,
  UserConvertCustomerMergeRecommendationService,
  BusinessReportService,
  UserShopAddressV2Service,
  AddressValidationService,
  UserAddressV2Service,
  AddressService,

  // New services
  CustomerProductService,
  EntityHasMediaService,
  SimpleCustomerProductService,
  CustomerProductImportService,
  CompletePhysicalProductService,
  CompleteDigitalProductService,
  CompleteEventProductService,
  CompleteServiceProductService,
  CompleteComboProductService,
  WebhookSepayHubService,
} from './services';
import { SystemCustomFieldInitializationService } from '../services/system-custom-field-initialization.service';
import {
  OrderLogisticsProcessorService,
  ShippingFeeCalculatorService,
  MockAddressService,
  AddressProcessorService,
} from './services/shipment';

import {
  OrderProductProcessorService,
  OrderPaymentProcessorService,
  OrderValidationService,
  OrderQRPaymentService,
} from './services/order';
import { GHTKConfigValidationHelper } from './helpers/ghtk-config-validation.helper';
import { GHNConfigValidationHelper } from './helpers/ghn-config-validation.helper';

import {
  ValidationHelper,
  BusinessReportHelper,
  MetadataHelper,
  ComboValidationHelper,
  VariantValidationHelper,
  ProductResponseHelper,
} from './helpers';
import { GHTKIntegrationHelper } from './helpers/ghtk-integration.helper';
import { OrderStatusConfigHelper } from './helpers/order-status-config.helper';
import { BusinessIntegrationInterceptor } from './interceptors/business-integration.interceptor';
import { MediaRepository } from '@modules/data/media/repositories/media.repository';
import { Media } from '@modules/data/media/entities/media.entity';
import { DataSource } from 'typeorm';
import { SqlHelper } from '@common/helpers/sql.helper';

/**
 * Module quản lý chức năng business cho người dùng
 */
@Module({
  imports: [
    TypeOrmModule.forFeature([
      CustomField,
      UserOrder,
      Inventory,
      UserConvert,
      UserConvertCustomer,
      UserConvertCustomerMergeRecommendation,
      UserAddressV2,
      ProductAdvancedInfo,
      UserShopAddress,
      UserShopAddressV2,
      Product,
      Agent,
      CustomerProduct,
      EntityHasMedia,
      DigitalProduct,
      DigitalProductVersion,
      PhysicalProduct,
      PhysicalProductVariant,
      EventProduct,
      EventProductTicket,
      ServiceProduct,
      ServicePackageOption,
      ComboProduct,
      ProductInventory,
      Media,
      // External conversation entities for website chat
      ExternalConversationMessage,
      ExternalConversationMessageAttachment,
      ExternalCustomerPlatformData,
    ]),
    ServicesModule,
    IntegrationUserModule,
    LocationModule,
    forwardRef(() => AgentUserModule),
  ],
  controllers: [
    UserOrderController,
    UserOrderTrackingController,
    UserShopAddressV2Controller,
    UserAddressV2Controller,
    ShippingWebhookController,
    CustomFieldController,
    UserConvertController,
    UserConvertCustomerController,
    UserConvertCustomerMergeRecommendationController,
    BusinessReportController,
    PaymentQRController,
    AddressController,

    // New controllers
    EntityHasMediaController,
    SimpleCustomerProductController,
    CustomerProductController, // Moved to end for priority
    CompletePhysicalProductController,
    CompleteDigitalProductController,
    CompleteEventProductController,
    CompleteComboProductController,
    CompleteEventProductController,
    CompleteServiceProductController,
    WebhookSepayHubController,
    PublicOrderController,
  ],
  providers: [
    // Repositories
    UserOrderRepository,
    InventoryRepository,
    CustomFieldRepository,
    UserConvertRepository,
    UserConvertCustomerRepository,
    UserConvertCustomerMergeRecommendationRepository,
    UserAddressV2Repository,
    ProductAdvancedInfoRepository,
    UserShopAddressRepository,
    UserShopAddressV2Repository,

    ProductRepository,
    BusinessReportRepository,

    // New repositories
    CustomerProductRepository,
    EntityHasMediaRepository,
    DigitalProductRepository,
    DigitalProductVersionRepository,
    PhysicalProductRepository,
    PhysicalProductVariantRepository,
    EventProductRepository,
    EventProductTicketRepository,
    ServiceProductRepository,
    ServicePackageOptionRepository,
    ComboProductRepository,
    {
      provide: ProductInventoryRepository,
      useFactory: (dataSource: DataSource) => {
        return new ProductInventoryRepository(dataSource);
      },
      inject: [DataSource],
    },
    {
      provide: MediaRepository,
      useFactory: (dataSource: DataSource) => {
        return new MediaRepository(dataSource);
      },
      inject: [DataSource],
    },

    // Helpers
    ValidationHelper,
    BusinessReportHelper,
    MetadataHelper,
    ComboValidationHelper,
    VariantValidationHelper,
    ProductResponseHelper,
    OrderStatusConfigHelper,

    // Services
    UserOrderService,
    CustomFieldService,
    UserConvertService,
    UserConvertCustomerService,
    UserConvertCustomerMergeRecommendationService,
    BusinessReportService,
    AddressService,
    UserShopAddressV2Service,
    AddressValidationService,
    UserAddressV2Service,
    GHTKConfigValidationHelper,
    GHNConfigValidationHelper,
    GHTKIntegrationHelper,
    OrderProductProcessorService,
    OrderLogisticsProcessorService,
    ShippingFeeCalculatorService,
    MockAddressService,
    AddressProcessorService,
    OrderPaymentProcessorService,
    OrderValidationService,
    OrderQRPaymentService,

    // New services
    CustomerProductService,
    EntityHasMediaService,
    SimpleCustomerProductService,
    CustomerProductImportService,
    CompletePhysicalProductService,
    CompleteDigitalProductService,
    CompleteEventProductService,
    CompleteServiceProductService,
    CompleteComboProductService,
    WebhookSepayHubService,

    // System initialization
    SystemCustomFieldInitializationService,

    // Helpers
    SqlHelper,

    // Interceptors
    {
      provide: APP_INTERCEPTOR,
      useClass: BusinessIntegrationInterceptor,
    },
  ],
  exports: [
    TypeOrmModule,
    UserOrderService,
    CustomFieldService,
    UserConvertService,
    UserConvertCustomerService,
    UserConvertCustomerMergeRecommendationService,
    BusinessReportService,

    // New exports
    CustomerProductService,
    CustomerProductRepository,
    EntityHasMediaService,
    EntityHasMediaRepository,
    SimpleCustomerProductService,
    CompletePhysicalProductService,
    CompleteDigitalProductService,
    CompleteEventProductService,
    CompleteServiceProductService,
    CompleteComboProductService,
    DigitalProductRepository,
    DigitalProductVersionRepository,
    EventProductRepository,
    EventProductTicketRepository,
    ComboProductRepository,
    ServiceProductRepository,
    ServicePackageOptionRepository,
    ProductInventoryRepository,
  ],
})
export class BusinessUserModule {}

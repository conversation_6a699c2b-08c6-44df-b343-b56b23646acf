/*
----------------------------------------------------------------
File: facebook.service.ts
----------------------------------------------------------------
- Chứa logic cốt lõi để xác thực signed_request.
- Tương tác với cơ sở dữ liệu để xóa dữ liệu người dùng.
*/
import { ConfigService, ConfigType, FacebookConfig } from '@/config';
import { Injectable, Logger } from '@nestjs/common';
import * as crypto from 'crypto';

// Định nghĩa kiểu dữ liệu cho payload sau khi giải mã
export interface FacebookSignedRequestPayload {
    algorithm: 'HMAC-SHA256';
    issued_at: number;
    user_id: string;
    user?: {
        country: string;
        locale: string;
    };
}

@Injectable()
export class FacebookWebhookService {
    private readonly logger = new Logger(FacebookWebhookService.name);
    private readonly facebookConfig: FacebookConfig;

    constructor(
        private readonly configService: ConfigService,
    ) {
        this.facebookConfig = this.configService.getConfig<FacebookConfig>(
            ConfigType.Facebook,
        );
    }

    /**
     * Phân tích và xác thực một signed_request từ Facebook.
     * @param signedRequest Chuỗi signed_request.
     * @param appSecret App Secret của ứng dụng.
     * @returns Dữ liệu payload nếu hợp lệ.
     */
    async parseAndVerifySignedRequest(signedRequest: string): Promise<FacebookSignedRequestPayload> {
        const [encodedSignature, payload] = signedRequest.split('.');

        if (!encodedSignature || !payload) {
            throw new Error('Định dạng signed_request không hợp lệ.');
        }

        const signature = Buffer.from(encodedSignature.replace(/_/g, '/').replace(/-/g, '+'), 'base64');
        const data: FacebookSignedRequestPayload = JSON.parse(Buffer.from(payload, 'base64').toString('utf-8'));

        if (data.algorithm.toUpperCase() !== 'HMAC-SHA256') {
            throw new Error(`Thuật toán không được hỗ trợ: ${data.algorithm}.`);
        }

        const expectedSignature = crypto.createHmac('sha256', this.facebookConfig.appSecret).update(payload).digest();

        if (signature.length !== expectedSignature.length || !crypto.timingSafeEqual(signature, expectedSignature)) {
            throw new Error('Chữ ký không hợp lệ.');
        }

        return data;
    }
}
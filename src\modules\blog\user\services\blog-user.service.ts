import { User } from '@/modules/user/entities/user.entity';
import { Employee } from '@/modules/employee/entities/employee.entity';
import { Injectable } from '@nestjs/common';
import { Brackets } from 'typeorm';
import { Blog, BlogPurchase } from '../../entities';
import {
  AuthorDto,
  BlogResponseDto,
  CreateBlogDto,
  BlogQueryDto,
  BlogSearchByTagsDto,
  MediaTypeEnum,
  PaginatedBlogResponseDto,
  UpdateBlogMediaDto,
  UpdateBlogMediaOperationsDto,
  UpdateBlogMediaResponseDto,
  PresignedUrlBlogMediaDto,
  BlogMediaOperationType,
  BlogMediaTypeEnum,
  AddBlogMediaOperationDto,
  DeleteBlogMediaOperationDto,
} from '../../dto';
import { AuthorTypeEnum, BlogStatusEnum } from '../../enums';
import { BlogRepository, BlogPurchaseRepository } from '../../repositories';
import { S3Service } from '@/shared/services/s3.service';
import {
  CategoryFolderEnum,
  FileSizeEnum,
  generateS3Key,
  TimeIntervalEnum,
  MediaType,
  FileTypeEnum,
} from '@/shared/utils';
import { Between, Not, In } from 'typeorm';
import {
  ImageType,
  ImageTypeEnum,
} from '@/shared/utils/file/image-media_type.util';
import { MediaHelper } from '@modules/marketplace/helpers/media.helper';
import { SqlHelper } from '@common/helpers/sql.helper';
import { PaginatedResult } from '@/common/response';
import { AppException } from '@/common';
import { BLOG_ERROR_CODES } from '@modules/blog/exceptions';
import { USER_BLOG_ERROR_CODES } from '../exceptions';
import { ContentMediaType, ContentMediaTypeEnum } from '@/shared/utils/file/content-media-type.util';
import { ValidationHelper } from '../../helpers';
import { Transactional } from 'typeorm-transactional';
import { plainToInstance } from 'class-transformer';

@Injectable()
export class BlogUserService {
  constructor(
    private readonly blogRepository: BlogRepository,
    private readonly blogPurchaseRepository: BlogPurchaseRepository,
    private readonly s3Service: S3Service,
    private readonly sqlHelper: SqlHelper,
    private readonly validationHelper: ValidationHelper,
    private readonly mediaHelper: MediaHelper,
  ) {}

  /**
   * Format a CDN URL by adding the CDN_URL prefix if needed
   * @param url The URL or path to format
   * @returns Properly formatted CDN URL
   */
  private formatCdnUrl(url: string | null | undefined): string {
    if (!url) return '';

    // If the URL already starts with http, return it as is
    if (url.startsWith('http')) return url;

    // Add the CDN_URL prefix with a slash between domain and path
    const cdnUrl = process.env.CDN_URL || 'https://cdn.redai.vn';
    return `${cdnUrl}/${url}`;
  }

  /**
   * Helper method to convert PaginatedResult<BlogResponseDto> to PaginatedBlogResponseDto
   * @private
   */
  private async convertToPaginatedBlogResponseDto(
    paginatedResult: PaginatedResult<BlogResponseDto>,
  ): Promise<PaginatedBlogResponseDto> {
    // Transform the data to include author information
    const transformedItems = await Promise.all(
      paginatedResult.items.map(async (item) => {
        // Get author information
        const author = {
          id:
            item.authorType === AuthorTypeEnum.USER
              ? (item.userId ?? null)
              : (item.employeeId ?? null),
          name: item.user?.fullName || 'System',
          type: item.authorType || AuthorTypeEnum.SYSTEM,
          avatar:
            item.user?.avatar || 'https://cdn.example.com/avatars/default.jpg',
        };

        // If user information is not available in the item, fetch it
        if (item.userId && !item.user) {
          const users = await this.sqlHelper.select(
            'users',
            ['id', 'full_name as fullName', 'avatar'],
            [{ condition: 'id = :id', params: { id: item.userId } }],
          );

          if (users && users.length > 0) {
            author.name = users[0].fullName || 'User';
            author.avatar =
              users[0].avatar || 'https://cdn.example.com/avatars/default.jpg';
          }
        }

        // Create a new object with the author field properly set
        return {
          ...item,
          author, // Ensure author is included
          employeeModerator: item.employeeModerator || null,
        };
      }),
    );

    const result = new PaginatedBlogResponseDto();
    result.content = transformedItems;
    result.totalItems = paginatedResult.meta.totalItems;
    result.itemCount = paginatedResult.meta.itemCount;
    result.itemsPerPage = paginatedResult.meta.itemsPerPage;
    result.totalPages = paginatedResult.meta.totalPages;
    result.currentPage = paginatedResult.meta.currentPage;
    return result;
  }

  /**
   * Lấy danh sách bài viết đã được tinh giản
   * @param userId ID của người dùng hiện tại
   * @param dto Tham số truy vấn đã được tinh giản
   * @returns Danh sách bài viết có phân trang
   */
  async getBlogs(userId: number | null, dto: BlogQueryDto): Promise<PaginatedBlogResponseDto> {
    try {
      // Tạo query builder cơ bản
      const qb = this.blogRepository.createQueryBuilder('blog');

      // Chọn các trường cần thiết
      qb.select([
        'blog.id',
        'blog.title',
        'blog.description',
        'blog.content',
        'blog.point',
        'blog.viewCount',
        'blog.thumbnailUrl',
        'blog.tags',
        'blog.createdAt',
        'blog.updatedAt',
        'blog.userId',
        'blog.employeeId',
        'blog.authorType',
        'blog.status',
        'blog.enable',
        'blog.like',
        'blog.employeeModerator',
      ]);

      // Điều kiện cơ bản: blog phải được kích hoạt
      qb.where('blog.enable = :enable', { enable: true });

      // Xử lý loại blog đơn giản
      const blogType = dto.type || 'PURCHASABLE';

      if (blogType === 'MY_BLOGS' && userId) {
        // Lấy blog của người dùng với mọi trạng thái
        qb.andWhere('blog.userId = :userId', { userId });
      } else {
        // Mặc định: lấy blog có thể mua (APPROVED và không phải của user hiện tại)
        qb.andWhere('blog.status = :status', { status: BlogStatusEnum.APPROVED });

        if (userId) {
          // Loại bỏ blog của chính user hiện tại
          qb.andWhere(new Brackets(qb => {
            qb.where('blog.userId != :userId', { userId })
              .orWhere('blog.userId IS NULL');
          }));

          // Loại bỏ blog đã mua
          const purchasedBlogs = await this.sqlHelper.select(
            'blog_purchases',
            ['blog_id as blogId'],
            [{ condition: 'user_id = :userId', params: { userId } }]
          );

          if (purchasedBlogs && purchasedBlogs.length > 0) {
            const validIds = purchasedBlogs
              .map(p => {
                const id = Number(p.blogId);
                return isNaN(id) ? null : id;
              })
              .filter(id => id !== null);

            if (validIds.length > 0) {
              qb.andWhere('blog.id NOT IN (:...ids)', { ids: validIds });
            }
          }
        }
      }

      // Tìm kiếm theo tiêu đề và nội dung
      if (dto.search) {
        qb.andWhere(new Brackets(qb => {
          qb.where('blog.title ILIKE :search', { search: `%${dto.search}%` })
            .orWhere('blog.content ILIKE :search', { search: `%${dto.search}%` });
        }));
      }



      // In ra câu truy vấn SQL cuối cùng để debug
      console.log('Final SQL Query:', qb.getSql());
      console.log('Final SQL Parameters:', qb.getParameters());

      // Join với bảng user để lấy thông tin chi tiết của tác giả (nếu là user)
      qb.leftJoin(User, 'user', 'blog.userId = user.id')
        .addSelect([
          'user.id',
          'user.fullName',
          'user.avatar',
        ]);

      // Join với bảng employee để lấy thông tin chi tiết của tác giả (nếu là employee)
      qb.leftJoin(Employee, 'employee', 'blog.employeeId = employee.id')
        .addSelect([
          'employee.id',
          'employee.fullName',
          'employee.avatar',
        ]);

      // Sắp xếp
      qb.orderBy(`blog.${dto.sortBy || 'createdAt'}`, dto.sortDirection || 'DESC');

      // Phân trang
      const page = dto.page || 1;
      const limit = dto.limit || 10;
      const offset = (page - 1) * limit;
      qb.skip(offset).take(limit);

      // Thực thi truy vấn
      const [items, totalItems] = await qb.getManyAndCount();

      // Chuyển đổi kết quả sang DTO
      const blogResponseDtos = await Promise.all(items.map(async (item) => {
        const dto = new BlogResponseDto();
        dto.id = item.id;
        dto.title = item.title;
        dto.description = item.description;

        // Kiểm tra xem người dùng có quyền xem nội dung không
        let isPurchased = false;
        if (userId) {
          // Nếu người dùng là tác giả của bài viết, coi như đã mua
          if (item.userId === userId) {
            isPurchased = true;
          } else {
            // Kiểm tra người dùng đã mua bài viết chưa
            const hasPurchased = await this.sqlHelper.exists('blog_purchases', [
              { condition: 'user_id = :userId', params: { userId } },
              { condition: 'blog_id = :blogId', params: { blogId: item.id } },
            ]);
            isPurchased = hasPurchased;
          }
        }

        // Chỉ hiển thị nội dung nếu người dùng đã mua hoặc là tác giả
        dto.content = isPurchased ? this.formatCdnUrl(item.content) : undefined;
        dto.isPurchased = isPurchased;

        dto.point = item.point;
        dto.viewCount = item.viewCount;
        dto.thumbnailUrl = this.formatCdnUrl(item.thumbnailUrl);
        dto.tags = item.tags;
        dto.createdAt = item.createdAt;
        dto.updatedAt = item.updatedAt;
        dto.userId = item.userId;
        dto.employeeId = item.employeeId;
        dto.authorType = item.authorType;
        dto.status = item.status;
        dto.enable = item.enable;
        dto.like = item.like;
        dto.employeeModerator = item.employeeModerator;

        // Thông tin tác giả
        let authorName = 'Unknown';
        let authorAvatar = 'https://cdn.example.com/avatars/default.jpg';

        // Lấy thông tin chi tiết của tác giả dựa vào authorType
        if (item.authorType === AuthorTypeEnum.USER && item.userId) {
          // Lấy thông tin user từ bảng users
          const users = await this.sqlHelper.select(
            'users',
            ['id', 'full_name as fullName', 'avatar'],
            [{ condition: 'id = :id', params: { id: item.userId } }]
          );

          if (users && users.length > 0) {
            authorName = String(users[0].fullName || 'User');
            authorAvatar = String(users[0].avatar || 'https://cdn.example.com/avatars/default.jpg');
          }
        } else if (item.authorType === AuthorTypeEnum.SYSTEM && item.employeeId) {
          // Lấy thông tin employee từ bảng employees
          const employees = await this.sqlHelper.select(
            'employees',
            ['id', 'full_name as fullName', 'avatar'],
            [{ condition: 'id = :id', params: { id: item.employeeId } }]
          );

          if (employees && employees.length > 0) {
            authorName = String(employees[0].fullName || 'Employee');
            authorAvatar = String(employees[0].avatar || 'https://cdn.example.com/avatars/default.jpg');
          }
        }

        dto.author = {
          id: item.authorType === AuthorTypeEnum.USER ? (item.userId || 0) : (item.employeeId || 0),
          name: authorName,
          type: item.authorType || AuthorTypeEnum.SYSTEM,
          avatar: this.formatCdnUrl(authorAvatar),
        };

        return dto;
      }));

      // Tạo kết quả phân trang
      const result = new PaginatedBlogResponseDto();
      result.content = blogResponseDtos;
      result.totalItems = totalItems;
      result.itemCount = blogResponseDtos.length;
      result.itemsPerPage = limit;
      result.totalPages = Math.ceil(totalItems / limit);
      result.currentPage = page;

      return result;
    } catch (error) {
      console.error('Error in getBlogs:', error);

      // Nếu là lỗi đã được xử lý, tiếp tục ném lỗi
      if (error instanceof AppException) {
        throw error;
      }

      // Trả về kết quả trống thay vì ném lỗi 404
      if (error.name === 'QueryFailedError') {
        const emptyResult = new PaginatedBlogResponseDto();
        emptyResult.content = [];
        emptyResult.totalItems = 0;
        emptyResult.itemCount = 0;
        emptyResult.itemsPerPage = dto.limit || 10;
        emptyResult.totalPages = 0;
        emptyResult.currentPage = dto.page || 1;
        return emptyResult;
      }

      // Nếu là lỗi khác, ném lỗi chung
      throw new AppException(
        BLOG_ERROR_CODES.BLOG_NOT_FOUND,
        'Failed to fetch blogs. Please try again later.',
      );
    }
  }

  // Đã bỏ các phương thức debug không cần thiết

  /**
   * Lấy chi tiết bài viết theo ID
   * @param id ID của bài viết
   * @param userId ID của người dùng hiện tại (nếu đã đăng nhập)
   * @returns Thông tin chi tiết của bài viết
   */
  async findOne(id: number, userId?: number): Promise<BlogResponseDto> {
    try {
      console.log(`Getting blog detail for id=${id}, userId=${userId || 'guest'}`);

      // Sử dụng SqlHelper để lấy thông tin bài viết và tác giả
      const blogs = await this.sqlHelper.select(
        'blogs',
        [
          'blogs.id',
          'blogs.title',
          'blogs.description',
          'blogs.content',
          'blogs.point',
          'blogs.view_count as viewCount',
          'blogs.thumbnail_url as thumbnailUrl',
          'blogs.tags',
          'blogs.created_at as createdAt',
          'blogs.updated_at as updatedAt',
          'blogs.user_id as userId',
          'blogs.employee_id as employeeId',
          'blogs.author_type as authorType',
          'blogs.employee_moderator as employeeModerator',
          'blogs.status',
          'blogs.enable',
          'blogs.like',
          // User author fields
          'users.id as "user.id"',
          'users.full_name as "user.fullName"',
          'users.avatar as "user.avatar"',
          // Employee author fields
          'employees.id as "employee.id"',
          'employees.full_name as "employee.fullName"',
          'employees.avatar as "employee.avatar"',
          // Moderator fields
          'moderators.id as "moderator.id"',
          'moderators.full_name as "moderator.fullName"',
          'moderators.avatar as "moderator.avatar"',
        ],
        [
          { condition: 'blogs.id = :id', params: { id } },
          { condition: 'blogs.enable = :enable', params: { enable: true } },
          // Removed the status condition to allow blogs with any status to be returned
        ],
        [
          {
            type: 'LEFT',
            table: 'users',
            alias: 'users',
            condition: 'blogs.user_id = users.id',
          },
          {
            type: 'LEFT',
            table: 'employees',
            alias: 'employees',
            condition: 'blogs.employee_id = employees.id',
          },
          {
            type: 'LEFT',
            table: 'employees',
            alias: 'moderators',
            condition: 'blogs.employee_moderator = moderators.id',
          },
        ],
        [],
        [],
        undefined,
        undefined,
        { raw: true },
      );

      if (!blogs || blogs.length === 0) {
        throw new AppException(BLOG_ERROR_CODES.BLOG_NOT_FOUND);
      }

      const blog = blogs[0] as any;
      console.log('Blog data from database:', JSON.stringify(blog, null, 2));

      // Lấy thông tin tác giả
      const author: AuthorDto = {
        id: 0,
        name: 'System',
        type: (blog.authortype as AuthorTypeEnum) || AuthorTypeEnum.SYSTEM,
        avatar: this.formatCdnUrl('avatars/default.jpg'),
      };

      console.log('Author type:', blog.authortype);
      console.log('User ID:', blog.userid);
      console.log('Employee ID:', blog.employeeid);

      // Nếu tác giả là người dùng
      if (blog.authortype === AuthorTypeEnum.USER && blog.userid) {
        // Sử dụng dữ liệu đã join từ bảng users
        if (blog['user.id']) {
          author.id = Number(blog.userid);
          author.name = String(blog['user.fullName'] || 'User');
          author.avatar = this.formatCdnUrl(String(blog['user.avatar'] || 'avatars/default.jpg'));
          author.type = AuthorTypeEnum.USER;
        }
      }
      // Nếu tác giả là nhân viên
      else if (blog.authortype === AuthorTypeEnum.SYSTEM && blog.employeeid) {
        // Sử dụng dữ liệu đã join từ bảng employees
        if (blog['employee.id']) {
          author.id = Number(blog.employeeid);
          author.name = String(blog['employee.fullName'] || 'Employee');
          author.avatar = this.formatCdnUrl(String(blog['employee.avatar'] || 'avatars/default.jpg'));
          author.type = AuthorTypeEnum.SYSTEM;
        }
      }

      // Lấy thông tin người kiểm duyệt (nếu có)
      let employeeModerator: any = null;
      if (blog.employeemoderator && blog['moderator.id']) {
        employeeModerator = {
          id: Number(blog.employeemoderator),
          name: String(blog['moderator.fullName'] || 'Moderator'),
          avatar: this.formatCdnUrl(String(blog['moderator.avatar'] || 'avatars/default.jpg')),
        };
      }

      // Kiểm tra trạng thái mua của người dùng hiện tại
      let isPurchased = false;
      console.log(`[DEBUG] Checking purchase status for blogId=${id}, userId=${userId}, blogUserId=${blog.userid}`);

      if (userId) {
        // Nếu người dùng là tác giả của bài viết, coi như đã mua
        if (Number(blog.userid) === userId) {
          isPurchased = true;
          console.log(`[DEBUG] User is author, isPurchased=true`);
        } else {
          // Kiểm tra người dùng đã mua bài viết chưa
          const hasPurchased = await this.sqlHelper.exists('blog_purchases', [
            { condition: 'user_id = :userId', params: { userId } },
            { condition: 'blog_id = :blogId', params: { blogId: id } },
          ]);
          isPurchased = hasPurchased;
          console.log(`[DEBUG] Purchase check result: isPurchased=${isPurchased}`);
        }
      } else {
        console.log(`[DEBUG] No userId provided (guest user), isPurchased=false`);
      }

      // Upload URLs have been removed from the response

      // Cập nhật số lượt xem
      await this.blogRepository.incrementViewCount(id);

      // Parse tags from JSON if needed
      let tags: string[] = [];
      if (blog.tags) {
        try {
          if (typeof blog.tags === 'string') {
            tags = JSON.parse(blog.tags);
          } else if (Array.isArray(blog.tags)) {
            tags = blog.tags;
          }
        } catch (e) {
          console.error('Error parsing tags:', e);
        }
      }

      // Make sure thumbnailUrl has the CDN prefix if needed
      let thumbnailUrl = String(blog.thumbnailurl || '');
      if (thumbnailUrl && !thumbnailUrl.startsWith('http')) {
        const cdnUrl = process.env.CDN_URL || 'https://cdn.redai.vn';
        thumbnailUrl = `${cdnUrl}/${thumbnailUrl}`;
      }

      // Make sure content has the CDN prefix if needed
      let content = String(blog.content || '');
      if (content && !content.startsWith('http')) {
        const cdnUrl = process.env.CDN_URL || 'https://cdn.redai.vn';
        content = `${cdnUrl}/${content}`;
      }

      // Tạo đối tượng kết quả với đầy đủ thông tin
      console.log(`[DEBUG] Final isPurchased=${isPurchased}, content will be ${isPurchased ? 'included' : 'undefined'}`);
      console.log(`[DEBUG] Raw content from DB: ${blog.content}`);
      console.log(`[DEBUG] Formatted content: ${content}`);

      const result: Partial<BlogResponseDto> = {
        id: Number(blog.id),
        title: String(blog.title || ''),
        description: String(blog.description || ''),
        // Chỉ hiển thị nội dung nếu người dùng đã mua hoặc là tác giả
        content: isPurchased ? content : undefined,
        point: Number(blog.point || 0),
        viewCount: Number(blog.viewcount || 0),
        thumbnailUrl,
        tags,
        createdAt: Number(blog.createdat || 0),
        updatedAt: Number(blog.updatedat || 0),
        userId: blog.userid ? Number(blog.userid) : null,
        employeeId: blog.employeeid ? Number(blog.employeeid) : null,
        authorType: blog.authortype as AuthorTypeEnum,
        author,
        employeeModerator,
        status: blog.status as BlogStatusEnum,
        enable: Boolean(blog.enable),
        like: Number(blog.like || 0),
        isPurchased,
      };

      // Upload URLs have been removed from the response as requested

      // Format avatar URL
      if (result.author && result.author.avatar) {
        result.author.avatar = this.formatCdnUrl(result.author.avatar);
      }

      console.log('Final blog response:', JSON.stringify(result, null, 2));

      // Cast the result back to BlogResponseDto before returning
      return result as BlogResponseDto;
    } catch (error: unknown) {
      if (error instanceof AppException) {
        throw error;
      }
      console.error('Error in findOne:', error);
      throw new AppException(BLOG_ERROR_CODES.BLOG_NOT_FOUND);
    }
  }

  /**
   * Tạo bài viết mới
   * @param userId ID của người dùng tạo blog
   * @param createBlogDto Dữ liệu tạo blog
   * @returns Promise<any> Thông tin blog đã tạo với upload URLs
   */
  @Transactional()
  async create(userId: number, createBlogDto: CreateBlogDto) {
    // Business validations
    this.validationHelper.validateBlogPointPriceRange(createBlogDto.point);

    // Daily blog limit check
    const dailyCount = await this.getDailyBlogCount(userId);
    this.validationHelper.validateDailyBlogCreationLimit(dailyCount);

    try {
      const now = Date.now();

      // Tạo blog entity
      const blog = new Blog();
      blog.title = createBlogDto.title;
      blog.description = createBlogDto.description;
      blog.content = null; // Content sẽ được cập nhật sau khi upload file
      blog.point = createBlogDto.point;
      blog.tags = createBlogDto.tags;
      blog.userId = userId;
      blog.authorType = AuthorTypeEnum.USER;
      blog.status = createBlogDto.status || BlogStatusEnum.DRAFT;
      blog.enable = true;
      blog.createdAt = now;
      blog.updatedAt = now;
      blog.viewCount = 0;
      blog.like = 0;

      // Lưu blog qua repository
      const savedBlog = await this.blogRepository.save(blog);

      if (!savedBlog || !savedBlog.id) {
        throw new AppException(USER_BLOG_ERROR_CODES.USER_BLOG_CREATION_FAILED);
      }

      // Tạo URL tạm thời để upload content và thumbnail
      const contentKey = generateS3Key({
        baseFolder: 'blogs',
        prefix: `content-${savedBlog.id}`,
        categoryFolder: CategoryFolderEnum.DOCUMENT,
      });

      const thumbnailKey = generateS3Key({
        baseFolder: 'blogs',
        prefix: `thumbnail-${savedBlog.id}`,
        categoryFolder: CategoryFolderEnum.IMAGE,
      });

      // Xác định loại content media
      let contentMediaType: ContentMediaTypeEnum;
      try {
        contentMediaType = ContentMediaType.getMimeType(createBlogDto.contentMediaType);
      } catch {
        contentMediaType = ContentMediaTypeEnum.HTML; // Default to JPEG if not valid
      }

      // Xác định loại thumbnail media
      let thumbnailMediaType: ImageTypeEnum;
      try {
        thumbnailMediaType = ImageType.getType(
          createBlogDto.thumbnailMediaType,
        );
      } catch {
        thumbnailMediaType = ImageTypeEnum.JPEG; // Default to JPEG if not valid
      }

      // Tạo presigned URL cho content
      const contentUploadUrl = await this.s3Service.createPresignedWithID(
        contentKey,
        TimeIntervalEnum.ONE_HOUR,
        contentMediaType,
        FileSizeEnum.FIVE_MB,
      );

      // Tạo presigned URL cho thumbnail
      const thumbnailUploadUrl = await this.s3Service.createPresignedWithID(
        thumbnailKey,
        TimeIntervalEnum.ONE_HOUR,
        thumbnailMediaType,
        FileSizeEnum.TWO_MB,
      );

      // Cập nhật blog với content và thumbnail keys
      savedBlog.content = contentKey;
      savedBlog.thumbnailUrl = thumbnailKey;
      savedBlog.updatedAt = now;

      await this.blogRepository.save(savedBlog);

      return {
        contentUploadUrl: contentUploadUrl,
        thumbnailUploadUrl: thumbnailUploadUrl,
        blogId: savedBlog.id,
      };
    } catch (error: unknown) {
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(USER_BLOG_ERROR_CODES.USER_BLOG_CREATION_FAILED, error instanceof Error ? error.message : 'Unknown error');
    }
  }

  /**
   * Cập nhật media cho bài viết
   * @param id ID của bài viết
   * @param userId ID của người dùng
   * @param updateMediaDto Dữ liệu cập nhật media
   * @returns Promise<any> URL upload cho media
   */
  @Transactional()
  async updateMedia(
    id: number,
    userId: number,
    updateMediaDto: UpdateBlogMediaDto,
  ) {
    // Business validations
    const blog = await this.blogRepository.findOne({ where: { id, userId } });
    this.validationHelper.validateBlogExists(blog);
    this.validationHelper.validateUserOwnsBlog(blog!, userId);
    this.validationHelper.validateBlogIsDraft(blog!); // Chỉ cho phép update media khi blog ở trạng thái draft

    try {

      // Xác định loại media cần cập nhật
      const isContent = updateMediaDto.media_type === MediaTypeEnum.CONTENT;
      const mediaKey = isContent
        ? generateS3Key({
            baseFolder: 'blogs',
            prefix: `content-${id}`,
            categoryFolder: CategoryFolderEnum.DOCUMENT,
          })
        : generateS3Key({
            baseFolder: 'blogs',
            prefix: `thumbnail-${id}`,
            categoryFolder: CategoryFolderEnum.IMAGE,
          });

      // Xác định loại media content dựa vào loại media
      let mediaContentType: ContentMediaTypeEnum | ImageTypeEnum;
      try {
        if (isContent) {
          // Kiểm tra nếu media_content_type không phải là text/html
          if (updateMediaDto.media_content_type !== ContentMediaTypeEnum.HTML) {
            throw new AppException(
              BLOG_ERROR_CODES.BLOG_MEDIA_CONTENT_TYPE_INVALID,
              `Loại nội dung không hợp lệ cho content. Chỉ hỗ trợ 'text/html'.`
            );
          }
          // Sử dụng ContentMediaType cho nội dung
          mediaContentType = ContentMediaType.getMimeType(updateMediaDto.media_content_type);
        } else {
          // Kiểm tra nếu media_content_type không phải là loại hình ảnh
          if (!updateMediaDto.media_content_type?.startsWith('image/')) {
            throw new AppException(
              BLOG_ERROR_CODES.BLOG_MEDIA_CONTENT_TYPE_INVALID,
              `Loại nội dung không hợp lệ cho thumbnail. Chỉ hỗ trợ các định dạng hình ảnh: 'image/jpeg', 'image/png', 'image/webp', 'image/gif'.`
            );
          }
          // Sử dụng ImageType cho thumbnail
          mediaContentType = ImageType.getType(updateMediaDto.media_content_type!);
        }
      } catch (error) {
        if (error instanceof AppException) {
          throw error;
        }
        throw new AppException(
          BLOG_ERROR_CODES.BLOG_MEDIA_CONTENT_TYPE_INVALID,
          `Loại nội dung media không hợp lệ. ${isContent ? "Content chỉ hỗ trợ 'text/html'." : "Thumbnail chỉ hỗ trợ 'image/jpeg', 'image/png', 'image/webp', 'image/gif'."}`
        );
      }

      // Tạo presigned URL cho media
      const uploadUrl = await this.s3Service.createPresignedWithID(
        mediaKey,
        TimeIntervalEnum.ONE_HOUR,
        mediaContentType,
        isContent ? FileSizeEnum.FIVE_MB : FileSizeEnum.TWO_MB,
      );

      return {
        uploadUrl: uploadUrl,
      };
    } catch (error: unknown) {
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(USER_BLOG_ERROR_CODES.USER_BLOG_MEDIA_UPLOAD_FAILED, error instanceof Error ? error.message : 'Unknown error');
    }
  }

  /**
   * Cập nhật media cho bài viết với operations (ADD/DELETE)
   * @param id ID của bài viết
   * @param userId ID của người dùng
   * @param updateMediaDto Dữ liệu cập nhật media operations
   * @returns Promise<UpdateBlogMediaResponseDto> Thông tin blog và presigned URLs
   */
  @Transactional()
  async updateBlogMediaOperations(
    id: number,
    userId: number,
    updateMediaDto: UpdateBlogMediaOperationsDto,
  ): Promise<UpdateBlogMediaResponseDto> {
    try {
      // Business validations
      const blog = await this.blogRepository.findOne({ where: { id, userId } });
      this.validationHelper.validateBlogExists(blog);
      this.validationHelper.validateUserOwnsBlog(blog!, userId);
      this.validationHelper.validateBlogIsDraft(blog!); // Chỉ cho phép update media khi blog ở trạng thái draft

      const presignedUrls: PresignedUrlBlogMediaDto[] = [];
      const now = Date.now();

      // Xử lý các operations
      for (const operation of updateMediaDto.mediaOperations) {
        if (operation.operation === BlogMediaOperationType.DELETE) {
          // Xử lý DELETE operation
          const deleteOp = operation as DeleteBlogMediaOperationDto;

          if (deleteOp.mediaType === BlogMediaTypeEnum.CONTENT) {
            blog!.content = null;
          } else if (deleteOp.mediaType === BlogMediaTypeEnum.THUMBNAIL) {
            blog!.thumbnailUrl = null;
          }

        } else if (operation.operation === BlogMediaOperationType.ADD) {
          // Xử lý ADD operation
          const addOp = operation as AddBlogMediaOperationDto;

          // Validate media content type
          const isContent = addOp.mediaType === BlogMediaTypeEnum.CONTENT;
          const isValidContentType = isContent
            ? addOp.mimeType === 'text/html'
            : ['image/jpeg', 'image/png', 'image/webp', 'image/gif'].includes(addOp.mimeType);

          if (!isValidContentType) {
            throw new AppException(
              USER_BLOG_ERROR_CODES.USER_BLOG_INVALID_MEDIA_TYPE,
              `Loại nội dung media không hợp lệ. ${isContent ? "Content chỉ hỗ trợ 'text/html'." : "Thumbnail chỉ hỗ trợ 'image/jpeg', 'image/png', 'image/webp', 'image/gif'."}`
            );
          }

          // Tạo S3 key cho media
          const categoryFolder = isContent ? CategoryFolderEnum.DOCUMENT : CategoryFolderEnum.IMAGE;
          const mediaKey = generateS3Key({
            baseFolder: 'blogs',
            prefix: addOp.mediaType,
            categoryFolder: categoryFolder,
            fileName: addOp.name,
            useTimeFolder: true,
          });

          // Cập nhật blog entity với key mới
          if (isContent) {
            blog!.content = mediaKey;
          } else {
            blog!.thumbnailUrl = mediaKey;
          }

          // Xác định MediaType cho S3
          let mediaContentType: MediaType;
          try {
            if (isContent && addOp.mimeType === 'text/html') {
              mediaContentType = FileTypeEnum.HTML;
            } else if (!isContent) {
              mediaContentType = this.mediaHelper.getImageTypeFromMimeString(addOp.mimeType);
            } else {
              // Fallback cho các loại file khác
              mediaContentType = FileTypeEnum.HTML;
            }
          } catch (error) {
            // Mặc định nếu không tìm thấy
            mediaContentType = isContent ? FileTypeEnum.HTML : ImageTypeEnum.JPEG;
          }

          // Tạo presigned URL cho media
          const uploadUrl = await this.s3Service.createPresignedWithID(
            mediaKey,
            TimeIntervalEnum.ONE_HOUR,
            mediaContentType,
            isContent ? FileSizeEnum.FIVE_MB : FileSizeEnum.TWO_MB,
          );

          // Thêm vào danh sách presigned URLs
          presignedUrls.push({
            mediaType: addOp.mediaType,
            uploadUrl: uploadUrl,
            key: mediaKey,
          });
        }
      }

      // Cập nhật blog
      blog!.updatedAt = now;
      const updatedBlog = await this.blogRepository.save(blog!);

      // Chuyển đổi sang DTO response
      const blogResponseDto = plainToInstance(BlogResponseDto, updatedBlog, {
        excludeExtraneousValues: true,
      });

      return {
        blog: blogResponseDto,
        presignedUrls: presignedUrls.length > 0 ? presignedUrls : null,
      };

    } catch (error: unknown) {
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        USER_BLOG_ERROR_CODES.USER_BLOG_MEDIA_OPERATIONS_FAILED,
        error instanceof Error ? error.message : 'Unknown error'
      );
    }
  }

  /**
   * Gửi bài viết để kiểm duyệt
   * Chuyển trạng thái từ DRAFT sang PENDING và enable = true
   * @param id ID của bài viết
   * @param userId ID của người dùng
   * @returns Promise<void>
   */
  @Transactional()
  async submitForReview(id: number, userId: number): Promise<void> {
    // Business validations - lấy blog bao gồm cả enable = false nhưng chưa bị xóa hoàn toàn
    const blog = await this.blogRepository.findOne({
      where: {
        id,
        userId,
        status: Not(BlogStatusEnum.DELETED)
      }
    });
    this.validationHelper.validateBlogExists(blog);
    this.validationHelper.validateUserOwnsBlog(blog!, userId);
    this.validationHelper.validateBlogIsDraft(blog!);
    this.validationHelper.validateBlogStatusTransition(blog!.status, BlogStatusEnum.PENDING);

    try {
      // Cập nhật trạng thái bài viết và enable
      blog!.status = BlogStatusEnum.PENDING;
      blog!.enable = true; // Tự động set enable = true khi submit
      blog!.updatedAt = Date.now();

      await this.blogRepository.save(blog!);
    } catch (error: unknown) {
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(USER_BLOG_ERROR_CODES.USER_BLOG_SUBMIT_FOR_REVIEW_FAILED, error instanceof Error ? error.message : 'Unknown error');
    }
  }

  /**
   * Hủy gửi kiểm duyệt bài viết
   * Chuyển trạng thái từ PENDING về DRAFT và enable = false
   * @param id ID của bài viết
   * @param userId ID của người dùng
   * @returns Promise<void>
   */
  @Transactional()
  async cancelSubmit(id: number, userId: number): Promise<void> {
    // Business validations - lấy blog bao gồm cả enable = false nhưng chưa bị xóa hoàn toàn
    const blog = await this.blogRepository.findOne({
      where: {
        id,
        userId,
        status: Not(BlogStatusEnum.DELETED)
      }
    });
    this.validationHelper.validateBlogExists(blog);
    this.validationHelper.validateUserOwnsBlog(blog!, userId);
    this.validationHelper.validateBlogIsPending(blog!);
    this.validationHelper.validateBlogStatusTransition(blog!.status, BlogStatusEnum.DRAFT);

    try {
      // Cập nhật trạng thái bài viết và enable
      blog!.status = BlogStatusEnum.DRAFT;
      blog!.enable = false; // Tự động set enable = false khi cancel submit
      blog!.updatedAt = Date.now();

      await this.blogRepository.save(blog!);
    } catch (error: unknown) {
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(USER_BLOG_ERROR_CODES.USER_BLOG_CANCEL_REVIEW_FAILED, error instanceof Error ? error.message : 'Unknown error');
    }
  }

  /**
   * Xóa bài viết
   * Xóa mềm bằng cách đặt enable = false và status = DELETED
   * @param id ID của bài viết
   * @param userId ID của người dùng
   * @returns Promise<void>
   */
  @Transactional()
  async delete(id: number, userId: number): Promise<void> {
    // Business validations - lấy blog bao gồm cả enable = false nhưng chưa bị xóa hoàn toàn
    const blog = await this.blogRepository.findOne({
      where: {
        id,
        userId,
        status: Not(BlogStatusEnum.DELETED)
      }
    });
    this.validationHelper.validateBlogExists(blog);
    this.validationHelper.validateUserOwnsBlog(blog!, userId);

    // Không cho phép xóa blog đã được phê duyệt
    if (blog!.status === BlogStatusEnum.APPROVED) {
      throw new AppException(USER_BLOG_ERROR_CODES.USER_BLOG_CANNOT_EDIT_APPROVED);
    }

    try {
      // Xóa mềm bài viết: đặt enable = false và status = DELETED
      blog!.enable = false;
      blog!.status = BlogStatusEnum.DELETED;
      blog!.updatedAt = Date.now();

      await this.blogRepository.save(blog!);
    } catch (error: unknown) {
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(USER_BLOG_ERROR_CODES.USER_BLOG_DELETE_FAILED, error instanceof Error ? error.message : 'Unknown error');
    }
  }

  /**
   * Xóa nhiều bài viết cùng lúc
   * Xóa mềm bằng cách đặt enable = false và status = DELETED
   * @param ids Mảng ID các bài viết cần xóa
   * @param userId ID của người dùng
   * @returns Promise<void>
   */
  @Transactional()
  async bulkDelete(ids: number[], userId: number): Promise<void> {
    try {
      // Validate input
      if (!ids || ids.length === 0) {
        throw new AppException(USER_BLOG_ERROR_CODES.USER_BLOG_INVALID_INPUT, 'Danh sách ID không được rỗng');
      }

      // Lấy tất cả blogs thuộc về user (bao gồm cả enable = false) nhưng chưa bị xóa hoàn toàn
      const blogs = await this.blogRepository.find({
        where: {
          id: In(ids),
          userId,
          status: Not(BlogStatusEnum.DELETED) // Chỉ loại trừ những blog đã bị xóa hoàn toàn
        }
      });

      // Kiểm tra xem có blog nào không tồn tại hoặc không thuộc về user
      if (blogs.length !== ids.length) {
        const foundIds = blogs.map(blog => blog.id);
        const notFoundIds = ids.filter(id => !foundIds.includes(id));
        throw new AppException(
          USER_BLOG_ERROR_CODES.USER_BLOG_NOT_FOUND,
          `Không tìm thấy hoặc không có quyền xóa các bài viết với ID: ${notFoundIds.join(', ')}`
        );
      }

      // Kiểm tra xem có blog nào đã được phê duyệt không
      const approvedBlogs = blogs.filter(blog => blog.status === BlogStatusEnum.APPROVED);
      if (approvedBlogs.length > 0) {
        const approvedIds = approvedBlogs.map(blog => blog.id);
        throw new AppException(
          USER_BLOG_ERROR_CODES.USER_BLOG_CANNOT_EDIT_APPROVED,
          `Không thể xóa các bài viết đã được phê duyệt với ID: ${approvedIds.join(', ')}`
        );
      }

      // Xóa mềm tất cả blogs: đặt enable = false và status = DELETED
      const now = Date.now();
      await this.blogRepository.update(
        { id: In(ids), userId },
        {
          enable: false,
          status: BlogStatusEnum.DELETED,
          updatedAt: now
        }
      );

    } catch (error: unknown) {
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        USER_BLOG_ERROR_CODES.USER_BLOG_DELETE_FAILED,
        error instanceof Error ? error.message : 'Unknown error'
      );
    }
  }

  /**
   * Đếm số lượng blog đã tạo trong ngày
   * @param userId ID của người dùng
   * @returns Promise<number> Số lượng blog đã tạo trong ngày
   */
  private async getDailyBlogCount(userId: number): Promise<number> {
    try {
      const now = new Date();
      const startOfDay = new Date(now.getFullYear(), now.getMonth(), now.getDate()).getTime();
      const endOfDay = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59, 999).getTime();

      const count = await this.blogRepository.count({
        where: {
          userId,
          createdAt: Between(startOfDay, endOfDay),
          status: Not(BlogStatusEnum.DELETED)
        }
      });

      return count;
    } catch (error) {
      console.error('Error counting daily blogs:', error);
      return 0;
    }
  }

  /**
   * Tìm kiếm blog theo tags
   * @param searchDto DTO chứa thông tin tìm kiếm
   * @param userId ID của người dùng hiện tại (để check purchase status)
   * @returns Promise<PaginatedBlogResponseDto> Danh sách blog có phân trang
   */
  async searchBlogsByTags(searchDto: BlogSearchByTagsDto, userId?: number): Promise<PaginatedBlogResponseDto> {
    try {
      // Validate input
      if (!searchDto.tags || searchDto.tags.length === 0) {
        throw new AppException(USER_BLOG_ERROR_CODES.USER_BLOG_INVALID_SEARCH_PARAMS, 'Tags không được để trống');
      }

      // Gọi repository để tìm kiếm
      const { blogs, total } = await this.blogRepository.searchBlogsByTags(
        searchDto.tags,
        searchDto.page || 1,
        searchDto.limit || 10,
        searchDto.keyword
      );

      // Transform blogs thành response format
      const blogResponses: BlogResponseDto[] = [];

      for (const blog of blogs) {
        // Lấy thông tin author
        let author: AuthorDto;
        if (blog.authorType === AuthorTypeEnum.USER && blog.userId) {
          const users = await this.sqlHelper.select('users', ['id', 'full_name as fullName', 'avatar'], [
            { condition: 'id = :id', params: { id: blog.userId } }
          ]);
          const user = users && users.length > 0 ? users[0] : null;
          author = {
            id: Number(user?.id) || 0,
            name: String(user?.fullName) || 'Unknown User',
            type: AuthorTypeEnum.USER,
            avatar: this.formatCdnUrl(String(user?.avatar || '')),
          };
        } else if (blog.authorType === AuthorTypeEnum.SYSTEM && blog.employeeId) {
          const employees = await this.sqlHelper.select('employees', ['id', 'full_name as fullName', 'avatar'], [
            { condition: 'id = :id', params: { id: blog.employeeId } }
          ]);
          const employee = employees && employees.length > 0 ? employees[0] : null;
          author = {
            id: Number(employee?.id) || 0,
            name: String(employee?.fullName) || 'System',
            type: AuthorTypeEnum.SYSTEM,
            avatar: this.formatCdnUrl(String(employee?.avatar || '')),
          };
        } else {
          author = {
            id: 0,
            name: 'Unknown',
            type: AuthorTypeEnum.USER,
            avatar: '',
          };
        }

        // Kiểm tra trạng thái mua (nếu có userId)
        let isPurchased = false;
        if (userId) {
          if (Number(blog.userId) === userId) {
            // Nếu là tác giả thì coi như đã mua
            isPurchased = true;
          } else {
            // Kiểm tra đã mua chưa
            const hasPurchased = await this.sqlHelper.exists('blog_purchases', [
              { condition: 'user_id = :userId', params: { userId } },
              { condition: 'blog_id = :blogId', params: { blogId: blog.id } },
            ]);
            isPurchased = hasPurchased;
          }
        }

        // Tạo response object
        const blogResponse: BlogResponseDto = {
          id: blog.id,
          title: blog.title || '',
          description: blog.description || '',
          content: this.formatCdnUrl(blog.content),
          contentUploadUrl: undefined,
          thumbnailUploadUrl: undefined,
          point: blog.point || 0,
          viewCount: blog.viewCount || 0,
          thumbnailUrl: this.formatCdnUrl(blog.thumbnailUrl),
          tags: blog.tags || [],
          createdAt: blog.createdAt || null,
          updatedAt: blog.updatedAt || null,
          userId: blog.userId || null,
          employeeId: blog.employeeId || null,
          authorType: blog.authorType || AuthorTypeEnum.USER,
          author,
          status: blog.status,
          enable: blog.enable,
          like: blog.like || 0,
          isPurchased,
        };

        blogResponses.push(blogResponse);
      }

      // Tính toán thông tin phân trang
      const totalPages = Math.ceil(total / (searchDto.limit || 10));
      const currentPage = searchDto.page || 1;

      return {
        content: blogResponses,
        totalItems: total,
        itemCount: blogResponses.length,
        itemsPerPage: searchDto.limit || 10,
        totalPages,
        currentPage,
      };
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(USER_BLOG_ERROR_CODES.USER_BLOG_SEARCH_FAILED, error instanceof Error ? error.message : 'Unknown error');
    }
  }
}

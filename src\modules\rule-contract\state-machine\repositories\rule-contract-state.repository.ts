import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { RuleContractStateEntity } from '../entities/rule-contract-state.entity';
import { RuleContractState, RuleContractContext } from '../rule-contract.types';
import { ContractTypeEnum } from '../../entities/rule-contract.entity';
import { ContractStatus } from '../dto/admin-action.dto';
import { UserContractStatus } from '../dto/user-contract-query.dto';
import { User } from '@modules/user/entities/user.entity';

/**
 * Repository cho việc lưu trữ và khôi phục trạng thái state machine rule contract
 */
@Injectable()
export class RuleContractStateRepository {
  constructor(
    @InjectRepository(RuleContractStateEntity)
    private readonly repository: Repository<RuleContractStateEntity>,
  ) {}

  /**
   * <PERSON><PERSON><PERSON> trạng thái hiện tại của state machine
   */
  async saveState(
    userId: number,
    contractId: number | null,
    currentState: RuleContractState,
    contextData: RuleContractContext,
    completedSteps: RuleContractState[] = [],
    progressPercentage: number = 0,
  ): Promise<RuleContractStateEntity> {
    // Tìm kiếm record hiện có của user (bất kể isActive)
    const existingState = await this.repository.findOne({
      where: { userId },
      order: { updatedAt: 'DESC' },
    });

    if (existingState) {
      // Cập nhật trạng thái hiện có
      existingState.currentState = currentState;
      existingState.contextData = contextData;
      existingState.contractType = contextData.contractType;
      existingState.completedSteps = completedSteps;
      existingState.progressPercentage = progressPercentage;
      existingState.isActive = true; // Đảm bảo record được đánh dấu active

      // Cập nhật contractId nếu có thay đổi
      if (contractId !== null) {
        existingState.contractId = contractId;
      }

      return await this.repository.save(existingState);
    } else {
      // Tạo mới - sử dụng upsert để tránh race condition
      const newState = this.repository.create({
        userId,
        contractId,
        currentState,
        contextData,
        contractType: contextData.contractType,
        completedSteps,
        progressPercentage,
        isActive: true,
      });

      try {
        return await this.repository.save(newState);
      } catch (error) {
        // Nếu có lỗi unique constraint, thử update record hiện có
        if (error.code === '23505' && error.constraint === 'idx_user_id_unique') {
          const existingRecord = await this.repository.findOne({
            where: { userId },
          });

          if (existingRecord) {
            existingRecord.currentState = currentState;
            existingRecord.contextData = contextData;
            existingRecord.contractType = contextData.contractType;
            existingRecord.completedSteps = completedSteps;
            existingRecord.progressPercentage = progressPercentage;
            existingRecord.isActive = true;

            if (contractId !== null) {
              existingRecord.contractId = contractId;
            }

            return await this.repository.save(existingRecord);
          }
        }
        throw error;
      }
    }
  }

  /**
   * Lấy trạng thái đã lưu của user cho contract cụ thể
   */
  async getState(userId: number, contractId?: number | null): Promise<RuleContractStateEntity | null> {
    const whereCondition: any = { userId, isActive: true };
    if (contractId !== undefined) {
      whereCondition.contractId = contractId;
    }

    return await this.repository.findOne({
      where: whereCondition,
      order: { updatedAt: 'DESC' },
    });
  }

  /**
   * Lấy trạng thái theo contract ID
   */
  async getStateByContractId(contractId: number): Promise<RuleContractStateEntity | null> {
    return await this.repository.findOne({
      where: { contractId, isActive: true },
      order: { updatedAt: 'DESC' },
    });
  }

  /**
   * Xóa trạng thái (đánh dấu không active)
   */
  async clearState(userId: number, contractId?: number | null): Promise<void> {
    const whereCondition: any = { userId };
    if (contractId !== undefined) {
      whereCondition.contractId = contractId;
    }

    await this.repository.update(
      whereCondition,
      { isActive: false },
    );
  }

  /**
   * Kiểm tra user có trạng thái đang active không
   */
  async hasActiveState(userId: number, contractId?: number | null): Promise<boolean> {
    const whereCondition: any = { userId, isActive: true };
    if (contractId !== undefined) {
      whereCondition.contractId = contractId;
    }

    const count = await this.repository.count({
      where: whereCondition,
    });
    return count > 0;
  }

  /**
   * Reset hoàn toàn state của user (xóa khỏi database)
   * Dùng cho testing hoặc khi cần reset hoàn toàn
   */
  async resetState(userId: number): Promise<void> {
    await this.repository.delete({ userId });
  }

  /**
   * Lấy lịch sử các trạng thái của user
   */
  async getStateHistory(userId: number): Promise<RuleContractStateEntity[]> {
    return await this.repository.find({
      where: { userId },
      order: { createdAt: 'DESC' },
    });
  }

  /**
   * Lấy thống kê trạng thái theo contract type
   */
  async getStateStatistics(): Promise<any> {
    const stats = await this.repository
      .createQueryBuilder('state')
      .select('state.contractType', 'contractType')
      .addSelect('state.currentState', 'currentState')
      .addSelect('COUNT(*)', 'count')
      .where('state.isActive = :isActive', { isActive: true })
      .groupBy('state.contractType')
      .addGroupBy('state.currentState')
      .getRawMany();

    return stats;
  }

  /**
   * Lấy danh sách contracts đang ở trạng thái cụ thể
   */
  async getContractsByState(state: RuleContractState): Promise<RuleContractStateEntity[]> {
    return await this.repository.find({
      where: { currentState: state, isActive: true },
      order: { updatedAt: 'DESC' },
    });
  }

  /**
   * Lấy danh sách contracts theo contract type
   */
  async getContractsByType(contractType: ContractTypeEnum): Promise<RuleContractStateEntity[]> {
    return await this.repository.find({
      where: { contractType, isActive: true },
      order: { updatedAt: 'DESC' },
    });
  }

  /**
   * Cập nhật contract ID cho state hiện có
   */
  async updateContractId(userId: number, oldContractId: number | null, newContractId: number): Promise<void> {
    const whereCondition: any = { userId, isActive: true };
    if (oldContractId !== null) {
      whereCondition.contractId = oldContractId;
    } else {
      whereCondition.contractId = null;
    }

    await this.repository.update(
      whereCondition,
      { contractId: newContractId },
    );
  }

  /**
   * Lấy danh sách hợp đồng với pagination cho admin
   */
  async getPendingApprovalsWithPagination(
    page: number,
    limit: number,
    status?: string,
    contractType?: string,
    search?: string,
    sortBy?: string,
    sortDirection?: 'ASC' | 'DESC'
  ): Promise<{ items: any[], total: number }> {
    // Validate input parameters
    const validPage = Math.max(1, page || 1);
    const validLimit = Math.min(Math.max(1, limit || 10), 100);
    const validSortDirection = (sortDirection === 'ASC' || sortDirection === 'DESC') ? sortDirection : 'DESC';

    // Sử dụng raw query để tránh vấn đề TypeORM với JOIN và ORDER BY
    const whereConditions = ['state.is_active = true'];
    const queryParams: any[] = [];
    let paramIndex = 1;

    // Filter theo status - Map từ ContractStatus sang RuleContractState
    const validStates = [
      RuleContractState.PENDING_APPROVAL,
      RuleContractState.APPROVED,
      RuleContractState.REJECTED
    ];

    if (status && status !== ContractStatus.ALL) {
      // Map từ ContractStatus sang RuleContractState
      let mappedState: string;
      switch (status) {
        case ContractStatus.PENDING_APPROVAL:
          mappedState = RuleContractState.PENDING_APPROVAL;
          break;
        case ContractStatus.APPROVED:
          mappedState = RuleContractState.APPROVED;
          break;
        case ContractStatus.REJECTED:
          mappedState = RuleContractState.REJECTED;
          break;
        default:
          mappedState = RuleContractState.PENDING_APPROVAL;
      }

      whereConditions.push(`state.current_state = $${paramIndex}`);
      queryParams.push(mappedState);
      paramIndex++;
    } else {
      whereConditions.push(`state.current_state = ANY($${paramIndex})`);
      queryParams.push(validStates);
      paramIndex++;
    }

    // Filter theo contract type
    if (contractType) {
      whereConditions.push(`state.contract_type = $${paramIndex}`);
      queryParams.push(contractType);
      paramIndex++;
    }

    // Search - tìm trong context data (JSON), user info và số điện thoại
    console.log(`[getPendingApprovalsWithPagination] Search parameter received: "${search}"`);
    if (search) {
      console.log(`[getPendingApprovalsWithPagination] Adding search condition for: "${search}"`);
      whereConditions.push(`(
        LOWER(state.context_data::text) LIKE LOWER($${paramIndex}) OR
        LOWER(u.full_name) LIKE LOWER($${paramIndex}) OR
        LOWER(u.email) LIKE LOWER($${paramIndex}) OR
        u.phone_number LIKE $${paramIndex}
      )`);
      queryParams.push(`%${search}%`);
      paramIndex++;
    } else {
      console.log(`[getPendingApprovalsWithPagination] No search parameter provided`);
    }

    // Sort mapping
    let orderBy: string;
    switch (sortBy) {
      case 'userName':
        orderBy = `u.full_name ${validSortDirection}`;
        break;
      case 'createdAt':
        orderBy = `state.created_at ${validSortDirection}`;
        break;
      case 'updatedAt':
        orderBy = `state.updated_at ${validSortDirection}`;
        break;
      case 'currentState':
        orderBy = `state.current_state ${validSortDirection}`;
        break;
      case 'contractType':
        orderBy = `state.contract_type ${validSortDirection}`;
        break;
      default:
        orderBy = `state.created_at ${validSortDirection}`;
    }

    // Count query
    const countQuery = `
      SELECT COUNT(*) as total
      FROM rule_contract_states state
      LEFT JOIN users u ON u.id = state.user_id
      WHERE ${whereConditions.join(' AND ')}
    `;

    // Data query
    const dataQuery = `
      SELECT
        state.id,
        state.user_id,
        state.contract_id,
        state.current_state,
        state.context_data,
        state.contract_type,
        state.is_active,
        state.completed_steps,
        state.progress_percentage,
        state.created_at,
        state.updated_at,
        u.full_name as user_full_name,
        u.email as user_email,
        u.phone_number as user_phone_number
      FROM rule_contract_states state
      LEFT JOIN users u ON u.id = state.user_id
      WHERE ${whereConditions.join(' AND ')}
      ORDER BY ${orderBy}
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `;

    // Execute queries
    console.log('Count Query:', countQuery);
    console.log('Data Query:', dataQuery);
    console.log('Query Params:', queryParams);

    const [countResult] = await this.repository.query(countQuery, queryParams);
    const total = parseInt(countResult.total);

    const offset = (validPage - 1) * validLimit;
    const items = await this.repository.query(dataQuery, [...queryParams, validLimit, offset]);

    console.log('Query results - Total:', total, 'Items count:', items?.length);
    console.log('First item sample:', items?.[0]);

    return { items, total };
  }

  /**
   * Lấy danh sách hợp đồng của user với pagination
   */
  async getUserContractsWithPagination(
    userId: number,
    page: number,
    limit: number,
    status?: string,
    contractType?: string,
    search?: string,
    sortBy?: string,
    sortDirection?: 'ASC' | 'DESC'
  ): Promise<{ items: any[], total: number }> {
    // Validate input parameters
    const validPage = Math.max(1, page || 1);
    const validLimit = Math.min(Math.max(1, limit || 10), 100);
    const validSortDirection = (sortDirection === 'ASC' || sortDirection === 'DESC') ? sortDirection : 'DESC';

    // Sử dụng raw query để tránh vấn đề TypeORM với JOIN và ORDER BY
    const whereConditions = ['state.is_active = true', 'state.user_id = $1'];
    const queryParams: any[] = [userId];
    let paramIndex = 2;

    // Chỉ lấy các state hợp lệ (có thể có hợp đồng hoặc chưa)
    const validStates = [
      RuleContractState.PENDING_APPROVAL,
      RuleContractState.APPROVED,
      RuleContractState.REJECTED,
      RuleContractState.INDIVIDUAL_CONTRACT_REVIEW,
      RuleContractState.BUSINESS_CONTRACT_REVIEW,
      RuleContractState.INDIVIDUAL_HAND_SIGNATURE,
      RuleContractState.INDIVIDUAL_OTP_VERIFICATION,
      RuleContractState.BUSINESS_FILE_UPLOAD,
    ];

    if (status && status !== UserContractStatus.ALL) {
      // Map từ UserContractStatus sang RuleContractState
      let mappedState: string;
      switch (status) {
        case UserContractStatus.PENDING_APPROVAL:
          mappedState = RuleContractState.PENDING_APPROVAL;
          break;
        case UserContractStatus.APPROVED:
          mappedState = RuleContractState.APPROVED;
          break;
        case UserContractStatus.REJECTED:
          mappedState = RuleContractState.REJECTED;
          break;
        default:
          mappedState = RuleContractState.PENDING_APPROVAL;
      }

      whereConditions.push(`state.current_state = $${paramIndex}`);
      queryParams.push(mappedState);
      paramIndex++;
    } else {
      whereConditions.push(`state.current_state = ANY($${paramIndex})`);
      queryParams.push(validStates);
      paramIndex++;
    }

    // Filter theo contract type
    if (contractType) {
      whereConditions.push(`state.contract_type = $${paramIndex}`);
      queryParams.push(contractType);
      paramIndex++;
    }

    // Search - tìm trong context data (JSON)
    console.log(`[getUserContractsWithPagination] Search parameter received: "${search}"`);
    if (search) {
      console.log(`[getUserContractsWithPagination] Adding search condition for: "${search}"`);
      whereConditions.push(`LOWER(state.context_data::text) LIKE LOWER($${paramIndex})`);
      queryParams.push(`%${search}%`);
      paramIndex++;
    } else {
      console.log(`[getUserContractsWithPagination] No search parameter provided`);
    }

    // Sorting
    const validSortFields = ['created_at', 'updated_at', 'current_state', 'contract_type'];
    let sortField = 'created_at';
    if (sortBy && validSortFields.includes(sortBy)) {
      sortField = sortBy;
    } else if (sortBy === 'createdAt') {
      sortField = 'created_at';
    } else if (sortBy === 'updatedAt') {
      sortField = 'updated_at';
    } else if (sortBy === 'currentState') {
      sortField = 'current_state';
    } else if (sortBy === 'contractType') {
      sortField = 'contract_type';
    }

    const orderBy = `state.${sortField} ${validSortDirection}`;

    // Count query
    const countQuery = `
      SELECT COUNT(*) as total
      FROM rule_contract_states state
      WHERE ${whereConditions.join(' AND ')}
    `;

    // Data query
    const dataQuery = `
      SELECT
        state.id,
        state.user_id,
        state.contract_id,
        state.current_state,
        state.context_data,
        state.contract_type,
        state.is_active,
        state.completed_steps,
        state.progress_percentage,
        state.created_at,
        state.updated_at,
        u.full_name as user_full_name,
        u.email as user_email,
        u.phone_number as user_phone_number
      FROM rule_contract_states state
      LEFT JOIN users u ON u.id = state.user_id
      WHERE ${whereConditions.join(' AND ')}
      ORDER BY ${orderBy}
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `;

    // Execute queries
    console.log('Count Query:', countQuery);
    console.log('Data Query:', dataQuery);
    console.log('Query Params:', queryParams);

    const [countResult] = await this.repository.query(countQuery, queryParams);
    const total = parseInt(countResult.total);

    const offset = (validPage - 1) * validLimit;
    const items = await this.repository.query(dataQuery, [...queryParams, validLimit, offset]);

    console.log('Query results - Total:', total, 'Items count:', items?.length);
    console.log('First item sample:', items?.[0]);

    return { items, total };
  }

  /**
   * Xóa tất cả state records của user
   */
  async deleteByUserId(userId: number): Promise<void> {
    await this.repository.delete({ userId });
  }
}

import { Entity, Column, PrimaryGeneratedColumn, UpdateDateColumn, Index } from 'typeorm';

/**
 * Entity đại diện cho bảng generic_session_layouts trong cơ sở dữ liệu
 * Bảng lưu trữ layout configuration cho mỗi session của trang generic
 */
@Entity('generic_session_layouts')
@Index(['sessionId'], { unique: true })
export class GenericSessionLayout {
  /**
   * ID duy nhất của layout record, dạng UUID
   */
  @PrimaryGeneratedColumn('uuid')
  id: string;

  /**
   * Session ID tham chiếu đến GenericSession
   */
  @Column({ name: 'session_id', length: 255, unique: true })
  sessionId: string;

  /**
   * Dữ liệu layout dạng JSON theo format của react-grid-layout
   * Format: Array<{ i: string, x: number, y: number, w: number, h: number, ... }>
   */
  @Column({ name: 'layout_data', type: 'jsonb' })
  layoutData: Array<{
    i: string; // widget id
    x: number;
    y: number;
    w: number;
    h: number;
    minW?: number;
    minH?: number;
    maxW?: number;
    maxH?: number;
    static?: boolean;
    isDraggable?: boolean;
    isResizable?: boolean;
  }>;

  /**
   * Breakpoints configuration cho responsive layout
   */
  @Column({ type: 'jsonb', nullable: true })
  breakpoints?: {
    lg?: Array<any>;
    md?: Array<any>;
    sm?: Array<any>;
    xs?: Array<any>;
    xxs?: Array<any>;
  };

  /**
   * Grid configuration settings
   */
  @Column({ name: 'grid_config', type: 'jsonb', nullable: true })
  gridConfig?: {
    rowHeight?: number;
    margin?: [number, number];
    containerPadding?: [number, number];
    cols?: { lg: number; md: number; sm: number; xs: number; xxs: number };
    compactType?: 'vertical' | 'horizontal' | null;
    preventCollision?: boolean;
    autoSize?: boolean;
  };

  /**
   * Thời gian cập nhật layout
   */
  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  /**
   * Metadata bổ sung cho layout
   */
  @Column({ type: 'jsonb', nullable: true })
  metadata?: Record<string, any>;
}

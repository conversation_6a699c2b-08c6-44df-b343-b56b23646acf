import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsEnum,
  IsIn,
  IsNotEmpty,
  IsNumber,
  IsObject,
  IsOptional,
  IsString,
  Min,
  ValidateNested,
  Validate,
  ValidatorConstraint,
  ValidatorConstraintInterface,
  registerDecorator,
  ValidationOptions,
  ValidationArguments,
} from 'class-validator';
import { OrderAddressDto } from './user-address.dto';
import { CarrierEnum } from '@modules/business/enums';

/**
 * Custom validator cho GHTK: Khi deliver_option = "xteam" thì pick_option phải là "cod"
 */
@ValidatorConstraint({ name: 'ghtk-deliver-option', async: false })
export class GHTKDeliverOptionValidator implements ValidatorConstraintInterface {
  validate(value: unknown, args: ValidationArguments) {
    const object = args.object as Record<string, unknown>;

    // Chỉ validate khi preferredCarrier là GHTK
    if (object.preferredCarrier !== 'GHTK') {
      return true;
    }

    // Nếu deliver_option = "xteam" thì pick_option phải là "cod"
    if (object.deliver_option === 'xteam') {
      if (!object.pick_option || object.pick_option !== 'cod') {
        return false;
      }
    }

    return true;
  }

  defaultMessage(args: ValidationArguments) {
    return 'Khi sử dụng GHTK với deliver_option = "xteam", pick_option phải là "cod"';
  }
}

// Define valid shipping services for each carrier
const VALID_SHIPPING_SERVICES = {
  'GHN': [
    // Theo docs GHN chính thức
    'light',      // service_type_id: 2 (Hàng nhẹ)
    'heavy',      // service_type_id: 5 (Hàng nặng)
    'standard',   // Tự động chọn service_type_id
    // Legacy values (backward compatibility)
    'Chuẩn', 'Nhanh', 'Tiết kiệm', 'express', 'economy'
  ],
  'GHTK': [
    'standard',   // deliver_option: "none"
    'express',    // deliver_option: "xteam"
    // Legacy values (backward compatibility)
    'Đường bộ', 'Đường hàng không', 'road', 'fly'
  ],
  'AHAMOVE': ['Giao hàng trong ngày', 'same_day'],
  'JT': ['J&T Tiêu chuẩn', 'jt_standard']
};

/**
 * Custom validation decorator for shipping service based on carrier
 */
export function IsValidShippingService(validationOptions?: ValidationOptions) {
  return function (object: object, propertyName: string) {
    registerDecorator({
      name: 'isValidShippingService',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: {
        validate(value: unknown, args: ValidationArguments) {
          if (!value) return true; // Optional field

          const obj = args.object as Record<string, unknown>;
          const carrier = obj.preferredCarrier;

          if (!carrier || typeof carrier !== 'string') return true; // If no carrier specified, skip validation

          const validServices = VALID_SHIPPING_SERVICES[carrier];
          return validServices ? validServices.includes(value as string) : false;
        },
        defaultMessage(args: ValidationArguments) {
          const obj = args.object as Record<string, unknown>;
          const carrier = obj.preferredCarrier;
          const validServices = (typeof carrier === 'string' ? VALID_SHIPPING_SERVICES[carrier] : []) || [];
          return `Dịch vụ vận chuyển không hợp lệ cho ${carrier}. Các dịch vụ hợp lệ: ${validServices.join(', ')}`;
        },
      },
    });
  };
}

/**
 * Custom validator để kiểm tra sản phẩm chỉ là PHYSICAL hoặc COMBO
 */
@ValidatorConstraint({ name: 'shipping-product-type', async: true })
export class ShippingProductTypeValidator implements ValidatorConstraintInterface {
  async validate(productId: number, args: ValidationArguments) {
    // Note: Validation logic sẽ được implement trong service layer
    // vì cần access database để check product type
    return true;
  }

  defaultMessage(args: ValidationArguments) {
    return 'Chỉ sản phẩm PHYSICAL và COMBO mới cần tính phí vận chuyển';
  }
}

/**
 * DTO cho thông tin sản phẩm trong request tính phí vận chuyển
 * CHỈ HỖ TRỢ SẢN PHẨM PHYSICAL VÀ COMBO (vì chỉ 2 loại này cần vận chuyển)
 */
export class ShippingProductDto {
  @ApiProperty({
    description: 'ID sản phẩm (chỉ PHYSICAL hoặc COMBO)',
    example: 10,
    examples: {
      'Sản phẩm PHYSICAL': {
        summary: 'Sản phẩm vật lý - BẮT BUỘC có selectedVariantId',
        value: 10,
        description: 'ID của sản phẩm PHYSICAL, BẮT BUỘC kèm theo selectedVariantId để tính weight/dimensions chính xác'
      },
      'Sản phẩm COMBO': {
        summary: 'Sản phẩm combo - không cần selectedVariantId',
        value: 14,
        description: 'ID của sản phẩm COMBO, sử dụng weight/dimensions mặc định'
      }
    }
  })
  @IsNotEmpty()
  @IsNumber()
  @Min(1)
  @Validate(ShippingProductTypeValidator)
  productId: number;

  @ApiProperty({
    description: 'Số lượng sản phẩm',
    example: 2,
    examples: {
      'Số lượng nhỏ': {
        summary: 'Mua ít sản phẩm',
        value: 1,
        description: 'Mua 1 sản phẩm'
      },
      'Số lượng vừa': {
        summary: 'Mua nhiều sản phẩm',
        value: 2,
        description: 'Mua 2 sản phẩm, ảnh hưởng đến trọng lượng và phí vận chuyển'
      }
    }
  })
  @IsNotEmpty()
  @IsNumber()
  @Min(1)
  quantity: number;

  @ApiProperty({
    description: 'ID variant được chọn (BẮT BUỘC cho sản phẩm PHYSICAL)',
    example: 123,
    required: false,
    examples: {
      'Variant PHYSICAL': {
        summary: 'BẮT BUỘC cho sản phẩm PHYSICAL',
        value: 123,
        description: 'ID của variant được chọn, ảnh hưởng đến trọng lượng và kích thước. BẮT BUỘC cho PHYSICAL.'
      },
      'Không cần cho COMBO': {
        summary: 'COMBO không cần selectedVariantId',
        value: null,
        description: 'Sản phẩm COMBO không cần selectedVariantId'
      }
    }
  })
  @IsOptional()
  @IsNumber()
  selectedVariantId?: number;

  // Note: Validation cho selectedVariantId bắt buộc với PHYSICAL sẽ được thực hiện trong service layer
  // vì cần access database để check product type

  @ApiProperty({
    description: 'Tùy chọn tùy chỉnh cho sản phẩm',
    example: {
      color: 'Đỏ',
      size: 'L'
    },
    required: false,
    examples: {
      'Tùy chọn màu sắc và kích thước': {
        summary: 'Sản phẩm thời trang',
        value: {
          color: 'Đỏ',
          size: 'L'
        },
        description: 'Tùy chọn cho sản phẩm quần áo'
      },
      'Tùy chọn in ấn': {
        summary: 'Sản phẩm có thể tùy chỉnh',
        value: {
          customText: 'In tên khách hàng',
          font: 'Arial'
        },
        description: 'Tùy chọn in ấn cho sản phẩm'
      }
    }
  })
  @IsOptional()
  @IsObject()
  customOptions?: Record<string, string | number | boolean>;

  @ApiProperty({
    description: 'Ghi chú đặc biệt cho sản phẩm',
    example: 'Gói quà đặc biệt',
    required: false,
    examples: {
      'Ghi chú gói quà': {
        summary: 'Yêu cầu gói quà',
        value: 'Gói quà đặc biệt',
        description: 'Yêu cầu đóng gói đặc biệt cho sản phẩm'
      },
      'Ghi chú xử lý': {
        summary: 'Yêu cầu xử lý cẩn thận',
        value: 'Hàng dễ vỡ, xin xử lý cẩn thận',
        description: 'Ghi chú về cách xử lý sản phẩm'
      }
    }
  })
  @IsOptional()
  @IsString()
  productNote?: string;
}

/**
 * DTO cho request tính phí vận chuyển với hỗ trợ variant selection
 */
export class CalculateShippingFeeRequestDto {
  @ApiProperty({
    description: 'ID địa chỉ shop để lấy địa chỉ gửi từ bảng user_shop_addresses',
    example: 1,
    examples: {
      'Shop address ID': {
        summary: 'ID địa chỉ shop đã lưu',
        value: 1,
        description: 'Sử dụng địa chỉ shop đã được cấu hình trong hệ thống'
      }
    }
  })
  @IsNotEmpty()
  @IsNumber()
  @Min(1)
  userShopAddressId: number;

  @ApiProperty({
    description: 'Danh sách sản phẩm',
    type: [ShippingProductDto],
    examples: {
      'PHYSICAL với variant (BẮT BUỘC selectedVariantId)': {
        summary: 'Sản phẩm PHYSICAL - BẮT BUỘC có selectedVariantId',
        description: 'Sản phẩm PHYSICAL bắt buộc phải có selectedVariantId để lấy weight/dimensions từ variant',
        value: [
          {
            productId: 8,
            quantity: 1,
            selectedVariantId: 123,
            customOptions: {
              color: 'Đỏ',
              size: 'L'
            },
            productNote: 'Xử lý cẩn thận'
          }
        ]
      },
      'COMBO (không cần selectedVariantId)': {
        summary: 'Sản phẩm COMBO - không cần selectedVariantId',
        description: 'Sản phẩm COMBO sử dụng weight/dimensions tổng hợp từ các sản phẩm con',
        value: [
          {
            productId: 14,
            quantity: 1,
            productNote: 'Gói quà đặc biệt'
          }
        ]
      },
      'Mixed PHYSICAL + COMBO (thực tế)': {
        summary: 'Kết hợp PHYSICAL (có variant) và COMBO',
        description: 'Đơn hàng hỗn hợp: PHYSICAL bắt buộc có selectedVariantId, COMBO không cần',
        value: [
          {
            productId: 8,
            quantity: 2,
            selectedVariantId: 123
          },
          {
            productId: 14,
            quantity: 1
          }
        ]
      }
    }
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ShippingProductDto)
  products: ShippingProductDto[];

  @ApiProperty({
    description: 'ID khách hàng để lấy địa chỉ mặc định (nếu không có deliveryAddress)',
    example: 'a5541891-75e8-4d55-9185-586813d4bada',
    required: false,
    examples: {
      'Customer with default address': {
        summary: 'Khách hàng có địa chỉ mặc định',
        value: 'a5541891-75e8-4d55-9185-586813d4bada',
        description: 'Sử dụng địa chỉ mặc định của khách hàng khi không truyền deliveryAddress'
      }
    }
  })
  @IsOptional()
  @IsString()
  customerId?: string;

  @ApiProperty({
    description: 'Thông tin địa chỉ giao hàng (chọn có sẵn hoặc tạo mới). Nếu không truyền sẽ sử dụng địa chỉ của customer.',
    type: OrderAddressDto,
    required: false,
    examples: {
      'Sử dụng địa chỉ có sẵn': {
        summary: 'Chọn địa chỉ đã lưu trong hệ thống',
        description: 'Sử dụng khi khách hàng đã có địa chỉ được lưu trước đó',
        value: {
          addressId: 22
        }
      },
      'Tạo địa chỉ mới - TP.HCM': {
        summary: 'Tạo địa chỉ mới tại TP. Hồ Chí Minh',
        description: 'Phù hợp cho GHN với dịch vụ nhanh trong nội thành',
        value: {
          newAddress: {
            recipientName: "Nguyễn Văn A",
            recipientPhone: "0912345678",
            address: "123 Đường ABC, Phường 1",
            province: "TP. Hồ Chí Minh",
            district: "Quận 1",
            ward: "Phường Bến Nghé",
            postalCode: "70000",
            isDefault: false,
            addressType: "home",
            note: "Gần chợ Bến Thành"
          }
        }
      },
      'Tạo địa chỉ mới - Hà Nội': {
        summary: 'Tạo địa chỉ mới tại Hà Nội',
        description: 'Phù hợp cho GHTK với dịch vụ xTeam',
        value: {
          newAddress: {
            recipientName: "Trần Thị B",
            recipientPhone: "0987654321",
            address: "456 Đường XYZ, Phường 2",
            province: "Hà Nội",
            district: "Quận Hai Bà Trưng",
            ward: "Phường Bách Khoa",
            postalCode: "10000",
            isDefault: false,
            addressType: "office",
            note: "Gần trường Đại học Bách Khoa"
          }
        }
      }
    }
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => OrderAddressDto)
  deliveryAddress?: OrderAddressDto;

  @ApiProperty({
    description: 'Đơn vị vận chuyển ưu tiên',
    enum: CarrierEnum,
    example: CarrierEnum.GHN,
    examples: {
      'GHN - Giao Hàng Nhanh': {
        summary: 'Sử dụng GHN',
        value: CarrierEnum.GHN,
        description: 'Giao Hàng Nhanh - phù hợp cho nội thành và liên tỉnh, có nhiều dịch vụ (Chuẩn, Nhanh, Tiết kiệm)'
      },
      'GHTK - Giao Hàng Tiết Kiệm': {
        summary: 'Sử dụng GHTK',
        value: CarrierEnum.GHTK,
        description: 'Giao Hàng Tiết Kiệm - phù hợp cho các tỉnh xa, có dịch vụ xTeam và thường'
      }
    }
  })
  @IsNotEmpty()
  @IsEnum(CarrierEnum)
  preferredCarrier: CarrierEnum;

  @ApiProperty({
    description: 'Dịch vụ vận chuyển cụ thể (tùy chọn). Tuân thủ docs chính thức GHN và GHTK. ⚠️ LƯU Ý: Dịch vụ "heavy" yêu cầu khối lượng ≥ 20kg.',
    example: 'light',
    required: false,
    examples: {
      'GHN Light Goods': {
        summary: 'GHN - Hàng nhẹ (service_type_id: 2)',
        value: 'light',
        description: 'GHN Hàng nhẹ - Dùng length, width, height, weight theo docs chính thức. Phù hợp với hàng có khối lượng < 20kg'
      },
      'GHN Heavy Goods': {
        summary: 'GHN - Hàng nặng (service_type_id: 5)',
        value: 'heavy',
        description: 'GHN Hàng nặng - Dùng items[] với từng kiện hàng theo docs chính thức. YÊU CẦU: Khối lượng ≥ 20kg hoặc có khối lượng thực/khối lượng quy đổi lớn'
      },
      'GHN Standard (Auto)': {
        summary: 'GHN - Tự động chọn service_type_id',
        value: 'standard',
        description: 'GHN tự động chọn service_type_id phù hợp dựa trên sản phẩm'
      },
      'GHTK Standard': {
        summary: 'GHTK - Dịch vụ thường',
        value: 'standard',
        description: 'GHTK deliver_option: "none" - Dịch vụ tiêu chuẩn (3-5 ngày)'
      },
      'GHTK Express': {
        summary: 'GHTK - Dịch vụ xTeam',
        value: 'express',
        description: 'GHTK deliver_option: "xteam" - Dịch vụ nhanh (1-3 ngày)'
      }
    }
  })
  @IsOptional()
  @IsString()
  @IsValidShippingService({
    message: 'Dịch vụ vận chuyển không hợp lệ cho đơn vị vận chuyển đã chọn'
  })
  shippingService?: string;

  @ApiProperty({
    description: 'Dịch vụ xFast/xTeam cho GHTK (tùy chọn). Chỉ sử dụng khi preferredCarrier là GHTK. Đây là tham số bắt buộc cho GHTK API.',
    example: 'none',
    required: false,
    enum: ['xteam', 'none'],
    examples: {
      'GHTK xTeam - Dịch vụ nhanh': {
        summary: 'GHTK xTeam',
        value: 'xteam',
        description: 'GHTK xTeam - Dịch vụ nhanh (1-2 ngày), phí cao hơn nhưng giao nhanh'
      },
      'GHTK Thường - Dịch vụ tiêu chuẩn': {
        summary: 'GHTK None',
        value: 'none',
        description: 'GHTK Thường - Dịch vụ tiêu chuẩn (2-4 ngày), giá cả hợp lý'
      }
    }
  })
  @IsOptional()
  @IsString()
  @IsIn(['xteam', 'none'], { message: 'deliver_option phải là "xteam" hoặc "none"' })
  deliver_option?: string;

  @ApiProperty({
    description: 'Tùy chọn thu tiền cho GHTK (bắt buộc khi deliver_option = "xteam")',
    enum: ['cod', 'post'],
    example: 'cod',
    required: false,
    examples: {
      'Thu tiền khi giao': {
        summary: 'COD - Thu tiền khi giao',
        value: 'cod',
        description: 'Thu tiền mặt khi giao hàng'
      },
      'Chuyển khoản': {
        summary: 'POST - Chuyển khoản',
        value: 'post',
        description: 'Thanh toán bằng chuyển khoản'
      }
    }
  })
  @IsOptional()
  @IsString()
  @IsIn(['cod', 'post'], { message: 'pick_option phải là "cod" hoặc "post"' })
  pick_option?: string;

  // ❌ Xóa receiverPaysShipping vì giờ lấy từ User Provider Shipment setting
  // Người dùng sẽ cấu hình trong phần quản lý nhà cung cấp vận chuyển

  /**
   * Custom validation: Khi deliver_option = "xteam" thì pick_option phải là "cod"
   * Không cần type vì chỉ dùng cho validation
   */
  @Validate(GHTKDeliverOptionValidator)
  _ghtk_validation?: never;
}





/**
 * DTO cho response tính phí vận chuyển
 */
export class CalculateShippingFeeResponseDto {
  @ApiProperty({
    description: 'Đơn vị vận chuyển được chọn',
    example: 'GHN'
  })
  carrier: string;

  @ApiProperty({
    description: 'Phí vận chuyển (VND)',
    example: 36300
  })
  fee: number;

  @ApiProperty({
    description: 'Loại dịch vụ',
    example: 'light',
    examples: {
      'GHN Light': { summary: 'GHN Hàng nhẹ', value: 'light' },
      'GHN Heavy': { summary: 'GHN Hàng nặng', value: 'heavy' },
      'GHTK Standard': { summary: 'GHTK Thường', value: 'standard' },
      'GHTK Express': { summary: 'GHTK xTeam', value: 'express' }
    }
  })
  serviceType: string;

  @ApiProperty({
    description: 'Thời gian giao hàng dự kiến',
    example: '2-3 ngày',
    required: false
  })
  estimatedDeliveryTime?: string;



  @ApiProperty({
    description: 'Thông tin chi tiết sản phẩm cần vận chuyển (chỉ PHYSICAL và COMBO)',
    example: {
      totalWeight: 400, // gram - Lấy trực tiếp từ variant.shipmentConfig.weightGram
      totalValue: 900000,
      dimensions: {
        length: 30, // cm - Từ variant.shipmentConfig.lengthCm
        width: 25,  // cm - Từ variant.shipmentConfig.widthCm
        height: 10  // cm - Từ variant.shipmentConfig.heightCm
      },
      shippingProducts: [
        {
          productId: 8,
          name: 'Áo thun nam cao cấp - Phiên bản mới',
          quantity: 2,
          weight: 200, // gram từ variant
          selectedVariantId: 123,
          productType: 'PHYSICAL'
        }
      ],
      nonShippingProducts: []
    },
    required: false
  })
  productDetails?: {
    totalWeight: number;
    totalValue: number;
    dimensions?: {
      length: number;
      width: number;
      height: number;
    };
    items?: Array<{
      name: string;
      quantity: number;
      length: number;
      width: number;
      height: number;
      weight: number;
    }>;
    shippingProducts: Array<{
      productId: number;
      name: string;
      quantity: number;
      weight: number;
      dimensions: {
        length: number;
        width: number;
        height: number;
      };
    }>;
    nonShippingProducts: Array<{
      productId: number;
      name: string;
      quantity: number;
      productType: string;
    }>;
  };
}

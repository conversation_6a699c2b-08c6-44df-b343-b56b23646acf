import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsOptional, IsObject, IsArray, ValidateNested, IsEnum } from 'class-validator';
import { Type } from 'class-transformer';
import { CreateGenericWidgetDto, GenericWidgetResponseDto } from './generic-widget.dto';
import { LayoutItemDto } from './generic-layout.dto';

/**
 * Enum cho WebSocket event types
 */
export enum WebSocketEventType {
  // Client → Server events
  CONNECT = 'connect',
  ADD_WIDGET = 'add_widget',
  REMOVE_WIDGET = 'remove_widget',
  UPDATE_LAYOUT = 'update_layout',
  SYNC_STATE = 'sync_state',
  
  // Server → Client events
  WIDGET_ADDED = 'widget_added',
  WIDGET_REMOVED = 'widget_removed',
  LAYOUT_UPDATED = 'layout_updated',
  STATE_SYNCED = 'state_synced',
  SESSION_EXPIRED = 'session_expired',
  ERROR = 'error'
}

/**
 * Base DTO cho WebSocket events
 */
export class BaseWebSocketEventDto<T = any> {
  @ApiProperty({
    description: 'Event type',
    enum: WebSocketEventType,
    example: WebSocketEventType.ADD_WIDGET
  })
  @IsEnum(WebSocketEventType)
  type: WebSocketEventType;

  @ApiProperty({
    description: 'Session ID',
    example: 'session-123-abc'
  })
  @IsString()
  sessionId: string;

  @ApiProperty({
    description: 'Timestamp của event',
    example: '2024-01-08T10:30:00Z'
  })
  @IsString()
  timestamp: string;

  @ApiPropertyOptional({
    description: 'Event payload',
    example: {}
  })
  @IsOptional()
  @IsObject()
  payload?: T;
}

/**
 * DTO cho connect event
 */
export class ConnectEventDto {
  @ApiProperty({
    description: 'Session ID để connect',
    example: 'session-123-abc'
  })
  @IsString()
  sessionId: string;

  @ApiPropertyOptional({
    description: 'User ID (nếu authenticated)',
    example: 123
  })
  @IsOptional()
  userId?: number;

  @ApiPropertyOptional({
    description: 'Client metadata',
    example: { userAgent: 'Mozilla/5.0...', version: '1.0.0' }
  })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}

/**
 * DTO cho add widget event
 */
export class AddWidgetEventDto extends BaseWebSocketEventDto<{
  widget: CreateGenericWidgetDto;
  position?: { x: number; y: number };
}> {
  @ApiProperty({
    description: 'Widget data để add',
    type: CreateGenericWidgetDto
  })
  @ValidateNested()
  @Type(() => CreateGenericWidgetDto)
  declare payload: {
    widget: CreateGenericWidgetDto;
    position?: { x: number; y: number };
  };
}

/**
 * DTO cho remove widget event
 */
export class RemoveWidgetEventDto extends BaseWebSocketEventDto<{
  widgetId: string;
}> {
  @ApiProperty({
    description: 'Widget ID để remove',
    example: { widgetId: 'widget-123' }
  })
  declare payload: {
    widgetId: string;
  };
}

/**
 * DTO cho update layout event
 */
export class UpdateLayoutEventDto extends BaseWebSocketEventDto<{
  layout: LayoutItemDto[];
}> {
  @ApiProperty({
    description: 'Layout data mới',
    type: [LayoutItemDto]
  })
  declare payload: {
    layout: LayoutItemDto[];
  };
}

/**
 * DTO cho sync state event
 */
export class SyncStateEventDto extends BaseWebSocketEventDto<{
  widgets: GenericWidgetResponseDto[];
  layout: LayoutItemDto[];
}> {
  @ApiProperty({
    description: 'Full state để sync',
    example: { widgets: [], layout: [] }
  })
  declare payload: {
    widgets: GenericWidgetResponseDto[];
    layout: LayoutItemDto[];
  };
}

/**
 * DTO cho widget added response
 */
export class WidgetAddedEventDto extends BaseWebSocketEventDto<{
  widget: GenericWidgetResponseDto;
}> {
  @ApiProperty({
    description: 'Widget đã được add',
    type: GenericWidgetResponseDto
  })
  declare payload: {
    widget: GenericWidgetResponseDto;
  };
}

/**
 * DTO cho widget removed response
 */
export class WidgetRemovedEventDto extends BaseWebSocketEventDto<{
  widgetId: string;
}> {
  @ApiProperty({
    description: 'Widget ID đã được remove',
    example: { widgetId: 'widget-123' }
  })
  declare payload: {
    widgetId: string;
  };
}

/**
 * DTO cho layout updated response
 */
export class LayoutUpdatedEventDto extends BaseWebSocketEventDto<{
  layout: LayoutItemDto[];
}> {
  @ApiProperty({
    description: 'Layout data đã được update',
    type: [LayoutItemDto]
  })
  declare payload: {
    layout: LayoutItemDto[];
  };
}

/**
 * DTO cho state synced response
 */
export class StateSyncedEventDto extends BaseWebSocketEventDto<{
  widgets: GenericWidgetResponseDto[];
  layout: LayoutItemDto[];
}> {
  @ApiProperty({
    description: 'Full state đã được sync',
    example: { widgets: [], layout: [] }
  })
  declare payload: {
    widgets: GenericWidgetResponseDto[];
    layout: LayoutItemDto[];
  };
}

/**
 * DTO cho session expired event
 */
export class SessionExpiredEventDto extends BaseWebSocketEventDto<{
  sessionId: string;
  reason?: string;
}> {
  @ApiProperty({
    description: 'Session đã expired',
    example: { sessionId: 'session-123-abc', reason: 'timeout' }
  })
  declare payload: {
    sessionId: string;
    reason?: string;
  };
}

/**
 * DTO cho error event
 */
export class ErrorEventDto extends BaseWebSocketEventDto<{
  message: string;
  code?: string;
  details?: any;
}> {
  @ApiProperty({
    description: 'Error information',
    example: { message: 'Widget not found', code: 'WIDGET_NOT_FOUND' }
  })
  declare payload: {
    message: string;
    code?: string;
    details?: any;
  };
}

/**
 * Union type cho tất cả WebSocket events
 */
export type WebSocketEventDto = 
  | AddWidgetEventDto
  | RemoveWidgetEventDto
  | UpdateLayoutEventDto
  | SyncStateEventDto
  | WidgetAddedEventDto
  | WidgetRemovedEventDto
  | LayoutUpdatedEventDto
  | StateSyncedEventDto
  | SessionExpiredEventDto
  | ErrorEventDto;

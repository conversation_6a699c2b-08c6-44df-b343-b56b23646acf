/**
 * Interface định nghĩa cấu trúc của cấu hình <PERSON>ng dụng
 */
export interface AppConfig {
  // Server
  port: number;
  nodeEnv: string;
  apiPrefix: string;
  frontendUrl: string;

  // Database
  database: DatabaseConfig;

  // Storage
  storage: StorageConfig;

  // Authentication
  auth: AuthConfig;

  // S3
  s3: S3Config;

  // Secrect Key Model
  secrectKeyModel: SecretKeyModelConfig;

  // Facebook
  facebook: FacebookConfig;

  // Email
  email: EmailConfig;

  // Redis
  redis: RedisConfig;

  // Livechat
  livechat: LivechatConfig;

  // Referral
  referral: ReferralConfig;

  // Google
  google: GoogleConfig;

  // MCP Headers Encryption
  mcpHeadersEncryption: McpHeadersEncryptionConfig;

  // Encryption
  encryption: EncryptionConfig;

  // Automation Web
  automationWeb: AutomationWebConfig;

  // Webhook
  webhook: WebhookConfig;
}

/**
 * Interface định nghĩa cấu trúc của cấu hình secret key model
 */
export interface SecretKeyModelConfig {
  adminSecretKey: string;
  userSecretKey: string;
}

/**
 * Interface định nghĩa cấu trúc của cấu hình database
 */
export interface DatabaseConfig {
  host: string;
  port: number;
  username: string;
  password: string;
  database: string;
  ssl: boolean;
}

/**
 * Interface định nghĩa cấu trúc của cấu hình storage
 */
export interface StorageConfig {
  cloudflare: {
    region: string;
    accessKey: string;
    secretKey: string;
    endpoint: string;
    bucketName: string;
  };
  cdn: {
    url: string;
    secretKey: string;
  };
}

/**
 * Interface định nghĩa cấu trúc của cấu hình authentication
 */
export interface AuthConfig {
  jwt: {
    secret: string;
    expirationTime: string;
    accessTokenExpirationTime?: string;
    refreshSecret?: string;
    refreshExpirationTime?: string;
    otpExpirationTime?: string;
  };
}

// Redis
export interface RedisConfig {
  url: string;
  password?: string;
}

// Email
export interface EmailConfig {
  apiUrl?: string;
  smtpHost?: string;
  smtpPort?: number;
  smtpUser?: string;
  smtpPass?: string;
}

/**
 * Interface định nghĩa cấu trúc của cấu hình Facebook
 */
export interface FacebookConfig {
  appId: string;
  appSecret: string;
  graphApiVersion: string;
  redirectUri: string;
  webhookVerifyToken: string;
  webhookSecret: string;
}

export interface S3Config {
  s3?: {
    endpoint: string;
    accessKey: string;
    secretAccessKey: string;
    region: string;
    bucketName: string;
  };
  cdn?: {
    url: string;
    secretKey: string;
  };
}

export interface ShipmentConfig {
  ghtk: string;
  ghn: string;
  ahamove: string;
}

/**
 * Interface định nghĩa cấu trúc của cấu hình livechat
 */
export interface LivechatConfig {
  encryptionKey: string;
  livechatSRCUrl: string;
}

/**
 * Interface định nghĩa cấu trúc của cấu hình referral
 */
export interface ReferralConfig {
  baseUrl: string;
  path: string;
}

/**
 * Interface định nghĩa cấu trúc của cấu hình Google
 */
export interface GoogleConfig {
  clientId: string;
  clientSecret: string;
  redirectUri: string;
  appId: string;
  apiKey: string;
}

/**
 * Interface định nghĩa cấu trúc của cấu hình MCP Headers Encryption
 */
export interface McpHeadersEncryptionConfig {
  userEncryptionKey: string;
  adminEncryptionKey: string;
}

/**
 * Interface định nghĩa cấu trúc của cấu hình Encryption
 */
export interface EncryptionConfig {
  secretKey: string;
}

/**
 * Interface định nghĩa cấu trúc của cấu hình Automation Web
 */
export interface AutomationWebConfig {
  apiUrl: string;
  apiKey: string;
  timeout: number;
}

/**
 * Interface định nghĩa cấu trúc của cấu hình Webhook
 */
export interface WebhookConfig {
  baseUrl: string;
}
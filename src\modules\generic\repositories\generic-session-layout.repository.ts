import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { GenericSessionLayout } from '../entities/generic-session-layout.entity';
import { AppException } from '@/common';
import { GENERIC_ERROR_CODES } from '../exceptions';

@Injectable()
export class GenericSessionLayoutRepository extends Repository<GenericSessionLayout> {
  private readonly logger = new Logger(GenericSessionLayoutRepository.name);

  constructor(private dataSource: DataSource) {
    super(GenericSessionLayout, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder c<PERSON> bản cho GenericSessionLayout
   * @returns SelectQueryBuilder<GenericSessionLayout>
   */
  private createBaseQuery(): SelectQueryBuilder<GenericSessionLayout> {
    return this.createQueryBuilder('genericSessionLayout');
  }

  /**
   * Tìm layout theo ID
   * @param id ID của layout
   * @returns Layout nếu tìm thấy
   * @throws AppException nếu không tìm thấy layout
   */
  async findById(id: string): Promise<GenericSessionLayout> {
    try {
      const layout = await this.createBaseQuery()
        .where('genericSessionLayout.id = :id', { id })
        .getOne();

      if (!layout) {
        throw new AppException(
          GENERIC_ERROR_CODES.GENERIC_LAYOUT_NOT_FOUND,
          `Không tìm thấy layout với ID ${id}`,
        );
      }

      return layout;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error finding layout by ID: ${error.message}`, error.stack);
      throw new AppException(
        GENERIC_ERROR_CODES.GENERIC_LAYOUT_NOT_FOUND,
        `Lỗi khi tìm layout với ID ${id}`,
      );
    }
  }

  /**
   * Tìm layout theo sessionId
   * @param sessionId Session ID
   * @returns Layout nếu tìm thấy
   * @throws AppException nếu không tìm thấy layout
   */
  async findBySessionId(sessionId: string): Promise<GenericSessionLayout> {
    try {
      const layout = await this.createBaseQuery()
        .where('genericSessionLayout.sessionId = :sessionId', { sessionId })
        .getOne();

      if (!layout) {
        throw new AppException(
          GENERIC_ERROR_CODES.GENERIC_LAYOUT_NOT_FOUND,
          `Không tìm thấy layout cho session ${sessionId}`,
        );
      }

      return layout;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error finding layout by session ID: ${error.message}`, error.stack);
      throw new AppException(
        GENERIC_ERROR_CODES.GENERIC_LAYOUT_NOT_FOUND,
        `Lỗi khi tìm layout cho session ${sessionId}`,
      );
    }
  }

  /**
   * Tìm layout theo sessionId (optional - không throw error)
   * @param sessionId Session ID
   * @returns Layout nếu tìm thấy, null nếu không tìm thấy
   */
  async findBySessionIdOptional(sessionId: string): Promise<GenericSessionLayout | null> {
    try {
      return await this.createBaseQuery()
        .where('genericSessionLayout.sessionId = :sessionId', { sessionId })
        .getOne();
    } catch (error) {
      this.logger.error(`Error finding layout by session ID: ${error.message}`, error.stack);
      return null;
    }
  }

  /**
   * Tạo hoặc cập nhật layout cho session
   * @param sessionId Session ID
   * @param layoutData Layout data
   * @param breakpoints Breakpoints configuration (optional)
   * @param gridConfig Grid configuration (optional)
   * @param metadata Metadata (optional)
   * @returns Layout đã được tạo/cập nhật
   */
  async upsertLayout(
    sessionId: string,
    layoutData: Array<{
      i: string;
      x: number;
      y: number;
      w: number;
      h: number;
      minW?: number;
      minH?: number;
      maxW?: number;
      maxH?: number;
      static?: boolean;
      isDraggable?: boolean;
      isResizable?: boolean;
    }>,
    breakpoints?: any,
    gridConfig?: any,
    metadata?: Record<string, any>
  ): Promise<GenericSessionLayout> {
    try {
      // Tìm layout hiện tại
      const existingLayout = await this.findBySessionIdOptional(sessionId);

      if (existingLayout) {
        // Cập nhật layout hiện tại
        await this.createQueryBuilder()
          .update(GenericSessionLayout)
          .set({
            layoutData,
            breakpoints: breakpoints || existingLayout.breakpoints,
            gridConfig: gridConfig || existingLayout.gridConfig,
            metadata: metadata || existingLayout.metadata,
            updatedAt: new Date()
          })
          .where('sessionId = :sessionId', { sessionId })
          .execute();

        return await this.findBySessionId(sessionId);
      } else {
        // Tạo layout mới
        const newLayout = this.create({
          sessionId,
          layoutData,
          breakpoints,
          gridConfig,
          metadata
        });

        return await this.save(newLayout);
      }
    } catch (error) {
      this.logger.error(`Error upserting layout: ${error.message}`, error.stack);
      throw new AppException(
        GENERIC_ERROR_CODES.GENERIC_LAYOUT_UPDATE_ERROR,
        `Lỗi khi tạo/cập nhật layout cho session ${sessionId}`,
      );
    }
  }

  /**
   * Cập nhật layout data
   * @param sessionId Session ID
   * @param layoutData Layout data mới
   * @returns Layout đã được cập nhật
   */
  async updateLayoutData(
    sessionId: string,
    layoutData: Array<{
      i: string;
      x: number;
      y: number;
      w: number;
      h: number;
      minW?: number;
      minH?: number;
      maxW?: number;
      maxH?: number;
      static?: boolean;
      isDraggable?: boolean;
      isResizable?: boolean;
    }>
  ): Promise<GenericSessionLayout> {
    try {
      await this.createQueryBuilder()
        .update(GenericSessionLayout)
        .set({
          layoutData,
          updatedAt: new Date()
        })
        .where('sessionId = :sessionId', { sessionId })
        .execute();

      return await this.findBySessionId(sessionId);
    } catch (error) {
      this.logger.error(`Error updating layout data: ${error.message}`, error.stack);
      throw new AppException(
        GENERIC_ERROR_CODES.GENERIC_LAYOUT_UPDATE_ERROR,
        `Lỗi khi cập nhật layout data cho session ${sessionId}`,
      );
    }
  }

  /**
   * Cập nhật grid configuration
   * @param sessionId Session ID
   * @param gridConfig Grid configuration mới
   * @returns Layout đã được cập nhật
   */
  async updateGridConfig(sessionId: string, gridConfig: any): Promise<GenericSessionLayout> {
    try {
      await this.createQueryBuilder()
        .update(GenericSessionLayout)
        .set({
          gridConfig,
          updatedAt: new Date()
        })
        .where('sessionId = :sessionId', { sessionId })
        .execute();

      return await this.findBySessionId(sessionId);
    } catch (error) {
      this.logger.error(`Error updating grid config: ${error.message}`, error.stack);
      throw new AppException(
        GENERIC_ERROR_CODES.GENERIC_LAYOUT_UPDATE_ERROR,
        `Lỗi khi cập nhật grid config cho session ${sessionId}`,
      );
    }
  }

  /**
   * Cập nhật breakpoints configuration
   * @param sessionId Session ID
   * @param breakpoints Breakpoints configuration mới
   * @returns Layout đã được cập nhật
   */
  async updateBreakpoints(sessionId: string, breakpoints: any): Promise<GenericSessionLayout> {
    try {
      await this.createQueryBuilder()
        .update(GenericSessionLayout)
        .set({
          breakpoints,
          updatedAt: new Date()
        })
        .where('sessionId = :sessionId', { sessionId })
        .execute();

      return await this.findBySessionId(sessionId);
    } catch (error) {
      this.logger.error(`Error updating breakpoints: ${error.message}`, error.stack);
      throw new AppException(
        GENERIC_ERROR_CODES.GENERIC_LAYOUT_UPDATE_ERROR,
        `Lỗi khi cập nhật breakpoints cho session ${sessionId}`,
      );
    }
  }

  /**
   * Xóa layout của session
   * @param sessionId Session ID
   * @returns Số lượng layouts đã xóa
   */
  async removeBySessionId(sessionId: string): Promise<number> {
    try {
      const result = await this.createQueryBuilder()
        .delete()
        .from(GenericSessionLayout)
        .where('sessionId = :sessionId', { sessionId })
        .execute();

      return result.affected || 0;
    } catch (error) {
      this.logger.error(`Error removing layout: ${error.message}`, error.stack);
      throw new AppException(
        GENERIC_ERROR_CODES.GENERIC_LAYOUT_DELETE_ERROR,
        `Lỗi khi xóa layout của session ${sessionId}`,
      );
    }
  }

  /**
   * Tạo layout mặc định cho session
   * @param sessionId Session ID
   * @returns Layout mặc định đã được tạo
   */
  async createDefaultLayout(sessionId: string): Promise<GenericSessionLayout> {
    try {
      const defaultLayoutData = []; // Empty layout
      const defaultGridConfig = {
        rowHeight: 60,
        margin: [8, 8],
        containerPadding: [0, 0],
        cols: { lg: 12, md: 10, sm: 6, xs: 4, xxs: 2 },
        compactType: 'vertical',
        preventCollision: false,
        autoSize: true
      };

      return await this.upsertLayout(sessionId, defaultLayoutData, null, defaultGridConfig);
    } catch (error) {
      this.logger.error(`Error creating default layout: ${error.message}`, error.stack);
      throw new AppException(
        GENERIC_ERROR_CODES.GENERIC_LAYOUT_CREATE_ERROR,
        `Lỗi khi tạo layout mặc định cho session ${sessionId}`,
      );
    }
  }
}

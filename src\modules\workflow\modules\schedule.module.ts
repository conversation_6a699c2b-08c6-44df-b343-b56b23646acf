/**
 * @file Schedule Module
 * 
 * NestJS module cho schedule system với delayed jobs
 * Setup Bull queue, services và processors
 * 
 * @version 1.0.0
 * <AUTHOR> Assistant
 */

import { BullModule } from '@nestjs/bull';
import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';

// Entities
import { NodeDefinition } from '../entities/node-definition.entity';
import { Node } from '../entities/node.entity';
import { Workflow } from '../entities/workflow.entity';

// Services
import { CronHandlerService } from '../services/schedule/cron-handler.service';
import { EnhancedDelayedJobManagerService } from '../services/schedule/enhanced-delayed-job-manager.service';
import { NodeUpdateInterceptorService } from '../services/schedule/node-update-interceptor.service';
import { ScheduleDetectionService } from '../services/schedule/schedule-detection.service';

// External dependencies
/**
 * Schedule configuration interface
 */
export interface IScheduleModuleConfig {
    redis: {
        host: string;
        port: number;
        password?: string;
        db?: number;
    };
    queue: {
        concurrency: number;
        maxRetries: number;
        retryDelay: number;
        removeOnComplete: number;
        removeOnFail: number;
    };
}

/**
 * Default schedule configuration
 */
export const DEFAULT_SCHEDULE_CONFIG: IScheduleModuleConfig = {
    redis: {
        host: 'localhost',
        port: 6379,
        db: 0
    },
    queue: {
        concurrency: 5,
        maxRetries: 3,
        retryDelay: 60000,
        removeOnComplete: 10,
        removeOnFail: 5
    }
};

@Module({
    imports: [
        // TypeORM entities
        TypeOrmModule.forFeature([
            Node,
            NodeDefinition,
            Workflow
        ]),

        // Bull queue configuration
        BullModule.forRootAsync({
            imports: [ConfigModule],
            useFactory: async (configService: ConfigService) => {
                const redisHost = configService.get('REDIS_HOST', DEFAULT_SCHEDULE_CONFIG.redis.host);
                const redisPort = configService.get('REDIS_PORT', DEFAULT_SCHEDULE_CONFIG.redis.port);
                const redisPassword = configService.get('REDIS_PASSWORD');
                const redisDb = configService.get('REDIS_DB', DEFAULT_SCHEDULE_CONFIG.redis.db);

                return {
                    redis: {
                        host: redisHost,
                        port: redisPort,
                        password: redisPassword,
                        db: redisDb,
                        maxRetriesPerRequest: 3,
                        retryDelayOnFailover: 100,
                        enableReadyCheck: false,
                        lazyConnect: true
                    },
                    defaultJobOptions: {
                        removeOnComplete: DEFAULT_SCHEDULE_CONFIG.queue.removeOnComplete,
                        removeOnFail: DEFAULT_SCHEDULE_CONFIG.queue.removeOnFail,
                        attempts: DEFAULT_SCHEDULE_CONFIG.queue.maxRetries,
                        backoff: {
                            type: 'exponential',
                            delay: DEFAULT_SCHEDULE_CONFIG.queue.retryDelay
                        }
                    }
                };
            },
            inject: [ConfigService]
        }),

        // Register workflow execution queue
        BullModule.registerQueue({
            name: 'workflow-execution'
        })
    ],

    providers: [
        // Core services
        ScheduleDetectionService,
        CronHandlerService,
        EnhancedDelayedJobManagerService,
        NodeUpdateInterceptorService,
    ],

    exports: [
        // Export services for use in other modules
        ScheduleDetectionService,
        EnhancedDelayedJobManagerService,
        NodeUpdateInterceptorService,
        CronHandlerService
    ]
})
export class ScheduleModule { }

/**
 * Factory để create ScheduleModule với custom config
 */
export class ScheduleModuleFactory {

    /**
     * Create ScheduleModule với custom configuration
     */
    static forRoot(config?: Partial<IScheduleModuleConfig>) {
        const finalConfig = {
            ...DEFAULT_SCHEDULE_CONFIG,
            ...config,
            redis: {
                ...DEFAULT_SCHEDULE_CONFIG.redis,
                ...config?.redis
            },
            queue: {
                ...DEFAULT_SCHEDULE_CONFIG.queue,
                ...config?.queue
            }
        };

        return {
            module: ScheduleModule,
            imports: [
                TypeOrmModule.forFeature([
                    Node,
                    NodeDefinition,
                    Workflow
                ]),

                BullModule.forRoot({
                    redis: finalConfig.redis,
                    defaultJobOptions: {
                        removeOnComplete: finalConfig.queue.removeOnComplete,
                        removeOnFail: finalConfig.queue.removeOnFail,
                        attempts: finalConfig.queue.maxRetries,
                        backoff: {
                            type: 'exponential',
                            delay: finalConfig.queue.retryDelay
                        }
                    }
                }),

                BullModule.registerQueue({
                    name: 'workflow-execution'
                })
            ],
            providers: [
                ScheduleDetectionService,
                CronHandlerService,
                EnhancedDelayedJobManagerService,
                NodeUpdateInterceptorService,
            ],
            exports: [
                ScheduleDetectionService,
                EnhancedDelayedJobManagerService,
                NodeUpdateInterceptorService,
                CronHandlerService
            ]
        };
    }

    /**
     * Create ScheduleModule với async configuration
     */
    static forRootAsync(options: {
        imports?: any[];
        useFactory?: (...args: any[]) => Promise<IScheduleModuleConfig> | IScheduleModuleConfig;
        inject?: any[];
    }) {
        return {
            module: ScheduleModule,
            imports: [
                TypeOrmModule.forFeature([
                    Node,
                    NodeDefinition,
                    Workflow
                ]),

                BullModule.forRootAsync({
                    imports: options.imports,
                    useFactory: async (...args: any[]) => {
                        const config = options.useFactory ? await options.useFactory(...args) : DEFAULT_SCHEDULE_CONFIG;

                        return {
                            redis: config.redis,
                            defaultJobOptions: {
                                removeOnComplete: config.queue.removeOnComplete,
                                removeOnFail: config.queue.removeOnFail,
                                attempts: config.queue.maxRetries,
                                backoff: {
                                    type: 'exponential',
                                    delay: config.queue.retryDelay
                                }
                            }
                        };
                    },
                    inject: options.inject
                }),

                BullModule.registerQueue({
                    name: 'workflow-execution'
                })
            ],
            providers: [
                ScheduleDetectionService,
                CronHandlerService,
                EnhancedDelayedJobManagerService,
                NodeUpdateInterceptorService,
            ],
            exports: [
                ScheduleDetectionService,
                EnhancedDelayedJobManagerService,
                NodeUpdateInterceptorService,
                CronHandlerService
            ]
        };
    }
}

/**
 * Required environment variables
 */
export const REQUIRED_ENV_VARS = [
    // Redis không bắt buộc vì có default values
];

/**
 * Optional environment variables với default values
 */
export const OPTIONAL_ENV_VARS = {
    REDIS_HOST: 'localhost',
    REDIS_PORT: '6379',
    REDIS_PASSWORD: undefined,
    REDIS_DB: '0',
    QUEUE_CONCURRENCY: '5',
    QUEUE_MAX_RETRIES: '3',
    QUEUE_RETRY_DELAY: '60000',
    QUEUE_REMOVE_ON_COMPLETE: '10',
    QUEUE_REMOVE_ON_FAIL: '5'
};

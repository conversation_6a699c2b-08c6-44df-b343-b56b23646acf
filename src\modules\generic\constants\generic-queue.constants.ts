/**
 * Queue constants for Generic Page Dynamic Control
 */

/**
 * Queue name for generic page operations
 */
export const GENERIC_PAGE_QUEUE = 'generic-page';

/**
 * Job types for generic page queue
 */
export enum GenericPageJobType {
  ADD_WIDGET = 'add_widget',
  REMOVE_WIDGET = 'remove_widget',
  UPDATE_WIDGET = 'update_widget',
  UPDATE_LAYOUT = 'update_layout',
  SYNC_STATE = 'sync_state',
  CLEANUP_EXPIRED_SESSIONS = 'cleanup_expired_sessions',
  BROADCAST_TO_SESSION = 'broadcast_to_session',
  BROADCAST_TO_ALL = 'broadcast_to_all',
}

/**
 * Job priorities
 */
export enum GenericPageJobPriority {
  LOW = 1,
  NORMAL = 5,
  HIGH = 10,
  CRITICAL = 20,
}

/**
 * Job options defaults
 */
export const GENERIC_PAGE_JOB_OPTIONS = {
  attempts: 3,
  backoff: {
    type: 'exponential',
    delay: 2000,
  },
  removeOnComplete: 100,
  removeOnFail: 50,
};

/**
 * Job data interfaces
 */
export interface AddWidgetJobData {
  sessionId: string;
  widget: {
    widgetId: string;
    widgetType: string;
    widgetData: Record<string, any>;
    position: {
      x: number;
      y: number;
      w: number;
      h: number;
      minW?: number;
      minH?: number;
      maxW?: number;
      maxH?: number;
    };
    displayOrder?: number;
    isVisible?: boolean;
    metadata?: Record<string, any>;
  };
  triggeredBy?: string; // User ID hoặc system
}

export interface RemoveWidgetJobData {
  sessionId: string;
  widgetId: string;
  triggeredBy?: string;
}

export interface UpdateWidgetJobData {
  sessionId: string;
  widgetId: string;
  updateData: {
    widgetData?: Record<string, any>;
    position?: {
      x: number;
      y: number;
      w: number;
      h: number;
      minW?: number;
      minH?: number;
      maxW?: number;
      maxH?: number;
    };
    displayOrder?: number;
    isVisible?: boolean;
    metadata?: Record<string, any>;
  };
  triggeredBy?: string;
}

export interface UpdateLayoutJobData {
  sessionId: string;
  layoutData: Array<{
    i: string;
    x: number;
    y: number;
    w: number;
    h: number;
    minW?: number;
    minH?: number;
    maxW?: number;
    maxH?: number;
    static?: boolean;
    isDraggable?: boolean;
    isResizable?: boolean;
  }>;
  triggeredBy?: string;
}

export interface SyncStateJobData {
  sessionId: string;
  triggeredBy?: string;
}

export interface CleanupExpiredSessionsJobData {
  maxAge?: number; // Milliseconds
  triggeredBy?: string;
}

export interface BroadcastToSessionJobData {
  sessionId: string;
  event: {
    type: string;
    payload: any;
    timestamp?: string;
  };
  triggeredBy?: string;
}

export interface BroadcastToAllJobData {
  event: {
    type: string;
    payload: any;
    timestamp?: string;
  };
  excludeSessions?: string[]; // Sessions to exclude from broadcast
  triggeredBy?: string;
}

/**
 * Union type for all job data
 */
export type GenericPageJobData = 
  | AddWidgetJobData
  | RemoveWidgetJobData
  | UpdateWidgetJobData
  | UpdateLayoutJobData
  | SyncStateJobData
  | CleanupExpiredSessionsJobData
  | BroadcastToSessionJobData
  | BroadcastToAllJobData;

/**
 * Job result interfaces
 */
export interface GenericPageJobResult {
  success: boolean;
  message: string;
  data?: any;
  error?: string;
  timestamp: string;
}

/**
 * Cron job schedules
 */
export const GENERIC_PAGE_CRON_JOBS = {
  CLEANUP_EXPIRED_SESSIONS: '0 */10 * * * *', // Every 10 minutes
};

/**
 * Queue configuration
 */
export const GENERIC_PAGE_QUEUE_CONFIG = {
  defaultJobOptions: GENERIC_PAGE_JOB_OPTIONS,
  settings: {
    stalledInterval: 30 * 1000, // 30 seconds
    maxStalledCount: 1,
  },
  redis: {
    maxRetriesPerRequest: null,
  },
};

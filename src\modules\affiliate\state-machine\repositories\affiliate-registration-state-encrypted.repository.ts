import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DeepPartial, SaveOptions } from 'typeorm';
import { BaseEncryptedRepository, EncryptionFieldConfig } from '@/shared/repositories/base-encrypted.repository';
import { KeyPairEncryptionService } from '@/shared/services/encryption';
import { S3Service } from '@/shared/services/s3.service';
import { CdnService } from '@/shared/services/cdn.service';
import { AffiliateRegistrationStateEntity } from '../entities/affiliate-registration-state.entity';
import { AffiliateRegistrationState, AffiliateRegistrationContext } from '../affiliate-registration.types';
import { PersonalDataEncryptionService } from '../services/personal-data-encryption.service';
import { RegistrationStatus } from '../dto/admin-action.dto';

/**
 * Repository cho AffiliateRegistrationState với mã hóa ảnh và dữ liệu cá nhân tự động
 * Mã hóa citizenIdFrontUrl, citizenIdBackUrl trong contextData và dữ liệu cá nhân nhạy cảm
 */
@Injectable()
export class AffiliateRegistrationStateEncryptedRepository extends BaseEncryptedRepository<AffiliateRegistrationStateEntity> {
  constructor(
    @InjectRepository(AffiliateRegistrationStateEntity)
    repository: Repository<AffiliateRegistrationStateEntity>,
    keyPairEncryption: KeyPairEncryptionService,
    s3Service: S3Service,
    cdnService: CdnService,
    private readonly personalDataEncryption: PersonalDataEncryptionService,
  ) {
    super(repository, keyPairEncryption, s3Service, cdnService);
  }

  /**
   * Cấu hình mã hóa cho AffiliateRegistrationState
   * Mã hóa các URL ảnh trong contextData
   *
   * NOTE: citizenIdFrontUrl và citizenIdBackUrl được mã hóa riêng bởi SecureCitizenIdUploadService
   * nên không cần auto-encryption ở đây
   */
  protected getEncryptionConfig(): EncryptionFieldConfig[] {
    return [
      // Tắt auto-encryption cho ảnh CCCD vì đã được encrypt riêng
      // {
      //   urlField: 'citizenIdFrontUrl',
      //   publicKeyField: 'citizenIdFrontUrl_public_key',
      //   jsonPath: 'contextData',
      //   encrypt: false, // Đã được encrypt riêng
      // },
      // {
      //   urlField: 'citizenIdBackUrl',
      //   publicKeyField: 'citizenIdBackUrl_public_key',
      //   jsonPath: 'contextData',
      //   encrypt: false, // Đã được encrypt riêng
      // },
      {
        urlField: 'businessLicenseUrl',
        publicKeyField: 'businessLicenseUrl_public_key',
        jsonPath: 'contextData',
        encrypt: true,
      },
      {
        urlField: 'signedContractUrl',
        publicKeyField: 'signedContractUrl_public_key',
        jsonPath: 'contextData',
        encrypt: true,
      },
    ];
  }

  /**
   * Override save method để xử lý mã hóa dữ liệu cá nhân
   */
  async save(entity: DeepPartial<AffiliateRegistrationStateEntity>, options?: SaveOptions, userId?: number): Promise<AffiliateRegistrationStateEntity> {
    // Xử lý mã hóa dữ liệu cá nhân trước khi save
    const processedEntity = await this.processPersonalDataEncryption(entity, userId);

    // Gọi parent save để xử lý mã hóa ảnh
    return await super.save(processedEntity, options, userId);
  }

  /**
   * Override findOne để xử lý giải mã dữ liệu cá nhân
   */
  async findOne(options: any): Promise<AffiliateRegistrationStateEntity | null> {
    const entity = await super.findOne(options);
    if (!entity) return null;

    // Xử lý giải mã dữ liệu cá nhân
    return await this.processPersonalDataDecryption(entity);
  }

  /**
   * Override find để xử lý giải mã dữ liệu cá nhân
   */
  async find(options?: any): Promise<AffiliateRegistrationStateEntity[]> {
    const entities = await super.find(options);

    // Xử lý giải mã dữ liệu cá nhân cho tất cả entities
    return await Promise.all(
      entities.map(entity => this.processPersonalDataDecryption(entity))
    );
  }

  /**
   * Lưu trạng thái với mã hóa ảnh và dữ liệu cá nhân tự động
   */
  async saveState(
    userId: number,
    currentState: AffiliateRegistrationState,
    contextData: AffiliateRegistrationContext,
    completedSteps: AffiliateRegistrationState[] = [],
    progressPercentage: number = 0,
  ): Promise<AffiliateRegistrationStateEntity> {
    const existingState = await this.repository.findOne({
      where: { userId, isActive: true },
    });

    const stateData = {
      userId,
      currentState,
      contextData,
      accountType: contextData.accountType,
      completedSteps,
      progressPercentage,
      isActive: true,
    };

    if (existingState) {
      // Cập nhật trạng thái hiện có với mã hóa tự động
      Object.assign(existingState, stateData);
      return await this.save(existingState, {}, userId);
    } else {
      // Tạo mới với mã hóa tự động
      const newState = this.repository.create(stateData);
      return await this.save(newState, {}, userId);
    }
  }

  /**
   * Lấy trạng thái với giải mã ảnh tự động
   */
  async getState(userId: number): Promise<AffiliateRegistrationStateEntity | null> {
    return await this.findOne({
      where: { userId, isActive: true },
      order: { updatedAt: 'DESC' },
    });
  }

  /**
   * Cập nhật URLs ảnh CCCD trong contextData với mã hóa tự động
   */
  async updateCitizenIdUrls(
    userId: number,
    citizenIdFrontUrl: string,
    citizenIdBackUrl: string
  ): Promise<void> {
    const state = await this.getState(userId);
    if (!state) {
      throw new Error(`State cho user ${userId} không tồn tại`);
    }

    // Cập nhật URLs trong contextData - sẽ được mã hóa tự động
    state.contextData = {
      ...state.contextData,
      citizenIdFrontUrl,
      citizenIdBackUrl,
    };

    await this.save(state, {}, userId);
  }

  /**
   * Cập nhật URL và publicKey cho ảnh CCCD (không auto-encrypt)
   */
  async updateCitizenIdUrlWithKey(
    userId: number,
    type: 'front' | 'back',
    fileKey: string,
    publicKey: string
  ): Promise<void> {
    const state = await this.getState(userId);
    if (!state) {
      throw new Error(`State cho user ${userId} không tồn tại`);
    }

    // Cập nhật URL và publicKey trong contextData (không auto-encrypt)
    const urlField = type === 'front' ? 'citizenIdFrontUrl' : 'citizenIdBackUrl';
    const publicKeyField = type === 'front' ? 'citizenIdFrontUrl_public_key' : 'citizenIdBackUrl_public_key';

    state.contextData = {
      ...state.contextData,
      [urlField]: fileKey,
      [publicKeyField]: publicKey,
    };

    // Lưu trực tiếp không qua auto-encryption
    await this.repository.save(state);
  }

  /**
   * Cập nhật business license URL trong contextData với mã hóa tự động
   */
  async updateBusinessLicenseUrl(
    userId: number,
    businessLicenseUrl: string
  ): Promise<void> {
    const state = await this.getState(userId);
    if (!state) {
      throw new Error(`State cho user ${userId} không tồn tại`);
    }

    state.contextData = {
      ...state.contextData,
      businessLicenseUrl,
    };

    await this.save(state, {}, userId);
  }

  /**
   * Cập nhật signed contract URL trong contextData với mã hóa tự động
   */
  async updateSignedContractUrl(
    userId: number,
    signedContractUrl: string
  ): Promise<void> {
    const state = await this.getState(userId);
    if (!state) {
      throw new Error(`State cho user ${userId} không tồn tại`);
    }

    state.contextData = {
      ...state.contextData,
      signedContractUrl,
    };

    await this.save(state, {}, userId);
  }

  /**
   * Lấy URLs ảnh đã giải mã từ contextData
   */
  async getImageUrls(userId: number): Promise<{
    citizenIdFrontUrl: string | null;
    citizenIdBackUrl: string | null;
    businessLicenseUrl: string | null;
    signedContractUrl: string | null;
  }> {
    const state = await this.getState(userId);
    if (!state || !state.contextData) {
      return {
        citizenIdFrontUrl: null,
        citizenIdBackUrl: null,
        businessLicenseUrl: null,
        signedContractUrl: null,
      };
    }

    return {
      citizenIdFrontUrl: state.contextData.citizenIdFrontUrl || null,
      citizenIdBackUrl: state.contextData.citizenIdBackUrl || null,
      businessLicenseUrl: state.contextData.businessLicenseUrl || null,
      signedContractUrl: state.contextData.signedContractUrl || null,
    };
  }

  /**
   * Xóa trạng thái (đánh dấu không active)
   */
  async clearState(userId: number): Promise<void> {
    await this.repository.update(
      { userId },
      { isActive: false },
    );
  }

  /**
   * Kiểm tra user có trạng thái đang active không
   */
  async hasActiveState(userId: number): Promise<boolean> {
    const count = await this.count({
      where: { userId, isActive: true },
    });
    return count > 0;
  }

  /**
   * Reset hoàn toàn state của user
   */
  async resetState(userId: number): Promise<void> {
    await this.delete({ userId });
  }

  /**
   * Lấy lịch sử các trạng thái của user với giải mã tự động
   */
  async getStateHistory(userId: number): Promise<AffiliateRegistrationStateEntity[]> {
    return await this.find({
      where: { userId },
      order: { createdAt: 'DESC' },
    });
  }

  /**
   * Xử lý mã hóa dữ liệu cá nhân trước khi save
   */
  private async processPersonalDataEncryption(
    entity: DeepPartial<AffiliateRegistrationStateEntity>,
    userId?: number
  ): Promise<DeepPartial<AffiliateRegistrationStateEntity>> {
    if (!entity.contextData || !entity.contextData.userData) {
      return entity;
    }

    const userData = entity.contextData.userData;
    const sensitiveData = {
      citizenId: userData.citizenId,
      citizenIssuePlace: userData.citizenIssuePlace,
      citizenIssueDate: userData.citizenIssueDate,
    };

    // Kiểm tra xem có dữ liệu nhạy cảm cần mã hóa không
    if (!sensitiveData.citizenId && !sensitiveData.citizenIssuePlace && !sensitiveData.citizenIssueDate) {
      return entity;
    }

    try {
      // Sử dụng public key hiện có hoặc tạo mới
      const existingPublicKey = entity.personalDataPublicKey || entity.contextData.personalDataPublicKey;

      // Mã hóa dữ liệu cá nhân
      const encryptionResult = this.personalDataEncryption.encryptPersonalData(
        sensitiveData,
        existingPublicKey
      );

      // Cập nhật entity với dữ liệu đã mã hóa
      const processedEntity = { ...entity };

      // Lưu public key vào entity
      processedEntity.personalDataPublicKey = encryptionResult.publicKey;

      // Cập nhật contextData với public key
      processedEntity.contextData = {
        ...processedEntity.contextData,
        personalDataPublicKey: encryptionResult.publicKey,
        userData: {
          ...userData,
          // Thay thế dữ liệu gốc bằng dữ liệu đã mã hóa
          citizenId: encryptionResult.encryptedData ? '[ENCRYPTED]' : userData.citizenId,
          citizenIssuePlace: encryptionResult.encryptedData ? '[ENCRYPTED]' : userData.citizenIssuePlace,
          citizenIssueDate: encryptionResult.encryptedData ? '[ENCRYPTED]' : userData.citizenIssueDate,
          // Lưu dữ liệu đã mã hóa vào trường đặc biệt
          encryptedPersonalData: encryptionResult.encryptedData,
        } as any, // Type assertion để tránh lỗi TypeScript
      };

      this.logger.log(`Mã hóa dữ liệu cá nhân thành công cho user ${userId}`);
      return processedEntity;
    } catch (error) {
      this.logger.error(`Lỗi khi mã hóa dữ liệu cá nhân cho user ${userId}: ${error.message}`);
      return entity;
    }
  }

  /**
   * Xử lý giải mã dữ liệu cá nhân sau khi load
   */
  private async processPersonalDataDecryption(
    entity: AffiliateRegistrationStateEntity
  ): Promise<AffiliateRegistrationStateEntity> {
    if (!entity.contextData || !entity.contextData.userData || !entity.personalDataPublicKey) {
      return entity;
    }

    const userData = entity.contextData.userData;
    const encryptedData = (userData as any).encryptedPersonalData;

    // Kiểm tra xem có dữ liệu đã mã hóa không
    if (!encryptedData ||
        userData.citizenId !== '[ENCRYPTED]' ||
        userData.citizenIssuePlace !== '[ENCRYPTED]' ||
        userData.citizenIssueDate !== '[ENCRYPTED]') {
      return entity;
    }

    try {
      // Giải mã dữ liệu cá nhân
      const decryptionResult = this.personalDataEncryption.decryptPersonalData(
        encryptedData,
        entity.personalDataPublicKey
      );

      if (!decryptionResult.success) {
        this.logger.warn(`Không thể giải mã dữ liệu cá nhân cho user ${entity.userId}`);
        return entity;
      }

      // Cập nhật entity với dữ liệu đã giải mã
      const processedEntity = { ...entity };
      processedEntity.contextData = {
        ...entity.contextData,
        userData: {
          ...userData,
          citizenId: decryptionResult.data.citizenId || '',
          citizenIssuePlace: decryptionResult.data.citizenIssuePlace || '',
          citizenIssueDate: decryptionResult.data.citizenIssueDate || '',
          // Xóa dữ liệu mã hóa khỏi response
          encryptedPersonalData: undefined,
        } as any, // Type assertion để tránh lỗi TypeScript
      };

      this.logger.log(`Giải mã dữ liệu cá nhân thành công cho user ${entity.userId}`);
      return processedEntity;
    } catch (error) {
      this.logger.error(`Lỗi khi giải mã dữ liệu cá nhân cho user ${entity.userId}: ${error.message}`);
      return entity;
    }
  }

  /**
   * Lấy danh sách đơn đăng ký với pagination cho admin
   * Sử dụng raw query để tránh vấn đề với mã hóa trong admin view
   */
  async getPendingApprovalsWithPagination(
    page: number,
    limit: number,
    status?: string,
    accountType?: string,
    search?: string,
    sortBy?: string,
    sortDirection?: 'ASC' | 'DESC'
  ): Promise<{ items: any[], total: number }> {
    // Sử dụng raw query để tránh vấn đề N+1 và có control hoàn toàn
    const validStates = [
      RegistrationStatus.PENDING_APPROVAL,
      RegistrationStatus.APPROVED,
      RegistrationStatus.REJECTED
    ];

    // Build WHERE conditions
    const whereConditions = ['state.is_active = true'];
    const parameters: any[] = [];
    let paramIndex = 1;

    // Status filter
    if (status && status !== RegistrationStatus.ALL) {
      whereConditions.push(`state.current_state = $${paramIndex}`);
      parameters.push(status);
      paramIndex++;
    } else {
      whereConditions.push(`state.current_state = ANY($${paramIndex})`);
      parameters.push(validStates);
      paramIndex++;
    }

    // Account type filter
    if (accountType) {
      whereConditions.push(`state.account_type = $${paramIndex}`);
      parameters.push(accountType);
      paramIndex++;
    }

    // Search filter
    if (search) {
      whereConditions.push(`(
        LOWER(u.full_name) LIKE LOWER($${paramIndex}) OR
        LOWER(u.email) LIKE LOWER($${paramIndex}) OR
        LOWER(u.phone_number) LIKE LOWER($${paramIndex}) OR
        LOWER(state.context_data::text) LIKE LOWER($${paramIndex})
      )`);
      parameters.push(`%${search}%`);
      paramIndex++;
    }

    // Build ORDER BY
    let orderBy = 'state.created_at DESC';
    if (sortBy === 'userName' || sortBy === 'fullName') {
      orderBy = `u.full_name ${sortDirection || 'DESC'}`;
    } else if (sortBy === 'email') {
      orderBy = `u.email ${sortDirection || 'DESC'}`;
    } else if (sortBy === 'phoneNumber') {
      orderBy = `u.phone_number ${sortDirection || 'DESC'}`;
    } else if (sortBy) {
      // Convert camelCase to snake_case for database columns
      const dbColumnName = sortBy === 'createdAt' ? 'created_at' :
                          sortBy === 'updatedAt' ? 'updated_at' :
                          sortBy === 'currentState' ? 'current_state' :
                          sortBy === 'accountType' ? 'account_type' :
                          sortBy === 'progressPercentage' ? 'progress_percentage' :
                          sortBy;
      orderBy = `state.${dbColumnName} ${sortDirection || 'DESC'}`;
    }

    // Count query
    const countQuery = `
      SELECT COUNT(*) as total
      FROM affiliate_registration_states state
      LEFT JOIN users u ON u.id = state.user_id
      WHERE ${whereConditions.join(' AND ')}
    `;

    // Data query
    const dataQuery = `
      SELECT
        state.id,
        state.user_id,
        state.current_state,
        state.context_data,
        state.account_type,
        state.is_active,
        state.completed_steps,
        state.progress_percentage,
        state.personal_data_public_key,
        state.created_at,
        state.updated_at,
        u.full_name as user_full_name,
        u.email as user_email,
        u.phone_number as user_phone_number
      FROM affiliate_registration_states state
      LEFT JOIN users u ON u.id = state.user_id
      WHERE ${whereConditions.join(' AND ')}
      ORDER BY ${orderBy}
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `;

    // Execute queries
    const [countResult] = await this.repository.query(countQuery, parameters);
    const total = parseInt(countResult.total);

    const offset = (page - 1) * limit;
    const items = await this.repository.query(dataQuery, [...parameters, limit, offset]);

    return { items, total };
  }

  /**
   * Lấy danh sách đăng ký của user với pagination
   * Sử dụng raw query để tránh vấn đề với mã hóa trong user view
   */
  async getUserRegistrationsWithPagination(
    userId: number,
    page: number,
    limit: number,
    status?: string,
    accountType?: string,
    search?: string,
    sortBy?: string,
    sortDirection?: 'ASC' | 'DESC'
  ): Promise<{ items: any[], total: number }> {
    // Validate input parameters
    const validPage = Math.max(1, page || 1);
    const validLimit = Math.min(Math.max(1, limit || 10), 100);
    const validSortDirection = (sortDirection === 'ASC' || sortDirection === 'DESC') ? sortDirection : 'DESC';

    // Base where conditions
    const whereConditions = ['state.user_id = $1', 'state.is_active = true'];
    const parameters: any[] = [userId];
    let paramIndex = 2;

    // Filter by status
    if (status && status !== 'ALL') {
      whereConditions.push(`state.current_state = $${paramIndex}`);
      parameters.push(status);
      paramIndex++;
    }

    // Filter by account type
    if (accountType) {
      whereConditions.push(`state.account_type = $${paramIndex}`);
      parameters.push(accountType);
      paramIndex++;
    }

    // Search functionality
    if (search && search.trim()) {
      const searchTerm = `%${search.trim()}%`;
      whereConditions.push(`(
        u.full_name ILIKE $${paramIndex} OR
        u.email ILIKE $${paramIndex} OR
        u.phone_number ILIKE $${paramIndex} OR
        state.context_data::text ILIKE $${paramIndex}
      )`);
      parameters.push(searchTerm);
      paramIndex++;
    }

    // Sorting
    let orderBy = 'state.created_at DESC';
    if (sortBy) {
      const sortField = sortBy === 'createdAt' ? 'state.created_at' :
                       sortBy === 'updatedAt' ? 'state.updated_at' :
                       sortBy === 'currentState' ? 'state.current_state' :
                       sortBy === 'accountType' ? 'state.account_type' :
                       'state.created_at';
      orderBy = `${sortField} ${validSortDirection}`;
    }

    // Count query
    const countQuery = `
      SELECT COUNT(*) as total
      FROM affiliate_registration_states state
      LEFT JOIN users u ON u.id = state.user_id
      WHERE ${whereConditions.join(' AND ')}
    `;

    // Data query
    const dataQuery = `
      SELECT
        state.id,
        state.user_id,
        state.current_state,
        state.context_data,
        state.account_type,
        state.is_active,
        state.completed_steps,
        state.progress_percentage,
        state.personal_data_public_key,
        state.created_at,
        state.updated_at,
        u.full_name as user_full_name,
        u.email as user_email,
        u.phone_number as user_phone_number
      FROM affiliate_registration_states state
      LEFT JOIN users u ON u.id = state.user_id
      WHERE ${whereConditions.join(' AND ')}
      ORDER BY ${orderBy}
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `;

    // Execute queries
    const [countResult] = await this.repository.query(countQuery, parameters);
    const total = parseInt(countResult.total);

    const offset = (validPage - 1) * validLimit;
    const items = await this.repository.query(dataQuery, [...parameters, validLimit, offset]);

    return { items, total };
  }
}

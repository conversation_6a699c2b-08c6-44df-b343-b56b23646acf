import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsOptional, IsArray, IsEnum, IsInt, IsBoolean, Min, Max, ValidateNested, IsUUID, Matches } from 'class-validator';
import { Type } from 'class-transformer';
import { QueryDto } from '@common/dto';
import { SeedingGroupStatus, SeedingAccountStatus } from '../../entities/seeding-group.entity';

/**
 * DTO cho query Seeding Groups
 */
export class SeedingGroupQueryDto extends QueryDto {
  @ApiPropertyOptional({
    description: 'Tìm kiếm theo tên',
    example: 'Seeding Group 1',
  })
  @IsOptional()
  @IsString()
  declare search?: string;

  @ApiPropertyOptional({
    description: 'Lọc theo trạng thái',
    enum: SeedingGroupStatus,
    example: SeedingGroupStatus.ACTIVE,
  })
  @IsOptional()
  @IsEnum(SeedingGroupStatus)
  status?: SeedingGroupStatus;

  @ApiPropertyOptional({
    description: '<PERSON>ọ<PERSON> theo OA Account ID',
    example: 'oa-account-123',
  })
  @IsOptional()
  @IsString()
  oaAccountId?: string;

  @ApiPropertyOptional({
    description: 'Lọc theo Group ID',
    example: 'group-123',
  })
  @IsOptional()
  @IsString()
  groupId?: string;
}

/**
 * DTO cho tạo Seeding Group Account
 */
export class CreateSeedingGroupAccountDto {
  @ApiProperty({
    description: 'ID tài khoản cá nhân',
    example: 'personal-account-123',
  })
  @IsString()
  personalAccountId: string;

  @ApiProperty({
    description: 'ID agent',
    example: 'agent-123',
  })
  @IsString()
  agentId: string;
}

/**
 * DTO cho tạo Seeding Group
 */
export class CreateSeedingGroupDto {
  @ApiProperty({
    description: 'Tên seeding group',
    example: 'Seeding Group 1',
    maxLength: 255,
  })
  @IsString()
  name: string;

  @ApiPropertyOptional({
    description: 'Mô tả seeding group',
    example: 'Mô tả cho seeding group',
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: 'ID tài khoản OA',
    example: 'oa-account-123',
  })
  @IsString()
  oaAccountId: string;

  @ApiProperty({
    description: 'ID agent cho OA',
    example: 'agent-123',
  })
  @IsString()
  oaAgentId: string;

  @ApiProperty({
    description: 'ID nhóm Zalo',
    example: 'group-123',
  })
  @IsString()
  groupId: string;

  @ApiProperty({
    description: 'Danh sách tài khoản cá nhân',
    type: [CreateSeedingGroupAccountDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateSeedingGroupAccountDto)
  accounts: CreateSeedingGroupAccountDto[];

  // Cấu hình thời gian hoạt động
  @ApiProperty({
    description: 'Giờ bắt đầu (HH:mm)',
    example: '09:00',
    pattern: '^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$',
  })
  @IsString()
  @Matches(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, {
    message: 'startTime phải có định dạng HH:mm',
  })
  startTime: string;

  @ApiProperty({
    description: 'Giờ kết thúc (HH:mm)',
    example: '18:00',
    pattern: '^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$',
  })
  @IsString()
  @Matches(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, {
    message: 'endTime phải có định dạng HH:mm',
  })
  endTime: string;

  @ApiPropertyOptional({
    description: 'Ngày kết thúc (YYYY-MM-DD)',
    example: '2024-12-31',
    pattern: '^\\d{4}-\\d{2}-\\d{2}$',
  })
  @IsOptional()
  @IsString()
  @Matches(/^\d{4}-\d{2}-\d{2}$/, {
    message: 'endDate phải có định dạng YYYY-MM-DD',
  })
  endDate?: string;

  // Cấu hình tần xuất hoạt động
  @ApiProperty({
    description: 'Khoảng cách giữa các tin nhắn (phút)',
    example: 30,
    minimum: 5,
    maximum: 120,
  })
  @IsInt()
  @Min(5)
  @Max(120)
  intervalMinutes: number;

  @ApiProperty({
    description: 'Số tin nhắn tối đa mỗi ngày',
    example: 20,
    minimum: 1,
    maximum: 100,
  })
  @IsInt()
  @Min(1)
  @Max(100)
  maxPerDay: number;

  @ApiProperty({
    description: 'Có randomize thời gian để tránh phát hiện bot',
    example: true,
  })
  @IsBoolean()
  randomize: boolean;
}

/**
 * DTO cho cập nhật Seeding Group Account
 */
export class UpdateSeedingGroupAccountDto {
  @ApiPropertyOptional({
    description: 'ID tài khoản cá nhân',
    example: 'personal-account-123',
  })
  @IsOptional()
  @IsString()
  personalAccountId?: string;

  @ApiPropertyOptional({
    description: 'ID agent',
    example: 'agent-123',
  })
  @IsOptional()
  @IsString()
  agentId?: string;

  @ApiPropertyOptional({
    description: 'Trạng thái tài khoản',
    enum: SeedingAccountStatus,
    example: SeedingAccountStatus.ACTIVE,
  })
  @IsOptional()
  @IsEnum(SeedingAccountStatus)
  status?: SeedingAccountStatus;
}

/**
 * DTO cho cập nhật Seeding Group
 */
export class UpdateSeedingGroupDto {
  @ApiPropertyOptional({
    description: 'Tên seeding group',
    example: 'Seeding Group 1 Updated',
    maxLength: 255,
  })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiPropertyOptional({
    description: 'Mô tả seeding group',
    example: 'Mô tả cập nhật',
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({
    description: 'Trạng thái seeding group',
    enum: SeedingGroupStatus,
    example: SeedingGroupStatus.ACTIVE,
  })
  @IsOptional()
  @IsEnum(SeedingGroupStatus)
  status?: SeedingGroupStatus;

  @ApiPropertyOptional({
    description: 'Danh sách tài khoản cập nhật',
    type: [UpdateSeedingGroupAccountDto],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => UpdateSeedingGroupAccountDto)
  accounts?: UpdateSeedingGroupAccountDto[];

  // Cấu hình thời gian hoạt động
  @ApiPropertyOptional({
    description: 'Giờ bắt đầu (HH:mm)',
    example: '09:00',
    pattern: '^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$',
  })
  @IsOptional()
  @IsString()
  @Matches(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, {
    message: 'startTime phải có định dạng HH:mm',
  })
  startTime?: string;

  @ApiPropertyOptional({
    description: 'Giờ kết thúc (HH:mm)',
    example: '18:00',
    pattern: '^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$',
  })
  @IsOptional()
  @IsString()
  @Matches(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, {
    message: 'endTime phải có định dạng HH:mm',
  })
  endTime?: string;

  @ApiPropertyOptional({
    description: 'Ngày kết thúc (YYYY-MM-DD)',
    example: '2024-12-31',
    pattern: '^\\d{4}-\\d{2}-\\d{2}$',
  })
  @IsOptional()
  @IsString()
  @Matches(/^\d{4}-\d{2}-\d{2}$/, {
    message: 'endDate phải có định dạng YYYY-MM-DD',
  })
  endDate?: string;

  // Cấu hình tần xuất hoạt động
  @ApiPropertyOptional({
    description: 'Khoảng cách giữa các tin nhắn (phút)',
    example: 30,
    minimum: 5,
    maximum: 120,
  })
  @IsOptional()
  @IsInt()
  @Min(5)
  @Max(120)
  intervalMinutes?: number;

  @ApiPropertyOptional({
    description: 'Số tin nhắn tối đa mỗi ngày',
    example: 20,
    minimum: 1,
    maximum: 100,
  })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Max(100)
  maxPerDay?: number;

  @ApiPropertyOptional({
    description: 'Có randomize thời gian để tránh phát hiện bot',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  randomize?: boolean;
}

/**
 * DTO cho bulk delete
 */
export class BulkDeleteSeedingGroupDto {
  @ApiProperty({
    description: 'Danh sách ID cần xóa',
    example: ['uuid-1', 'uuid-2'],
  })
  @IsArray()
  @IsUUID('4', { each: true })
  ids: string[];
}

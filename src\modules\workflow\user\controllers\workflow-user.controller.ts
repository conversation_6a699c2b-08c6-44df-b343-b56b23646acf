import { SubscriptionGuard } from '@/modules/subscription/guards/subscription.guard';
import { ApiMultipleErrorResponses } from '@common/decorators/api-error-response.decorator';
import { ApiErrorResponseDto } from '@common/dto/api-error-response.dto';
import { ApiResponseDto, PaginatedResult } from '@common/response/api-response-dto';
import { SWAGGER_API_TAGS } from '@common/swagger';
import { CurrentUser } from '@modules/auth/decorators/current-user.decorator';
import { JwtUserGuard } from '@modules/auth/guards';
import {
  Body,
  Controller,
  Delete,
  Get,
  HttpStatus,
  Param,
  ParseUUIDPipe,
  Patch,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiBody,
  ApiExtraModels,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import {
  BulkDeleteWorkflowDto,
  BulkDeleteWorkflowResponseDto,
  CreateWorkflowDto,
  QueryWorkflowDto,
  UpdateWorkflowDto,
  WorkflowResponseDto,
} from '../../dto';
import { WorkflowDetailResponseDto } from '../../dto/response/workflow-detail-response.dto';
import { WORKFLOW_ERROR_CODES } from '../../exceptions';
import { WorkflowUserService } from '../services';

/**
 * Controller xử lý các API liên quan đến workflow cho user
 */
@ApiTags(SWAGGER_API_TAGS.USER_WORKFLOW)
@ApiExtraModels(
  ApiResponseDto,
  WorkflowResponseDto,
  CreateWorkflowDto,
  UpdateWorkflowDto,
  QueryWorkflowDto,
  BulkDeleteWorkflowDto,
  BulkDeleteWorkflowResponseDto,
  PaginatedResult,
  ApiErrorResponseDto,
)
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtUserGuard, SubscriptionGuard)
@Controller('workflow')
export class WorkflowUserController {
  constructor(
    private readonly workflowUserService: WorkflowUserService,
  ) { }

  /**
   * Lấy danh sách workflows của user
   */
  @Get()
  @ApiOperation({ summary: 'Lấy danh sách workflows của user' })
  @ApiResponse({
    status: 200,
    description: 'Danh sách workflows',
    schema: ApiResponseDto.getPaginatedSchema(WorkflowResponseDto),
  })
  @ApiMultipleErrorResponses(
    HttpStatus.INTERNAL_SERVER_ERROR,
    [
      WORKFLOW_ERROR_CODES.WORKFLOW_FETCH_ERROR,
      WORKFLOW_ERROR_CODES.WORKFLOW_GENERAL_ERROR
    ]
  )
  async getWorkflows(
    @CurrentUser('id') userId: number,
    @Query() queryDto: QueryWorkflowDto,
  ): Promise<ApiResponseDto<PaginatedResult<WorkflowResponseDto>>> {
    const result = await this.workflowUserService.getWorkflows(userId, queryDto);
    return ApiResponseDto.paginated(result, 'Lấy danh sách workflows thành công');
  }

  /**
   * Lấy workflow theo ID với đầy đủ thông tin (nodes + connections)
   */
  @Get(':id')
  @ApiOperation({
    summary: 'Lấy workflow theo ID với đầy đủ thông tin',
    description: 'Trả về thông tin workflow bao gồm danh sách nodes và connections'
  })
  @ApiParam({
    name: 'id',
    description: 'ID của workflow',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @ApiResponse({
    status: 200,
    description: 'Thông tin workflow với nodes và connections',
    schema: ApiResponseDto.getSchema(WorkflowDetailResponseDto),
  })
  @ApiMultipleErrorResponses(
    HttpStatus.NOT_FOUND,
    [WORKFLOW_ERROR_CODES.WORKFLOW_NOT_FOUND]
  )
  @ApiMultipleErrorResponses(
    HttpStatus.FORBIDDEN,
    [WORKFLOW_ERROR_CODES.WORKFLOW_OWNERSHIP_ERROR]
  )
  @ApiMultipleErrorResponses(
    HttpStatus.INTERNAL_SERVER_ERROR,
    [WORKFLOW_ERROR_CODES.WORKFLOW_FETCH_ERROR]
  )
  async getWorkflowById(
    @CurrentUser('id') userId: number,
    @Param('id', ParseUUIDPipe) workflowId: string,
  ): Promise<ApiResponseDto<WorkflowDetailResponseDto>> {
    const result = await this.workflowUserService.getWorkflowById(userId, workflowId);
    return ApiResponseDto.success(result, 'Lấy thông tin workflow với nodes và connections thành công');
  }

  /**
   * Tạo workflow mới
   */
  @Post()
  @ApiOperation({ summary: 'Tạo workflow mới' })
  @ApiBody({ type: CreateWorkflowDto })
  @ApiResponse({
    status: 201,
    description: 'Workflow đã được tạo',
    schema: ApiResponseDto.getSchema(WorkflowResponseDto),
  })
  @ApiMultipleErrorResponses(
    HttpStatus.BAD_REQUEST,
    [
      WORKFLOW_ERROR_CODES.WORKFLOW_INVALID_NAME,
      WORKFLOW_ERROR_CODES.WORKFLOW_INVALID_SETTINGS,
      WORKFLOW_ERROR_CODES.WORKFLOW_INVALID_INPUT
    ]
  )
  @ApiMultipleErrorResponses(
    HttpStatus.INTERNAL_SERVER_ERROR,
    [WORKFLOW_ERROR_CODES.WORKFLOW_CREATION_ERROR]
  )
  async createWorkflow(
    @CurrentUser('id') userId: number,
    @Body() createDto: CreateWorkflowDto,
  ): Promise<ApiResponseDto<WorkflowResponseDto>> {
    const result = await this.workflowUserService.createWorkflow(userId, createDto);
    return ApiResponseDto.created(result, 'Tạo workflow thành công');
  }

  /**
   * Cập nhật workflow
   */
  @Patch(':id')
  @ApiOperation({ summary: 'Cập nhật workflow' })
  @ApiParam({
    name: 'id',
    description: 'ID của workflow',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @ApiBody({ type: UpdateWorkflowDto })
  @ApiResponse({
    status: 200,
    description: 'Workflow đã được cập nhật',
    schema: ApiResponseDto.getSchema(WorkflowResponseDto),
  })
  @ApiMultipleErrorResponses(
    HttpStatus.NOT_FOUND,
    [WORKFLOW_ERROR_CODES.WORKFLOW_NOT_FOUND]
  )
  @ApiMultipleErrorResponses(
    HttpStatus.FORBIDDEN,
    [WORKFLOW_ERROR_CODES.WORKFLOW_OWNERSHIP_ERROR]
  )
  @ApiMultipleErrorResponses(
    HttpStatus.BAD_REQUEST,
    [
      WORKFLOW_ERROR_CODES.WORKFLOW_INVALID_NAME,
      WORKFLOW_ERROR_CODES.WORKFLOW_INVALID_SETTINGS,
      WORKFLOW_ERROR_CODES.WORKFLOW_INVALID_INPUT
    ]
  )
  @ApiMultipleErrorResponses(
    HttpStatus.INTERNAL_SERVER_ERROR,
    [WORKFLOW_ERROR_CODES.WORKFLOW_UPDATE_ERROR]
  )
  async updateWorkflow(
    @CurrentUser('id') userId: number,
    @Param('id', ParseUUIDPipe) workflowId: string,
    @Body() updateDto: UpdateWorkflowDto,
  ): Promise<ApiResponseDto<WorkflowResponseDto>> {
    const result = await this.workflowUserService.updateWorkflow(userId, workflowId, updateDto);
    return ApiResponseDto.success(result, 'Cập nhật workflow thành công');
  }

  /**
   * Bulk delete workflows
   */
  @Delete()
  @ApiOperation({ summary: 'Xóa nhiều workflows' })
  @ApiBody({ type: BulkDeleteWorkflowDto })
  @ApiResponse({
    status: 200,
    description: 'Kết quả xóa workflows',
    schema: ApiResponseDto.getSchema(BulkDeleteWorkflowResponseDto),
  })
  @ApiMultipleErrorResponses(
    HttpStatus.BAD_REQUEST,
    [
      WORKFLOW_ERROR_CODES.WORKFLOW_EMPTY_IDS_LIST,
      WORKFLOW_ERROR_CODES.WORKFLOW_INVALID_ID,
      WORKFLOW_ERROR_CODES.WORKFLOW_INVALID_INPUT
    ]
  )
  @ApiMultipleErrorResponses(
    HttpStatus.FORBIDDEN,
    [WORKFLOW_ERROR_CODES.WORKFLOW_OWNERSHIP_ERROR]
  )
  @ApiMultipleErrorResponses(
    HttpStatus.INTERNAL_SERVER_ERROR,
    [WORKFLOW_ERROR_CODES.WORKFLOW_BULK_DELETE_ERROR]
  )
  async bulkDeleteWorkflows(
    @CurrentUser('id') userId: number,
    @Body() bulkDeleteDto: BulkDeleteWorkflowDto,
  ): Promise<ApiResponseDto<BulkDeleteWorkflowResponseDto>> {
    const result = await this.workflowUserService.bulkDeleteWorkflows(userId, bulkDeleteDto);
    return ApiResponseDto.success(result, 'Xóa workflows thành công');
  }
}

import { Injectable, Logger } from '@nestjs/common';
import { InjectQueue } from '@nestjs/bullmq';
import { Queue } from 'bullmq';
import {
  GENERIC_PAGE_QUEUE,
  GenericPageJobType,
  GenericPageJobPriority,
  GENERIC_PAGE_JOB_OPTIONS,
  AddWidgetJobData,
  RemoveWidgetJobData,
  UpdateWidgetJobData,
  UpdateLayoutJobData,
  SyncStateJobData,
  CleanupExpiredSessionsJobData,
  BroadcastToSessionJobData,
  BroadcastToAllJobData,
} from '../constants/generic-queue.constants';

/**
 * Service để quản lý Generic Page queue operations
 */
@Injectable()
export class GenericQueueService {
  private readonly logger = new Logger(GenericQueueService.name);

  constructor(
    @InjectQueue(GENERIC_PAGE_QUEUE) private readonly genericPageQueue: Queue,
  ) {}

  /**
   * Thêm job add widget vào queue
   */
  async addWidgetJob(
    sessionId: string,
    widget: AddWidgetJobData['widget'],
    options?: {
      priority?: GenericPageJobPriority;
      delay?: number;
      triggeredBy?: string;
    }
  ): Promise<void> {
    try {
      const jobData: AddWidgetJobData = {
        sessionId,
        widget,
        triggeredBy: options?.triggeredBy,
      };

      await this.genericPageQueue.add(
        GenericPageJobType.ADD_WIDGET,
        jobData,
        {
          ...GENERIC_PAGE_JOB_OPTIONS,
          priority: options?.priority || GenericPageJobPriority.NORMAL,
          delay: options?.delay,
        }
      );

      this.logger.log(`Added widget job for session ${sessionId}, widget ${widget.widgetId}`);
    } catch (error) {
      this.logger.error(`Failed to add widget job: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Thêm job remove widget vào queue
   */
  async removeWidgetJob(
    sessionId: string,
    widgetId: string,
    options?: {
      priority?: GenericPageJobPriority;
      delay?: number;
      triggeredBy?: string;
    }
  ): Promise<void> {
    try {
      const jobData: RemoveWidgetJobData = {
        sessionId,
        widgetId,
        triggeredBy: options?.triggeredBy,
      };

      await this.genericPageQueue.add(
        GenericPageJobType.REMOVE_WIDGET,
        jobData,
        {
          ...GENERIC_PAGE_JOB_OPTIONS,
          priority: options?.priority || GenericPageJobPriority.NORMAL,
          delay: options?.delay,
        }
      );

      this.logger.log(`Added remove widget job for session ${sessionId}, widget ${widgetId}`);
    } catch (error) {
      this.logger.error(`Failed to add remove widget job: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Thêm job update widget vào queue
   */
  async updateWidgetJob(
    sessionId: string,
    widgetId: string,
    updateData: UpdateWidgetJobData['updateData'],
    options?: {
      priority?: GenericPageJobPriority;
      delay?: number;
      triggeredBy?: string;
    }
  ): Promise<void> {
    try {
      const jobData: UpdateWidgetJobData = {
        sessionId,
        widgetId,
        updateData,
        triggeredBy: options?.triggeredBy,
      };

      await this.genericPageQueue.add(
        GenericPageJobType.UPDATE_WIDGET,
        jobData,
        {
          ...GENERIC_PAGE_JOB_OPTIONS,
          priority: options?.priority || GenericPageJobPriority.NORMAL,
          delay: options?.delay,
        }
      );

      this.logger.log(`Added update widget job for session ${sessionId}, widget ${widgetId}`);
    } catch (error) {
      this.logger.error(`Failed to add update widget job: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Thêm job update layout vào queue
   */
  async updateLayoutJob(
    sessionId: string,
    layoutData: UpdateLayoutJobData['layoutData'],
    options?: {
      priority?: GenericPageJobPriority;
      delay?: number;
      triggeredBy?: string;
    }
  ): Promise<void> {
    try {
      const jobData: UpdateLayoutJobData = {
        sessionId,
        layoutData,
        triggeredBy: options?.triggeredBy,
      };

      await this.genericPageQueue.add(
        GenericPageJobType.UPDATE_LAYOUT,
        jobData,
        {
          ...GENERIC_PAGE_JOB_OPTIONS,
          priority: options?.priority || GenericPageJobPriority.NORMAL,
          delay: options?.delay,
        }
      );

      this.logger.log(`Added update layout job for session ${sessionId}`);
    } catch (error) {
      this.logger.error(`Failed to add update layout job: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Thêm job sync state vào queue
   */
  async syncStateJob(
    sessionId: string,
    options?: {
      priority?: GenericPageJobPriority;
      delay?: number;
      triggeredBy?: string;
    }
  ): Promise<void> {
    try {
      const jobData: SyncStateJobData = {
        sessionId,
        triggeredBy: options?.triggeredBy,
      };

      await this.genericPageQueue.add(
        GenericPageJobType.SYNC_STATE,
        jobData,
        {
          ...GENERIC_PAGE_JOB_OPTIONS,
          priority: options?.priority || GenericPageJobPriority.HIGH,
          delay: options?.delay,
        }
      );

      this.logger.log(`Added sync state job for session ${sessionId}`);
    } catch (error) {
      this.logger.error(`Failed to add sync state job: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Thêm job cleanup expired sessions vào queue
   */
  async cleanupExpiredSessionsJob(
    options?: {
      priority?: GenericPageJobPriority;
      delay?: number;
      maxAge?: number;
      triggeredBy?: string;
    }
  ): Promise<void> {
    try {
      const jobData: CleanupExpiredSessionsJobData = {
        maxAge: options?.maxAge,
        triggeredBy: options?.triggeredBy,
      };

      await this.genericPageQueue.add(
        GenericPageJobType.CLEANUP_EXPIRED_SESSIONS,
        jobData,
        {
          ...GENERIC_PAGE_JOB_OPTIONS,
          priority: options?.priority || GenericPageJobPriority.LOW,
          delay: options?.delay,
        }
      );

      this.logger.log('Added cleanup expired sessions job');
    } catch (error) {
      this.logger.error(`Failed to add cleanup expired sessions job: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Thêm job broadcast to session vào queue
   */
  async broadcastToSessionJob(
    sessionId: string,
    event: BroadcastToSessionJobData['event'],
    options?: {
      priority?: GenericPageJobPriority;
      delay?: number;
      triggeredBy?: string;
    }
  ): Promise<void> {
    try {
      const jobData: BroadcastToSessionJobData = {
        sessionId,
        event,
        triggeredBy: options?.triggeredBy,
      };

      await this.genericPageQueue.add(
        GenericPageJobType.BROADCAST_TO_SESSION,
        jobData,
        {
          ...GENERIC_PAGE_JOB_OPTIONS,
          priority: options?.priority || GenericPageJobPriority.HIGH,
          delay: options?.delay,
        }
      );

      this.logger.log(`Added broadcast to session job for session ${sessionId}`);
    } catch (error) {
      this.logger.error(`Failed to add broadcast to session job: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Thêm job broadcast to all vào queue
   */
  async broadcastToAllJob(
    event: BroadcastToAllJobData['event'],
    options?: {
      priority?: GenericPageJobPriority;
      delay?: number;
      excludeSessions?: string[];
      triggeredBy?: string;
    }
  ): Promise<void> {
    try {
      const jobData: BroadcastToAllJobData = {
        event,
        excludeSessions: options?.excludeSessions,
        triggeredBy: options?.triggeredBy,
      };

      await this.genericPageQueue.add(
        GenericPageJobType.BROADCAST_TO_ALL,
        jobData,
        {
          ...GENERIC_PAGE_JOB_OPTIONS,
          priority: options?.priority || GenericPageJobPriority.HIGH,
          delay: options?.delay,
        }
      );

      this.logger.log('Added broadcast to all job');
    } catch (error) {
      this.logger.error(`Failed to add broadcast to all job: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Lấy thống kê queue
   */
  async getQueueStats(): Promise<{
    waiting: number;
    active: number;
    completed: number;
    failed: number;
    delayed: number;
  }> {
    try {
      const [waiting, active, completed, failed, delayed] = await Promise.all([
        this.genericPageQueue.getWaiting(),
        this.genericPageQueue.getActive(),
        this.genericPageQueue.getCompleted(),
        this.genericPageQueue.getFailed(),
        this.genericPageQueue.getDelayed(),
      ]);

      return {
        waiting: waiting.length,
        active: active.length,
        completed: completed.length,
        failed: failed.length,
        delayed: delayed.length,
      };
    } catch (error) {
      this.logger.error(`Failed to get queue stats: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Xóa tất cả jobs trong queue
   */
  async clearQueue(): Promise<void> {
    try {
      await this.genericPageQueue.obliterate({ force: true });
      this.logger.log('Queue cleared successfully');
    } catch (error) {
      this.logger.error(`Failed to clear queue: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Pause queue
   */
  async pauseQueue(): Promise<void> {
    try {
      await this.genericPageQueue.pause();
      this.logger.log('Queue paused');
    } catch (error) {
      this.logger.error(`Failed to pause queue: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Resume queue
   */
  async resumeQueue(): Promise<void> {
    try {
      await this.genericPageQueue.resume();
      this.logger.log('Queue resumed');
    } catch (error) {
      this.logger.error(`Failed to resume queue: ${error.message}`, error.stack);
      throw error;
    }
  }
}

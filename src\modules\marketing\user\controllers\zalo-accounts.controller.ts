import {
  Controller,
  Get,
  UseGuards,
  Logger,
  Query,
  Version,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { CurrentUser } from '@modules/auth/decorators/current-user.decorator';
import { JwtPayload } from '@/modules/auth/guards/jwt.util';
import { ApiResponseDto, PaginatedResult } from '@/common/response';
import { JwtUserGuard } from '@/modules/auth/guards';
import { SWAGGER_API_TAGS } from '@/common/swagger';
import { ZaloAccountsService } from '../services';
import {
  ZaloAccountResponseDto,
  ZaloAccountsQueryDto,
} from '../dto/zalo-accounts';

/**
 * Controller xử lý API lấy danh sách Zalo accounts đã được tích hợp
 * Bao gồm cả Zalo OA và Zalo Personal
 */
@ApiTags(SWAGGER_API_TAGS.ZALO_ACCOUNTS)
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtUserGuard)
@Controller('marketing/zalo-accounts')
export class ZaloAccountsController {
  private readonly logger = new Logger(ZaloAccountsController.name);

  constructor(
    private readonly zaloAccountsService: ZaloAccountsService,
  ) {}

  /**
   * Lấy danh sách tất cả Zalo accounts đã được tích hợp (OA + Personal)
   */
  @Get('accounts')
  @ApiOperation({
    summary: 'Lấy danh sách Zalo accounts đã được tích hợp',
    description:
      'Lấy danh sách tất cả Zalo OA và Zalo Personal đã được tích hợp vào hệ thống',
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy danh sách thành công',
    type: PaginatedResult<ZaloAccountResponseDto>,
  })
  async getZaloAccounts(
    @CurrentUser() user: JwtPayload,
    @Query() queryDto: ZaloAccountsQueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<ZaloAccountResponseDto>>> {
    try {
      this.logger.log(
        `User ${user.id} getting Zalo accounts list with query:`,
        queryDto,
      );

      const accounts = await this.zaloAccountsService.getZaloAccountsByUserId(
        user.id,
        queryDto,
      );

      return ApiResponseDto.success(
        accounts,
        'Lấy danh sách Zalo accounts thành công',
      );
    } catch (error) {
      this.logger.error('Error getting Zalo accounts:', error);
      throw error;
    }
  }

}

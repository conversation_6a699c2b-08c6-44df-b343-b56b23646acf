import { Injectable, NotFoundException } from '@nestjs/common';
import { UserAudienceRepository } from '../repositories/user-audience.repository';
import { UserAudienceCustomFieldRepository } from '../repositories/user-audience-custom-field.repository';
import { UserAudienceCustomFieldDefinitionRepository } from '../repositories/user-audience-custom-field-definition.repository';
import { UserTagRepository } from '../repositories/user-tag.repository';
import { UserAudienceHasTagRepository } from '../repositories/user-audience-has-tag.repository';
import { In, Like, FindOptionsWhere, Or, Not, IsNull } from 'typeorm';
import { Transactional } from 'typeorm-transactional';
import {
  CreateAudienceDto,
  UpdateAudienceDto,
  UpdateAudienceBasicDto,
  AudienceResponseDto,
  CustomFieldResponseDto,
  AudienceQueryDto,
  AudienceAllQueryDto,
  MergeUserAudienceDto,
  MergeUserAudienceResponseDto,
} from '../dto/audience';
import {
  CreateAvatarUploadUrlDto,
  AvatarUploadUrlResponseDto,
} from '../dto/audience/avatar-upload.dto';
import { TagResponseDto } from '../dto/tag';
import { PaginatedResponseDto, PaginationMetaDto } from '../dto/common';
import {
  UserAudience,
  UserAudienceCustomField,
  UserTag,
  UserSegment,
  UserAudienceHasTag,
} from '../entities';
import { BulkDeleteResponseDto } from '@/modules/marketing/common/dto';
import { S3Service } from '@/shared/services/s3.service';
import { CdnService } from '@/shared/services/cdn.service';
import {
  generateS3Key,
  CategoryFolderEnum,
} from '@/shared/utils/generators/s3-key-generator.util';
import { TimeIntervalEnum, FileSizeEnum } from '@/shared/utils';
import { ImageTypeEnum } from '@shared/utils/file/image-media_type.util';
import { AppException } from '@common/exceptions';
import { MARKETING_ERROR_CODES } from '@modules/marketing/errors/marketing-error.code';
import { UserSegmentRepository } from '../repositories';

/**
 * Service xử lý logic liên quan đến audience
 */
@Injectable()
export class UserAudienceService {
  constructor(
    private readonly userAudienceRepository: UserAudienceRepository,
    private readonly userAudienceCustomFieldRepository: UserAudienceCustomFieldRepository,
    private readonly userAudienceCustomFieldDefinitionRepository: UserAudienceCustomFieldDefinitionRepository,
    private readonly userTagRepository: UserTagRepository,
    private readonly userAudienceHasTagRepository: UserAudienceHasTagRepository,
    private readonly userSegmentRepository: UserSegmentRepository,
    private readonly s3Service: S3Service,
    private readonly cdnService: CdnService,
  ) {}

  /**
   * Tạo audience mới
   * @param userId ID của người dùng
   * @param createAudienceDto Dữ liệu tạo audience
   * @returns Thông tin audience đã tạo
   */
  @Transactional()
  async create(
    userId: number,
    createAudienceDto: CreateAudienceDto,
  ): Promise<AudienceResponseDto> {
    const now = Math.floor(Date.now() / 1000);

    // Validate email không được trùng lặp
    if (createAudienceDto.email && createAudienceDto.email.trim()) {
      const existingEmailAudience =
        await this.userAudienceRepository.findByEmail(
          createAudienceDto.email,
          userId,
        );
      if (existingEmailAudience) {
        throw new AppException(
          MARKETING_ERROR_CODES.AUDIENCE_EMAIL_ALREADY_EXISTS,
          `Email ${createAudienceDto.email} đã được sử dụng bởi khách hàng khác`,
        );
      }
    }

    // Validate phone không được trùng lặp (nếu có cả countryCode và phoneNumber)
    if (
      createAudienceDto.countryCode &&
      createAudienceDto.phoneNumber &&
      createAudienceDto.phoneNumber.trim()
    ) {
      const existingPhoneAudience =
        await this.userAudienceRepository.findByPhoneNumber(
          createAudienceDto.countryCode,
          createAudienceDto.phoneNumber,
          userId,
        );
      if (existingPhoneAudience) {
        throw new AppException(
          MARKETING_ERROR_CODES.AUDIENCE_PHONE_ALREADY_EXISTS,
          `Số điện thoại +${createAudienceDto.countryCode}${createAudienceDto.phoneNumber} đã được sử dụng bởi khách hàng khác`,
        );
      }
    }

    // Tạo audience
    const audience = new UserAudience();
    audience.userId = userId;
    audience.name = createAudienceDto.name;
    audience.email = createAudienceDto.email || '';
    audience.countryCode = createAudienceDto.countryCode || null;
    audience.phoneNumber = createAudienceDto.phoneNumber || null;
    audience.avatar = createAudienceDto.avatar || null;
    audience.createdAt = now;
    audience.updatedAt = now;

    const savedAudience = await this.userAudienceRepository.save(audience);

    // Tạo các trường tùy chỉnh
    const customFields: UserAudienceCustomField[] = [];
    if (
      createAudienceDto.customFields &&
      createAudienceDto.customFields.length > 0
    ) {
      for (const fieldDto of createAudienceDto.customFields) {
        const customField = new UserAudienceCustomField();
        customField.audienceId = (savedAudience as UserAudience).id;
        customField.fieldId = fieldDto.fieldId;
        customField.fieldValue = fieldDto.fieldValue;
        customField.createdAt = now;
        customField.updatedAt = now;
        customFields.push(customField);
      }

      await this.userAudienceCustomFieldRepository.save(customFields);
    }

    // Lấy thông tin tags nếu có
    let tags: UserTag[] = [];
    if (createAudienceDto.tagIds && createAudienceDto.tagIds.length > 0) {
      tags = await this.userTagRepository.find({
        where: {
          id: In(createAudienceDto.tagIds),
          userId,
        },
      });
    }

    // Lấy custom fields với definition để có configJson
    const customFieldsWithDefinition = await this.getCustomFieldsWithDefinition(
      (savedAudience as UserAudience).id,
      userId,
    );

    // Đảm bảo savedAudience là một đối tượng UserAudience, không phải mảng
    return this.mapToDto(
      savedAudience as UserAudience,
      customFieldsWithDefinition,
      tags,
    );
  }

  /**
   * Cập nhật audience
   * @param userId ID của người dùng
   * @param id ID của audience
   * @param updateAudienceDto Dữ liệu cập nhật audience
   * @returns Thông tin audience đã cập nhật
   */
  @Transactional()
  async update(
    userId: number,
    id: number,
    updateAudienceDto: UpdateAudienceDto,
  ): Promise<AudienceResponseDto> {
    const audience = await this.userAudienceRepository.findOne({
      where: { id, userId },
    });
    if (!audience) {
      throw new NotFoundException(`Audience với ID ${id} không tồn tại`);
    }

    // Validate email không được trùng lặp (trừ chính audience hiện tại)
    if (
      updateAudienceDto.email !== undefined &&
      updateAudienceDto.email &&
      updateAudienceDto.email.trim()
    ) {
      const isEmailExists = await this.userAudienceRepository.isEmailExists(
        updateAudienceDto.email,
        userId,
        id,
      );
      if (isEmailExists) {
        throw new AppException(
          MARKETING_ERROR_CODES.AUDIENCE_EMAIL_ALREADY_EXISTS,
          `Email ${updateAudienceDto.email} đã được sử dụng bởi khách hàng khác`,
        );
      }
    }

    // Validate phone không được trùng lặp (trừ chính audience hiện tại)
    if (
      updateAudienceDto.countryCode !== undefined &&
      updateAudienceDto.phoneNumber !== undefined &&
      updateAudienceDto.countryCode &&
      updateAudienceDto.phoneNumber &&
      updateAudienceDto.phoneNumber.trim()
    ) {
      const isPhoneExists =
        await this.userAudienceRepository.isPhoneNumberExists(
          updateAudienceDto.countryCode,
          updateAudienceDto.phoneNumber,
          userId,
          id,
        );
      if (isPhoneExists) {
        throw new AppException(
          MARKETING_ERROR_CODES.AUDIENCE_PHONE_ALREADY_EXISTS,
          `Số điện thoại +${updateAudienceDto.countryCode}${updateAudienceDto.phoneNumber} đã được sử dụng bởi khách hàng khác`,
        );
      }
    }

    const now = Math.floor(Date.now() / 1000);

    // Cập nhật thông tin audience
    if (updateAudienceDto.name !== undefined) {
      audience.name = updateAudienceDto.name;
    }

    if (updateAudienceDto.email !== undefined) {
      audience.email = updateAudienceDto.email;
    }

    if (updateAudienceDto.countryCode !== undefined) {
      audience.countryCode = updateAudienceDto.countryCode;
    }

    if (updateAudienceDto.phoneNumber !== undefined) {
      audience.phoneNumber = updateAudienceDto.phoneNumber || null;
    }

    if (updateAudienceDto.avatar !== undefined) {
      audience.avatar = updateAudienceDto.avatar;
    }

    if (updateAudienceDto.address !== undefined) {
      audience.address = updateAudienceDto.address || null;
    }

    audience.updatedAt = now;
    const updatedAudience = await this.userAudienceRepository.save(audience);

    // Cập nhật các trường tùy chỉnh
    let customFields: UserAudienceCustomField[] = [];
    if (updateAudienceDto.customFields !== undefined) {
      // Xóa các trường tùy chỉnh hiện tại
      await this.userAudienceCustomFieldRepository.delete({ audienceId: id });

      // Tạo các trường tùy chỉnh mới
      if (updateAudienceDto.customFields.length > 0) {
        for (const fieldDto of updateAudienceDto.customFields) {
          const customField = new UserAudienceCustomField();
          customField.audienceId = id;
          customField.fieldId = fieldDto.fieldId;
          customField.fieldValue = fieldDto.fieldValue;
          customField.createdAt = now;
          customField.updatedAt = now;
          customFields.push(customField);
        }

        const savedCustomFields =
          await this.userAudienceCustomFieldRepository.save(customFields);
        customFields = Array.isArray(savedCustomFields)
          ? savedCustomFields
          : [savedCustomFields];
      }
    } else {
      // Lấy các trường tùy chỉnh hiện tại
      customFields = await this.userAudienceCustomFieldRepository.find({
        where: { audienceId: id },
      });
    }

    // Cập nhật tags nếu có
    let tags: UserTag[] = [];
    if (updateAudienceDto.tagIds !== undefined) {
      // Xóa tất cả tag relationships cũ
      await this.userAudienceHasTagRepository.deleteByAudienceId(id);

      // Thêm tag relationships mới nếu có
      if (updateAudienceDto.tagIds.length > 0) {
        // Validate tags tồn tại và thuộc về user
        tags = await this.userTagRepository.find({
          where: {
            id: In(updateAudienceDto.tagIds),
            userId,
          },
        });

        if (tags.length !== updateAudienceDto.tagIds.length) {
          const foundIds = tags.map((t) => t.id);
          const missingIds = updateAudienceDto.tagIds.filter(
            (id) => !foundIds.includes(id),
          );
          throw new AppException(
            MARKETING_ERROR_CODES.TAG_NOT_FOUND,
            `Không tìm thấy tags với ID: ${missingIds.join(', ')}`,
          );
        }

        // Tạo các relationships mới
        const audienceTagRelations = updateAudienceDto.tagIds.map((tagId) => {
          const relation = new UserAudienceHasTag();
          relation.audienceId = id;
          relation.tagId = tagId;
          return relation;
        });

        await this.userAudienceHasTagRepository.save(audienceTagRelations);
      }
    } else {
      // Nếu không có tagIds trong request, lấy tags hiện tại
      const audienceHasTags = await this.userAudienceHasTagRepository.find({
        where: { audienceId: id },
      });

      if (audienceHasTags.length > 0) {
        const tagIds = audienceHasTags.map((aht) => aht.tagId);
        tags = await this.userTagRepository.find({
          where: {
            id: In(tagIds),
            userId,
          },
        });
      }
    }

    // Lấy custom fields với definition để có configJson
    const customFieldsWithDefinition = await this.getCustomFieldsWithDefinition(
      id,
      userId,
    );

    // Đảm bảo updatedAudience là một đối tượng UserAudience, không phải mảng
    return this.mapToDto(
      updatedAudience as UserAudience,
      customFieldsWithDefinition,
      tags,
    );
  }

  /**
   * Cập nhật cơ bản thông tin audience (chỉ bao gồm: tên, email, số điện thoại, tag, địa chỉ)
   * @param userId ID của người dùng
   * @param id ID của audience
   * @param updateAudienceBasicDto Dữ liệu cập nhật cơ bản
   * @returns Audience đã cập nhật
   */
  @Transactional()
  async updateBasic(
    userId: number,
    id: number,
    updateAudienceBasicDto: UpdateAudienceBasicDto,
  ): Promise<AudienceResponseDto> {
    // Kiểm tra audience tồn tại và thuộc về user
    const audience = await this.userAudienceRepository.findOne({
      where: { id, userId },
    });
    if (!audience) {
      throw new NotFoundException(`Audience với ID ${id} không tồn tại`);
    }

    // Kiểm tra email trùng lặp nếu có cập nhật email
    if (
      updateAudienceBasicDto.email &&
      updateAudienceBasicDto.email !== audience.email
    ) {
      const existingAudience = await this.userAudienceRepository.findOne({
        where: { email: updateAudienceBasicDto.email, userId },
      });
      if (existingAudience && existingAudience.id !== id) {
        throw new AppException(
          MARKETING_ERROR_CODES.AUDIENCE_EMAIL_ALREADY_EXISTS,
        );
      }
    }

    // Kiểm tra số điện thoại trùng lặp nếu có cập nhật số điện thoại
    if (
      updateAudienceBasicDto.countryCode !== undefined &&
      updateAudienceBasicDto.phoneNumber !== undefined &&
      updateAudienceBasicDto.countryCode &&
      updateAudienceBasicDto.phoneNumber &&
      updateAudienceBasicDto.phoneNumber.trim()
    ) {
      const isPhoneExists =
        await this.userAudienceRepository.isPhoneNumberExists(
          updateAudienceBasicDto.countryCode,
          updateAudienceBasicDto.phoneNumber,
          userId,
          id,
        );
      if (isPhoneExists) {
        throw new AppException(
          MARKETING_ERROR_CODES.AUDIENCE_PHONE_ALREADY_EXISTS,
          `Số điện thoại +${updateAudienceBasicDto.countryCode}${updateAudienceBasicDto.phoneNumber} đã được sử dụng bởi khách hàng khác`,
        );
      }
    }

    const now = Math.floor(Date.now() / 1000);

    // Cập nhật thông tin cơ bản
    if (updateAudienceBasicDto.name !== undefined) {
      audience.name = updateAudienceBasicDto.name;
    }

    if (updateAudienceBasicDto.email !== undefined) {
      audience.email = updateAudienceBasicDto.email;
    }

    if (updateAudienceBasicDto.countryCode !== undefined) {
      audience.countryCode = updateAudienceBasicDto.countryCode;
    }

    if (updateAudienceBasicDto.phoneNumber !== undefined) {
      audience.phoneNumber = updateAudienceBasicDto.phoneNumber || null;
    }

    if (updateAudienceBasicDto.address !== undefined) {
      audience.address = updateAudienceBasicDto.address || null;
    }

    audience.updatedAt = now;
    const updatedAudience = await this.userAudienceRepository.save(audience);

    // Xử lý tags nếu có
    let tags: UserTag[] = [];
    if (updateAudienceBasicDto.tagIds !== undefined) {
      // Xóa tất cả tags hiện tại
      await this.userAudienceHasTagRepository.deleteByAudienceId(id);

      if (updateAudienceBasicDto.tagIds.length > 0) {
        // Kiểm tra tags có tồn tại và thuộc về user không
        tags = await this.userTagRepository.find({
          where: {
            id: In(updateAudienceBasicDto.tagIds),
            userId,
          },
        });

        if (tags.length !== updateAudienceBasicDto.tagIds.length) {
          throw new NotFoundException('Một hoặc nhiều tag không tồn tại');
        }

        // Tạo các liên kết mới
        const audienceTagRelations = updateAudienceBasicDto.tagIds.map(
          (tagId) => ({
            audienceId: id,
            tagId,
          }),
        );

        await this.userAudienceHasTagRepository.save(audienceTagRelations);
      }
    } else {
      // Nếu không có tagIds trong request, lấy tags hiện tại
      const audienceHasTags = await this.userAudienceHasTagRepository.find({
        where: { audienceId: id },
      });

      if (audienceHasTags.length > 0) {
        const tagIds = audienceHasTags.map((aht) => aht.tagId);
        tags = await this.userTagRepository.find({
          where: {
            id: In(tagIds),
            userId,
          },
        });
      }
    }

    // Lấy custom fields với definition để có configJson
    const customFieldsWithDefinition = await this.getCustomFieldsWithDefinition(
      id,
      userId,
    );

    return this.mapToDto(
      updatedAudience as UserAudience,
      customFieldsWithDefinition,
      tags,
    );
  }

  /**
   * Xóa audience
   * @param userId ID của người dùng
   * @param id ID của audience
   * @returns true nếu xóa thành công
   */
  @Transactional()
  async remove(userId: number, id: number): Promise<boolean> {
    const audience = await this.userAudienceRepository.findOne({
      where: { id, userId },
    });
    if (!audience) {
      throw new NotFoundException(`Audience với ID ${id} không tồn tại`);
    }

    // Xóa các trường tùy chỉnh
    await this.userAudienceCustomFieldRepository.delete({ audienceId: id });

    // Xóa audience
    await this.userAudienceRepository.remove(audience);
    return true;
  }

  /**
   * Xóa nhiều audience
   * @param userId ID của người dùng
   * @param ids Danh sách ID audience cần xóa
   * @returns Kết quả xóa nhiều
   */
  @Transactional()
  async bulkDelete(
    userId: number,
    ids: number[],
  ): Promise<BulkDeleteResponseDto> {
    const deletedIds: number[] = [];
    const failedIds: number[] = [];

    for (const id of ids) {
      try {
        const audience = await this.userAudienceRepository.findOne({
          where: { id, userId },
        });
        if (!audience) {
          failedIds.push(id);
          continue;
        }

        // Xóa các trường tùy chỉnh
        await this.userAudienceCustomFieldRepository.delete({ audienceId: id });

        // Xóa audience
        await this.userAudienceRepository.remove(audience);
        deletedIds.push(id);
      } catch (error) {
        failedIds.push(id);
      }
    }

    const deletedCount = deletedIds.length;
    const failedCount = failedIds.length;
    const message =
      failedCount > 0
        ? `Đã xóa ${deletedCount} audience thành công, ${failedCount} audience không thể xóa`
        : `Đã xóa ${deletedCount} audience thành công`;

    return {
      deletedCount,
      failedCount,
      deletedIds,
      failedIds,
      message,
    };
  }

  /**
   * Lấy danh sách audience của người dùng với phân trang và filter
   * @param userId ID của người dùng
   * @param query Tham số truy vấn
   * @returns Danh sách audience với phân trang
   */
  async findAll(
    userId: number,
    query: AudienceQueryDto,
  ): Promise<PaginatedResponseDto<AudienceResponseDto>> {
    const {
      page = 1,
      limit = 10,
      search,
      name,
      email,
      phone,
      hasPhoneNumber,
      hasEmail,
      tagId,
      customFieldName,
      customFieldValue,
      platform,
      integrationId,
      segmentId,
      segmentIds,
      excludeSegmentId,
      excludeSegmentIds,
      zaloUserIsFollower,
      sortBy = 'createdAt',
      sortDirection = 'DESC',
    } = query;

    // Tính toán offset
    const offset = (page - 1) * limit;

    // Tạo điều kiện cơ bản
    let where:
      | FindOptionsWhere<UserAudience>
      | FindOptionsWhere<UserAudience>[] = { userId };

    // Thêm điều kiện filter theo platform (importResource)
    if (platform) {
      (where as FindOptionsWhere<UserAudience>).importResource = platform;
    }

    // Thêm điều kiện filter theo integrationId
    if (integrationId) {
      (where as FindOptionsWhere<UserAudience>).integrationId = integrationId;
    }

    // Thêm điều kiện tìm kiếm tổng hợp (search trong name, email, phoneNumber)
    if (search) {
      const baseConditions = {
        userId,
        ...(platform && { importResource: platform }),
        ...(integrationId && { integrationId }),
        ...(zaloUserIsFollower !== undefined && { zaloUserIsFollower }),
      };

      where = [
        { ...baseConditions, name: Like(`%${search}%`) },
        { ...baseConditions, email: Like(`%${search}%`) },
        { ...baseConditions, phoneNumber: Like(`%${search}%`) },
      ];
    } else {
      // Thêm điều kiện tìm kiếm theo tên
      if (name) {
        (where as FindOptionsWhere<UserAudience>).name = Like(`%${name}%`);
      }

      // Thêm điều kiện tìm kiếm theo email
      if (email) {
        (where as FindOptionsWhere<UserAudience>).email = Like(`%${email}%`);
      }

      // Thêm điều kiện tìm kiếm theo số điện thoại (backward compatibility)
      if (phone) {
        (where as FindOptionsWhere<UserAudience>).phoneNumber = Like(
          `%${phone}%`,
        );
      }
    }

    // Thêm điều kiện filter theo việc có số điện thoại hay không
    if (hasPhoneNumber !== undefined) {
      if (Array.isArray(where)) {
        if (hasPhoneNumber) {
          // Có số điện thoại: phoneNumber IS NOT NULL AND phoneNumber != ''
          where = where.map((w) => ({
            ...w,
            phoneNumber: Not(Or(IsNull(), Like(''))),
          }));
        } else {
          // Không có số điện thoại: phoneNumber IS NULL OR phoneNumber = ''
          where = where.map((w) => ({
            ...w,
            phoneNumber: Or(IsNull(), Like('')),
          }));
        }
      } else {
        if (hasPhoneNumber) {
          // Có số điện thoại: phoneNumber IS NOT NULL AND phoneNumber != ''
          (where as FindOptionsWhere<UserAudience>).phoneNumber = Not(
            Or(IsNull(), Like('')),
          );
        } else {
          // Không có số điện thoại: phoneNumber IS NULL OR phoneNumber = ''
          (where as FindOptionsWhere<UserAudience>).phoneNumber = Or(
            IsNull(),
            Like(''),
          );
        }
      }
    }

    // Thêm điều kiện filter theo việc có email hay không
    if (hasEmail !== undefined) {
      if (Array.isArray(where)) {
        if (hasEmail) {
          // Có email: email IS NOT NULL AND email != ''
          where = where.map((w) => ({
            ...w,
            email: Not(Or(IsNull(), Like(''))),
          }));
        } else {
          // Không có email: email IS NULL OR email = ''
          where = where.map((w) => ({
            ...w,
            email: Or(IsNull(), Like('')),
          }));
        }
      } else {
        if (hasEmail) {
          // Có email: email IS NOT NULL AND email != ''
          (where as FindOptionsWhere<UserAudience>).email = Not(
            Or(IsNull(), Like('')),
          );
        } else {
          // Không có email: email IS NULL OR email = ''
          (where as FindOptionsWhere<UserAudience>).email = Or(
            IsNull(),
            Like(''),
          );
        }
      }
    }

    // Thêm điều kiện filter theo trạng thái theo dõi Zalo OA
    if (zaloUserIsFollower !== undefined) {
      if (Array.isArray(where)) {
        where = where.map((w) => ({
          ...w,
          zaloUserIsFollower,
        }));
      } else {
        (where as FindOptionsWhere<UserAudience>).zaloUserIsFollower =
          zaloUserIsFollower;
      }
    }

    // Nếu có filter theo tagId, cần lấy audience IDs từ bảng liên kết trước
    let filteredAudienceIds: number[] | undefined;
    if (tagId) {
      const audienceHasTagsForFilter =
        await this.userAudienceHasTagRepository.find({
          where: { tagId },
        });
      filteredAudienceIds = audienceHasTagsForFilter.map(
        (aht) => aht.audienceId,
      );

      // Nếu không có audience nào có tag này, trả về kết quả rỗng
      if (filteredAudienceIds.length === 0) {
        return {
          data: [],
          meta: {
            total: 0,
            page,
            limit,
            totalPages: 0,
            hasPreviousPage: false,
            hasNextPage: false,
          },
        };
      }

      // Thêm điều kiện filter theo audience IDs
      if (Array.isArray(where)) {
        where = where.map((w) => ({ ...w, id: In(filteredAudienceIds!) }));
      } else {
        where = { ...where, id: In(filteredAudienceIds!) };
      }
    }

    // Lấy audience IDs từ segment nếu có filter theo segment
    if (
      segmentId ||
      segmentIds?.length ||
      excludeSegmentId ||
      excludeSegmentIds?.length
    ) {
      const segmentFilteredAudienceIds =
        await this.getAudienceIdsBySegmentFilter(
          userId,
          segmentId,
          segmentIds,
          excludeSegmentId,
          excludeSegmentIds,
        );

      // Nếu đã có filter theo tagId, lấy giao của hai tập hợp
      if (filteredAudienceIds) {
        filteredAudienceIds = filteredAudienceIds.filter((id) =>
          segmentFilteredAudienceIds.includes(id),
        );
      } else {
        filteredAudienceIds = segmentFilteredAudienceIds;
      }

      // Nếu không có audience nào phù hợp với filter, trả về kết quả rỗng
      if (filteredAudienceIds.length === 0) {
        return {
          data: [],
          meta: {
            total: 0,
            page,
            limit,
            totalPages: 0,
            hasPreviousPage: false,
            hasNextPage: false,
          },
        };
      }

      // Thêm điều kiện filter theo audience IDs
      if (Array.isArray(where)) {
        where = where.map((w) => ({ ...w, id: In(filteredAudienceIds!) }));
      } else {
        where = { ...where, id: In(filteredAudienceIds!) };
      }
    }

    // Đếm tổng số audience
    const total = await this.userAudienceRepository.count({ where });

    // Lấy danh sách audience với phân trang và sắp xếp
    const audiences = await this.userAudienceRepository.find({
      where,
      order: { [sortBy]: sortDirection },
      skip: offset,
      take: limit,
    });

    // Lấy danh sách ID của audience
    const audienceIds = audiences.map((a) => a.id);

    // Lấy tất cả các trường tùy chỉnh của các audience
    const customFieldsQuery: FindOptionsWhere<UserAudienceCustomField> = {
      audienceId: In(audienceIds),
    };

    // Thêm điều kiện tìm kiếm theo giá trị trường tùy chỉnh
    if (customFieldValue) {
      customFieldsQuery.fieldValue = Like(`%${customFieldValue}%`);
    }

    // TODO: Để tìm kiếm theo tên trường, cần join với bảng custom field definitions
    // Hiện tại chỉ hỗ trợ tìm kiếm theo giá trị trường
    if (customFieldName) {
      // Tạm thời bỏ qua tìm kiếm theo tên trường vì cần join với bảng definitions
      // Có thể implement sau bằng cách sử dụng query builder
    }

    const customFields = await this.userAudienceCustomFieldRepository.find({
      where: customFieldsQuery,
    });

    // Lấy tất cả tags của các audience
    const audienceHasTags = await this.userAudienceHasTagRepository.find({
      where: { audienceId: In(audienceIds) },
    });

    // Lấy tất cả tag IDs
    const tagIds = [...new Set(audienceHasTags.map((aht) => aht.tagId))];

    // Lấy thông tin chi tiết của các tags
    let allTags: UserTag[] = [];
    if (tagIds.length > 0) {
      allTags = await this.userTagRepository.find({
        where: { id: In(tagIds), userId },
      });
    }

    // Tạo map để dễ dàng tìm tags theo audience ID
    const audienceTagsMap = new Map<number, UserTag[]>();
    for (const audienceHasTag of audienceHasTags) {
      const tag = allTags.find((t) => t.id === audienceHasTag.tagId);
      if (tag) {
        if (!audienceTagsMap.has(audienceHasTag.audienceId)) {
          audienceTagsMap.set(audienceHasTag.audienceId, []);
        }
        audienceTagsMap.get(audienceHasTag.audienceId)!.push(tag);
      }
    }

    // Lấy custom fields với definition cho tất cả audience
    const customFieldsWithDefinitionMap =
      await this.getCustomFieldsWithDefinitionForMultiple(audienceIds, userId);

    // Chuyển đổi kết quả thành DTO
    const data: AudienceResponseDto[] = [];
    for (const audience of audiences) {
      const audienceCustomFields =
        customFieldsWithDefinitionMap[audience.id] || [];
      const audienceTags = audienceTagsMap.get(audience.id) || [];
      data.push(this.mapToDto(audience, audienceCustomFields, audienceTags));
    }

    // Tạo thông tin phân trang
    const totalPages = Math.ceil(total / limit);
    const meta: PaginationMetaDto = {
      total,
      page,
      limit,
      totalPages,
      hasPreviousPage: page > 1,
      hasNextPage: page < totalPages,
    };

    return {
      data,
      meta,
    };
  }

  /**
   * Lấy thông tin audience theo ID
   * @param userId ID của người dùng
   * @param id ID của audience
   * @returns Thông tin audience
   */
  async findOne(userId: number, id: number): Promise<AudienceResponseDto> {
    const audience = await this.userAudienceRepository.findOne({
      where: { id, userId },
    });
    if (!audience) {
      throw new NotFoundException(`Audience với ID ${id} không tồn tại`);
    }

    // Lấy custom fields với join definition để có configJson
    const customFieldsWithDefinition = await this.getCustomFieldsWithDefinition(
      id,
      userId,
    );

    // Lấy các tag của audience
    const audienceHasTags = await this.userAudienceHasTagRepository.find({
      where: { audienceId: id },
    });

    // Lấy thông tin chi tiết của các tags
    let tags: UserTag[] = [];
    if (audienceHasTags.length > 0) {
      const tagIds = audienceHasTags.map((aht) => aht.tagId);
      tags = await this.userTagRepository.find({
        where: {
          id: In(tagIds),
          userId, // Đảm bảo tags thuộc về user
        },
      });
    }

    return this.mapToDto(audience, customFieldsWithDefinition, tags);
  }

  /**
   * Lấy custom fields với join definition để có thông tin đầy đủ cho nhiều audience
   * @param audienceIds Danh sách ID của audience
   * @param userId ID của người dùng
   * @returns Map custom fields theo audienceId với thông tin từ definition (fieldName, fieldType, configJson)
   */
  private async getCustomFieldsWithDefinitionForMultiple(
    audienceIds: number[],
    userId: number,
  ): Promise<Record<number, any[]>> {
    if (audienceIds.length === 0) {
      return {};
    }

    // Lấy tất cả custom fields của các audience
    const customFields = await this.userAudienceCustomFieldRepository.find({
      where: { audienceId: In(audienceIds) },
    });

    // Lấy tất cả fieldIds để query definition
    const fieldIds = [...new Set(customFields.map((cf) => cf.fieldId))];

    if (fieldIds.length === 0) {
      return {};
    }

    // Lấy definitions của các fields
    const definitions =
      await this.userAudienceCustomFieldDefinitionRepository.find({
        where: {
          id: In(fieldIds),
          userId,
        },
      });

    // Tạo map để tra cứu nhanh definition theo fieldId
    const definitionMap = definitions.reduce(
      (map, def) => {
        map[def.id] = def;
        return map;
      },
      {} as Record<number, any>,
    );

    // Group custom fields theo audienceId và merge với definition
    const result: Record<number, any[]> = {};

    for (const field of customFields) {
      if (!result[field.audienceId]) {
        result[field.audienceId] = [];
      }

      const definition = definitionMap[field.fieldId];
      result[field.audienceId].push({
        ...field,
        // Thêm thông tin từ definition
        fieldName: definition?.displayName || null,
        fieldType: definition?.dataType || null,
        configJson: definition?.config || null,
      });
    }

    return result;
  }

  /**
   * Lấy custom fields với join definition để có thông tin đầy đủ
   * @param audienceId ID của audience
   * @param userId ID của người dùng
   * @returns Danh sách custom fields với thông tin từ definition (fieldName, fieldType, configJson)
   */
  private async getCustomFieldsWithDefinition(
    audienceId: number,
    userId: number,
  ): Promise<any[]> {
    const result = await this.getCustomFieldsWithDefinitionForMultiple(
      [audienceId],
      userId,
    );
    return result[audienceId] || [];
  }

  /**
   * Chuyển đổi entity thành DTO
   * @param audience Entity audience
   * @param customFields Danh sách các trường tùy chỉnh (có thể có configJson)
   * @param tags Danh sách các tag
   * @returns DTO audience
   */
  private mapToDto(
    audience: UserAudience,
    customFields: any[],
    tags: UserTag[],
  ): AudienceResponseDto {
    const dto = new AudienceResponseDto();

    // Map tất cả các trường từ entity
    dto.id = audience.id;
    dto.userId = audience.userId;
    dto.name = audience.name;
    dto.email = audience.email;
    dto.countryCode = audience.countryCode;
    dto.phoneNumber = audience.phoneNumber;

    // Chuyển đổi avatar S3 key thành CDN URL
    let avatarUrl: string | null = audience.avatar;
    if (audience.avatar) {
      try {
        const cdnUrl = this.cdnService.generateUrlView(
          audience.avatar,
          TimeIntervalEnum.ONE_DAY,
        );
        avatarUrl = cdnUrl || audience.avatar; // Fallback to original key if CDN fails
      } catch (error) {
        console.warn(
          `Không thể tạo URL CDN cho avatar audience ${audience.id}: ${error.message}`,
        );
        avatarUrl = audience.avatar; // Keep original key as fallback
      }
    }
    dto.avatar = avatarUrl;

    dto.address = audience.address;
    dto.zaloSocialId = audience.zaloSocialId;
    dto.integrationId = audience.integrationId;
    dto.avatarsExternal = audience.avatarsExternal;
    dto.importResource = audience.importResource;
    dto.zaloOfficialAccountId = audience.zaloOfficialAccountId
      ? String(audience.zaloOfficialAccountId)
      : null;
    dto.zaloUserIsFollower = audience.zaloUserIsFollower;
    dto.userLastInteractionDate = audience.userLastInteractionDate;
    dto.createdAt = audience.createdAt;
    dto.updatedAt = audience.updatedAt;

    // Chuyển đổi các trường tùy chỉnh
    dto.customFields = customFields.map((field) => {
      const fieldDto = new CustomFieldResponseDto();
      fieldDto.id = field.id;
      fieldDto.audienceId = field.audienceId;
      fieldDto.fieldId = field.fieldId;
      fieldDto.fieldName = field.fieldName || null; // Tên hiển thị từ definition
      fieldDto.fieldType = field.fieldType || null; // Kiểu dữ liệu từ definition
      fieldDto.fieldValue = field.fieldValue;
      fieldDto.configJson = field.configJson || null; // Cấu hình từ definition
      fieldDto.createdAt = field.createdAt;
      fieldDto.updatedAt = field.updatedAt;
      return fieldDto;
    });

    // Chuyển đổi các tag
    dto.tags = tags.map((tag) => {
      const tagDto = new TagResponseDto();
      tagDto.id = tag.id;
      tagDto.name = tag.name;
      tagDto.color = tag.color;
      tagDto.audienceCount = 0; // TODO: Có thể tính toán thực tế nếu cần
      tagDto.createdAt = tag.createdAt;
      tagDto.updatedAt = tag.updatedAt;
      return tagDto;
    });

    return dto;
  }

  /**
   * Tạo presigned URL để upload avatar
   * @param userId ID của người dùng
   * @param audienceId ID của audience
   * @param createAvatarUploadUrlDto Dữ liệu tạo URL upload
   * @returns Thông tin URL upload
   */
  async createAvatarUploadUrl(
    userId: number,
    audienceId: number,
    createAvatarUploadUrlDto: CreateAvatarUploadUrlDto,
  ): Promise<AvatarUploadUrlResponseDto> {
    // Kiểm tra audience tồn tại và thuộc về user
    const audience = await this.userAudienceRepository.findOne({
      where: { id: audienceId, userId },
    });
    if (!audience) {
      throw new NotFoundException(
        `Audience với ID ${audienceId} không tồn tại`,
      );
    }

    // Tạo S3 key cho avatar
    const s3Key = generateS3Key({
      baseFolder: 'marketing',
      categoryFolder: CategoryFolderEnum.CUSTOMER_AVATAR,
      useTimeFolder: true,
      prefix: `user_${userId}`,
      fileName: `avatar.${createAvatarUploadUrlDto.mediaType.split('/')[1]}`,
    });

    // Tạo presigned URL
    const uploadUrl = await this.s3Service.createPresignedWithID(
      s3Key,
      TimeIntervalEnum.ONE_HOUR, // URL hết hạn sau 1 giờ
      createAvatarUploadUrlDto.mediaType as any, // Cast to MediaType
      FileSizeEnum.FIVE_MB, // Giới hạn 5MB cho avatar
    );

    // Lưu S3 key vào database
    audience.avatar = s3Key;
    audience.updatedAt = Date.now();
    await this.userAudienceRepository.save(audience);

    // Tính thời gian hết hạn
    const expiresAt = Math.floor(Date.now() / 1000) + 60 * 60; // 1 giờ

    return {
      uploadUrl,
      s3Key,
      expiresAt,
    };
  }

  /**
   * Xóa avatar của audience
   * @param userId ID của người dùng
   * @param audienceId ID của audience
   * @returns Thông tin audience đã cập nhật
   */
  async removeAvatar(
    userId: number,
    audienceId: number,
  ): Promise<AudienceResponseDto> {
    // Kiểm tra audience tồn tại và thuộc về user
    const audience = await this.userAudienceRepository.findOne({
      where: { id: audienceId, userId },
    });
    if (!audience) {
      throw new NotFoundException(
        `Audience với ID ${audienceId} không tồn tại`,
      );
    }

    const now = Math.floor(Date.now() / 1000);

    // Xóa avatar trên S3 nếu có
    if (audience.avatar) {
      try {
        await this.s3Service.deleteFile(audience.avatar);
      } catch (error) {
        // Log lỗi nhưng không dừng quá trình cập nhật
        console.warn(`Không thể xóa avatar: ${audience.avatar}`, error);
      }
    }

    // Cập nhật database
    audience.avatar = null;
    audience.updatedAt = now;

    const updatedAudience = await this.userAudienceRepository.save(audience);

    // Lấy custom fields với definition để có configJson
    const customFieldsWithDefinition = await this.getCustomFieldsWithDefinition(
      audienceId,
      userId,
    );

    return this.mapToDto(
      updatedAudience as UserAudience,
      customFieldsWithDefinition,
      [],
    );
  }

  /**
   * Merge nhiều audience thành một audience mới
   * @param userId ID của người dùng
   * @param mergeDto Thông tin merge audience
   * @returns Kết quả merge audience
   */
  @Transactional()
  async mergeAudiences(
    userId: number,
    mergeDto: MergeUserAudienceDto,
  ): Promise<MergeUserAudienceResponseDto> {
    // Validate các audience tồn tại và thuộc về user
    const audiences = await this.userAudienceRepository.find({
      where: {
        id: In(mergeDto.audienceIds),
        userId,
      },
    });

    if (audiences.length !== mergeDto.audienceIds.length) {
      const foundIds = audiences.map((a) => a.id);
      const missingIds = mergeDto.audienceIds.filter(
        (id) => !foundIds.includes(id),
      );
      throw new AppException(
        MARKETING_ERROR_CODES.AUDIENCE_NOT_FOUND,
        `Không tìm thấy audience với ID: ${missingIds.join(', ')}`,
      );
    }

    // Validate email không trùng lặp nếu có
    if (mergeDto.newAudienceEmail) {
      const existingEmailAudience =
        await this.userAudienceRepository.findByEmail(
          mergeDto.newAudienceEmail,
          userId,
        );
      if (existingEmailAudience) {
        throw new AppException(
          MARKETING_ERROR_CODES.AUDIENCE_EMAIL_ALREADY_EXISTS,
          `Email ${mergeDto.newAudienceEmail} đã được sử dụng bởi khách hàng khác`,
        );
      }
    }

    // Tạo audience mới
    const newAudience = new UserAudience();
    newAudience.userId = userId;
    newAudience.name =
      mergeDto.newAudienceName ||
      `Merged Audience - ${new Date().toISOString()}`;
    newAudience.email = mergeDto.newAudienceEmail || '';
    newAudience.phoneNumber = mergeDto.newAudiencePhone || null;
    newAudience.createdAt = Math.floor(Date.now() / 1000);
    newAudience.updatedAt = Math.floor(Date.now() / 1000);

    // Lưu audience mới
    const savedNewAudience =
      await this.userAudienceRepository.save(newAudience);

    // Sử dụng custom fields từ frontend thay vì thu thập từ audience cũ
    const customFieldsToCreate: any[] = [];
    if (mergeDto.customFields && mergeDto.customFields.length > 0) {
      mergeDto.customFields.forEach((cf) => {
        customFieldsToCreate.push({
          audienceId: savedNewAudience.id,
          fieldId: cf.fieldId,
          fieldValue: cf.fieldValue,
          createdAt: Math.floor(Date.now() / 1000),
          updatedAt: Math.floor(Date.now() / 1000),
        });
      });
    }

    // Lưu custom fields cho audience mới
    if (customFieldsToCreate.length > 0) {
      const newCustomFields = customFieldsToCreate.map((cf) => {
        const customField = new UserAudienceCustomField();
        customField.audienceId = cf.audienceId;
        customField.fieldId = cf.fieldId;
        customField.fieldValue = cf.fieldValue;
        customField.createdAt = cf.createdAt;
        customField.updatedAt = cf.updatedAt;
        return customField;
      });
      await this.userAudienceCustomFieldRepository.save(newCustomFields);
    }

    // Xóa các audience cũ
    const deletedIds: number[] = [];
    for (const audienceId of mergeDto.audienceIds) {
      try {
        // Xóa custom fields
        await this.userAudienceCustomFieldRepository.delete({ audienceId });

        // Xóa audience
        const audienceToDelete = audiences.find((a) => a.id === audienceId);
        if (audienceToDelete) {
          await this.userAudienceRepository.remove(audienceToDelete);
          deletedIds.push(audienceId);
        }
      } catch (error) {
        // Log lỗi nhưng không dừng quá trình
        console.warn(`Không thể xóa audience ${audienceId}:`, error);
      }
    }

    return {
      newAudience: {
        id: savedNewAudience.id,
        name: savedNewAudience.name,
        email: savedNewAudience.email,
        phone: savedNewAudience.phoneNumber || undefined,
        createdAt: savedNewAudience.createdAt,
        updatedAt: savedNewAudience.updatedAt,
      },
      mergedAudienceIds: deletedIds,
      mergedCount: deletedIds.length,
      message: `Đã merge ${deletedIds.length} audience thành công`,
    };
  }

  /**
   * Lấy audience IDs theo segment filter
   * @private
   */
  private async getAudienceIdsBySegmentFilter(
    userId: number,
    segmentId?: number,
    segmentIds?: number[],
    excludeSegmentId?: number,
    excludeSegmentIds?: number[],
  ): Promise<number[]> {
    const includeAudienceIds: Set<number> = new Set();
    const excludeAudienceIds: Set<number> = new Set();

    // Lấy audience IDs từ segment include
    if (segmentId) {
      const audienceIds = await this.getAudienceIdsFromSegment(
        userId,
        segmentId,
      );
      audienceIds.forEach((id) => includeAudienceIds.add(id));
    }

    if (segmentIds?.length) {
      for (const sId of segmentIds) {
        const audienceIds = await this.getAudienceIdsFromSegment(userId, sId);
        audienceIds.forEach((id) => includeAudienceIds.add(id));
      }
    }

    // Lấy audience IDs từ segment exclude
    if (excludeSegmentId) {
      const audienceIds = await this.getAudienceIdsFromSegment(
        userId,
        excludeSegmentId,
      );
      audienceIds.forEach((id) => excludeAudienceIds.add(id));
    }

    if (excludeSegmentIds?.length) {
      for (const sId of excludeSegmentIds) {
        const audienceIds = await this.getAudienceIdsFromSegment(userId, sId);
        audienceIds.forEach((id) => excludeAudienceIds.add(id));
      }
    }

    // Nếu không có include filter, lấy tất cả audience của user
    if (includeAudienceIds.size === 0) {
      const allAudiences = await this.userAudienceRepository.find({
        where: { userId },
        select: ['id'],
      });
      allAudiences.forEach((a) => includeAudienceIds.add(a.id));
    }

    // Loại trừ audience từ exclude filter
    const result: number[] = [];
    includeAudienceIds.forEach((id) => {
      if (!excludeAudienceIds.has(id)) {
        result.push(id);
      }
    });

    return result;
  }

  /**
   * Lấy audience IDs từ một segment cụ thể
   * @private
   */
  private async getAudienceIdsFromSegment(
    userId: number,
    segmentId: number,
  ): Promise<number[]> {
    const segment = await this.userSegmentRepository.findOne({
      where: { id: segmentId, userId },
    });

    if (!segment) {
      return [];
    }

    // Sử dụng logic từ UserSegmentService để lấy audience
    const audiences = await this.getAudiencesInSegment(userId, segment);
    return audiences.map((a) => a.id);
  }

  /**
   * Lấy audience phù hợp với segment (copy từ UserSegmentService)
   * @private
   */
  private async getAudiencesInSegment(
    userId: number,
    segment: UserSegment,
  ): Promise<UserAudience[]> {
    // Logic này được copy từ UserSegmentService.getAudiencesInSegment
    // Để tránh circular dependency, tôi sẽ implement lại logic cơ bản

    if (!segment.criteria || !segment.criteria.groups) {
      return [];
    }

    const allAudiences = await this.userAudienceRepository.find({
      where: { userId },
    });

    // Áp dụng logic filter theo criteria (simplified version)
    const matchedAudiences: UserAudience[] = [];

    for (const audience of allAudiences) {
      let isMatch = false;

      for (const group of segment.criteria.groups) {
        let groupMatch = true;

        for (const condition of group.conditions) {
          let conditionMatch = false;

          switch (condition.field) {
            case 'source':
              conditionMatch = this.evaluateCondition(
                audience.importResource,
                condition.operator,
                condition.value,
              );
              break;
            case 'integrationId':
              conditionMatch = this.evaluateCondition(
                audience.integrationId,
                condition.operator,
                condition.value,
              );
              break;
            case 'zaloUserId':
              conditionMatch = this.evaluateCondition(
                audience.zaloSocialId,
                condition.operator,
                condition.value,
              );
              break;
            case 'email':
              conditionMatch = this.evaluateCondition(
                audience.email,
                condition.operator,
                condition.value,
              );
              break;
            case 'name':
              conditionMatch = this.evaluateCondition(
                audience.name,
                condition.operator,
                condition.value,
              );
              break;
            // Thêm các field khác nếu cần
            default:
              conditionMatch = false;
          }

          if (group.logicalOperator === 'AND' && !conditionMatch) {
            groupMatch = false;
            break;
          } else if (group.logicalOperator === 'OR' && conditionMatch) {
            groupMatch = true;
            break;
          }
        }

        if (groupMatch) {
          isMatch = true;
          break;
        }
      }

      if (isMatch) {
        matchedAudiences.push(audience);
      }
    }

    return matchedAudiences;
  }

  /**
   * Đánh giá điều kiện filter
   * @private
   */
  private evaluateCondition(
    fieldValue: any,
    operator: string,
    conditionValue: string,
  ): boolean {
    if (fieldValue === null || fieldValue === undefined) {
      return operator === 'not_empty'
        ? false
        : operator === 'empty'
          ? true
          : false;
    }

    const fieldStr = String(fieldValue).toLowerCase();
    const conditionStr = String(conditionValue).toLowerCase();

    switch (operator) {
      case 'equals':
        return fieldStr === conditionStr;
      case 'not_equals':
        return fieldStr !== conditionStr;
      case 'contains':
        return fieldStr.includes(conditionStr);
      case 'not_contains':
        return !fieldStr.includes(conditionStr);
      case 'starts_with':
        return fieldStr.startsWith(conditionStr);
      case 'ends_with':
        return fieldStr.endsWith(conditionStr);
      case 'empty':
        return fieldStr === '';
      case 'not_empty':
        return fieldStr !== '';
      case 'greater_than':
        return parseFloat(fieldStr) > parseFloat(conditionStr);
      case 'less_than':
        return parseFloat(fieldStr) < parseFloat(conditionStr);
      default:
        return false;
    }
  }

  /**
   * Lấy tất cả audience với filter (không phân trang)
   */
  async findAllAudiences(
    userId: number,
    query: AudienceAllQueryDto,
  ): Promise<AudienceResponseDto[]> {
    const {
      search,
      name,
      email,
      phone,
      hasPhoneNumber,
      hasEmail,
      tagId,
      platform,
      integrationId,
      segmentId,
      segmentIds,
      excludeSegmentId,
      excludeSegmentIds,
      zaloUserIsFollower,
      limit = 1000,
      basicInfo = false,
      sortBy = 'createdAt',
      sortDirection = 'DESC',
    } = query;

    // Tạo điều kiện cơ bản
    let where:
      | FindOptionsWhere<UserAudience>
      | FindOptionsWhere<UserAudience>[] = { userId };

    // Thêm điều kiện filter theo platform (importResource)
    if (platform) {
      (where as FindOptionsWhere<UserAudience>).importResource = platform;
    }

    // Thêm điều kiện filter theo integrationId
    if (integrationId) {
      (where as FindOptionsWhere<UserAudience>).integrationId = integrationId;
    }

    // Thêm điều kiện tìm kiếm tổng hợp (search trong name, email, phoneNumber)
    if (search) {
      const baseConditions = {
        userId,
        ...(platform && { importResource: platform }),
        ...(integrationId && { integrationId }),
        ...(zaloUserIsFollower !== undefined && { zaloUserIsFollower }),
      };

      where = [
        { ...baseConditions, name: Like(`%${search}%`) },
        { ...baseConditions, email: Like(`%${search}%`) },
        { ...baseConditions, phoneNumber: Like(`%${search}%`) },
      ];
    } else {
      // Thêm điều kiện tìm kiếm theo tên
      if (name) {
        (where as FindOptionsWhere<UserAudience>).name = Like(`%${name}%`);
      }

      // Thêm điều kiện tìm kiếm theo email
      if (email) {
        (where as FindOptionsWhere<UserAudience>).email = Like(`%${email}%`);
      }

      // Thêm điều kiện tìm kiếm theo số điện thoại
      if (phone) {
        (where as FindOptionsWhere<UserAudience>).phoneNumber = Like(
          `%${phone}%`,
        );
      }
    }

    // Thêm điều kiện filter theo việc có số điện thoại hay không
    if (hasPhoneNumber !== undefined) {
      if (Array.isArray(where)) {
        if (hasPhoneNumber) {
          // Có số điện thoại: phoneNumber IS NOT NULL AND phoneNumber != ''
          where = where.map((w) => ({
            ...w,
            phoneNumber: Not(Or(IsNull(), Like(''))),
          }));
        } else {
          // Không có số điện thoại: phoneNumber IS NULL OR phoneNumber = ''
          where = where.map((w) => ({
            ...w,
            phoneNumber: Or(IsNull(), Like('')),
          }));
        }
      } else {
        if (hasPhoneNumber) {
          // Có số điện thoại: phoneNumber IS NOT NULL AND phoneNumber != ''
          (where as FindOptionsWhere<UserAudience>).phoneNumber = Not(
            Or(IsNull(), Like('')),
          );
        } else {
          // Không có số điện thoại: phoneNumber IS NULL OR phoneNumber = ''
          (where as FindOptionsWhere<UserAudience>).phoneNumber = Or(
            IsNull(),
            Like(''),
          );
        }
      }
    }

    // Thêm điều kiện filter theo việc có email hay không
    if (hasEmail !== undefined) {
      if (Array.isArray(where)) {
        if (hasEmail) {
          // Có email: email IS NOT NULL AND email != ''
          where = where.map((w) => ({
            ...w,
            email: Not(Or(IsNull(), Like(''))),
          }));
        } else {
          // Không có email: email IS NULL OR email = ''
          where = where.map((w) => ({
            ...w,
            email: Or(IsNull(), Like('')),
          }));
        }
      } else {
        if (hasEmail) {
          // Có email: email IS NOT NULL AND email != ''
          (where as FindOptionsWhere<UserAudience>).email = Not(
            Or(IsNull(), Like('')),
          );
        } else {
          // Không có email: email IS NULL OR email = ''
          (where as FindOptionsWhere<UserAudience>).email = Or(
            IsNull(),
            Like(''),
          );
        }
      }
    }

    // Thêm điều kiện filter theo trạng thái theo dõi Zalo OA
    if (zaloUserIsFollower !== undefined) {
      if (Array.isArray(where)) {
        where = where.map((w) => ({
          ...w,
          zaloUserIsFollower,
        }));
      } else {
        (where as FindOptionsWhere<UserAudience>).zaloUserIsFollower =
          zaloUserIsFollower;
      }
    }

    // Nếu có filter theo tagId, cần lấy audience IDs từ bảng liên kết trước
    let filteredAudienceIds: number[] | undefined;
    if (tagId) {
      const audienceHasTagsForFilter =
        await this.userAudienceHasTagRepository.find({
          where: { tagId },
        });
      filteredAudienceIds = audienceHasTagsForFilter.map(
        (aht) => aht.audienceId,
      );

      // Nếu không có audience nào có tag này, trả về mảng rỗng
      if (filteredAudienceIds.length === 0) {
        return [];
      }

      // Thêm điều kiện filter theo audience IDs
      if (Array.isArray(where)) {
        where = where.map((w) => ({ ...w, id: In(filteredAudienceIds!) }));
      } else {
        where = { ...where, id: In(filteredAudienceIds!) };
      }
    }

    // Xử lý filter theo segment
    if (
      segmentId ||
      segmentIds?.length ||
      excludeSegmentId ||
      excludeSegmentIds?.length
    ) {
      const segmentFilteredAudienceIds =
        await this.getAudienceIdsBySegmentFilter(
          userId,
          segmentId,
          segmentIds,
          excludeSegmentId,
          excludeSegmentIds,
        );

      // Nếu đã có filter theo tagId, lấy giao của hai tập hợp
      if (filteredAudienceIds) {
        filteredAudienceIds = filteredAudienceIds.filter((id) =>
          segmentFilteredAudienceIds.includes(id),
        );
      } else {
        filteredAudienceIds = segmentFilteredAudienceIds;
      }

      // Nếu không có audience nào phù hợp với filter, trả về mảng rỗng
      if (filteredAudienceIds.length === 0) {
        return [];
      }

      // Thêm điều kiện filter theo audience IDs
      if (Array.isArray(where)) {
        where = where.map((w) => ({ ...w, id: In(filteredAudienceIds!) }));
      } else {
        where = { ...where, id: In(filteredAudienceIds!) };
      }
    }

    // Lấy danh sách audience với sắp xếp và giới hạn
    const audiences = await this.userAudienceRepository.find({
      where,
      order: { [sortBy]: sortDirection },
      take: limit,
      ...(basicInfo && {
        select: [
          'id',
          'userId',
          'name',
          'email',
          'countryCode',
          'phoneNumber',
          'avatar',
          'zaloSocialId',
          'integrationId',
          'avatarsExternal',
          'importResource',
          'zaloOfficialAccountId',
          'zaloUserIsFollower',
          'userLastInteractionDate',
          'createdAt',
          'updatedAt',
        ],
      }),
    });

    // Nếu chỉ cần thông tin cơ bản, trả về ngay với tất cả các trường
    if (basicInfo) {
      return audiences.map((audience) => ({
        id: audience.id,
        userId: audience.userId,
        name: audience.name,
        email: audience.email,
        countryCode: audience.countryCode,
        phoneNumber: audience.phoneNumber,
        avatar: audience.avatar,
        address: audience.address,
        zaloSocialId: audience.zaloSocialId,
        integrationId: audience.integrationId,
        avatarsExternal: audience.avatarsExternal,
        importResource: audience.importResource,
        zaloOfficialAccountId: audience.zaloOfficialAccountId
          ? String(audience.zaloOfficialAccountId)
          : null,
        zaloUserIsFollower: audience.zaloUserIsFollower,
        userLastInteractionDate: audience.userLastInteractionDate,
        createdAt: audience.createdAt,
        updatedAt: audience.updatedAt,
        customFields: [],
        tags: [],
      }));
    }

    // Lấy custom fields và tags cho audience (logic đầy đủ)
    const audienceIds = audiences.map((a) => a.id);
    const customFieldsWithDefinitionMap =
      await this.getCustomFieldsWithDefinitionForMultiple(audienceIds, userId);

    // Lấy tất cả tags của các audience
    const audienceHasTags = await this.userAudienceHasTagRepository.find({
      where: { audienceId: In(audienceIds) },
    });

    // Lấy tất cả tag IDs
    const tagIds = [...new Set(audienceHasTags.map((aht) => aht.tagId))];

    // Lấy thông tin chi tiết của các tags
    let allTags: UserTag[] = [];
    if (tagIds.length > 0) {
      allTags = await this.userTagRepository.find({
        where: { id: In(tagIds), userId },
      });
    }

    // Tạo map để dễ dàng tìm tags theo audience ID
    const audienceTagsMap = new Map<number, UserTag[]>();
    for (const audienceHasTag of audienceHasTags) {
      const tag = allTags.find((t) => t.id === audienceHasTag.tagId);
      if (tag) {
        if (!audienceTagsMap.has(audienceHasTag.audienceId)) {
          audienceTagsMap.set(audienceHasTag.audienceId, []);
        }
        audienceTagsMap.get(audienceHasTag.audienceId)!.push(tag);
      }
    }

    // Chuyển đổi kết quả thành DTO
    const data: AudienceResponseDto[] = [];
    for (const audience of audiences) {
      const audienceCustomFields =
        customFieldsWithDefinitionMap[audience.id] || [];
      const audienceTags = audienceTagsMap.get(audience.id) || [];
      data.push(this.mapToDto(audience, audienceCustomFields, audienceTags));
    }

    return data;
  }
}

import { registerDecorator, ValidationOptions, ValidationArguments, ValidatorConstraint, ValidatorConstraintInterface } from 'class-validator';
import { IssueType, IssueTypeDisplayNames } from '../enums';

@ValidatorConstraint({ name: 'isValidIssueType', async: false })
export class IsValidIssueTypeConstraint implements ValidatorConstraintInterface {
  validate(issueType: any, args: ValidationArguments) {
    if (!issueType) {
      return true; // Let @IsNotEmpty handle empty values
    }

    // Check if the value is a valid enum value (English)
    if (Object.values(IssueType).includes(issueType)) {
      return true;
    }

    // Check if the value is a valid Vietnamese display name
    const vietnameseValues = Object.values(IssueTypeDisplayNames);
    return vietnameseValues.includes(issueType);
  }

  defaultMessage(args: ValidationArguments) {
    const englishValues = Object.values(IssueType).join(', ');
    const vietnameseValues = Object.values(IssueTypeDisplayNames).join(', ');
    return `Loại vấn đề không hợp lệ. Các giá trị cho phép: ${englishValues} hoặc ${vietnameseValues}`;
  }
}

/**
 * Decorator để validate IssueType enum
 * @param validationOptions Tùy chọn validation
 * @returns Decorator function
 */
export function IsValidIssueType(validationOptions?: ValidationOptions) {
  return function (object: object, propertyName: string) {
    registerDecorator({
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      constraints: [],
      validator: IsValidIssueTypeConstraint,
    });
  };
}

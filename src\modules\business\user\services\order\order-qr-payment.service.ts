import { Injectable, Logger } from '@nestjs/common';
import { AppException, ErrorCode } from '@common/exceptions';
import { SystemConfigSharedService } from '@shared/services/system-config-shared.service';
import { AgentPaymentGatewayService } from '@modules/agent/user/services/agent-payment-gateway.service';
import { IntegrationRepository } from '@modules/integration/repositories';
import { PaymentMethodEnum } from '../../../enums';

/**
 * Service xử lý tạo QR code thanh toán cho đơn hàng
 */
@Injectable()
export class OrderQRPaymentService {
  private readonly logger = new Logger(OrderQRPaymentService.name);

  constructor(
    private readonly systemConfigSharedService: SystemConfigSharedService,
    private readonly agentPaymentGatewayService: AgentPaymentGatewayService,
    private readonly integrationRepository: IntegrationRepository,
  ) {}

  /**
   * Tạo QR code thanh toán cho đơn hàng
   * @param orderId ID đơn hàng
   * @param amount Số tiền thanh toán
   * @param paymentMethod Phương thức thanh toán
   * @param paymentGatewayId ID payment gateway (tùy chọn)
   * @param agentId ID agent (tùy chọn)
   * @param userId ID user
   * @returns URL QR code
   */
  async generateOrderQRCode(
    orderId: string,
    amount: number,
    paymentMethod: PaymentMethodEnum,
    paymentGatewayId?: string,
    agentId?: string,
    userId?: number,
  ): Promise<string | null> {
    try {
      // Chỉ tạo QR code khi phương thức thanh toán là QR_CODE
      if (paymentMethod !== PaymentMethodEnum.QR_CODE) {
        return null;
      }

      this.logger.log(
        `Generating QR code for order ${orderId}, amount: ${amount}, paymentGatewayId: ${paymentGatewayId}, agentId: ${agentId}`,
      );

      let bankCode: string;
      let accountNumber: string;

      // 1. Nếu có paymentGatewayId, sử dụng payment gateway đó
      if (paymentGatewayId) {
        const paymentGateway = await this.getPaymentGatewayById(
          paymentGatewayId,
          userId,
        );
        bankCode = paymentGateway.bankCode;
        accountNumber = paymentGateway.accountNumber;
      }
      // 2. Nếu có agentId, lấy payment gateway từ agent
      else if (agentId && userId) {
        const agentPaymentGateway = await this.getPaymentGatewayFromAgent(
          agentId,
          userId,
        );
        if (!agentPaymentGateway) {
          throw new AppException(
            ErrorCode.VALIDATION_ERROR,
            'Agent chưa có payment gateway được cấu hình',
          );
        }
        bankCode = agentPaymentGateway.bankCode!;
        accountNumber = agentPaymentGateway.accountNumber!;
      }
      // 3. Sử dụng cấu hình hệ thống mặc định
      else {
        return await this.systemConfigSharedService.generateQRPaymentUrl(
          amount,
          orderId,
        );
      }

      // Tạo QR code với thông tin ngân hàng
      const qrUrl = this.generateQRPaymentUrl(
        bankCode,
        accountNumber,
        amount,
        orderId,
      );

      this.logger.log(`Generated QR code URL for order ${orderId}: ${qrUrl}`);
      return qrUrl;
    } catch (error) {
      this.logger.error(
        `Error generating QR code for order ${orderId}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Lấy thông tin payment gateway theo ID
   * @param paymentGatewayId ID payment gateway
   * @param userId ID user (để kiểm tra quyền sở hữu)
   * @returns Thông tin payment gateway
   */
  private async getPaymentGatewayById(
    paymentGatewayId: string,
    userId?: number,
  ) {
    const paymentGateway = await this.integrationRepository.findOne({
      where: { id: paymentGatewayId },
    });

    if (!paymentGateway) {
      throw new AppException(
        ErrorCode.NOT_FOUND,
        'Payment gateway không tồn tại',
      );
    }

    // Kiểm tra quyền sở hữu nếu có userId
    if (userId && paymentGateway.userId !== userId) {
      throw new AppException(
        ErrorCode.FORBIDDEN,
        'Không có quyền truy cập payment gateway này',
      );
    }

    // Lấy thông tin ngân hàng từ metadata
    const metadata = paymentGateway.metadata as any;
    const bankCode = metadata?.bankCode;
    const accountNumber = metadata?.accountNumber;

    if (!bankCode || !accountNumber) {
      throw new AppException(
        ErrorCode.VALIDATION_ERROR,
        'Payment gateway thiếu thông tin ngân hàng',
      );
    }

    return {
      bankCode,
      accountNumber,
    };
  }

  /**
   * Lấy thông tin payment gateway từ agent
   * @param agentId ID agent
   * @param userId ID user
   * @returns Thông tin payment gateway hoặc null
   */
  private async getPaymentGatewayFromAgent(agentId: string, userId: number) {
    try {
      const agentPaymentGateway =
        await this.agentPaymentGatewayService.getPaymentGateway(
          agentId,
          userId,
        );

      if (!agentPaymentGateway) {
        return null;
      }

      return {
        bankCode: agentPaymentGateway.bankCode || '',
        accountNumber: agentPaymentGateway.accountNumber || '',
      };
    } catch (error) {
      this.logger.warn(
        `Could not get payment gateway from agent ${agentId}: ${error.message}`,
      );
      return null;
    }
  }

  /**
   * Tạo URL QR thanh toán
   * @param bankCode Mã ngân hàng
   * @param accountNumber Số tài khoản
   * @param amount Số tiền
   * @param orderId ID đơn hàng
   * @returns URL QR thanh toán
   */
  private generateQRPaymentUrl(
    bankCode: string,
    accountNumber: string,
    amount: number,
    orderId: string,
  ): string {
    return `https://qr.sepay.vn/img?bank=${bankCode}&acc=${accountNumber}&template=compact&amount=${amount.toFixed(2)}&des=REDAI${orderId}SEPAYORDER`;
  }
}

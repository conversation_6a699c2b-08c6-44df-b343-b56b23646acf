-- =====================================================
-- Migration: Create Generic Session Tables
-- Date: 2025-01-08
-- Author: System
-- Description: 
--   Create tables for Generic Page Dynamic Control:
--   - generic_sessions: Session management
--   - generic_session_widgets: Widget management per session
--   - generic_session_layouts: Layout configuration per session
-- =====================================================

-- STEP 1: CREATE generic_sessions TABLE
CREATE TABLE IF NOT EXISTS generic_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_id VARCHAR(255) UNIQUE NOT NULL,
    user_id INTEGER NULL,
    page_config JSONB DEFAULT '{}',
    is_active BOOLEAN DEFAULT true,
    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    metadata JSONB NULL,
    expires_at TIMESTAMP NULL
);

-- Create indexes for generic_sessions
CREATE INDEX IF NOT EXISTS idx_generic_sessions_session_id ON generic_sessions(session_id);
CREATE INDEX IF NOT EXISTS idx_generic_sessions_user_id ON generic_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_generic_sessions_is_active ON generic_sessions(is_active);
CREATE INDEX IF NOT EXISTS idx_generic_sessions_last_activity ON generic_sessions(last_activity);
CREATE INDEX IF NOT EXISTS idx_generic_sessions_expires_at ON generic_sessions(expires_at);

-- STEP 2: CREATE generic_session_widgets TABLE
CREATE TABLE IF NOT EXISTS generic_session_widgets (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_id VARCHAR(255) NOT NULL,
    widget_id VARCHAR(255) NOT NULL,
    widget_type VARCHAR(100) NOT NULL,
    widget_data JSONB NOT NULL,
    position JSONB NOT NULL,
    display_order INTEGER DEFAULT 0,
    is_visible BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    metadata JSONB NULL,
    
    -- Unique constraint: one widget_id per session
    CONSTRAINT uk_generic_session_widgets_session_widget UNIQUE (session_id, widget_id)
);

-- Create indexes for generic_session_widgets
CREATE INDEX IF NOT EXISTS idx_generic_session_widgets_session_id ON generic_session_widgets(session_id);
CREATE INDEX IF NOT EXISTS idx_generic_session_widgets_widget_id ON generic_session_widgets(widget_id);
CREATE INDEX IF NOT EXISTS idx_generic_session_widgets_widget_type ON generic_session_widgets(widget_type);
CREATE INDEX IF NOT EXISTS idx_generic_session_widgets_display_order ON generic_session_widgets(display_order);
CREATE INDEX IF NOT EXISTS idx_generic_session_widgets_is_visible ON generic_session_widgets(is_visible);

-- STEP 3: CREATE generic_session_layouts TABLE
CREATE TABLE IF NOT EXISTS generic_session_layouts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_id VARCHAR(255) UNIQUE NOT NULL,
    layout_data JSONB NOT NULL,
    breakpoints JSONB NULL,
    grid_config JSONB NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    metadata JSONB NULL
);

-- Create indexes for generic_session_layouts
CREATE INDEX IF NOT EXISTS idx_generic_session_layouts_session_id ON generic_session_layouts(session_id);
CREATE INDEX IF NOT EXISTS idx_generic_session_layouts_updated_at ON generic_session_layouts(updated_at);

-- STEP 4: ADD FOREIGN KEY CONSTRAINTS (Optional - following user preference to not use TypeORM relations)
-- Note: These are database-level constraints, not TypeORM relations

-- Foreign key from generic_session_widgets to generic_sessions
ALTER TABLE generic_session_widgets 
ADD CONSTRAINT fk_generic_session_widgets_session_id 
FOREIGN KEY (session_id) REFERENCES generic_sessions(session_id) 
ON DELETE CASCADE ON UPDATE CASCADE;

-- Foreign key from generic_session_layouts to generic_sessions  
ALTER TABLE generic_session_layouts 
ADD CONSTRAINT fk_generic_session_layouts_session_id 
FOREIGN KEY (session_id) REFERENCES generic_sessions(session_id) 
ON DELETE CASCADE ON UPDATE CASCADE;

-- STEP 5: ADD COMMENTS FOR DOCUMENTATION
COMMENT ON TABLE generic_sessions IS 'Sessions for generic page dynamic control with WebSocket real-time sync';
COMMENT ON COLUMN generic_sessions.session_id IS 'Unique session identifier for frontend tracking';
COMMENT ON COLUMN generic_sessions.user_id IS 'Optional user ID for authenticated sessions';
COMMENT ON COLUMN generic_sessions.page_config IS 'Page configuration in JSON format (theme, settings, etc.)';
COMMENT ON COLUMN generic_sessions.is_active IS 'Whether the session is currently active';
COMMENT ON COLUMN generic_sessions.last_activity IS 'Last activity timestamp for session cleanup';
COMMENT ON COLUMN generic_sessions.expires_at IS 'Optional session expiration timestamp';

COMMENT ON TABLE generic_session_widgets IS 'Widgets configuration for each generic session';
COMMENT ON COLUMN generic_session_widgets.widget_id IS 'Unique widget identifier within the session';
COMMENT ON COLUMN generic_session_widgets.widget_type IS 'Type of widget (data-count, marketing-overview, etc.)';
COMMENT ON COLUMN generic_session_widgets.widget_data IS 'Widget configuration data in JSON format';
COMMENT ON COLUMN generic_session_widgets.position IS 'Widget position and size in grid layout (x, y, w, h)';
COMMENT ON COLUMN generic_session_widgets.display_order IS 'Display order for widget z-index';
COMMENT ON COLUMN generic_session_widgets.is_visible IS 'Whether the widget is currently visible';

COMMENT ON TABLE generic_session_layouts IS 'Layout configuration for each generic session';
COMMENT ON COLUMN generic_session_layouts.layout_data IS 'React-grid-layout format data in JSON';
COMMENT ON COLUMN generic_session_layouts.breakpoints IS 'Responsive breakpoints configuration';
COMMENT ON COLUMN generic_session_layouts.grid_config IS 'Grid settings (rowHeight, margin, cols, etc.)';

-- STEP 6: CREATE TRIGGER FOR UPDATED_AT AUTO-UPDATE
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply trigger to tables with updated_at column
CREATE TRIGGER update_generic_sessions_updated_at 
    BEFORE UPDATE ON generic_sessions 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_generic_session_widgets_updated_at 
    BEFORE UPDATE ON generic_session_widgets 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_generic_session_layouts_updated_at 
    BEFORE UPDATE ON generic_session_layouts 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- STEP 7: INSERT SAMPLE DATA FOR TESTING (Optional)
-- Uncomment below to insert sample data

/*
-- Sample session
INSERT INTO generic_sessions (session_id, user_id, page_config, metadata) 
VALUES (
    'sample-session-123',
    NULL,
    '{"theme": "dark", "autoLayout": true}',
    '{"userAgent": "Mozilla/5.0", "ip": "127.0.0.1"}'
);

-- Sample widgets
INSERT INTO generic_session_widgets (session_id, widget_id, widget_type, widget_data, position) 
VALUES 
(
    'sample-session-123',
    'widget-data-count-1',
    'data-count',
    '{"title": "Total Users", "value": 1234, "icon": "users"}',
    '{"x": 0, "y": 0, "w": 4, "h": 3}'
),
(
    'sample-session-123', 
    'widget-marketing-overview-1',
    'marketing-overview',
    '{"title": "Marketing Overview", "campaigns": 5, "leads": 150}',
    '{"x": 4, "y": 0, "w": 4, "h": 3}'
);

-- Sample layout
INSERT INTO generic_session_layouts (session_id, layout_data, grid_config)
VALUES (
    'sample-session-123',
    '[{"i": "widget-data-count-1", "x": 0, "y": 0, "w": 4, "h": 3}, {"i": "widget-marketing-overview-1", "x": 4, "y": 0, "w": 4, "h": 3}]',
    '{"rowHeight": 60, "margin": [8, 8], "containerPadding": [0, 0], "cols": {"lg": 12, "md": 10, "sm": 6, "xs": 4, "xxs": 2}}'
);
*/

-- Migration completed successfully
SELECT 'Generic Session Tables created successfully!' as result;

import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { AppException } from '@common/exceptions/app.exception';
import { ErrorCode } from '@common/exceptions';
import { lastValueFrom } from 'rxjs';

/**
 * Interface cho kết quả download file
 */
export interface DownloadFileResult {
  buffer: Buffer;
  contentType: string;
  filename: string;
  size: number;
}

/**
 * Service để download file từ URL
 * Sử dụng chung cho knowledge files và media
 */
@Injectable()
export class FileDownloadService {
  private readonly logger = new Logger(FileDownloadService.name);

  constructor(private readonly httpService: HttpService) {}

  /**
   * Download file từ URL
   * @param url URL của file cần download
   * @param allowedTypes Danh sách MIME types được phép (optional)
   * @param maxSizeBytes Kích thước tối đa cho phép (bytes, optional)
   * @returns DownloadFileResult
   */
  async downloadFileFromUrl(
    url: string,
    allowedTypes?: string[],
    maxSizeBytes?: number,
  ): Promise<DownloadFileResult> {
    try {
      this.logger.log(`Bắt đầu download file từ URL: ${url}`);

      // Validate URL
      if (!this.isValidUrl(url)) {
        throw new AppException(
          ErrorCode.BAD_REQUEST,
          'URL không hợp lệ',
          { url },
        );
      }

      // Download file với timeout 5 phút
      const response = await lastValueFrom(
        this.httpService.get(url, {
          responseType: 'arraybuffer',
          timeout: 300000, // 5 minutes
          maxContentLength: maxSizeBytes || 100 * 1024 * 1024, // 100MB default
          maxBodyLength: maxSizeBytes || 100 * 1024 * 1024,
        }),
      );

      const buffer = Buffer.from(response.data);
      const contentType = response.headers['content-type'] || 'application/octet-stream';
      const filename = this.extractFilenameFromUrl(url, response.headers);
      const size = buffer.length;

      this.logger.log(`Download thành công: ${filename}, size: ${size} bytes, type: ${contentType}`);

      // Validate content type nếu có danh sách allowed types
      if (allowedTypes && allowedTypes.length > 0) {
        if (!this.validateContentType(contentType, allowedTypes)) {
          throw new AppException(
            ErrorCode.BAD_REQUEST,
            `Loại file không được hỗ trợ. Chỉ hỗ trợ: ${allowedTypes.join(', ')}`,
            { contentType, allowedTypes },
          );
        }
      }

      // Validate file size
      if (maxSizeBytes && size > maxSizeBytes) {
        throw new AppException(
          ErrorCode.BAD_REQUEST,
          `File quá lớn. Kích thước tối đa: ${Math.round(maxSizeBytes / (1024 * 1024))}MB`,
          { size, maxSizeBytes },
        );
      }

      return {
        buffer,
        contentType,
        filename,
        size,
      };
    } catch (error) {
      this.logger.error(`Lỗi khi download file từ URL ${url}: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      // Handle HTTP errors
      if (error.response) {
        const status = error.response.status;
        if (status === 404) {
          throw new AppException(
            ErrorCode.NOT_FOUND,
            'File không tồn tại tại URL đã cung cấp',
            { url, status },
          );
        } else if (status === 403) {
          throw new AppException(
            ErrorCode.FORBIDDEN,
            'Không có quyền truy cập file tại URL này',
            { url, status },
          );
        } else if (status >= 400) {
          throw new AppException(
            ErrorCode.BAD_REQUEST,
            `Lỗi HTTP khi download file: ${status}`,
            { url, status },
          );
        }
      }

      // Handle timeout
      if (error.code === 'ECONNABORTED') {
        throw new AppException(
          ErrorCode.REQUEST_TIMEOUT,
          'Timeout khi download file. File quá lớn hoặc kết nối chậm',
          { url },
        );
      }

      // Generic error
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        `Không thể download file: ${error.message}`,
        { url },
      );
    }
  }

  /**
   * Validate URL format
   */
  private isValidUrl(url: string): boolean {
    try {
      const urlObj = new URL(url);
      return urlObj.protocol === 'http:' || urlObj.protocol === 'https:';
    } catch {
      return false;
    }
  }

  /**
   * Validate content type
   */
  private validateContentType(contentType: string, allowedTypes: string[]): boolean {
    return allowedTypes.some(allowedType => {
      // Support wildcard matching (e.g., "image/*")
      if (allowedType.endsWith('/*')) {
        const prefix = allowedType.slice(0, -2);
        return contentType.startsWith(prefix);
      }
      return contentType === allowedType || contentType.startsWith(allowedType);
    });
  }

  /**
   * Extract filename từ URL hoặc response headers
   */
  private extractFilenameFromUrl(url: string, headers: any): string {
    try {
      // Thử lấy từ Content-Disposition header trước
      const contentDisposition = headers['content-disposition'];
      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/);
        if (filenameMatch && filenameMatch[1]) {
          const filename = filenameMatch[1].replace(/['"]/g, '');
          if (filename) {
            return filename;
          }
        }
      }

      // Fallback: lấy từ URL path
      const urlObj = new URL(url);
      const pathname = urlObj.pathname;
      const filename = pathname.split('/').pop() || 'downloaded-file';
      
      // Nếu không có extension, thêm .bin
      if (!filename.includes('.')) {
        return `${filename}.bin`;
      }
      
      return filename;
    } catch {
      return `downloaded-file-${Date.now()}.bin`;
    }
  }

  /**
   * Get allowed MIME types cho knowledge files
   */
  static getKnowledgeFileAllowedTypes(): string[] {
    return [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-powerpoint',
      'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      'application/rtf',
      'application/vnd.oasis.opendocument.text',
      'application/vnd.oasis.opendocument.presentation',
      'text/plain',
      'text/markdown',
      'text/csv',
    ];
  }

  /**
   * Get allowed MIME types cho media files
   */
  static getMediaAllowedTypes(): string[] {
    return [
      'image/jpeg',
      'image/jpg',
      'image/png',
      'image/gif',
      'image/bmp',
      'image/webp',
      'image/tiff',
      'image/tif',
      'image/svg+xml',
      'image/x-icon',
    ];
  }
}

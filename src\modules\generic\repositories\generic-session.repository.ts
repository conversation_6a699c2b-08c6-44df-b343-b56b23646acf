import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { GenericSession } from '../entities/generic-session.entity';
import { PaginatedResult } from '@/common/response';
import { AppException } from '@/common';
import { GENERIC_ERROR_CODES } from '../exceptions';

@Injectable()
export class GenericSessionRepository extends Repository<GenericSession> {
  private readonly logger = new Logger(GenericSessionRepository.name);

  constructor(private dataSource: DataSource) {
    super(GenericSession, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder c<PERSON> bản cho GenericSession
   * @returns SelectQueryBuilder<GenericSession>
   */
  private createBaseQuery(): SelectQueryBuilder<GenericSession> {
    return this.createQueryBuilder('genericSession');
  }

  /**
   * Tìm session theo ID
   * @param id ID của session
   * @returns Session nếu tìm thấy
   * @throws AppException nếu không tìm thấy session
   */
  async findById(id: string): Promise<GenericSession> {
    try {
      const session = await this.createBaseQuery()
        .where('genericSession.id = :id', { id })
        .getOne();

      if (!session) {
        throw new AppException(
          GENERIC_ERROR_CODES.GENERIC_SESSION_NOT_FOUND,
          `Không tìm thấy session với ID ${id}`,
        );
      }

      return session;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Error finding session by ID: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        GENERIC_ERROR_CODES.GENERIC_SESSION_NOT_FOUND,
        `Lỗi khi tìm session với ID ${id}`,
      );
    }
  }

  /**
   * Tìm session theo sessionId
   * @param sessionId Session ID
   * @returns Session nếu tìm thấy
   * @throws AppException nếu không tìm thấy session
   */
  async findBySessionId(sessionId: string): Promise<GenericSession> {
    try {
      const session = await this.createBaseQuery()
        .where('genericSession.sessionId = :sessionId', { sessionId })
        .getOne();

      if (!session) {
        throw new AppException(
          GENERIC_ERROR_CODES.GENERIC_SESSION_NOT_FOUND,
          `Không tìm thấy session với sessionId ${sessionId}`,
        );
      }

      return session;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Error finding session by sessionId: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        GENERIC_ERROR_CODES.GENERIC_SESSION_NOT_FOUND,
        `Lỗi khi tìm session với sessionId ${sessionId}`,
      );
    }
  }

  /**
   * Tìm session theo sessionId (optional - không throw error)
   * @param sessionId Session ID
   * @returns Session nếu tìm thấy, null nếu không tìm thấy
   */
  async findBySessionIdOptional(
    sessionId: string,
  ): Promise<GenericSession | null> {
    try {
      return await this.createBaseQuery()
        .where('genericSession.sessionId = :sessionId', { sessionId })
        .getOne();
    } catch (error) {
      this.logger.error(
        `Error finding session by sessionId: ${error.message}`,
        error.stack,
      );
      return null;
    }
  }

  /**
   * Kiểm tra xem sessionId đã tồn tại chưa
   * @param sessionId Session ID cần kiểm tra
   * @param excludeId ID của session cần loại trừ (dùng khi cập nhật)
   * @returns true nếu sessionId đã tồn tại, false nếu chưa
   */
  async isSessionIdExists(
    sessionId: string,
    excludeId?: string,
  ): Promise<boolean> {
    try {
      const query = this.createBaseQuery().where(
        'genericSession.sessionId = :sessionId',
        { sessionId },
      );

      if (excludeId) {
        query.andWhere('genericSession.id != :excludeId', { excludeId });
      }

      const count = await query.getCount();
      return count > 0;
    } catch (error) {
      this.logger.error(
        `Error checking sessionId existence: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        GENERIC_ERROR_CODES.GENERIC_SESSION_CHECK_ERROR,
        'Lỗi khi kiểm tra sessionId',
      );
    }
  }

  /**
   * Tìm tất cả sessions active của user
   * @param userId User ID
   * @returns Danh sách sessions active
   */
  async findActiveSessionsByUserId(userId: number): Promise<GenericSession[]> {
    try {
      return await this.createBaseQuery()
        .where('genericSession.userId = :userId', { userId })
        .andWhere('genericSession.isActive = :isActive', { isActive: true })
        .orderBy('genericSession.lastActivity', 'DESC')
        .getMany();
    } catch (error) {
      this.logger.error(
        `Error finding active sessions by userId: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        GENERIC_ERROR_CODES.GENERIC_SESSION_QUERY_ERROR,
        `Lỗi khi tìm sessions của user ${userId}`,
      );
    }
  }

  /**
   * Tìm tất cả sessions active
   * @returns Danh sách sessions active
   */
  async findActiveSessions(): Promise<GenericSession[]> {
    try {
      return await this.createBaseQuery()
        .where('genericSession.isActive = :isActive', { isActive: true })
        .orderBy('genericSession.lastActivity', 'DESC')
        .getMany();
    } catch (error) {
      this.logger.error(
        `Error finding active sessions: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        GENERIC_ERROR_CODES.GENERIC_SESSION_QUERY_ERROR,
        'Lỗi khi tìm sessions active',
      );
    }
  }

  /**
   * Cập nhật last activity của session
   * @param sessionId Session ID
   * @returns Session đã được cập nhật
   */
  async updateLastActivity(sessionId: string): Promise<GenericSession> {
    try {
      await this.createQueryBuilder()
        .update(GenericSession)
        .set({
          lastActivity: new Date(),
          updatedAt: new Date(),
        })
        .where('sessionId = :sessionId', { sessionId })
        .execute();

      return await this.findBySessionId(sessionId);
    } catch (error) {
      this.logger.error(
        `Error updating last activity: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        GENERIC_ERROR_CODES.GENERIC_SESSION_UPDATE_ERROR,
        `Lỗi khi cập nhật last activity cho session ${sessionId}`,
      );
    }
  }

  /**
   * Deactivate session
   * @param sessionId Session ID
   * @returns Session đã được deactivate
   */
  async deactivateSession(sessionId: string): Promise<GenericSession> {
    try {
      await this.createQueryBuilder()
        .update(GenericSession)
        .set({
          isActive: false,
          updatedAt: new Date(),
        })
        .where('sessionId = :sessionId', { sessionId })
        .execute();

      return await this.findBySessionId(sessionId);
    } catch (error) {
      this.logger.error(
        `Error deactivating session: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        GENERIC_ERROR_CODES.GENERIC_SESSION_UPDATE_ERROR,
        `Lỗi khi deactivate session ${sessionId}`,
      );
    }
  }

  /**
   * Tìm sessions hết hạn
   * @returns Danh sách sessions hết hạn
   */
  async findExpiredSessions(): Promise<GenericSession[]> {
    try {
      const now = new Date();
      return await this.createBaseQuery()
        .where('genericSession.expiresAt IS NOT NULL')
        .andWhere('genericSession.expiresAt < :now', { now })
        .andWhere('genericSession.isActive = :isActive', { isActive: true })
        .getMany();
    } catch (error) {
      this.logger.error(
        `Error finding expired sessions: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        GENERIC_ERROR_CODES.GENERIC_SESSION_QUERY_ERROR,
        'Lỗi khi tìm sessions hết hạn',
      );
    }
  }

  /**
   * Cleanup sessions hết hạn
   * @returns Số lượng sessions đã được cleanup
   */
  async cleanupExpiredSessions(): Promise<number> {
    try {
      const expiredSessions = await this.findExpiredSessions();

      if (expiredSessions.length === 0) {
        return 0;
      }

      const sessionIds = expiredSessions.map((s) => s.sessionId);

      await this.createQueryBuilder()
        .update(GenericSession)
        .set({
          isActive: false,
          updatedAt: new Date(),
        })
        .where('sessionId IN (:...sessionIds)', { sessionIds })
        .execute();

      this.logger.log(`Cleaned up ${expiredSessions.length} expired sessions`);
      return expiredSessions.length;
    } catch (error) {
      this.logger.error(
        `Error cleaning up expired sessions: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        GENERIC_ERROR_CODES.GENERIC_SESSION_CLEANUP_ERROR,
        'Lỗi khi cleanup sessions hết hạn',
      );
    }
  }
}

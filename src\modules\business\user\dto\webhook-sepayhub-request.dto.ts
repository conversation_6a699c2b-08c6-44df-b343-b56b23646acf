import { IsString, <PERSON>N<PERSON>ber, IsOptional } from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';

export class WebhooksSepayHubRequestDto {
  @ApiProperty({
    description: 'Tên cổng thực hiện',
    example: 'MBBank'
  })
  @IsString()
  gateway: string; // Tên cổng thực hiện (Ví dụ: "MBBank", "OCB")

  @ApiProperty({
    description: 'Thời gian nhận giao dịch',
    example: '2023-08-15T10:15:00Z'
  })
  @IsString()
  transaction_date: string; // Thời gian nhận giao dịch

  @ApiProperty({
    description: 'Số tài khoản',
    example: '**********'
  })
  @IsString()
  account_number: string; // Số tài khoản

  @ApiProperty({
    description: 'ID tài khoản ngân hàng tương ứng',
    example: 'bank_account_123'
  })
  @IsString()
  bank_account_id: string; // ID tài khoản ngân hàng tương ứng

  @ApiProperty({
    description: 'Số VA (nếu có)',
    example: 'VA123456',
    required: false
  })
  @IsOptional()
  @IsString()
  va?: string; // Số VA (nếu có)

  @ApiProperty({
    description: 'Mã thanh toán (nếu có)',
    example: 'PAY123456',
    required: false
  })
  @IsOptional()
  @IsString()
  payment_code?: string; // Mã thanh toán (nếu có)

  @ApiProperty({
    description: 'Nội dung giao dịch',
    example: 'REDAI123456HUB'
  })
  @IsString()
  content: string; // Nội dung giao dịch

  @ApiProperty({
    description: 'Loại giao dịch (credit: tiền vào, debit: tiền ra)',
    example: 'credit',
    enum: ['credit', 'debit']
  })
  @IsString()
  transfer_type: string; // Loại giao dịch (credit: tiền vào, debit: tiền ra)

  @ApiProperty({
    description: 'Số tiền giao dịch (số không âm)',
    example: 100000
  })
  @Type(() => Number)
  @IsNumber()
  amount: number; // Số tiền giao dịch (số không âm)

  @ApiProperty({
    description: 'Mã tham chiếu FT',
    example: 'FT123456789'
  })
  @IsString()
  reference_code: string; // Mã tham chiếu FT

  @ApiProperty({
    description: 'Số dư sau giao dịch (giá trị mặc định là 0)',
    example: 5000000
  })
  @Type(() => Number)
  @IsNumber()
  accumulated: number; // Số dư sau giao dịch (giá trị mặc định là 0)

  @ApiProperty({
    description: 'ID giao dịch',
    example: 'TXN123456789'
  })
  @IsString()
  transaction_id: string; // ID giao dịch
}

/**
 * DTO cho phản hồi webhook SepayHub
 */
export class WebhookSepayHubResponseDto {
  @ApiProperty({
    description: 'Trạng thái xử lý webhook',
    example: true
  })
  success: boolean;

  @ApiProperty({
    description: 'Thông báo',
    example: 'Webhook processed successfully'
  })
  message: string;
}

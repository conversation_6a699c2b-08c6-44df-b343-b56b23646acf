import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBearerAuth,
  ApiBody,
} from '@nestjs/swagger';
import { JwtUserGuard } from '@modules/auth/guards/jwt-user.guard';
import { CurrentUser } from '@modules/auth/decorators/current-user.decorator';
import { JwtPayload } from '@modules/auth/guards/jwt.util';
import {
  CreateCalendarEventDto,
  UpdateCalendarEventDto,
} from '../../dto/calendar-event.dto';
import { CalendarEventQueryDto } from '../../dto/calendar-query.dto';
import { CalendarEventResponseDto } from '../../dto/calendar-response.dto';
import { ApiResponseDto } from '@common/response/api-response-dto';
import { CalendarService } from '../services';
import { SWAGGER_API_TAGS } from '@/common/swagger';
import { CalendarEvent } from '../../entities';

@ApiTags(SWAGGER_API_TAGS.CALENDAR_EVENTS)
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtUserGuard)
@Controller('calendar/events')
export class CalendarController {
  constructor(private readonly calendarService: CalendarService) {}

  @Post()
  @ApiOperation({ summary: 'Tạo sự kiện calendar mới' })
  @ApiBody({
    type: CreateCalendarEventDto,
    description: 'Dữ liệu tạo sự kiện calendar',
    examples: {
      'Nhiệm vụ hàng ngày': {
        summary: 'Tạo nhiệm vụ hàng ngày',
        description: 'Ví dụ tạo nhiệm vụ cho agent thực hiện hàng ngày',
        value: {
          eventType: 'task',
          title: 'Gửi báo cáo doanh số hàng ngày',
          description:
            'Agent tự động gửi báo cáo doanh số cho khách hàng vào 9h sáng mỗi ngày',
          startTime: '2024-01-15T09:00:00.000Z',
          endTime: '2024-01-15T09:30:00.000Z',
          timeZone: 'Asia/Ho_Chi_Minh',
          allDay: false,
          priority: 'high',
          color: '#FF6B6B',
          syncWithGoogle: true,
          metadata: {
            agentId: 'agent-123',
            taskType: 'report_generation',
            recipients: ['<EMAIL>'],
          },
        },
      },
      'Báo cáo tuần': {
        summary: 'Tạo báo cáo tuần',
        description: 'Ví dụ tạo báo cáo tự động hàng tuần',
        value: {
          eventType: 'report',
          title: 'Báo cáo hiệu suất bán hàng tuần',
          description:
            'Tạo và gửi báo cáo tổng hợp hiệu suất bán hàng trong tuần',
          startTime: '2024-01-15T17:00:00.000Z',
          timeZone: 'Asia/Ho_Chi_Minh',
          allDay: false,
          priority: 'medium',
          color: '#4ECDC4',
          metadata: {
            reportType: 'weekly_sales',
            includeCharts: true,
            format: 'pdf',
          },
        },
      },
      'Nhắc lịch họp': {
        summary: 'Tạo nhắc lịch họp',
        description: 'Ví dụ tạo nhắc nhở cho cuộc họp quan trọng',
        value: {
          eventType: 'reminder',
          title: 'Nhắc nhở: Họp team marketing',
          description: 'Nhắc nhở tham gia cuộc họp team marketing hàng tuần',
          startTime: '2024-01-16T14:00:00.000Z',
          endTime: '2024-01-16T15:30:00.000Z',
          timeZone: 'Asia/Ho_Chi_Minh',
          location: 'Phòng họp A - Tầng 3',
          meetingUrl: 'https://meet.google.com/abc-defg-hij',
          priority: 'high',
          color: '#45B7D1',
          syncWithGoogle: true,
          metadata: {
            reminderChannels: ['email', 'sms'],
            attendees: ['<EMAIL>'],
            agenda: 'Review chiến dịch Q1 2024',
          },
        },
      },
      'Sự kiện cả ngày': {
        summary: 'Tạo sự kiện cả ngày',
        description: 'Ví dụ tạo sự kiện diễn ra cả ngày',
        value: {
          eventType: 'reminder',
          title: 'Ngày nghỉ lễ - Tết Nguyên Đán',
          description: 'Ngày nghỉ lễ Tết Nguyên Đán 2024',
          startTime: '2024-02-10T00:00:00.000Z',
          timeZone: 'Asia/Ho_Chi_Minh',
          allDay: true,
          priority: 'low',
          color: '#FFA07A',
          metadata: {
            holidayType: 'national',
            autoReply: true,
          },
        },
      },
      'Meeting với Google Meet': {
        summary: 'Tạo meeting online',
        description: 'Ví dụ tạo cuộc họp online với Google Meet',
        value: {
          eventType: 'reminder',
          title: 'Demo sản phẩm cho khách hàng',
          description:
            'Trình bày demo tính năng mới của hệ thống RedAI cho khách hàng tiềm năng',
          startTime: '2024-01-17T10:00:00.000Z',
          endTime: '2024-01-17T11:00:00.000Z',
          timeZone: 'Asia/Ho_Chi_Minh',
          meetingUrl: 'https://meet.google.com/xyz-abcd-efgh',
          priority: 'urgent',
          color: '#9B59B6',
          syncWithGoogle: true,
          meetingData: {
            platform: 'google_meet',
            recordMeeting: true,
            allowScreenShare: true,
          },
          metadata: {
            customerInfo: {
              company: 'ABC Corp',
              contact: '<EMAIL>',
            },
            preparationNotes: 'Chuẩn bị slide demo và tài khoản test',
          },
        },
      },
      'Task với location cụ thể': {
        summary: 'Nhiệm vụ tại địa điểm cụ thể',
        description: 'Ví dụ tạo nhiệm vụ cần thực hiện tại một địa điểm cụ thể',
        value: {
          eventType: 'task',
          title: 'Kiểm tra hệ thống tại chi nhánh Hà Nội',
          description:
            'Agent thực hiện kiểm tra và bảo trì hệ thống tại chi nhánh',
          startTime: '2024-01-18T08:00:00.000Z',
          endTime: '2024-01-18T12:00:00.000Z',
          timeZone: 'Asia/Ho_Chi_Minh',
          location: '123 Đường ABC, Quận Ba Đình, Hà Nội',
          locationData: {
            address: '123 Đường ABC, Quận Ba Đình, Hà Nội',
            lat: 21.0285,
            lng: 105.8542,
            placeId: 'ChIJ0T2NLikpNTERKxE8d61aX_E',
          },
          priority: 'high',
          color: '#FF8C00',
          metadata: {
            taskType: 'maintenance',
            branchId: 'HN001',
            equipmentList: ['server', 'network', 'backup_system'],
            contactPerson: 'Nguyễn Văn A - 0901234567',
          },
        },
      },
      'Report với múi giờ khác': {
        summary: 'Báo cáo với múi giờ khác',
        description: 'Ví dụ tạo báo cáo cho khách hàng ở múi giờ khác',
        value: {
          eventType: 'report',
          title: 'Báo cáo doanh thu cho khách hàng Singapore',
          description:
            'Tạo và gửi báo cáo doanh thu hàng tháng cho khách hàng tại Singapore',
          startTime: '2024-01-20T02:00:00.000Z',
          timeZone: 'Asia/Singapore',
          priority: 'medium',
          color: '#32CD32',
          metadata: {
            reportType: 'monthly_revenue',
            currency: 'SGD',
            clientTimezone: 'Asia/Singapore',
            deliveryMethod: 'email',
            recipients: ['<EMAIL>'],
            language: 'en',
          },
        },
      },
      'Reminder không có thời gian kết thúc': {
        summary: 'Nhắc nhở đơn giản',
        description: 'Ví dụ tạo nhắc nhở đơn giản không cần thời gian kết thúc',
        value: {
          eventType: 'reminder',
          title: 'Nhắc nhở: Gia hạn license phần mềm',
          description: 'Nhắc nhở gia hạn license phần mềm trước khi hết hạn',
          startTime: '2024-01-25T09:00:00.000Z',
          timeZone: 'Asia/Ho_Chi_Minh',
          priority: 'urgent',
          color: '#DC143C',
          metadata: {
            reminderType: 'license_renewal',
            softwareName: 'RedAI Enterprise',
            expiryDate: '2024-01-30',
            renewalUrl: 'https://redai.com/renew',
            contactSupport: '<EMAIL>',
          },
        },
      },
      'Task với metadata phức tạp': {
        summary: 'Nhiệm vụ với cấu hình phức tạp',
        description: 'Ví dụ nhiệm vụ có metadata và cấu hình phức tạp',
        value: {
          eventType: 'task',
          title: 'Backup dữ liệu và đồng bộ cloud',
          description:
            'Agent thực hiện backup dữ liệu hệ thống và đồng bộ lên cloud storage',
          startTime: '2024-01-22T02:00:00.000Z',
          endTime: '2024-01-22T06:00:00.000Z',
          timeZone: 'Asia/Ho_Chi_Minh',
          priority: 'high',
          color: '#4169E1',
          metadata: {
            taskType: 'system_backup',
            backupTargets: ['database', 'files', 'configurations'],
            cloudProvider: 'aws_s3',
            encryptionEnabled: true,
            retentionDays: 30,
            notificationChannels: ['email', 'slack'],
            rollbackPlan: {
              enabled: true,
              maxRollbackHours: 24,
            },
            dependencies: ['database_maintenance', 'network_check'],
            estimatedDataSize: '500GB',
          },
        },
      },
      'Event tối thiểu': {
        summary: 'Sự kiện với thông tin tối thiểu',
        description: 'Ví dụ tạo sự kiện chỉ với các trường bắt buộc',
        value: {
          eventType: 'reminder',
          title: 'Sự kiện đơn giản',
          startTime: '2024-01-30T10:00:00.000Z',
        },
      },
      'Task lặp hàng ngày': {
        summary: 'Nhiệm vụ lặp lại hàng ngày',
        description: 'Ví dụ tạo nhiệm vụ tự động lặp lại hàng ngày',
        value: {
          eventType: 'task',
          title: 'Backup dữ liệu hàng ngày',
          description:
            'Agent tự động backup dữ liệu hệ thống vào 2h sáng mỗi ngày',
          startTime: '2024-01-15T02:00:00.000Z',
          endTime: '2024-01-15T03:00:00.000Z',
          timeZone: 'Asia/Ho_Chi_Minh',
          priority: 'high',
          color: '#FF6B6B',
          metadata: {
            taskType: 'daily_backup',
            recurrence: {
              type: 'daily',
              interval: 1,
              startDate: '2024-01-15',
              endDate: '2024-12-31',
              timeZone: 'Asia/Ho_Chi_Minh',
            },
            backupConfig: {
              targets: ['database', 'files'],
              retention: 7,
            },
          },
        },
      },
      'Report lặp hàng tuần': {
        summary: 'Báo cáo lặp lại hàng tuần',
        description: 'Ví dụ tạo báo cáo tự động hàng tuần vào thứ 2',
        value: {
          eventType: 'report',
          title: 'Báo cáo doanh số tuần',
          description:
            'Tạo và gửi báo cáo doanh số hàng tuần vào 9h sáng thứ 2',
          startTime: '2024-01-15T09:00:00.000Z',
          timeZone: 'Asia/Ho_Chi_Minh',
          priority: 'medium',
          color: '#4ECDC4',
          metadata: {
            reportType: 'weekly_sales',
            recurrence: {
              type: 'weekly',
              interval: 1,
              daysOfWeek: [2], // Monday = 2 (Sunday=1, Monday=2, ...)
              startDate: '2024-01-15',
              maxOccurrences: 52, // 1 năm
              timeZone: 'Asia/Ho_Chi_Minh',
            },
            recipients: ['<EMAIL>', '<EMAIL>'],
          },
        },
      },
      'Reminder lặp hàng tháng': {
        summary: 'Nhắc nhở lặp lại hàng tháng',
        description: 'Ví dụ nhắc nhở thanh toán hóa đơn vào ngày 1 hàng tháng',
        value: {
          eventType: 'reminder',
          title: 'Nhắc nhở: Thanh toán hóa đơn tháng',
          description: 'Nhắc nhở thanh toán các hóa đơn định kỳ hàng tháng',
          startTime: '2024-02-01T09:00:00.000Z',
          timeZone: 'Asia/Ho_Chi_Minh',
          priority: 'high',
          color: '#FFA500',
          metadata: {
            reminderType: 'monthly_payment',
            recurrence: {
              type: 'monthly',
              interval: 1,
              dayOfMonth: 1, // Ngày 1 hàng tháng
              startDate: '2024-02-01',
              timeZone: 'Asia/Ho_Chi_Minh',
            },
            paymentInfo: {
              bills: ['electricity', 'internet', 'office_rent'],
              totalEstimate: 5000000,
            },
          },
        },
      },
      'Task lặp hàng năm': {
        summary: 'Nhiệm vụ lặp lại hàng năm',
        description: 'Ví dụ nhiệm vụ bảo trì hệ thống hàng năm',
        value: {
          eventType: 'task',
          title: 'Bảo trì hệ thống hàng năm',
          description: 'Thực hiện bảo trì tổng thể hệ thống vào đầu năm',
          startTime: '2024-01-01T08:00:00.000Z',
          endTime: '2024-01-01T18:00:00.000Z',
          timeZone: 'Asia/Ho_Chi_Minh',
          priority: 'urgent',
          color: '#9B59B6',
          metadata: {
            taskType: 'annual_maintenance',
            recurrence: {
              type: 'yearly',
              interval: 1,
              monthOfYear: 1, // Tháng 1
              dayOfMonth: 1, // Ngày 1
              startDate: '2024-01-01',
              timeZone: 'Asia/Ho_Chi_Minh',
            },
            maintenanceScope: {
              hardware: true,
              software: true,
              security: true,
              performance: true,
            },
          },
        },
      },
      'Reminder lặp tùy chỉnh': {
        summary: 'Nhắc nhở với lịch tùy chỉnh',
        description: 'Ví dụ nhắc nhở mỗi 3 ngày một lần',
        value: {
          eventType: 'reminder',
          title: 'Kiểm tra email marketing',
          description:
            'Nhắc nhở kiểm tra và phản hồi email marketing mỗi 3 ngày',
          startTime: '2024-01-15T14:00:00.000Z',
          timeZone: 'Asia/Ho_Chi_Minh',
          priority: 'medium',
          color: '#17A2B8',
          metadata: {
            reminderType: 'custom_interval',
            recurrence: {
              type: 'daily',
              interval: 3, // Mỗi 3 ngày một lần
              startDate: '2024-01-15',
              maxOccurrences: 30, // 30 lần = 90 ngày
              timeZone: 'Asia/Ho_Chi_Minh',
            },
            taskDetails: {
              checkInbox: true,
              respondToQueries: true,
              updateCampaigns: false,
            },
          },
        },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Sự kiện đã được tạo thành công',
    type: CalendarEventResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Dữ liệu đầu vào không hợp lệ',
  })
  @ApiResponse({
    status: HttpStatus.CONFLICT,
    description: 'Sự kiện bị xung đột thời gian',
  })
  async createEvent(
    @CurrentUser() user: JwtPayload,
    @Body() createEventDto: CreateCalendarEventDto,
  ): Promise<ApiResponseDto<CalendarEventResponseDto>> {
    const event = await this.calendarService.createEvent(
      user.id,
      createEventDto,
    );

    return ApiResponseDto.created(
      this.mapToResponseDto(event),
      'Sự kiện đã được tạo thành công',
    );
  }

  @Get()
  @ApiOperation({ summary: 'Lấy danh sách sự kiện calendar' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Danh sách sự kiện đã được lấy thành công',
  })
  @ApiQuery({ name: 'page', required: false, description: 'Số trang' })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Số lượng mỗi trang',
  })
  @ApiQuery({ name: 'eventType', required: false, description: 'Loại sự kiện' })
  @ApiQuery({
    name: 'status',
    required: false,
    description: 'Trạng thái sự kiện',
  })
  @ApiQuery({
    name: 'startTimeFrom',
    required: false,
    description: 'Thời gian bắt đầu từ',
  })
  @ApiQuery({
    name: 'startTimeTo',
    required: false,
    description: 'Thời gian bắt đầu đến',
  })
  async getEvents(
    @CurrentUser() user: JwtPayload,
    @Query() queryDto: CalendarEventQueryDto,
  ): Promise<ApiResponseDto<any>> {
    const result = await this.calendarService.getEvents(user.id, queryDto);

    return ApiResponseDto.paginated(
      result,
      'Danh sách sự kiện đã được lấy thành công',
    );
  }

  @Get(':id')
  @ApiOperation({ summary: 'Lấy chi tiết sự kiện calendar' })
  @ApiParam({ name: 'id', description: 'ID sự kiện' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Chi tiết sự kiện đã được lấy thành công',
    type: CalendarEventResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Không tìm thấy sự kiện',
  })
  async getEventById(
    @CurrentUser() user: JwtPayload,
    @Param('id') eventId: string,
  ): Promise<ApiResponseDto<CalendarEventResponseDto>> {
    const event = await this.calendarService.getEventById(eventId, user.id);

    return ApiResponseDto.success(
      this.mapToResponseDto(event),
      'Chi tiết sự kiện đã được lấy thành công',
    );
  }

  @Put(':id')
  @ApiOperation({ summary: 'Cập nhật sự kiện calendar' })
  @ApiParam({ name: 'id', description: 'ID sự kiện' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Sự kiện đã được cập nhật thành công',
    type: CalendarEventResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Không tìm thấy sự kiện',
  })
  @ApiResponse({
    status: HttpStatus.CONFLICT,
    description: 'Sự kiện bị xung đột thời gian',
  })
  async updateEvent(
    @CurrentUser() user: JwtPayload,
    @Param('id') eventId: string,
    @Body() updateEventDto: UpdateCalendarEventDto,
  ): Promise<ApiResponseDto<CalendarEventResponseDto>> {
    const event = await this.calendarService.updateEvent(
      eventId,
      user.id,
      updateEventDto,
    );

    return ApiResponseDto.updated(
      this.mapToResponseDto(event),
      'Sự kiện đã được cập nhật thành công',
    );
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Xóa sự kiện calendar' })
  @ApiParam({ name: 'id', description: 'ID sự kiện' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Sự kiện đã được xóa thành công',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Không tìm thấy sự kiện',
  })
  async deleteEvent(
    @CurrentUser() user: JwtPayload,
    @Param('id') eventId: string,
  ): Promise<ApiResponseDto<null>> {
    await this.calendarService.deleteEvent(eventId, user.id);

    return ApiResponseDto.deleted(null, 'Sự kiện đã được xóa thành công');
  }

  @Get(':id/attendees')
  @ApiOperation({ summary: 'Lấy danh sách người tham gia sự kiện' })
  @ApiParam({ name: 'id', description: 'ID sự kiện' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Danh sách người tham gia đã được lấy thành công',
  })
  async getEventAttendees(
    @CurrentUser() user: JwtPayload,
    @Param('id') eventId: string,
  ): Promise<ApiResponseDto<any[]>> {
    const event = await this.calendarService.getEventById(eventId, user.id);

    return ApiResponseDto.success(
      event.actionConfig?.attendees || [],
      'Danh sách người tham gia đã được lấy thành công',
    );
  }

  @Get(':id/resources')
  @ApiOperation({ summary: 'Lấy danh sách tài nguyên sự kiện' })
  @ApiParam({ name: 'id', description: 'ID sự kiện' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Danh sách tài nguyên đã được lấy thành công',
  })
  async getEventResources(
    @CurrentUser() user: JwtPayload,
    @Param('id') eventId: string,
  ): Promise<ApiResponseDto<any[]>> {
    const event = await this.calendarService.getEventById(eventId, user.id);

    return ApiResponseDto.success(
      event.actionConfig?.resources || [],
      'Danh sách tài nguyên đã được lấy thành công',
    );
  }

  @Post(':id/execute')
  @ApiOperation({
    summary: 'Thực thi sự kiện calendar (task, reminder, report)',
  })
  @ApiParam({
    name: 'id',
    description: 'ID sự kiện',
    type: 'string',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Sự kiện đã được lên lịch thực thi thành công',
  })
  async executeEvent(
    @CurrentUser() user: JwtPayload,
    @Param('id') eventId: string,
  ): Promise<ApiResponseDto<void>> {
    await this.calendarService.executeEvent(eventId, user.id);

    return ApiResponseDto.success(
      undefined,
      'Sự kiện đã được lên lịch thực thi thành công',
    );
  }

  @Post(':id/cancel')
  @ApiOperation({ summary: 'Hủy thực thi sự kiện calendar' })
  @ApiParam({
    name: 'id',
    description: 'ID sự kiện',
    type: 'string',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Thực thi sự kiện đã được hủy thành công',
  })
  async cancelExecution(
    @CurrentUser() user: JwtPayload,
    @Param('id') eventId: string,
  ): Promise<ApiResponseDto<void>> {
    await this.calendarService.cancelExecution(eventId, user.id);

    return ApiResponseDto.success(
      undefined,
      'Thực thi sự kiện đã được hủy thành công',
    );
  }

  /**
   * Helper method để chuyển đổi CalendarEvent sang CalendarEventResponseDto
   */
  private mapToResponseDto(event: CalendarEvent): CalendarEventResponseDto {
    return {
      id: event.id,
      userId: event.userId,
      actionType: event.actionType,
      title: event.title,
      description: event.description,
      startTime: event.startTime,
      endTime: event.endTime,
      timeZone: event.timeZone,
      status: event.status,
      priority: event.priority,
      actionConfig: event.actionConfig,
      executionStatus: event.executionStatus,
      executionStartTime: event.executionStartTime,
      executionEndTime: event.executionEndTime,
      executionResult: event.executionResult,
      executionError: event.executionError,
      jobId: event.jobId,
      retryCount: event.retryCount,
      nextRetryTime: event.nextRetryTime,
      isActive: event.isActive,
      metadata: event.metadata,
      createdAt: event.createdAt,
      updatedAt: event.updatedAt,
      executionHistory:
        event.executionHistory?.map((history) => ({
          id: history.id,
          eventId: history.eventId,
          executionTime: history.executionTime,
          status: history.status as any, // Type mapping từ ExecutionHistoryStatus sang ExecutionStatus
          startTime: history.startTime,
          endTime: history.endTime,
          duration: history.duration,
          result: history.result,
          error: history.error,
          jobId: history.jobId,
          retryAttempt: history.retryAttempt,
          executionConfig: history.executionConfig,
          metadata: history.metadata,
          createdAt: history.createdAt,
        })) || [],
    } as CalendarEventResponseDto;
  }
}

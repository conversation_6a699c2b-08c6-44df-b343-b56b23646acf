import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsOptional, IsObject, IsBoolean, IsNumber, IsArray, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * DTO cho layout item (single widget layout)
 */
export class LayoutItemDto {
  @ApiProperty({
    description: 'Widget ID',
    example: 'widget-123'
  })
  @IsString()
  i: string;

  @ApiProperty({
    description: 'X coordinate trong grid',
    example: 0
  })
  @IsNumber()
  x: number;

  @ApiProperty({
    description: 'Y coordinate trong grid',
    example: 0
  })
  @IsNumber()
  y: number;

  @ApiProperty({
    description: 'Width trong grid units',
    example: 4
  })
  @IsNumber()
  w: number;

  @ApiProperty({
    description: 'Height trong grid units',
    example: 3
  })
  @IsNumber()
  h: number;

  @ApiPropertyOptional({
    description: 'Minimum width',
    example: 2
  })
  @IsOptional()
  @IsNumber()
  minW?: number;

  @ApiPropertyOptional({
    description: 'Minimum height',
    example: 2
  })
  @IsOptional()
  @IsNumber()
  minH?: number;

  @ApiPropertyOptional({
    description: 'Maximum width',
    example: 8
  })
  @IsOptional()
  @IsNumber()
  maxW?: number;

  @ApiPropertyOptional({
    description: 'Maximum height',
    example: 6
  })
  @IsOptional()
  @IsNumber()
  maxH?: number;

  @ApiPropertyOptional({
    description: 'Widget có static không (không thể move/resize)',
    example: false
  })
  @IsOptional()
  @IsBoolean()
  static?: boolean;

  @ApiPropertyOptional({
    description: 'Widget có thể drag không',
    example: true
  })
  @IsOptional()
  @IsBoolean()
  isDraggable?: boolean;

  @ApiPropertyOptional({
    description: 'Widget có thể resize không',
    example: true
  })
  @IsOptional()
  @IsBoolean()
  isResizable?: boolean;
}

/**
 * DTO cho grid configuration
 */
export class GridConfigDto {
  @ApiPropertyOptional({
    description: 'Row height trong pixels',
    example: 60
  })
  @IsOptional()
  @IsNumber()
  rowHeight?: number;

  @ApiPropertyOptional({
    description: 'Margin giữa các widgets [horizontal, vertical]',
    example: [8, 8]
  })
  @IsOptional()
  @IsArray()
  margin?: [number, number];

  @ApiPropertyOptional({
    description: 'Container padding [horizontal, vertical]',
    example: [0, 0]
  })
  @IsOptional()
  @IsArray()
  containerPadding?: [number, number];

  @ApiPropertyOptional({
    description: 'Số columns cho mỗi breakpoint',
    example: { lg: 12, md: 10, sm: 6, xs: 4, xxs: 2 }
  })
  @IsOptional()
  @IsObject()
  cols?: { lg: number; md: number; sm: number; xs: number; xxs: number };

  @ApiPropertyOptional({
    description: 'Compact type cho layout',
    example: 'vertical'
  })
  @IsOptional()
  @IsString()
  compactType?: 'vertical' | 'horizontal' | null;

  @ApiPropertyOptional({
    description: 'Prevent collision giữa widgets',
    example: false
  })
  @IsOptional()
  @IsBoolean()
  preventCollision?: boolean;

  @ApiPropertyOptional({
    description: 'Auto size container',
    example: true
  })
  @IsOptional()
  @IsBoolean()
  autoSize?: boolean;
}

/**
 * DTO cho cập nhật layout
 */
export class UpdateGenericLayoutDto {
  @ApiProperty({
    description: 'Layout data cho tất cả widgets',
    type: [LayoutItemDto]
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => LayoutItemDto)
  layoutData: LayoutItemDto[];

  @ApiPropertyOptional({
    description: 'Breakpoints configuration',
    example: { lg: [], md: [], sm: [], xs: [], xxs: [] }
  })
  @IsOptional()
  @IsObject()
  breakpoints?: {
    lg?: Array<any>;
    md?: Array<any>;
    sm?: Array<any>;
    xs?: Array<any>;
    xxs?: Array<any>;
  };

  @ApiPropertyOptional({
    description: 'Grid configuration settings',
    type: GridConfigDto
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => GridConfigDto)
  gridConfig?: GridConfigDto;

  @ApiPropertyOptional({
    description: 'Metadata bổ sung cho layout',
    example: { lastModifiedBy: 'system', reason: 'auto-layout' }
  })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}

/**
 * DTO cho response layout
 */
export class GenericLayoutResponseDto {
  @ApiProperty({
    description: 'ID của layout record',
    example: 'uuid-123-456'
  })
  id: string;

  @ApiProperty({
    description: 'Session ID',
    example: 'session-123-abc'
  })
  sessionId: string;

  @ApiProperty({
    description: 'Layout data',
    type: [LayoutItemDto]
  })
  layoutData: LayoutItemDto[];

  @ApiPropertyOptional({
    description: 'Breakpoints configuration',
    example: { lg: [], md: [], sm: [], xs: [], xxs: [] }
  })
  breakpoints?: {
    lg?: Array<any>;
    md?: Array<any>;
    sm?: Array<any>;
    xs?: Array<any>;
    xxs?: Array<any>;
  };

  @ApiPropertyOptional({
    description: 'Grid configuration settings',
    type: GridConfigDto
  })
  gridConfig?: GridConfigDto;

  @ApiProperty({
    description: 'Thời gian cập nhật',
    example: '2024-01-08T10:30:00Z'
  })
  updatedAt: Date;

  @ApiPropertyOptional({
    description: 'Metadata bổ sung',
    example: { lastModifiedBy: 'system', reason: 'auto-layout' }
  })
  metadata?: Record<string, any>;
}

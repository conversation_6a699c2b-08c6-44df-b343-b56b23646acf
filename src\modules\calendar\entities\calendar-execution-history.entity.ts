import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  ManyToOne,
  Join<PERSON><PERSON>umn,
} from 'typeorm';
import { CalendarEvent } from './calendar-event.entity';

/**
 * Enum định nghĩa trạng thái thực thi
 */
export enum ExecutionHistoryStatus {
  PENDING = 'pending',
  RUNNING = 'running',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
}

/**
 * Entity lưu lịch sử thực thi calendar events
 * Giúp tracking và debugging các lần thực thi
 */
@Entity('calendar_execution_history')
export class CalendarExecutionHistory {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  /**
   * ID sự kiện calendar liên quan
   */
  @Column({ name: 'event_id' })
  eventId: string;

  /**
   * Thời gian thực thi
   */
  @Column({ name: 'execution_time', type: 'timestamp' })
  executionTime: Date;

  /**
   * Trạng thái thực thi
   */
  @Column({
    type: 'enum',
    enum: ExecutionHistoryStatus,
    default: ExecutionHistoryStatus.PENDING,
  })
  status: ExecutionHistoryStatus;

  /**
   * Thời gian bắt đầu thực thi
   */
  @Column({ name: 'start_time', type: 'timestamp', nullable: true })
  startTime?: Date;

  /**
   * Thời gian kết thúc thực thi
   */
  @Column({ name: 'end_time', type: 'timestamp', nullable: true })
  endTime?: Date;

  /**
   * Thời gian thực thi (milliseconds)
   */
  @Column({ name: 'duration', type: 'integer', nullable: true })
  duration?: number;

  /**
   * Kết quả thực thi (JSONB)
   */
  @Column({ type: 'jsonb', nullable: true })
  result?: any;

  /**
   * Lỗi thực thi (nếu có)
   */
  @Column({ type: 'text', nullable: true })
  error?: string;

  /**
   * ID job trong queue
   */
  @Column({ name: 'job_id', nullable: true })
  jobId?: string;

  /**
   * Số lần retry của execution này
   */
  @Column({ name: 'retry_attempt', default: 0 })
  retryAttempt: number;

  /**
   * Cấu hình thực thi tại thời điểm chạy (JSONB)
   * Snapshot của actionConfig để debug
   */
  @Column({ name: 'execution_config', type: 'jsonb', nullable: true })
  executionConfig?: any;

  /**
   * Metadata bổ sung (JSONB)
   * Có thể chứa thông tin về environment, version, etc.
   */
  @Column({ type: 'jsonb', nullable: true })
  metadata?: any;

  /**
   * Thời gian tạo record
   */
  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  // Relations
  @ManyToOne(() => CalendarEvent)
  @JoinColumn({ name: 'event_id' })
  event: CalendarEvent;
}

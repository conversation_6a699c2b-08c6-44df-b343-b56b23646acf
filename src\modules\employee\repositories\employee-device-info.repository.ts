import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { EmployeeDeviceInfo } from '../entities/employee-device-info.entity';
import { v4 as uuidv4 } from 'uuid';

/**
 * Repository cho EmployeeDeviceInfo entity
 */
@Injectable()
export class EmployeeDeviceInfoRepository {
  constructor(
    @InjectRepository(EmployeeDeviceInfo)
    private readonly repository: Repository<EmployeeDeviceInfo>,
  ) {}

  /**
   * Tìm thiết bị theo fingerprint và employee ID
   * @param fingerprint Fingerprint của thiết bị
   * @param employeeId ID của nhân viên
   * @returns Thông tin thiết bị hoặc null
   */
  async findByFingerprintAndEmployeeId(
    fingerprint: string,
    employeeId: number,
  ): Promise<EmployeeDeviceInfo | null> {
    return this.repository.findOne({
      where: { fingerprint, employeeId },
    });
  }

  /**
   * Tì<PERSON> tất cả thiết bị của nhân viên
   * @param employeeId ID của nhân viên
   * @returns Danh sách thiết bị
   */
  async findByEmployeeId(employeeId: number): Promise<EmployeeDeviceInfo[]> {
    return this.repository.find({
      where: { employeeId },
      order: { lastLogin: 'DESC' },
    });
  }

  /**
   * Tìm hoặc tạo thiết bị
   * @param fingerprint Fingerprint của thiết bị
   * @param employeeId ID của nhân viên
   * @param ipAddress Địa chỉ IP
   * @param userAgent User agent
   * @param browserInfo Thông tin trình duyệt và OS
   * @returns Thông tin thiết bị
   */
  async findOrCreate(
    fingerprint: string,
    employeeId: number,
    ipAddress?: string,
    userAgent?: string,
    browserInfo?: { browser?: string; os?: string },
  ): Promise<EmployeeDeviceInfo> {
    // Tìm thiết bị hiện có
    const device = await this.findByFingerprintAndEmployeeId(fingerprint, employeeId);

    if (device) {
      // Cập nhật thông tin đăng nhập gần nhất
      device.lastLogin = Date.now();
      device.updatedAt = Date.now();
      if (ipAddress) device.ipAddress = ipAddress;
      if (userAgent) device.userAgent = userAgent;
      if (browserInfo?.browser) device.browser = browserInfo.browser;
      if (browserInfo?.os) device.operatingSystem = browserInfo.os;
      return this.repository.save(device);
    }

    // Tạo thiết bị mới nếu chưa tồn tại
    const now = Date.now();
    return this.create({
      id: uuidv4(),
      fingerprint,
      employeeId,
      ipAddress,
      userAgent,
      browser: browserInfo?.browser,
      operatingSystem: browserInfo?.os,
      isTrusted: false, // Mặc định thiết bị chưa đáng tin cậy
      lastLogin: now,
      createdAt: now,
      updatedAt: now,
    });
  }

  /**
   * Tìm thông tin thiết bị theo ID
   * @param id ID của thiết bị
   * @returns Thông tin thiết bị
   */
  async findById(id: string): Promise<EmployeeDeviceInfo | null> {
    return this.repository.findOne({ where: { id } });
  }

  /**
   * Lưu thông tin thiết bị
   * @param device Entity cần lưu
   * @returns Entity đã lưu
   */
  async save(device: EmployeeDeviceInfo): Promise<EmployeeDeviceInfo> {
    device.updatedAt = Date.now();
    return this.repository.save(device);
  }

  /**
   * Tạo thông tin thiết bị mới
   * @param data Dữ liệu tạo
   * @returns Entity đã tạo
   */
  create(data: Partial<EmployeeDeviceInfo>): EmployeeDeviceInfo {
    return this.repository.create(data);
  }

  /**
   * Xóa thông tin thiết bị
   * @param device Entity cần xóa
   */
  async remove(device: EmployeeDeviceInfo): Promise<void> {
    await this.repository.remove(device);
  }

  /**
   * Xóa thiết bị theo ID
   * @param id ID của thiết bị
   */
  async removeById(id: string): Promise<void> {
    await this.repository.delete(id);
  }
}

import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
} from 'typeorm';
import { User } from '@modules/user/entities/user.entity';
import { CalendarExecutionHistory } from './calendar-execution-history.entity';

/**
 * Enum định nghĩa các loại action calendar
 */
export enum CalendarActionType {
  /**
   * Nhiệm vụ - dùng để hẹn giờ cho agent thực hiện nhiệm vụ
   */
  TASK = 'task',

  /**
   * Nhắc nhở - dùng để nhắc nhở cho user
   */
  REMINDER = 'reminder',

  /**
   * Báo cáo - dùng để tạo và gửi báo cáo theo lịch
   */
  REPORT = 'report',
}

/**
 * Enum định nghĩa trạng thái sự kiện
 */
export enum CalendarEventStatus {
  SCHEDULED = 'scheduled',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
}

/**
 * Enum định nghĩa trạng thái thực thi
 */
export enum ExecutionStatus {
  PENDING = 'pending',
  RUNNING = 'running',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
}

/**
 * Enum định nghĩa mức độ ưu tiên
 */
export enum CalendarPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  URGENT = 'urgent',
}

/**
 * Entity tối ưu cho sự kiện calendar - gộp chung task, reminder, report
 * Thay thế cho CalendarEvent, CalendarTask, CalendarReminder, CalendarReport
 */
@Entity('calendar_events')
export class CalendarEvent {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  /**
   * ID người dùng tạo sự kiện
   */
  @Column({ name: 'user_id' })
  userId: number;

  /**
   * Loại action (task, reminder, report)
   */
  @Column({
    type: 'enum',
    enum: CalendarActionType,
    name: 'action_type',
  })
  actionType: CalendarActionType;

  /**
   * Tiêu đề sự kiện
   */
  @Column({ length: 255 })
  title: string;

  /**
   * Mô tả sự kiện
   */
  @Column({ type: 'text', nullable: true })
  description?: string;

  /**
   * Thời gian bắt đầu
   */
  @Column({ name: 'start_time', type: 'timestamp' })
  startTime: Date;

  /**
   * Thời gian kết thúc (tùy chọn)
   */
  @Column({ name: 'end_time', type: 'timestamp', nullable: true })
  endTime?: Date;

  /**
   * Múi giờ
   */
  @Column({ name: 'time_zone', length: 50, default: 'Asia/Ho_Chi_Minh' })
  timeZone: string;

  /**
   * Trạng thái sự kiện
   */
  @Column({
    type: 'enum',
    enum: CalendarEventStatus,
    default: CalendarEventStatus.SCHEDULED,
  })
  status: CalendarEventStatus;

  /**
   * Mức độ ưu tiên
   */
  @Column({
    type: 'enum',
    enum: CalendarPriority,
    default: CalendarPriority.MEDIUM,
  })
  priority: CalendarPriority;

  /**
   * Cấu hình action cụ thể (JSONB)
   * - TASK: { agentId, taskId, resources, executionConfig }
   * - REMINDER: { channels, message, template, reminderMinutes, recipients }
   * - REPORT: { reportType, config, dataSources, channels, format, recipients }
   */
  @Column({ name: 'action_config', type: 'jsonb' })
  actionConfig: any;

  /**
   * Trạng thái thực thi
   */
  @Column({
    type: 'enum',
    enum: ExecutionStatus,
    name: 'execution_status',
    default: ExecutionStatus.PENDING,
  })
  executionStatus: ExecutionStatus;

  /**
   * Thời gian bắt đầu thực thi
   */
  @Column({ name: 'execution_start_time', type: 'timestamp', nullable: true })
  executionStartTime?: Date;

  /**
   * Thời gian kết thúc thực thi
   */
  @Column({ name: 'execution_end_time', type: 'timestamp', nullable: true })
  executionEndTime?: Date;

  /**
   * Kết quả thực thi (JSONB)
   */
  @Column({ name: 'execution_result', type: 'jsonb', nullable: true })
  executionResult?: any;

  /**
   * Lỗi thực thi (nếu có)
   */
  @Column({ name: 'execution_error', type: 'text', nullable: true })
  executionError?: string;

  /**
   * ID job trong queue
   */
  @Column({ name: 'job_id', nullable: true })
  jobId?: string;

  /**
   * Số lần retry
   */
  @Column({ name: 'retry_count', default: 0 })
  retryCount: number;

  /**
   * Thời gian retry tiếp theo
   */
  @Column({ name: 'next_retry_time', type: 'timestamp', nullable: true })
  nextRetryTime?: Date;

  /**
   * Có được kích hoạt không
   */
  @Column({ name: 'is_active', default: true })
  isActive: boolean;

  /**
   * Metadata bổ sung (JSONB)
   */
  @Column({ type: 'jsonb', nullable: true })
  metadata?: any;

  /**
   * Thời gian tạo
   */
  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  /**
   * Thời gian cập nhật
   */
  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // Relations
  @ManyToOne(() => User)
  @JoinColumn({ name: 'user_id' })
  user: User;

  @OneToMany(() => CalendarExecutionHistory, (execution) => execution.event)
  executionHistory: CalendarExecutionHistory[];
}


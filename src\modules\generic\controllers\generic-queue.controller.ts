import { 
  Controller, 
  Get, 
  Post, 
  Delete, 
  Body, 
  Param, 
  Query, 
  UseGuards,
  Logger 
} from '@nestjs/common';
import { 
  ApiTags, 
  ApiOperation, 
  ApiResponse, 
  ApiParam, 
  ApiQuery,
  ApiBearerAuth 
} from '@nestjs/swagger';
import { GenericQueueService } from '../services';
import { GenericWebSocketGateway } from '../gateways/generic-websocket.gateway';
import { ApiResponseDto } from '@/common/response';
import { GenericPageJobPriority } from '../constants/generic-queue.constants';

/**
 * Controller cho Generic Queue Management
 * Quản lý queue operations và broadcasting (Admin only)
 */
@ApiTags('Generic Queue (Admin)')
@Controller('admin/generic/queue')
export class GenericQueueController {
  private readonly logger = new Logger(GenericQueueController.name);

  constructor(
    private readonly genericQueueService: GenericQueueService,
    private readonly genericWebSocketGateway: GenericWebSocketGateway,
  ) {}

  /**
   * <PERSON><PERSON><PERSON> thống kê queue
   */
  @Get('stats')
  @ApiOperation({ 
    summary: '<PERSON><PERSON>y thống kê queue',
    description: 'Lấy thống kê số lượng jobs trong queue'
  })
  @ApiResponse({
    status: 200,
    description: 'Thống kê queue'
  })
  async getQueueStats(): Promise<ApiResponseDto<{
    waiting: number;
    active: number;
    completed: number;
    failed: number;
    delayed: number;
  }>> {
    try {
      const stats = await this.genericQueueService.getQueueStats();
      
      return ApiResponseDto.success(stats, 'Lấy thống kê queue thành công');
    } catch (error) {
      this.logger.error(`Error getting queue stats: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Lấy thống kê WebSocket connections
   */
  @Get('connections')
  @ApiOperation({ 
    summary: 'Lấy thống kê WebSocket connections',
    description: 'Lấy số lượng connections hiện tại'
  })
  @ApiResponse({
    status: 200,
    description: 'Thống kê connections'
  })
  async getConnectionStats(): Promise<ApiResponseDto<{ total: number; sessions: number }>> {
    try {
      const stats = this.genericWebSocketGateway.getConnectionCount();
      
      return ApiResponseDto.success(stats, 'Lấy thống kê connections thành công');
    } catch (error) {
      this.logger.error(`Error getting connection stats: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Broadcast event đến session cụ thể
   */
  @Post('broadcast/session/:sessionId')
  @ApiOperation({ 
    summary: 'Broadcast đến session',
    description: 'Broadcast event đến session cụ thể'
  })
  @ApiParam({
    name: 'sessionId',
    description: 'Session ID',
    example: 'session-123-abc'
  })
  @ApiResponse({
    status: 200,
    description: 'Event đã được broadcast'
  })
  async broadcastToSession(
    @Param('sessionId') sessionId: string,
    @Body() body: {
      event: {
        type: string;
        payload: any;
      };
      useQueue?: boolean;
    }
  ): Promise<ApiResponseDto<{ success: boolean }>> {
    try {
      if (body.useQueue) {
        // Sử dụng queue
        await this.genericQueueService.broadcastToSessionJob(
          sessionId,
          body.event,
          {
            priority: GenericPageJobPriority.HIGH,
            triggeredBy: 'admin',
          }
        );
      } else {
        // Broadcast trực tiếp
        this.genericWebSocketGateway.broadcastToSession(sessionId, {
          ...body.event,
          sessionId,
          timestamp: new Date().toISOString(),
        });
      }
      
      this.logger.log(`Broadcasted event to session ${sessionId}`);
      
      return ApiResponseDto.success({ success: true }, 'Event đã được broadcast thành công');
    } catch (error) {
      this.logger.error(`Error broadcasting to session: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Broadcast event đến tất cả sessions
   */
  @Post('broadcast/all')
  @ApiOperation({ 
    summary: 'Broadcast đến tất cả sessions',
    description: 'Broadcast event đến tất cả sessions active'
  })
  @ApiResponse({
    status: 200,
    description: 'Event đã được broadcast'
  })
  async broadcastToAll(
    @Body() body: {
      event: {
        type: string;
        payload: any;
      };
      excludeSessions?: string[];
      useQueue?: boolean;
    }
  ): Promise<ApiResponseDto<{ success: boolean }>> {
    try {
      if (body.useQueue) {
        // Sử dụng queue
        await this.genericQueueService.broadcastToAllJob(
          body.event,
          {
            excludeSessions: body.excludeSessions,
            priority: GenericPageJobPriority.HIGH,
            triggeredBy: 'admin',
          }
        );
      } else {
        // Broadcast trực tiếp
        this.genericWebSocketGateway.broadcastToAll({
          ...body.event,
          timestamp: new Date().toISOString(),
        });
      }
      
      this.logger.log('Broadcasted event to all sessions');
      
      return ApiResponseDto.success(
        { success: true },
        'Event đã được broadcast đến tất cả sessions'
      );
    } catch (error) {
      this.logger.error(`Error broadcasting to all: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Trigger cleanup expired sessions job
   */
  @Post('cleanup-sessions')
  @ApiOperation({ 
    summary: 'Cleanup expired sessions',
    description: 'Trigger job cleanup sessions hết hạn'
  })
  @ApiResponse({
    status: 200,
    description: 'Cleanup job đã được trigger'
  })
  async triggerCleanupSessions(
    @Body() body?: { maxAge?: number }
  ): Promise<ApiResponseDto<{ success: boolean }>> {
    try {
      await this.genericQueueService.cleanupExpiredSessionsJob({
        maxAge: body?.maxAge,
        priority: GenericPageJobPriority.NORMAL,
        triggeredBy: 'admin',
      });
      
      this.logger.log('Triggered cleanup expired sessions job');
      
      return ApiResponseDto.success(
        { success: true },
        'Cleanup job đã được trigger thành công'
      );
    } catch (error) {
      this.logger.error(`Error triggering cleanup: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Trigger sync state job cho session
   */
  @Post('sync-state/:sessionId')
  @ApiOperation({ 
    summary: 'Sync state cho session',
    description: 'Trigger job sync state cho session cụ thể'
  })
  @ApiParam({
    name: 'sessionId',
    description: 'Session ID',
    example: 'session-123-abc'
  })
  @ApiResponse({
    status: 200,
    description: 'Sync state job đã được trigger'
  })
  async triggerSyncState(
    @Param('sessionId') sessionId: string
  ): Promise<ApiResponseDto<{ success: boolean }>> {
    try {
      await this.genericQueueService.syncStateJob(sessionId, {
        priority: GenericPageJobPriority.HIGH,
        triggeredBy: 'admin',
      });
      
      this.logger.log(`Triggered sync state job for session ${sessionId}`);
      
      return ApiResponseDto.success(
        { success: true },
        'Sync state job đã được trigger thành công'
      );
    } catch (error) {
      this.logger.error(`Error triggering sync state: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Pause queue
   */
  @Post('pause')
  @ApiOperation({ 
    summary: 'Pause queue',
    description: 'Tạm dừng xử lý queue jobs'
  })
  @ApiResponse({
    status: 200,
    description: 'Queue đã được pause'
  })
  async pauseQueue(): Promise<ApiResponseDto<{ success: boolean }>> {
    try {
      await this.genericQueueService.pauseQueue();
      
      this.logger.log('Queue paused');
      
      return ApiResponseDto.success(
        { success: true },
        'Queue đã được pause thành công'
      );
    } catch (error) {
      this.logger.error(`Error pausing queue: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Resume queue
   */
  @Post('resume')
  @ApiOperation({ 
    summary: 'Resume queue',
    description: 'Tiếp tục xử lý queue jobs'
  })
  @ApiResponse({
    status: 200,
    description: 'Queue đã được resume'
  })
  async resumeQueue(): Promise<ApiResponseDto<{ success: boolean }>> {
    try {
      await this.genericQueueService.resumeQueue();
      
      this.logger.log('Queue resumed');
      
      return ApiResponseDto.success(
        { success: true },
        'Queue đã được resume thành công'
      );
    } catch (error) {
      this.logger.error(`Error resuming queue: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Clear queue (Danger!)
   */
  @Delete('clear')
  @ApiOperation({ 
    summary: 'Clear queue (Danger!)',
    description: 'Xóa tất cả jobs trong queue - CHỈ SỬ DỤNG KHI CẦN THIẾT!'
  })
  @ApiResponse({
    status: 200,
    description: 'Queue đã được clear'
  })
  async clearQueue(): Promise<ApiResponseDto<{ success: boolean }>> {
    try {
      await this.genericQueueService.clearQueue();
      
      this.logger.warn('Queue cleared by admin');
      
      return ApiResponseDto.success(
        { success: true },
        'Queue đã được clear thành công'
      );
    } catch (error) {
      this.logger.error(`Error clearing queue: ${error.message}`, error.stack);
      throw error;
    }
  }
}

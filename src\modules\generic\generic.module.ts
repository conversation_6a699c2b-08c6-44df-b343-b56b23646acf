import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BullModule } from '@nestjs/bullmq';
import { ScheduleModule } from '@nestjs/schedule';
import { GenericAdminModule } from './admin/generic-admin.module';
import { GenericUserModule } from './user/generic-user.module';
import {
  GenericPage,
  GenericPageTemplate,
  GenericSession,
  GenericSessionWidget,
  GenericSessionLayout,
} from './entities';
import {
  GenericPageRepository,
  GenericPageTemplateRepository,
  GenericSessionRepository,
  GenericSessionWidgetRepository,
  GenericSessionLayoutRepository,
} from './repositories';
import {
  GenericSessionService,
  GenericWidgetService,
  GenericLayoutService,
  GenericQueueService,
  GenericRecoveryService,
} from './services';
import { GenericWebSocketGateway } from './gateways/generic-websocket.gateway';
import { GenericPageWorker } from './workers/generic-page.worker';
import {
  GenericSessionController,
  GenericWidgetController,
  GenericLayoutController,
  GenericQueueController,
} from './controllers';
import { GENERIC_PAGE_QUEUE } from './constants/generic-queue.constants';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      GenericPage,
      GenericPageTemplate,
      GenericSession,
      GenericSessionWidget,
      GenericSessionLayout,
    ]),
    BullModule.registerQueue({
      name: GENERIC_PAGE_QUEUE,
    }),
    ScheduleModule.forRoot(),
    GenericAdminModule,
    GenericUserModule,
  ],
  providers: [
    GenericPageRepository,
    GenericPageTemplateRepository,
    GenericSessionRepository,
    GenericSessionWidgetRepository,
    GenericSessionLayoutRepository,
    GenericSessionService,
    GenericWidgetService,
    GenericLayoutService,
    GenericQueueService,
    GenericRecoveryService,
    GenericWebSocketGateway,
    GenericPageWorker,
  ],
  controllers: [
    GenericSessionController,
    GenericWidgetController,
    GenericLayoutController,
    GenericQueueController,
  ],
  exports: [GenericAdminModule, GenericUserModule, TypeOrmModule],
})
export class GenericModule {}

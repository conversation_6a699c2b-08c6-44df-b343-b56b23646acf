/**
 * @file Base Integration Node Interfaces
 * 
 * Định nghĩa base interfaces cho tất cả integration nodes
 * Theo patterns từ Make.com và n8n industry standards
 * 
 * @version 1.0.0
 * <AUTHOR> Assistant
 */

import {
    IBaseNodeInput,
    IBaseNodeOutput
} from '../../execute.interface';

import {
    ECredentialName,
    ENodeAuthType
} from '../../node-manager.interface';

// =================================================================
// SECTION 1: BASE INTEGRATION ENUMS
// =================================================================

/**
 * Enum định nghĩa các loại operation cho integration nodes
 * Theo Make.com module types: Triggers, Actions, Searches
 */
export enum EIntegrationOperationType {
    // === TRIGGERS ===
    /** Watch for new items (polling trigger) */
    WATCH = 'watch',
    /** Instant webhook trigger */
    WEBHOOK = 'webhook',

    // === ACTIONS ===
    /** Create new item */
    CREATE = 'create',
    /** Update existing item */
    UPDATE = 'update',
    /** Delete item */
    DELETE = 'delete',
    /** Get single item by ID */
    GET = 'get',

    // === SEARCHES ===
    /** Search items with filters */
    SEARCH = 'search',
    /** List all items */
    LIST = 'list',

    // === UNIVERSAL ===
    /** Custom API call */
    API_CALL = 'apiCall'
}

/**
 * Enum định nghĩa error handling strategies cho integration nodes
 */
export enum EIntegrationErrorHandling {
    /** Stop workflow on error */
    STOP = 'stop',
    /** Continue workflow, skip failed items */
    CONTINUE = 'continue',
    /** Retry failed operations */
    RETRY = 'retry'
}

/**
 * Enum định nghĩa data formats
 */
export enum EDataFormat {
    /** JSON format */
    JSON = 'json',
    /** Raw text */
    TEXT = 'text',
    /** Binary data */
    BINARY = 'binary',
    /** Form data */
    FORM_DATA = 'formData'
}

// =================================================================
// SECTION 2: BASE INTEGRATION INTERFACES
// =================================================================

/**
 * Base interface cho tất cả integration node parameters
 */
export interface IBaseIntegrationParameters {
    /** ID của integration từ integration entity */
    integration_id: string;
    
    /** Loại operation */
    operation: string;

    /** Error handling strategy */
    error_handling?: EIntegrationErrorHandling;

    /** Timeout cho API calls (seconds) */
    timeout?: number;

    /** Retry attempts cho failed operations */
    retry_attempts?: number;

    /** Delay giữa retry attempts (milliseconds) */
    retry_delay?: number;
}

/**
 * Interface cho trigger parameters
 */
export interface ITriggerParameters extends IBaseIntegrationParameters {
    operation: string; // Allow any string operation for flexibility

    /** Polling interval cho watch triggers (minutes) */
    polling_interval?: number;

    /** Limit số items mỗi lần poll */
    limit?: number;

    /** Filter conditions */
    filters?: Record<string, any>;
}

/**
 * Interface cho action parameters
 */
export interface IActionParameters extends IBaseIntegrationParameters {
    operation: string; // Allow any string operation for flexibility

    /** Data để gửi (cho CREATE/UPDATE) */
    data?: Record<string, any>;

    /** ID của item (cho GET/UPDATE/DELETE) */
    item_id?: string;

    /** Fields để return */
    fields?: string[];
}

/**
 * Interface cho search parameters
 */
export interface ISearchParameters extends IBaseIntegrationParameters {
    operation: string; // Allow any string operation for flexibility

    /** Search query */
    query?: string;

    /** Filter conditions */
    filters?: Record<string, any>;

    /** Sort order */
    sort?: {
        field: string;
        direction: 'asc' | 'desc';
    };

    /** Limit results */
    limit?: number;

    /** Offset for pagination */
    offset?: number;
}

/**
 * Interface cho API call parameters
 */
export interface IApiCallParameters extends IBaseIntegrationParameters {
    operation: EIntegrationOperationType.API_CALL;

    /** HTTP method */
    method: 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';

    /** API endpoint path */
    endpoint: string;

    /** Request headers */
    headers?: Record<string, string>;

    /** Request body */
    body?: any;

    /** Query parameters */
    query_params?: Record<string, string>;

    /** Response data format */
    response_format?: EDataFormat;
}

// =================================================================
// SECTION 3: INPUT/OUTPUT INTERFACES
// =================================================================

/**
 * Base input interface cho integration nodes
 */
export interface IBaseIntegrationInput extends IBaseNodeInput {
    /** Integration connection data */
    integration?: {
        id: string;
        type: string;
        credentials: Record<string, any>;
    };
}

/**
 * Base output interface cho integration nodes
 */
export interface IBaseIntegrationOutput extends IBaseNodeOutput {
    /** Operation result */
    success: boolean;

    /** Result data */
    data?: any;

    /** Error information */
    error?: {
        code: string;
        message: string;
        details?: any;
    };

    /** Metadata */
    metadata?: {
        operation: string;
        timestamp: number;
        execution_time: number;
        items_processed?: number;
    };
}

// =================================================================
// SECTION 4: CREDENTIAL DEFINITIONS
// =================================================================

/**
 * Base credential definition cho integration providers
 */
export interface IBaseIntegrationCredential {
    /** Provider name */
    provider: string;

    /** Credential name */
    name: ECredentialName;

    /** Display name */
    displayName: string;

    /** Description */
    description: string;

    /** Required flag */
    required: boolean;

    /** Auth type */
    authType: ENodeAuthType;

    /** Test connection support */
    testable?: boolean;

    /** Test URL */
    testUrl?: string;
}

// =================================================================
// SECTION 5: VALIDATION FUNCTIONS
// =================================================================

/**
 * Validate base integration parameters
 */
export function validateBaseIntegrationParameters(
    params: Partial<IBaseIntegrationParameters>
): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!params.integration_id) {
        errors.push('Integration ID is required');
    }

    if (!params.operation) {
        errors.push('Operation is required');
    }

    if (params.timeout && (params.timeout < 1 || params.timeout > 300)) {
        errors.push('Timeout must be between 1 and 300 seconds');
    }

    if (params.retry_attempts && (params.retry_attempts < 0 || params.retry_attempts > 10)) {
        errors.push('Retry attempts must be between 0 and 10');
    }

    return {
        isValid: errors.length === 0,
        errors
    };
}

/**
 * Type guard cho trigger parameters
 */
export function isTriggerParameters(params: any): params is ITriggerParameters {
    return params && (
        params.operation === EIntegrationOperationType.WATCH ||
        params.operation === EIntegrationOperationType.WEBHOOK
    );
}

/**
 * Type guard cho action parameters
 */
export function isActionParameters(params: any): params is IActionParameters {
    return params && [
        EIntegrationOperationType.CREATE,
        EIntegrationOperationType.UPDATE,
        EIntegrationOperationType.DELETE,
        EIntegrationOperationType.GET
    ].includes(params.operation);
}

/**
 * Type guard cho search parameters
 */
export function isSearchParameters(params: any): params is ISearchParameters {
    return params && (
        params.operation === EIntegrationOperationType.SEARCH ||
        params.operation === EIntegrationOperationType.LIST
    );
}

/**
 * Type guard cho integration API call parameters
 */
export function isIntegrationApiCallParameters(params: any): params is IApiCallParameters {
    return params && params.operation === EIntegrationOperationType.API_CALL;
}

/**
 * @file Enhanced Delayed Job Manager
 * 
 * Service quản lý delayed jobs với CRON support
 * Handle tất cả schedule types including CRON expressions
 * 
 * @version 1.0.0
 * <AUTHOR> Assistant
 */

import { Injectable, Logger } from '@nestjs/common';
import { InjectQueue } from '@nestjs/bull';
import { Queue, Job } from 'bull';
import { DateTime } from 'luxon';
import { IScheduleParameters, EScheduleType, EIntervalType } from '../../interfaces/core/utility/schedule.interface';
import { CronHandlerService } from './cron-handler.service';

/**
 * Interface cho job data
 */
interface IScheduledJobData {
    nodeId: string;
    workflowId: string;
    scheduleConfig: IScheduleParameters;
    executionTime: number;
    isRecurring: boolean;
    scheduleType: EScheduleType;
    originalCronExpression?: string; // For CRON jobs
}

/**
 * Interface cho job creation result
 */
interface IJobCreationResult {
    jobId: string;
    nextExecutionTime: number;
    delay: number;
    isValid: boolean;
    error?: string;
}

@Injectable()
export class EnhancedDelayedJobManagerService {
    private readonly logger = new Logger(EnhancedDelayedJobManagerService.name);
    
    constructor(
        @InjectQueue('workflow-execution')
        private readonly workflowQueue: Queue,
        
        private readonly cronHandlerService: CronHandlerService
    ) {}
    
    /**
     * Create hoặc update delayed job cho schedule node
     */
    async createOrUpdateDelayedJob(
        nodeId: string,
        workflowId: string,
        scheduleConfig: IScheduleParameters
    ): Promise<IJobCreationResult> {
        try {
            // Cancel existing job if exists
            if (scheduleConfig.jobId) {
                await this.cancelJob(scheduleConfig.jobId);
            }
            
            // Calculate next execution time based on schedule type
            const executionInfo = await this.calculateNextExecutionTime(scheduleConfig);
            
            if (!executionInfo) {
                return {
                    jobId: '',
                    nextExecutionTime: 0,
                    delay: 0,
                    isValid: false,
                    error: 'Failed to calculate execution time'
                };
            }
            
            // Create job data
            const jobData: IScheduledJobData = {
                nodeId,
                workflowId,
                scheduleConfig,
                executionTime: executionInfo.nextExecutionTime,
                isRecurring: this.isRecurringSchedule(scheduleConfig),
                scheduleType: scheduleConfig.schedule_type,
                originalCronExpression: scheduleConfig.schedule_type === EScheduleType.CRON 
                    ? scheduleConfig.cron_config?.expression 
                    : undefined
            };
            
            // Create delayed job
            const job = await this.workflowQueue.add(
                'execute-scheduled-workflow',
                jobData,
                {
                    delay: executionInfo.delay,
                    attempts: 3,
                    backoff: {
                        type: 'exponential',
                        delay: 60000
                    },
                    removeOnComplete: 10,
                    removeOnFail: 5,
                    jobId: `schedule-${nodeId}-${Date.now()}` // Unique job ID
                }
            );
            
            this.logger.log(`Created delayed job: ${job.id} for node: ${nodeId}, ` +
                          `type: ${scheduleConfig.schedule_type}, delay: ${executionInfo.delay}ms`);
            
            return {
                jobId: job.id.toString(),
                nextExecutionTime: executionInfo.nextExecutionTime,
                delay: executionInfo.delay,
                isValid: true
            };
            
        } catch (error) {
            this.logger.error(`Failed to create delayed job for node: ${nodeId}`, error);
            return {
                jobId: '',
                nextExecutionTime: 0,
                delay: 0,
                isValid: false,
                error: error.message
            };
        }
    }
    
    /**
     * Calculate next execution time cho tất cả schedule types
     */
    private async calculateNextExecutionTime(
        config: IScheduleParameters
    ): Promise<{ nextExecutionTime: number; delay: number } | null> {
        const now = Date.now();
        
        try {
            switch (config.schedule_type) {
                case EScheduleType.ONCE:
                    return this.calculateOnceExecution(config, now);
                    
                case EScheduleType.DAILY:
                    return this.calculateDailyExecution(config, now);
                    
                case EScheduleType.WEEKLY:
                    return this.calculateWeeklyExecution(config, now);
                    
                case EScheduleType.MONTHLY:
                    return this.calculateMonthlyExecution(config, now);
                    
                case EScheduleType.INTERVAL:
                    return this.calculateIntervalExecution(config, now);
                    
                case EScheduleType.CRON:
                    return this.calculateCronExecution(config, now);
                    
                default:
                    this.logger.error(`Unsupported schedule type: ${config.schedule_type}`);
                    return null;
            }
        } catch (error) {
            this.logger.error(`Failed to calculate execution time for type: ${config.schedule_type}`, error);
            return null;
        }
    }
    
    /**
     * Calculate CRON execution time
     */
    private async calculateCronExecution(
        config: IScheduleParameters,
        currentTime: number
    ): Promise<{ nextExecutionTime: number; delay: number } | null> {
        if (!config.cron_config?.expression) {
            this.logger.error('CRON expression is required for CRON schedule');
            return null;
        }
        
        const cronInfo = await this.cronHandlerService.calculateNextCronExecution(
            {
                expression: config.cron_config.expression,
                timezone: config.cron_config.timezone || 'Asia/Ho_Chi_Minh'
            },
            new Date(currentTime)
        );
        
        if (!cronInfo || !cronInfo.isValid) {
            this.logger.error(`Invalid CRON configuration: ${config.cron_config.expression}`);
            return null;
        }
        
        return {
            nextExecutionTime: cronInfo.nextExecutionTime.getTime(),
            delay: cronInfo.delay
        };
    }
    
    /**
     * Calculate once execution time
     */
    private calculateOnceExecution(
        config: IScheduleParameters,
        currentTime: number
    ): { nextExecutionTime: number; delay: number } | null {
        if (!config.once_config?.datetime) {
            return null;
        }
        
        const executionTime = new Date(config.once_config.datetime).getTime();
        const delay = executionTime - currentTime;
        
        if (delay <= 0) {
            this.logger.warn(`Once execution time is in the past: ${config.once_config.datetime}`);
            return null;
        }
        
        return { nextExecutionTime: executionTime, delay };
    }
    
    /**
     * Calculate daily execution time
     */
    private calculateDailyExecution(
        config: IScheduleParameters,
        currentTime: number
    ): { nextExecutionTime: number; delay: number } | null {
        if (!config.daily_config) {
            return null;
        }
        
        const timezone = config.daily_config.timezone || 'Asia/Ho_Chi_Minh';
        const now = DateTime.fromMillis(currentTime).setZone(timezone);
        
        let nextRun = now.set({
            hour: config.daily_config.hour || 0,
            minute: config.daily_config.minute || 0,
            second: config.daily_config.second || 0,
            millisecond: 0
        });
        
        // If time has passed today, move to tomorrow
        if (nextRun <= now) {
            nextRun = nextRun.plus({ days: 1 });
        }
        
        const delay = nextRun.toMillis() - currentTime;
        return { nextExecutionTime: nextRun.toMillis(), delay };
    }
    
    /**
     * Calculate weekly execution time
     */
    private calculateWeeklyExecution(
        config: IScheduleParameters,
        currentTime: number
    ): { nextExecutionTime: number; delay: number } | null {
        if (!config.weekly_config?.days_of_week?.length) {
            return null;
        }
        
        const timezone = config.weekly_config.timezone || 'Asia/Ho_Chi_Minh';
        const now = DateTime.fromMillis(currentTime).setZone(timezone);
        const currentDay = now.weekday; // 1 = Monday, 7 = Sunday
        
        // Convert days_of_week (0=Sunday) to Luxon format (1=Monday)
        const targetDays = config.weekly_config.days_of_week
            .map((day: number) => day === 0 ? 7 : day)
            .sort();
        
        // Find next target day
        const nextDay = targetDays.find((day: number) => day > currentDay);
        let daysToAdd = 0;
        
        if (nextDay) {
            daysToAdd = nextDay - currentDay;
        } else {
            // Next week
            daysToAdd = (7 - currentDay) + targetDays[0];
        }
        
        const nextRun = now.plus({ days: daysToAdd }).set({
            hour: config.weekly_config.hour || 0,
            minute: config.weekly_config.minute || 0,
            second: config.weekly_config.second || 0,
            millisecond: 0
        });
        
        const delay = nextRun.toMillis() - currentTime;
        return { nextExecutionTime: nextRun.toMillis(), delay };
    }
    
    /**
     * Calculate monthly execution time
     */
    private calculateMonthlyExecution(
        config: IScheduleParameters,
        currentTime: number
    ): { nextExecutionTime: number; delay: number } | null {
        if (!config.monthly_config) {
            return null;
        }
        
        const timezone = config.monthly_config.timezone || 'Asia/Ho_Chi_Minh';
        const now = DateTime.fromMillis(currentTime).setZone(timezone);
        
        let nextRun = now.set({
            hour: config.monthly_config.hour || 0,
            minute: config.monthly_config.minute || 0,
            second: config.monthly_config.second || 0,
            millisecond: 0
        });
        
        if (config.monthly_config.day_of_month === 'last') {
            // Last day of month
            nextRun = nextRun.endOf('month').startOf('day').set({
                hour: config.monthly_config.hour || 0,
                minute: config.monthly_config.minute || 0,
                second: config.monthly_config.second || 0
            });
        } else {
            nextRun = nextRun.set({ day: config.monthly_config.day_of_month || 1 });
        }
        
        // If time has passed this month, move to next month
        if (nextRun <= now) {
            nextRun = nextRun.plus({ months: 1 });
            
            if (config.monthly_config.day_of_month === 'last') {
                nextRun = nextRun.endOf('month').startOf('day').set({
                    hour: config.monthly_config.hour || 0,
                    minute: config.monthly_config.minute || 0,
                    second: config.monthly_config.second || 0
                });
            }
        }
        
        const delay = nextRun.toMillis() - currentTime;
        return { nextExecutionTime: nextRun.toMillis(), delay };
    }
    
    /**
     * Calculate interval execution time
     */
    private calculateIntervalExecution(
        config: IScheduleParameters,
        currentTime: number
    ): { nextExecutionTime: number; delay: number } | null {
        if (!config.interval_config) {
            return null;
        }
        
        const { value, type } = config.interval_config;
        const now = DateTime.fromMillis(currentTime);
        let nextRun: DateTime;
        
        switch (type) {
            case EIntervalType.SECONDS:
                nextRun = now.plus({ seconds: value });
                break;
            case EIntervalType.MINUTES:
                nextRun = now.plus({ minutes: value });
                break;
            case EIntervalType.HOURS:
                nextRun = now.plus({ hours: value });
                break;
            case EIntervalType.DAYS:
                nextRun = now.plus({ days: value });
                break;
            default:
                return null;
        }
        
        const delay = nextRun.toMillis() - currentTime;
        return { nextExecutionTime: nextRun.toMillis(), delay };
    }
    
    /**
     * Check if schedule is recurring
     */
    private isRecurringSchedule(config: IScheduleParameters): boolean {
        return config.schedule_type !== EScheduleType.ONCE;
    }
    
    /**
     * Cancel existing job
     */
    async cancelJob(jobId: string): Promise<void> {
        try {
            const job = await this.workflowQueue.getJob(jobId);
            
            if (job) {
                await job.remove();
                this.logger.log(`Cancelled job: ${jobId}`);
            }
        } catch (error) {
            this.logger.error(`Failed to cancel job: ${jobId}`, error);
        }
    }
    
    /**
     * Create next job for recurring schedules (called after execution)
     */
    async createNextRecurringJob(
        originalJobData: IScheduledJobData
    ): Promise<IJobCreationResult> {
        try {
            // For CRON jobs, recalculate next execution
            if (originalJobData.scheduleType === EScheduleType.CRON && originalJobData.originalCronExpression) {
                const cronInfo = await this.cronHandlerService.calculateNextCronExecution({
                    expression: originalJobData.originalCronExpression,
                    timezone: originalJobData.scheduleConfig.cron_config?.timezone || 'Asia/Ho_Chi_Minh'
                });
                
                if (cronInfo && cronInfo.isValid) {
                    // Update schedule config with new execution time
                    const updatedConfig = {
                        ...originalJobData.scheduleConfig,
                        nextExecutionTime: cronInfo.nextExecutionTime.getTime()
                    };
                    
                    return this.createOrUpdateDelayedJob(
                        originalJobData.nodeId,
                        originalJobData.workflowId,
                        updatedConfig
                    );
                }
            } else {
                // For other recurring types, recalculate normally
                return this.createOrUpdateDelayedJob(
                    originalJobData.nodeId,
                    originalJobData.workflowId,
                    originalJobData.scheduleConfig
                );
            }
            
            return {
                jobId: '',
                nextExecutionTime: 0,
                delay: 0,
                isValid: false,
                error: 'Failed to create next recurring job'
            };
            
        } catch (error) {
            this.logger.error(`Failed to create next recurring job for node: ${originalJobData.nodeId}`, error);
            return {
                jobId: '',
                nextExecutionTime: 0,
                delay: 0,
                isValid: false,
                error: error.message
            };
        }
    }
    
    /**
     * Get job status
     */
    async getJobStatus(jobId: string): Promise<{
        exists: boolean;
        state?: string;
        progress?: number;
        delay?: number;
    }> {
        try {
            const job = await this.workflowQueue.getJob(jobId);
            
            if (!job) {
                return { exists: false };
            }
            
            const state = await job.getState();
            const progress = job.progress();
            
            return {
                exists: true,
                state,
                progress,
                delay: job.opts.delay
            };
            
        } catch (error) {
            this.logger.error(`Failed to get job status: ${jobId}`, error);
            return { exists: false };
        }
    }
}

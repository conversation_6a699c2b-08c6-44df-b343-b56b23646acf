import { 
  Controller, 
  Get, 
  Put, 
  Delete, 
  Body, 
  Param, 
  Query, 
  UseGuards,
  Logger 
} from '@nestjs/common';
import { 
  ApiTags, 
  ApiOperation, 
  ApiResponse, 
  ApiParam, 
  ApiQuery,
  ApiBearerAuth 
} from '@nestjs/swagger';
import { GenericLayoutService, GenericQueueService } from '../services';
import { 
  UpdateGenericLayoutDto, 
  GenericLayoutResponseDto,
  LayoutItemDto 
} from '../dto/generic-layout.dto';
import { ApiResponseDto } from '@/common/response';
import { GenericPageJobPriority } from '../constants/generic-queue.constants';

/**
 * Controller cho Generic Layout Management
 * Quản lý layout configuration cho sessions
 */
@ApiTags('Generic Layout')
@Controller('generic/sessions/:sessionId/layout')
export class GenericLayoutController {
  private readonly logger = new Logger(GenericLayoutController.name);

  constructor(
    private readonly genericLayoutService: GenericLayoutService,
    private readonly genericQueueService: GenericQueueService,
  ) {}

  /**
   * Lấy layout của session
   */
  @Get()
  @ApiOperation({ 
    summary: 'Lấy layout của session',
    description: 'Lấy layout configuration hiện tại của session'
  })
  @ApiParam({
    name: 'sessionId',
    description: 'Session ID',
    example: 'session-123-abc'
  })
  @ApiResponse({
    status: 200,
    description: 'Layout configuration',
    type: ApiResponseDto<GenericLayoutResponseDto>
  })
  @ApiResponse({
    status: 404,
    description: 'Không tìm thấy layout'
  })
  async getLayout(
    @Param('sessionId') sessionId: string
  ): Promise<ApiResponseDto<GenericLayoutResponseDto>> {
    try {
      const layout = await this.genericLayoutService.getLayoutBySessionId(sessionId);
      
      return ApiResponseDto.success(layout, 'Lấy layout thành công');
    } catch (error) {
      this.logger.error(`Error getting layout: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Cập nhật layout
   */
  @Put()
  @ApiOperation({ 
    summary: 'Cập nhật layout',
    description: 'Cập nhật layout configuration và broadcast real-time update'
  })
  @ApiParam({
    name: 'sessionId',
    description: 'Session ID',
    example: 'session-123-abc'
  })
  @ApiResponse({
    status: 200,
    description: 'Layout đã được cập nhật thành công hoặc job đã được thêm vào queue',
    type: ApiResponseDto<GenericLayoutResponseDto>
  })
  async updateLayout(
    @Param('sessionId') sessionId: string,
    @Body() updateDto: UpdateGenericLayoutDto,
    @Query('useQueue') useQueue?: boolean
  ): Promise<ApiResponseDto<GenericLayoutResponseDto | null>> {
    try {
      if (useQueue) {
        // Sử dụng queue để xử lý async
        await this.genericQueueService.updateLayoutJob(
          sessionId,
          updateDto.layoutData,
          {
            priority: GenericPageJobPriority.HIGH,
            triggeredBy: 'api',
          }
        );

        return ApiResponseDto.success(null, 'Layout update job đã được thêm vào queue');
      } else {
        // Xử lý trực tiếp
        const layout = await this.genericLayoutService.updateLayout(sessionId, updateDto);
        
        this.logger.log(`Updated layout for session ${sessionId}`);
        
        return ApiResponseDto.updated(layout, 'Layout đã được cập nhật thành công');
      }
    } catch (error) {
      this.logger.error(`Error updating layout: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Cập nhật chỉ layout data (positions)
   */
  @Put('data')
  @ApiOperation({ 
    summary: 'Cập nhật layout data',
    description: 'Cập nhật chỉ positions của widgets trong layout'
  })
  @ApiParam({
    name: 'sessionId',
    description: 'Session ID',
    example: 'session-123-abc'
  })
  @ApiResponse({
    status: 200,
    description: 'Layout data đã được cập nhật thành công hoặc job đã được thêm vào queue',
    type: ApiResponseDto<GenericLayoutResponseDto>
  })
  async updateLayoutData(
    @Param('sessionId') sessionId: string,
    @Body() body: { layoutData: LayoutItemDto[] },
    @Query('useQueue') useQueue?: boolean
  ): Promise<ApiResponseDto<GenericLayoutResponseDto | null>> {
    try {
      if (useQueue) {
        // Sử dụng queue để xử lý async
        await this.genericQueueService.updateLayoutJob(
          sessionId,
          body.layoutData,
          {
            priority: GenericPageJobPriority.NORMAL,
            triggeredBy: 'api',
          }
        );

        return ApiResponseDto.success(null, 'Layout data update job đã được thêm vào queue');
      } else {
        // Xử lý trực tiếp
        const layout = await this.genericLayoutService.updateLayoutData(sessionId, body.layoutData);
        
        this.logger.log(`Updated layout data for session ${sessionId}`);
        
        return ApiResponseDto.updated(layout, 'Layout data đã được cập nhật thành công');
      }
    } catch (error) {
      this.logger.error(`Error updating layout data: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Cập nhật grid configuration
   */
  @Put('grid-config')
  @ApiOperation({ 
    summary: 'Cập nhật grid configuration',
    description: 'Cập nhật grid settings (rowHeight, margin, cols, etc.)'
  })
  @ApiParam({
    name: 'sessionId',
    description: 'Session ID',
    example: 'session-123-abc'
  })
  @ApiResponse({
    status: 200,
    description: 'Grid config đã được cập nhật',
    type: ApiResponseDto<GenericLayoutResponseDto>
  })
  async updateGridConfig(
    @Param('sessionId') sessionId: string,
    @Body() body: { gridConfig: any }
  ): Promise<ApiResponseDto<GenericLayoutResponseDto>> {
    try {
      const layout = await this.genericLayoutService.updateGridConfig(sessionId, body.gridConfig);
      
      this.logger.log(`Updated grid config for session ${sessionId}`);
      
      return ApiResponseDto.updated(layout, 'Grid config đã được cập nhật thành công');
    } catch (error) {
      this.logger.error(`Error updating grid config: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Cập nhật breakpoints configuration
   */
  @Put('breakpoints')
  @ApiOperation({ 
    summary: 'Cập nhật breakpoints',
    description: 'Cập nhật responsive breakpoints configuration'
  })
  @ApiParam({
    name: 'sessionId',
    description: 'Session ID',
    example: 'session-123-abc'
  })
  @ApiResponse({
    status: 200,
    description: 'Breakpoints đã được cập nhật',
    type: ApiResponseDto<GenericLayoutResponseDto>
  })
  async updateBreakpoints(
    @Param('sessionId') sessionId: string,
    @Body() body: { breakpoints: any }
  ): Promise<ApiResponseDto<GenericLayoutResponseDto>> {
    try {
      const layout = await this.genericLayoutService.updateBreakpoints(sessionId, body.breakpoints);
      
      this.logger.log(`Updated breakpoints for session ${sessionId}`);
      
      return ApiResponseDto.updated(layout, 'Breakpoints đã được cập nhật thành công');
    } catch (error) {
      this.logger.error(`Error updating breakpoints: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Compact layout
   */
  @Put('compact')
  @ApiOperation({ 
    summary: 'Compact layout',
    description: 'Tự động sắp xếp lại widgets để tối ưu layout'
  })
  @ApiParam({
    name: 'sessionId',
    description: 'Session ID',
    example: 'session-123-abc'
  })
  @ApiQuery({
    name: 'type',
    description: 'Compact type',
    enum: ['vertical', 'horizontal'],
    required: false
  })
  @ApiResponse({
    status: 200,
    description: 'Layout đã được compact',
    type: ApiResponseDto<GenericLayoutResponseDto>
  })
  async compactLayout(
    @Param('sessionId') sessionId: string,
    @Query('type') compactType: 'vertical' | 'horizontal' = 'vertical'
  ): Promise<ApiResponseDto<GenericLayoutResponseDto>> {
    try {
      const layout = await this.genericLayoutService.compactLayout(sessionId, compactType);
      
      this.logger.log(`Compacted layout for session ${sessionId} with type ${compactType}`);
      
      return ApiResponseDto.success(layout, 'Layout đã được compact thành công');
    } catch (error) {
      this.logger.error(`Error compacting layout: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Reset layout về mặc định
   */
  @Put('reset')
  @ApiOperation({ 
    summary: 'Reset layout',
    description: 'Reset layout về trạng thái mặc định'
  })
  @ApiParam({
    name: 'sessionId',
    description: 'Session ID',
    example: 'session-123-abc'
  })
  @ApiResponse({
    status: 200,
    description: 'Layout đã được reset',
    type: ApiResponseDto<GenericLayoutResponseDto>
  })
  async resetLayout(
    @Param('sessionId') sessionId: string
  ): Promise<ApiResponseDto<GenericLayoutResponseDto>> {
    try {
      const layout = await this.genericLayoutService.resetLayout(sessionId);
      
      this.logger.log(`Reset layout for session ${sessionId}`);
      
      return ApiResponseDto.success(layout, 'Layout đã được reset thành công');
    } catch (error) {
      this.logger.error(`Error resetting layout: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Xóa layout
   */
  @Delete()
  @ApiOperation({ 
    summary: 'Xóa layout',
    description: 'Xóa layout configuration của session'
  })
  @ApiParam({
    name: 'sessionId',
    description: 'Session ID',
    example: 'session-123-abc'
  })
  @ApiResponse({
    status: 200,
    description: 'Layout đã được xóa'
  })
  async deleteLayout(
    @Param('sessionId') sessionId: string
  ): Promise<ApiResponseDto<{ deletedCount: number }>> {
    try {
      const deletedCount = await this.genericLayoutService.deleteLayout(sessionId);
      
      this.logger.log(`Deleted layout for session ${sessionId}`);
      
      return ApiResponseDto.deleted({ deletedCount }, 'Layout đã được xóa thành công');
    } catch (error) {
      this.logger.error(`Error deleting layout: ${error.message}`, error.stack);
      throw error;
    }
  }
}

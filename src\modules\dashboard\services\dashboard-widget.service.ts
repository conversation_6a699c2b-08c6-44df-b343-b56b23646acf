import { Injectable, Logger } from '@nestjs/common';
import { DashboardWidgetRepository } from '../repositories/dashboard-widget.repository';
import { DashboardPageRepository } from '../repositories/dashboard-page.repository';
import { DashboardWidget } from '../entities/dashboard-widget.entity';
import {
  CreateDashboardWidgetDto,
  UpdateDashboardWidgetDto,
  QueryDashboardWidgetDto,
  DashboardWidgetResponseDto
} from '../dto/dashboard-widget.dto';
import { DashboardWidgetType } from '../enums/dashboard-widget-type.enum';
import { AppException } from '@common/exceptions';
import { DASHBOARD_ERROR_CODES } from '../exceptions/dashboard.exception';

@Injectable()
export class DashboardWidgetService {
  private readonly logger = new Logger(DashboardWidgetService.name);

  constructor(
    private readonly dashboardWidgetRepository: DashboardWidgetRepository,
    private readonly dashboardPageRepository: DashboardPageRepository,
  ) {}

  /**
   * Tạo dashboard widget mới
   */
  async create(
    dto: CreateDashboardWidgetDto,
    userId?: string,
    employeeId?: string,
    userRoles?: string[]
  ): Promise<DashboardWidgetResponseDto> {
    try {
      this.logger.log(`Creating dashboard widget: ${dto.name}`);

      // Kiểm tra dashboard page tồn tại và quyền truy cập
      const dashboardPage = await this.dashboardPageRepository.findById(dto.dashboardPageId);
      if (!dashboardPage) {
        throw new AppException(
          DASHBOARD_ERROR_CODES.DASHBOARD_PAGE_NOT_FOUND,
          `Không tìm thấy dashboard page ${dto.dashboardPageId}`
        );
        // throw new NotFoundException(`Dashboard page với ID ${dto.dashboardPageId} không tồn tại`);
      }

      if (!dashboardPage.canAccess(userId, employeeId, userRoles)) {
        throw new AppException(
          DASHBOARD_ERROR_CODES.DASHBOARD_PAGE_ACCESS_DENIED,
          `Bạn không có quyền truy cập dashboard page ${dto.dashboardPageId}`
        );
        // throw new NotFoundException(`Dashboard page với ID ${dto.dashboardPageId} không tồn tại`);
      }

      // Kiểm tra quyền chỉnh sửa (chỉ owner mới được thêm widget)
      const isOwner = (dashboardPage.ownerType === 'USER' && dashboardPage.userId?.toString() === userId?.toString()) ||
                      (dashboardPage.ownerType === 'EMPLOYEE' && dashboardPage.employeeId?.toString() === employeeId?.toString());
      
      if (!isOwner) {
        throw new AppException(
          DASHBOARD_ERROR_CODES.DASHBOARD_WIDGET_MODIFY_DENIED,
          `Bạn không có quyền thêm widget vào dashboard page này`
        );
        // throw new BadRequestException('Bạn không có quyền thêm widget vào dashboard page này');
      }

      // Kiểm tra widgetKey đã tồn tại chưa
      const keyExists = await this.dashboardWidgetRepository.isWidgetKeyExists(
        dto.widgetKey, 
        dto.dashboardPageId
      );
      if (keyExists) {
        throw new AppException(
          DASHBOARD_ERROR_CODES.DASHBOARD_WIDGET_KEY_EXISTS,
          `Widget key "${dto.widgetKey}" đã tồn tại trong dashboard page này`
        );
        // throw new ConflictException(`Widget key "${dto.widgetKey}" đã tồn tại trong dashboard page này`);
      }

      // Validate layout
      if (!this.validateLayout(dto.layout)) {
        throw new AppException(
          DASHBOARD_ERROR_CODES.DASHBOARD_WIDGET_LAYOUT_INVALID,
          'Layout không hợp lệ'
        );
        // throw new BadRequestException('Layout không hợp lệ');
      }

      // Tạo widget
      const widget = await this.dashboardWidgetRepository.create(dto);
      
      this.logger.log(`Created dashboard widget: ${widget.id}`);
      return this.toResponseDto(widget);
    } catch (error) {
      this.logger.error(`Error creating dashboard widget: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Tạo nhiều widgets cùng lúc
   */
  async createMany(
    widgets: CreateDashboardWidgetDto[],
    userId?: string,
    employeeId?: string,
    userRoles?: string[]
  ): Promise<DashboardWidgetResponseDto[]> {
    try {
      this.logger.log(`Creating ${widgets.length} dashboard widgets`);

      // Validate tất cả widgets trước khi tạo
      for (const dto of widgets) {
        // Kiểm tra dashboard page
        const dashboardPage = await this.dashboardPageRepository.findById(dto.dashboardPageId);
        if (!dashboardPage) {
          throw new AppException(
            DASHBOARD_ERROR_CODES.DASHBOARD_PAGE_NOT_FOUND,
            `Không tìm thấy dashboard page ${dto.dashboardPageId}`
          );
          // throw new NotFoundException(`Dashboard page với ID ${dto.dashboardPageId} không tồn tại`);
        }

        if (!dashboardPage.canAccess(userId, employeeId, userRoles)) {
          throw new AppException(
            DASHBOARD_ERROR_CODES.DASHBOARD_PAGE_ACCESS_DENIED,
            `Bạn không có quyền truy cập dashboard page ${dto.dashboardPageId}`
          );
          // throw new NotFoundException(`Dashboard page với ID ${dto.dashboardPageId} không tồn tại`);
        }

        // Kiểm tra quyền
        const isOwner = (dashboardPage.ownerType === 'USER' && dashboardPage.userId?.toString() === userId?.toString()) ||
                        (dashboardPage.ownerType === 'EMPLOYEE' && dashboardPage.employeeId?.toString() === employeeId?.toString());
        
        if (!isOwner) {
          throw new AppException(
            DASHBOARD_ERROR_CODES.DASHBOARD_WIDGET_MODIFY_DENIED,
            `Bạn không có quyền thêm widget vào dashboard page này`
          );
          // throw new BadRequestException('Bạn không có quyền thêm widget vào dashboard page này');
        }

        // Validate layout
        if (!this.validateLayout(dto.layout)) {
          throw new AppException(
            DASHBOARD_ERROR_CODES.DASHBOARD_WIDGET_LAYOUT_INVALID,
            `Layout không hợp lệ cho widget ${dto.name}`
          );
          // throw new BadRequestException(`Layout không hợp lệ cho widget ${dto.name}`);
        }
      }

      // Kiểm tra duplicate widget keys
      const widgetKeys = widgets.map(w => `${w.dashboardPageId}:${w.widgetKey}`);
      const uniqueKeys = new Set(widgetKeys);
      if (widgetKeys.length !== uniqueKeys.size) {
        throw new AppException(
            DASHBOARD_ERROR_CODES.DASHBOARD_WIDGET_KEY_EXISTS,
            'Có widget keys bị trùng lặp'
          );
          // throw new ConflictException('Có widget keys bị trùng lặp');
      }

      // Tạo widgets
      const createdWidgets = await this.dashboardWidgetRepository.createMany(widgets);
      
      this.logger.log(`Created ${createdWidgets.length} dashboard widgets`);
      return createdWidgets.map(widget => this.toResponseDto(widget));
    } catch (error) {
      this.logger.error(`Error creating dashboard widgets: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Lấy widget theo ID
   */
  async findById(
    id: string,
    userId?: string,
    employeeId?: string,
    userRoles?: string[]
  ): Promise<DashboardWidgetResponseDto> {
    try {
      const widget = await this.dashboardWidgetRepository.findById(id);

      if (!widget) {
        throw new AppException(DASHBOARD_ERROR_CODES.DASHBOARD_WIDGET_NOT_FOUND);
      }

      // Kiểm tra quyền truy cập dashboard page
      if (!widget.dashboardPage.canAccess(userId, employeeId, userRoles)) {
        throw new AppException(DASHBOARD_ERROR_CODES.DASHBOARD_WIDGET_ACCESS_DENIED);
      }

      return this.toResponseDto(widget);
    } catch (error) {
      this.logger.error(`Error finding widget by ID ${id}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Lấy widgets theo dashboard page ID
   */
  async findByDashboardPageId(
    dashboardPageId: string,
    userId?: string,
    employeeId?: string,
    userRoles?: string[]
  ): Promise<DashboardWidgetResponseDto[]> {
    try {
      // Kiểm tra quyền truy cập dashboard page
      const dashboardPage = await this.dashboardPageRepository.findById(dashboardPageId);
      if (!dashboardPage) {
        throw new AppException(DASHBOARD_ERROR_CODES.DASHBOARD_WIDGET_NOT_FOUND);
      }

      if (!dashboardPage.canAccess(userId, employeeId, userRoles)) {
        throw new AppException(DASHBOARD_ERROR_CODES.DASHBOARD_WIDGET_ACCESS_DENIED);
        // throw new NotFoundException(`Dashboard page với ID ${dashboardPageId} không tồn tại`);
      }

      const widgets = await this.dashboardWidgetRepository.findByDashboardPageId(dashboardPageId);
      return widgets.map(widget => this.toResponseDto(widget));
    } catch (error) {
      this.logger.error(`Error finding widgets by dashboard page ${dashboardPageId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Query widgets
   */
  async findMany(
    query: QueryDashboardWidgetDto,
    userId?: string,
    employeeId?: string,
    userRoles?: string[]
  ): Promise<DashboardWidgetResponseDto[]> {
    try {
      // Nếu có dashboardPageId, kiểm tra quyền truy cập
      if (query.dashboardPageId) {
        const dashboardPage = await this.dashboardPageRepository.findById(query.dashboardPageId);
        if (!dashboardPage || !dashboardPage.canAccess(userId, employeeId, userRoles)) {
          return [];
        }
      }

      const widgets = await this.dashboardWidgetRepository.findMany(query);
      
      // Filter widgets theo quyền truy cập dashboard page
      const accessibleWidgets: DashboardWidget[] = [];
      for (const widget of widgets) {
        const dashboardPage = await this.dashboardPageRepository.findById(widget.dashboardPageId);
        if (dashboardPage && dashboardPage.canAccess(userId, employeeId, userRoles)) {
          accessibleWidgets.push(widget);
        }
      }

      return accessibleWidgets.map(widget => this.toResponseDto(widget));
    } catch (error) {
      this.logger.error(`Error querying widgets: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Cập nhật widget
   */
  async update(
    id: string,
    dto: UpdateDashboardWidgetDto,
    userId?: string,
    employeeId?: string,
    userRoles?: string[]
  ): Promise<DashboardWidgetResponseDto> {
    try {
      this.logger.log(`Updating dashboard widget: ${id}`);

      // Kiểm tra widget tồn tại
      const existingWidget = await this.dashboardWidgetRepository.findById(id);
      if (!existingWidget) {
        throw new AppException(DASHBOARD_ERROR_CODES.DASHBOARD_WIDGET_NOT_FOUND);
      }

      // Kiểm tra quyền truy cập và chỉnh sửa
      const dashboardPage = existingWidget.dashboardPage;
      if (!dashboardPage.canAccess(userId, employeeId, userRoles)) {
        throw new AppException(DASHBOARD_ERROR_CODES.DASHBOARD_WIDGET_ACCESS_DENIED);
      }

      const isOwner = (dashboardPage.ownerType === 'USER' && dashboardPage.userId?.toString() === userId?.toString()) ||
                      (dashboardPage.ownerType === 'EMPLOYEE' && dashboardPage.employeeId?.toString() === employeeId?.toString());
      
      if (!isOwner) {
        throw new AppException(
          DASHBOARD_ERROR_CODES.DASHBOARD_WIDGET_MODIFY_DENIED,
          `Bạn không có quyền chỉnh sửa widget này`
        );
        // throw new BadRequestException('Bạn không có quyền chỉnh sửa widget này');
      }

      // Kiểm tra widgetKey nếu có thay đổi
      if (dto.widgetKey && dto.widgetKey !== existingWidget.widgetKey) {
        const keyExists = await this.dashboardWidgetRepository.isWidgetKeyExists(
          dto.widgetKey, 
          existingWidget.dashboardPageId, 
          id
        );
        if (keyExists) {
          throw new AppException(
            DASHBOARD_ERROR_CODES.DASHBOARD_WIDGET_KEY_EXISTS,
            `Widget key "${dto.widgetKey}" đã tồn tại trong dashboard page này`
          );
          // throw new ConflictException(`Widget key "${dto.widgetKey}" đã tồn tại trong dashboard page này`);
        }
      }

      // Validate layout nếu có thay đổi
      if (dto.layout && !this.validateLayout(dto.layout)) {
        throw new AppException(
          DASHBOARD_ERROR_CODES.DASHBOARD_WIDGET_LAYOUT_INVALID,
          'Layout không hợp lệ'
        );
        // throw new BadRequestException('Layout không hợp lệ');
      }

      const updatedWidget = await this.dashboardWidgetRepository.update(id, dto);
      
      if (!updatedWidget) {
        throw new AppException(
          DASHBOARD_ERROR_CODES.DASHBOARD_WIDGET_NOT_FOUND,
          `Widget với ID ${id} không tồn tại`
        );
        // throw new NotFoundException(`Widget với ID ${id} không tồn tại`);
      }

      this.logger.log(`Updated dashboard widget: ${id}`);
      return this.toResponseDto(updatedWidget);
    } catch (error) {
      this.logger.error(`Error updating widget ${id}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Cập nhật layout của nhiều widgets (dùng cho drag & drop)
   */
  async updateLayouts(
    updates: Array<{ id: string; layout: any; responsiveLayout?: any }>,
    userId?: string,
    employeeId?: string,
    userRoles?: string[]
  ): Promise<void> {
    try {
      this.logger.log(`Updating layouts for ${updates.length} widgets`);

      // Validate tất cả widgets trước khi update
      for (const update of updates) {
        const widget = await this.dashboardWidgetRepository.findById(update.id);
        if (!widget) {
          throw new AppException(
            DASHBOARD_ERROR_CODES.DASHBOARD_WIDGET_NOT_FOUND,
            `Widget với ID ${update.id} không tồn tại`
          );
          // throw new NotFoundException(`Widget với ID ${update.id} không tồn tại`);
        }

        // Kiểm tra quyền
        const dashboardPage = widget.dashboardPage;
        if (!dashboardPage.canAccess(userId, employeeId, userRoles)) {
          throw new AppException(
            DASHBOARD_ERROR_CODES.DASHBOARD_WIDGET_ACCESS_DENIED,
            `Bạn không có quyền truy cập widget ${update.id}`
          );
          // throw new NotFoundException(`Widget với ID ${update.id} không tồn tại`);
        }

        const isOwner = (dashboardPage.ownerType === 'USER' && dashboardPage.userId?.toString() === userId?.toString()) ||
                        (dashboardPage.ownerType === 'EMPLOYEE' && dashboardPage.employeeId?.toString() === employeeId?.toString());
        
        if (!isOwner) {
          throw new AppException(
            DASHBOARD_ERROR_CODES.DASHBOARD_WIDGET_MODIFY_DENIED,
            `Bạn không có quyền chỉnh sửa widget ${update.id}`
          );
          // throw new BadRequestException(`Bạn không có quyền chỉnh sửa widget ${update.id}`);
        }

        // Validate layout
        if (!this.validateLayout(update.layout)) {
          throw new AppException(
            DASHBOARD_ERROR_CODES.DASHBOARD_WIDGET_LAYOUT_INVALID,
            `Layout không hợp lệ cho widget ${update.id}`
          );
          // throw new BadRequestException(`Layout không hợp lệ cho widget ${update.id}`);
        }
      }

      await this.dashboardWidgetRepository.updateLayouts(updates);
      
      this.logger.log(`Updated layouts for ${updates.length} widgets`);
    } catch (error) {
      this.logger.error(`Error updating widget layouts: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Xóa widget
   */
  async delete(
    id: string,
    userId?: string,
    employeeId?: string,
    userRoles?: string[]
  ): Promise<void> {
    try {
      this.logger.log(`Deleting dashboard widget: ${id}`);

      // Kiểm tra widget tồn tại và quyền
      const existingWidget = await this.dashboardWidgetRepository.findById(id);
      if (!existingWidget) {
        throw new AppException(
          DASHBOARD_ERROR_CODES.DASHBOARD_WIDGET_NOT_FOUND,
          `Widget với ID ${id} không tồn tại`
        );
        // throw new NotFoundException(`Widget với ID ${id} không tồn tại`);
      }

      const dashboardPage = existingWidget.dashboardPage;
      if (!dashboardPage.canAccess(userId, employeeId, userRoles)) {
        throw new AppException(
          DASHBOARD_ERROR_CODES.DASHBOARD_WIDGET_ACCESS_DENIED,
          `Bạn không có quyền truy cập widget ${id}`
        );
        // throw new NotFoundException(`Widget với ID ${id} không tồn tại`);
      }

      const isOwner = (dashboardPage.ownerType === 'USER' && dashboardPage.userId?.toString() === userId?.toString()) ||
                      (dashboardPage.ownerType === 'EMPLOYEE' && dashboardPage.employeeId?.toString() === employeeId?.toString());
      
      if (!isOwner) {
        throw new AppException(
          DASHBOARD_ERROR_CODES.DASHBOARD_WIDGET_MODIFY_DENIED,
          `Bạn không có quyền xóa widget này`
        );
        // throw new BadRequestException('Bạn không có quyền xóa widget này');
      }

      const deleted = await this.dashboardWidgetRepository.delete(id);
      
      if (!deleted) {
        throw new AppException(
          DASHBOARD_ERROR_CODES.DASHBOARD_WIDGET_NOT_FOUND,
          `Widget với ID ${id} không tồn tại`
        );
        // throw new NotFoundException(`Widget với ID ${id} không tồn tại`);
      }

      this.logger.log(`Deleted dashboard widget: ${id}`);
    } catch (error) {
      this.logger.error(`Error deleting widget ${id}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Tìm vị trí trống cho widget mới
   */
  async findEmptyPosition(
    dashboardPageId: string,
    widgetType: DashboardWidgetType,
    userId?: string,
    employeeId?: string,
    userRoles?: string[]
  ): Promise<{ x: number; y: number; w: number; h: number }> {
    try {
      // Kiểm tra quyền truy cập dashboard page
      const dashboardPage = await this.dashboardPageRepository.findById(dashboardPageId);
      if (!dashboardPage || !dashboardPage.canAccess(userId, employeeId, userRoles)) {
        throw new AppException(
          DASHBOARD_ERROR_CODES.DASHBOARD_PAGE_ACCESS_DENIED,
          `Bạn không có quyền truy cập dashboard page ${dashboardPageId}`
        );
        // throw new NotFoundException(`Dashboard page với ID ${dashboardPageId} không tồn tại`);
      }

      // Lấy default size từ widget metadata
      const defaultLayout = DashboardWidget.createDefaultLayout(widgetType);
      
      // Tìm vị trí trống
      const position = await this.dashboardWidgetRepository.findEmptyPosition(
        dashboardPageId,
        defaultLayout.w,
        defaultLayout.h
      );

      return {
        x: position.x,
        y: position.y,
        w: defaultLayout.w,
        h: defaultLayout.h,
      };
    } catch (error) {
      this.logger.error(`Error finding empty position: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Validate layout
   */
  private validateLayout(layout: any): boolean {
    if (!layout || typeof layout !== 'object') {
      return false;
    }

    const { x, y, w, h } = layout;
    
    // Kiểm tra các giá trị cơ bản
    if (typeof x !== 'number' || typeof y !== 'number' || 
        typeof w !== 'number' || typeof h !== 'number') {
      return false;
    }
    
    // Kiểm tra giá trị âm
    if (x < 0 || y < 0 || w <= 0 || h <= 0) {
      return false;
    }
    
    // Kiểm tra constraints
    if (layout.minW && w < layout.minW) return false;
    if (layout.maxW && w > layout.maxW) return false;
    if (layout.minH && h < layout.minH) return false;
    if (layout.maxH && h > layout.maxH) return false;
    
    return true;
  }

  /**
   * Convert entity to response DTO
   */
  private toResponseDto(widget: DashboardWidget): DashboardWidgetResponseDto {
    return {
      id: widget.id,
      name: widget.name,
      widgetKey: widget.widgetKey,
      widgetType: widget.widgetType,
      description: widget.description,
      layout: widget.layout,
      responsiveLayout: widget.responsiveLayout,
      config: widget.config,
      isVisible: widget.isVisible,
      zIndex: widget.zIndex,
      metadata: widget.metadata,
      dashboardPageId: widget.dashboardPageId,
      createdAt: widget.createdAt,
      updatedAt: widget.updatedAt,
      apiEndpoint: widget.apiEndpoint,
      widgetMetadata: widget.widgetMetadata,
    };
  }
}

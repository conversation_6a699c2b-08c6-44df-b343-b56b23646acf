import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { HttpService } from '@nestjs/axios';
import { lastValueFrom } from 'rxjs';
import { ZaloApiClient } from '@shared/services/zalo/zalo-api.client';
import { ZaloService as SharedZaloService } from '@shared/services/zalo/zalo.service';
import { ZaloOaService } from '@shared/services/zalo/zalo-oa.service';
import { ZaloConsultationService } from '@shared/services/zalo/zalo-consultation.service';
import { ZaloUserManagementService } from '@shared/services/zalo/zalo-user-management.service';
import { ZaloTagService } from '@shared/services/zalo/zalo-tag.service';
import { AppException, ErrorCode } from '@common/exceptions';
import {
  ILike,
  Like,
  ArrayContains,
  MoreThanOrEqual,
  LessThanOrEqual,
  Between,
  Not,
  IsNull,
} from 'typeorm';
import { parseInternationalPhone } from '@/shared/validators';
import { IntegrationTypeEnum } from '@/modules/integration/enums';
import { ZaloOALegacyWrapperService } from '@/modules/integration/services/zalo-oa-legacy-wrapper.service';
import { ZaloOAIntegrationService } from '@/modules/integration/services/zalo-oa-integration.service';
import { ZaloOAMetadata } from '@/modules/integration/interfaces/zalo-oa-metadata.interface';
import { MARKETING_ERROR_CODES } from '@modules/marketing/errors/marketing-error.code';
import { QueueService } from '@/shared/queue/queue.service';
import { QueueName } from '@/shared/queue/queue.constants';
import { ZaloOaMessageCampaignStatus } from '../entities/zalo-oa-message-campaign.entity';
import { ZaloZnsCampaignStatus } from '../entities/zalo-zns-campaign.entity';

import {
  ZaloZnsTemplateRepository,
  ZaloMessageRepository,
  ZaloZnsMessageRepository,
  ZaloFollowerRepository,
  ZaloWebhookLogRepository,
  ZaloSegmentRepository,
  ZaloCampaignRepository,
  ZaloCampaignLogRepository,
  ZaloAutomationRepository,
  ZaloAutomationLogRepository,
  ZaloMessageTemplateRepository,
  UserAudienceRepository,
  ZaloAdsAccountRepository,
  ZaloAdsCampaignRepository,
  ZaloAdsPerformanceRepository,
  ZaloVideoUploadRepository,
  ZaloOaMessageCampaignRepository,
  ZaloZnsCampaignRepository,
} from '../repositories';
import {
  ZaloFollower,
  ZaloMessage,
  ZaloOaMessageCampaign,
  ZaloZnsCampaign,
  ZaloSegment,
  ZaloCampaign,
  ZaloCampaignLog,
  ZaloAutomation,
  ZaloAutomationLog,
} from '../entities';
import {
  ZaloCampaignStatus,
  ZaloCampaignType,
  ZaloCampaignMessageContentDto,
  ZaloCampaignZnsContentDto,
  ZaloCampaignGroupMessageContentDto,
  ZaloAutomationStatus,
  ZaloAutomationTriggerType,
  ZaloAutomationActionType,
  OfficialAccountQueryDto,
  ZaloOfficialAccountInfoResponseDto,
  ConsultationMessageType,
} from '../dto/zalo';
import {
  ZaloSegmentConditionDto,
  ZaloSegmentOperator,
} from '../dto/zalo/zalo-segment.dto';

/**
 * Service xử lý logic liên quan đến Zalo
 */
@Injectable()
export class ZaloService {
  private readonly logger = new Logger(ZaloService.name);
  private readonly zaloAppId: string | undefined;
  private readonly zaloAppSecret: string | undefined;

  constructor(
    private readonly configService: ConfigService,
    private readonly httpService: HttpService,
    private readonly zaloApiClient: ZaloApiClient,
    private readonly sharedZaloService: SharedZaloService,
    private readonly zaloOaService: ZaloOaService,
    private readonly zaloConsultationService: ZaloConsultationService,
    private readonly zaloUserManagementService: ZaloUserManagementService,
    private readonly zaloTagService: ZaloTagService,
    private readonly zaloOfficialAccountRepository: ZaloOALegacyWrapperService,
    private readonly zaloOAIntegrationService: ZaloOAIntegrationService,
    private readonly zaloZnsTemplateRepository: ZaloZnsTemplateRepository,
    private readonly zaloMessageRepository: ZaloMessageRepository,
    private readonly zaloZnsMessageRepository: ZaloZnsMessageRepository,
    private readonly zaloFollowerRepository: ZaloFollowerRepository,
    private readonly zaloWebhookLogRepository: ZaloWebhookLogRepository,
    private readonly zaloSegmentRepository: ZaloSegmentRepository,
    private readonly zaloCampaignRepository: ZaloCampaignRepository,
    private readonly zaloCampaignLogRepository: ZaloCampaignLogRepository,
    private readonly zaloAutomationRepository: ZaloAutomationRepository,
    private readonly zaloAutomationLogRepository: ZaloAutomationLogRepository,
    private readonly zaloMessageTemplateRepository: ZaloMessageTemplateRepository,
    private readonly userAudienceRepository: UserAudienceRepository,
    private readonly zaloAdsAccountRepository: ZaloAdsAccountRepository,
    private readonly zaloAdsCampaignRepository: ZaloAdsCampaignRepository,
    private readonly zaloAdsPerformanceRepository: ZaloAdsPerformanceRepository,
    private readonly zaloVideoUploadRepository: ZaloVideoUploadRepository,
    private readonly zaloOaMessageCampaignRepository: ZaloOaMessageCampaignRepository,
    private readonly zaloZnsCampaignRepository: ZaloZnsCampaignRepository,
    private readonly queueService: QueueService,
  ) {
    this.zaloAppId = this.configService.get<string>('ZALO_APP_ID');
    this.zaloAppSecret = this.configService.get<string>('ZALO_APP_SECRET');

    if (!this.zaloAppId || !this.zaloAppSecret) {
      this.logger.error('ZALO_APP_ID or ZALO_APP_SECRET is not defined');
    }
  }

  /**
   * Lấy OA access token từ authorization code theo API v4 mới
   * @param appId ID của ứng dụng
   * @param appSecret Secret của ứng dụng
   * @param code Authorization code từ Zalo
   * @param codeVerifier Code verifier cho PKCE (tùy chọn)
   * @returns Access token response
   */
  async getOaAccessTokenV4(
    appId: string,
    appSecret: string,
    code: string,
    codeVerifier?: string,
  ): Promise<{
    access_token: string;
    refresh_token: string;
    expires_in: string;
  }> {
    try {
      const url = 'https://oauth.zaloapp.com/v4/oa/access_token';

      // Tạo form data theo định dạng x-www-form-urlencoded
      const formData = new URLSearchParams();
      formData.append('code', code);
      formData.append('app_id', appId);
      formData.append('grant_type', 'authorization_code');

      // Thêm code_verifier nếu có (cho PKCE)
      if (codeVerifier) {
        formData.append('code_verifier', codeVerifier);
      }

      const config = {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          secret_key: appSecret,
        },
        timeout: 10000, // Timeout 10 giây để tránh chờ quá lâu
      };

      const requestStartTime = Date.now();
      this.logger.debug(`Calling Zalo OA access token API: ${url}`);
      this.logger.debug(
        `Request payload: code=${code.substring(0, 20)}..., app_id=${appId}, grant_type=authorization_code`,
      );
      this.logger.debug(
        `Request headers: Content-Type=${config.headers['Content-Type']}, secret_key=***`,
      );

      const response = await lastValueFrom(
        this.httpService.post(url, formData.toString(), config),
      );

      const requestDuration = Date.now() - requestStartTime;
      this.logger.debug(`Zalo API request completed in ${requestDuration}ms`);

      this.logger.debug(`Zalo API response: ${JSON.stringify(response.data)}`);

      // Xử lý các lỗi cụ thể từ Zalo API
      if (response.data.error) {
        const errorCode = response.data.error;
        const errorName = response.data.error_name;
        const errorDescription = response.data.error_description;

        // Xử lý lỗi code hết hạn
        if (errorCode === -14019 || errorName === 'Authorized code expired') {
          throw new AppException(
            ErrorCode.EXTERNAL_SERVICE_ERROR,
            'Mã xác thực đã hết hạn. Vui lòng thực hiện lại quá trình kết nối Zalo OA.',
          );
        }

        // Xử lý lỗi code không hợp lệ
        if (errorCode === -14018 || errorName === 'Invalid authorized code') {
          throw new AppException(
            ErrorCode.EXTERNAL_SERVICE_ERROR,
            'Mã xác thực không hợp lệ. Vui lòng thực hiện lại quá trình kết nối Zalo OA.',
          );
        }

        // Xử lý lỗi app không hợp lệ
        if (errorCode === -14001 || errorName === 'Invalid app') {
          throw new AppException(
            ErrorCode.CONFIGURATION_ERROR,
            'Cấu hình ứng dụng Zalo không hợp lệ. Vui lòng liên hệ quản trị viên.',
          );
        }

        // Lỗi chung
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi từ Zalo API: ${errorDescription || errorName || `Mã lỗi ${errorCode}`}`,
        );
      }

      if (!response.data.access_token) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          'Không nhận được access token từ Zalo API',
        );
      }

      return {
        access_token: response.data.access_token,
        refresh_token: response.data.refresh_token || '',
        expires_in: response.data.expires_in || '90000',
      };
    } catch (error) {
      this.logger.error(`Failed to get OA access token v4: ${error.message}`);

      // Log chi tiết response nếu có
      if (error.response?.data) {
        this.logger.error(
          `Zalo API error response: ${JSON.stringify(error.response.data)}`,
        );
      }

      // Nếu đã là AppException thì throw lại
      if (error instanceof AppException) {
        throw error;
      }

      // Xử lý lỗi network hoặc timeout
      if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          'Kết nối đến Zalo API bị timeout. Vui lòng thử lại sau.',
        );
      }

      // Lỗi chung
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        `Lỗi khi kết nối Zalo API: ${error.message}`,
      );
    }
  }

  /**
   * Tạo code verifier cho PKCE
   * @param length Độ dài của code verifier (mặc định 43)
   * @returns Code verifier
   */
  private generateCodeVerifier(length: number = 43): string {
    const charset =
      'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let verifier = '';
    for (let i = 0; i < length; i++) {
      verifier += charset.charAt(Math.floor(Math.random() * charset.length));
    }
    return verifier;
  }

  /**
   * Tạo code challenge từ code verifier
   * @param codeVerifier Code verifier
   * @returns Code challenge
   */
  private async generateCodeChallenge(codeVerifier: string): Promise<string> {
    const crypto = require('crypto');
    const hash = crypto.createHash('sha256').update(codeVerifier).digest();
    return hash
      .toString('base64')
      .replace(/\+/g, '-')
      .replace(/\//g, '_')
      .replace(/=+$/, ''); // Xóa padding
  }

  /**
   * Tạo URL OAuth Zalo v4 cho Official Account
   * @param redirectUri URL redirect sau khi OAuth thành công
   * @param enablePKCE Bật PKCE để tăng bảo mật
   * @returns URL OAuth, state và code verifier (nếu PKCE được bật)
   */
  async generateOAuthUrl(
    redirectUri?: string,
    enablePKCE?: boolean,
  ): Promise<{
    oauthUrl: string;
    state: string;
    codeVerifier?: string;
    codeChallenge?: string;
  }> {
    const urlId = `gen_url_${Date.now()}`;

    try {
      this.logger.log(
        `[${urlId}] Generating OAuth URL with PKCE=${enablePKCE}`,
      );

      if (!this.zaloAppId) {
        this.logger.error(`[${urlId}] ZALO_APP_ID not configured`);
        throw new AppException(
          ErrorCode.CONFIGURATION_ERROR,
          'ZALO_APP_ID không được định nghĩa',
        );
      }

      // Tự tạo state với tiền tố zalo_integration_oa
      const timestamp = Date.now();
      const randomString = Math.random().toString(36).substring(2, 15);
      const finalState = `zalo_integration_oa_${timestamp}_${randomString}`;

      // URL mặc định nếu không được cung cấp
      const finalRedirectUri =
        redirectUri || 'https://v2.redai.vn/integration/zalo/oa/callback';

      this.logger.debug(`[${urlId}] Using redirect URI: ${finalRedirectUri}`);
      this.logger.debug(`[${urlId}] Generated state: ${finalState}`);

      // Tạo URL OAuth theo chuẩn Zalo v4
      const params = new URLSearchParams({
        app_id: this.zaloAppId,
        redirect_uri: finalRedirectUri,
        state: finalState,
      });

      let codeVerifier: string | undefined;
      let codeChallenge: string | undefined;

      // Thêm PKCE parameters nếu được bật
      if (enablePKCE) {
        this.logger.debug(`[${urlId}] Generating PKCE parameters...`);

        codeVerifier = this.generateCodeVerifier();
        codeChallenge = await this.generateCodeChallenge(codeVerifier);

        params.append('code_challenge', codeChallenge);
        params.append('code_challenge_method', 'S256');

        this.logger.debug(
          `[${urlId}] PKCE code challenge generated: ${codeChallenge.substring(0, 20)}...`,
        );
      }

      // Thêm nocache parameter để tránh cache
      params.append('nocache', Date.now().toString());

      const oauthUrl = `https://oauth.zaloapp.com/v4/oa/permission?${params.toString()}`;

      this.logger.log(`[${urlId}] OAuth URL generated successfully`);
      this.logger.debug(`[${urlId}] OAuth URL: ${oauthUrl}`);

      const result: {
        oauthUrl: string;
        state: string;
        codeVerifier?: string;
        codeChallenge?: string;
      } = {
        oauthUrl,
        state: finalState,
      };

      if (enablePKCE) {
        result.codeVerifier = codeVerifier;
        result.codeChallenge = codeChallenge;
      }

      return result;
    } catch (error) {
      this.logger.error(
        `[${urlId}] Failed to generate OAuth URL: ${error.message}`,
      );

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        `Không thể tạo URL OAuth: ${error.message}`,
      );
    }
  }

  /**
   * Xử lý callback OAuth và lấy access token
   * @param code Authorization code từ Zalo
   * @param userId ID của người dùng
   * @param codeVerifier Code verifier cho PKCE (tùy chọn)
   * @returns Official Account đã kết nối
   */
  async handleOAuthCallback(
    code: string,
    userId: number,
    codeVerifier?: string,
  ): Promise<ZaloOfficialAccount> {
    const startTime = Date.now();
    const requestId = `oauth_${userId}_${Date.now()}`;

    try {
      this.logger.log(
        `[${requestId}] Starting OAuth callback processing for user ${userId}`,
      );
      this.logger.debug(
        `[${requestId}] Code length: ${code?.length}, Code prefix: ${code?.substring(0, 20)}...`,
      );

      // Validate code format
      if (code.startsWith('=')) {
        this.logger.warn(
          `[${requestId}] Authorization code starts with '=' - potential encoding issue`,
        );
      }

      if (!this.zaloAppId || !this.zaloAppSecret) {
        this.logger.error(`[${requestId}] Missing Zalo app configuration`);
        throw new AppException(
          ErrorCode.CONFIGURATION_ERROR,
          'ZALO_APP_ID hoặc ZALO_APP_SECRET không được định nghĩa',
        );
      }

      this.logger.debug(`[${requestId}] Using Zalo App ID: ${this.zaloAppId}`);

      // Kiểm tra thời gian từ khi nhận code đến khi xử lý
      const processingDelay = Date.now() - startTime;
      if (processingDelay > 5000) {
        this.logger.warn(
          `[${requestId}] Processing delay detected: ${processingDelay}ms`,
        );
      }

      // Lấy access token từ authorization code theo API v4 mới
      this.logger.debug(`[${requestId}] Calling getOaAccessTokenV4...`);
      const tokenStartTime = Date.now();

      const tokenResponse = await this.getOaAccessTokenV4(
        this.zaloAppId,
        this.zaloAppSecret,
        code,
        codeVerifier, // Truyền code verifier nếu có (cho PKCE)
      );

      const tokenDuration = Date.now() - tokenStartTime;
      this.logger.log(
        `[${requestId}] Token exchange completed in ${tokenDuration}ms`,
      );
      this.logger.debug(
        `[${requestId}] Token response received: access_token length=${tokenResponse.access_token?.length}, expires_in=${tokenResponse.expires_in}`,
      );

      // Kết nối Official Account với hệ thống (thời hạn sẽ được tính tự động)
      this.logger.debug(`[${requestId}] Connecting Official Account...`);
      const connectStartTime = Date.now();

      const result = await this.connectOfficialAccount(
        userId,
        tokenResponse.access_token,
        tokenResponse.refresh_token || '',
      );

      const connectDuration = Date.now() - connectStartTime;
      const totalDuration = Date.now() - startTime;

      this.logger.log(
        `[${requestId}] OAuth callback completed successfully in ${totalDuration}ms (token: ${tokenDuration}ms, connect: ${connectDuration}ms)`,
      );
      this.logger.log(
        `[${requestId}] Connected OA: ${result.oaId} - ${result.name}`,
      );

      return result;
    } catch (error) {
      const totalDuration = Date.now() - startTime;
      this.logger.error(
        `[${requestId}] OAuth callback failed after ${totalDuration}ms: ${error.message}`,
      );

      // Log chi tiết lỗi
      if (error.response?.data) {
        this.logger.error(
          `[${requestId}] Error response data: ${JSON.stringify(error.response.data)}`,
        );
      }

      if (error.stack) {
        this.logger.debug(`[${requestId}] Error stack: ${error.stack}`);
      }

      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        `Không thể xử lý callback OAuth: ${error.message}`,
      );
    }
  }

  /**
   * Lấy thông tin Official Account
   * @param accessToken Access token của Official Account
   * @returns Thông tin Official Account
   */
  async getOfficialAccountInfo(
    accessToken: string,
  ): Promise<ZaloOfficialAccountInfoResponseDto> {
    const infoId = `oa_info_${Date.now()}`;

    try {
      this.logger.debug(
        `[${infoId}] Getting OA info with access token: ${accessToken.substring(0, 20)}...`,
      );

      const apiStartTime = Date.now();
      const response = await this.zaloOaService.getOaInfo(accessToken);
      const apiDuration = Date.now() - apiStartTime;

      this.logger.debug(
        `[${infoId}] Zalo OA API call completed in ${apiDuration}ms`,
      );
      this.logger.debug(
        `[${infoId}] Zalo OA Info Response: ${JSON.stringify(response)}`,
      );

      // Kiểm tra response có lỗi không
      if (response.error && response.error !== 0) {
        this.logger.error(
          `[${infoId}] Zalo OA API returned error: ${response.error} - ${response.message}`,
        );
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi từ Zalo OA API: ${response.message || `Mã lỗi ${response.error}`}`,
        );
      }

      // Kiểm tra có data không
      if (!response.data) {
        this.logger.error(`[${infoId}] No data in Zalo OA API response`);
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          'Không nhận được thông tin Official Account từ Zalo API',
        );
      }

      this.logger.debug(
        `[${infoId}] OA Info: id=${response.data.oa_id}, name=${response.data.name}, followers=${response.data.num_follower}`,
      );

      // Response từ Zalo API đã có đúng cấu trúc wrapper
      const wrapperResponse = {
        data: {
          oa_id: response.data.oa_id, // Sử dụng oaid từ response.data
          name: response.data.name,
          description: response.data.description,
          oa_alias: response.data.oa_alias,
          is_verified: response.data.is_verified,
          oa_type: response.data.oa_type,
          cate_name: response.data.cate_name,
          num_follower: response.data.num_follower,
          avatar: response.data.avatar,
          cover: response.data.cover,
          package_name: response.data.package_name,
          package_valid_through_date: response.data.package_valid_through_date,
          package_auto_renew_date: response.data.package_auto_renew_date,
          linked_zca: response.data.linked_ZCA,
        },
        error: response.error,
        message: response.message,
      };

      this.logger.log(
        `[${infoId}] Successfully retrieved OA info for: ${response.data.oa_id} - ${response.data.name}`,
      );
      return { data: wrapperResponse };
    } catch (error) {
      this.logger.error(
        `[${infoId}] Failed to get Official Account info: ${error.message}`,
      );

      if (error.stack) {
        this.logger.debug(`[${infoId}] Error stack: ${error.stack}`);
      }

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        `Không thể lấy thông tin Official Account: ${error.message}`,
      );
    }
  }

  /**
   * Kết nối Official Account với hệ thống
   * @param userId ID của người dùng
   * @param accessToken Access token của Official Account
   * @param refreshToken Refresh token của Official Account
   * @returns Zalo OA Integration đã kết nối
   */
  async connectOfficialAccount(
    userId: number,
    accessToken: string,
    refreshToken: string,
  ): Promise<any> {
    const connectId = `connect_${userId}_${Date.now()}`;

    try {
      this.logger.log(
        `[${connectId}] Starting Official Account connection for user ${userId}`,
      );
      this.logger.debug(
        `[${connectId}] Access token length: ${accessToken?.length}, Refresh token length: ${refreshToken?.length}`,
      );

      // Tính toán thời hạn tự động
      const now = Date.now();
      // Access Token có hiệu lực 25 giờ
      const accessTokenExpiresAt = now + 25 * 60 * 60 * 1000;
      // Refresh Token có hiệu lực 3 tháng (90 ngày)
      const refreshTokenExpiresAt = now + 90 * 24 * 60 * 60 * 1000;

      this.logger.debug(
        `[${connectId}] Access token expires at: ${new Date(accessTokenExpiresAt).toISOString()}`,
      );
      this.logger.debug(
        `[${connectId}] Refresh token expires at: ${new Date(refreshTokenExpiresAt).toISOString()}`,
      );

      // Lấy thông tin Official Account từ Zalo API
      this.logger.debug(`[${connectId}] Fetching OA info from Zalo API...`);
      const oaInfoStartTime = Date.now();

      const oaInfo = await this.getOfficialAccountInfo(accessToken);

      // Kiểm tra xem Official Account đã tồn tại chưa (sử dụng Integration entity)
      const existingOA =
        await this.zaloOfficialAccountRepository.findByUserIdAndOaId(
          userId,
          oaInfo.data.data.oa_id,
        );

      if (existingOA) {
        this.logger.log(
          `[${connectId}] Updating existing OA: ${existingOA.id}`,
        );

        // Cập nhật thông tin nếu đã tồn tại
        // Sử dụng ZaloOALegacyWrapperService để cập nhật với mã hóa mới
        const updatedOA = await this.zaloOfficialAccountRepository.update(
          existingOA.id,
          {
            name: oaInfo.data.data.name,
            description: oaInfo.data.data.description,
            avatarUrl: oaInfo.data.data.avatar,
            accessToken,
            refreshToken,
            expiresAt: accessTokenExpiresAt,
            refreshTokenExpiresAt,
            status: 'active',
          },
        );

        this.logger.log(
          `Updated existing Zalo OA Integration: ${existingOA.id} for user: ${userId}`,
        );
        return updatedOA;
      }

      // Tạo mới nếu chưa tồn tại
      // Sử dụng ZaloOALegacyWrapperService để tạo với mã hóa
      const newOA = await this.zaloOfficialAccountRepository.create({
        userId,
        oaId: oaInfo.data.data.oa_id,
        name: oaInfo.data.data.name,
        description: oaInfo.data.data.description,
        avatarUrl: oaInfo.data.data.avatar,
        accessToken,
        refreshToken,
        expiresAt: accessTokenExpiresAt,
        refreshTokenExpiresAt,
        status: 'active',
        createdAt: now,
        updatedAt: now,
      });

      this.logger.log(
        `Created new Zalo OA Integration: ${newOA.id} for user: ${userId}`,
      );
      return newOA;
    } catch (error) {
      this.logger.error(
        `[${connectId}] Failed to connect Official Account: ${error.message}`,
      );

      if (error.stack) {
        this.logger.debug(`[${connectId}] Error stack: ${error.stack}`);
      }

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        `Không thể kết nối Official Account: ${error.message}`,
      );
    }
  }

  /**
   * Lấy access token của Official Account
   * @param oaId ID của Official Account
   * @returns Access token
   */
  async getOaToken(oaId: string): Promise<string> {
    try {
      // Làm mới token nếu cần
      const oa = await this.refreshAccessToken(oaId);
      return oa.accessToken;
    } catch (error) {
      this.logger.error(`Failed to get OA token: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Không thể lấy access token',
      );
    }
  }

  /**
   * Làm mới access token của Official Account
   * @param oaId ID của Official Account
   * @returns Official Account đã cập nhật
   */
  async refreshAccessToken(oaId: string): Promise<ZaloOfficialAccount> {
    try {
      // Lấy thông tin Official Account từ database với token đã giải mã
      const oa =
        await this.zaloOfficialAccountRepository.findByOaIdWithTokens(oaId);
      if (!oa) {
        throw new AppException(
          ErrorCode.RESOURCE_NOT_FOUND,
          'Không tìm thấy Official Account',
        );
      }

      // Kiểm tra xem token có cần làm mới không
      const now = Date.now();
      if (oa.expiresAt > now + 60000) {
        // Token còn hạn ít nhất 1 phút
        return oa;
      }

      // Kiểm tra xem zaloAppId và zaloAppSecret có tồn tại không
      if (!this.zaloAppId || !this.zaloAppSecret) {
        throw new AppException(
          ErrorCode.CONFIGURATION_ERROR,
          'ZALO_APP_ID hoặc ZALO_APP_SECRET không được định nghĩa',
        );
      }

      // Làm mới token
      const response = await this.sharedZaloService.refreshOaAccessToken(
        this.zaloAppId,
        this.zaloAppSecret,
        oa.refreshToken,
      );

      // Cập nhật thông tin token
      const updatedOA = await this.zaloOfficialAccountRepository.update(oa.id, {
        accessToken: response.access_token,
        refreshToken: response.refresh_token,
        expiresAt: now + response.expires_in * 1000,
        updatedAt: now,
      });

      // Đảm bảo không trả về null
      if (!updatedOA) {
        throw new AppException(
          ErrorCode.DATABASE_ERROR,
          'Không thể cập nhật thông tin token',
        );
      }

      return updatedOA;
    } catch (error) {
      this.logger.error(
        `Failed to refresh access token: ${error.message}`,
        error.stack,
      );

      // Kiểm tra loại lỗi để xử lý phù hợp
      if (error.message?.includes('Không thể giải mã')) {
        throw new AppException(
          ErrorCode.CONFIGURATION_ERROR,
          'Lỗi khi làm mới access token: Dữ liệu mã hóa bị hỏng, vui lòng kết nối lại Zalo OA',
        );
      }

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Không thể làm mới access token',
      );
    }
  }

  /**
   * Làm mới access token cho Official Account theo Integration ID và userId
   * @param userId ID của người dùng
   * @param integrationId ID của Integration (khóa chính)
   * @returns Official Account đã cập nhật với token mới
   */
  async refreshOfficialAccountToken(
    userId: number,
    integrationId: string,
  ): Promise<ZaloOfficialAccount> {
    try {
      // Lấy thông tin Official Account từ database
      const oa = await this.getOfficialAccountByIntegrationId(
        userId,
        integrationId,
      );

      if (!oa) {
        throw new AppException(
          ErrorCode.RESOURCE_NOT_FOUND,
          'Không tìm thấy Official Account hoặc bạn không có quyền truy cập',
        );
      }

      // Kiểm tra trạng thái OA
      if (oa.status !== 'active') {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          `Official Account ${oa.oaId} không ở trạng thái active`,
        );
      }

      // Kiểm tra cấu hình app
      if (!this.zaloAppId || !this.zaloAppSecret) {
        throw new AppException(
          ErrorCode.CONFIGURATION_ERROR,
          'ZALO_APP_ID hoặc ZALO_APP_SECRET không được định nghĩa',
        );
      }

      // Sử dụng refresh token đã lưu trong integration (đã được mã hóa)
      const refreshTokenToUse = oa.refreshToken;
      if (!refreshTokenToUse) {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          'Không có refresh token để làm mới access token',
        );
      }

      this.logger.log(
        `Refreshing access token for OA ${oa.oaId} (Integration ID: ${integrationId})`,
      );

      // Gọi API refresh token
      const response = await this.sharedZaloService.refreshOaAccessToken(
        this.zaloAppId,
        this.zaloAppSecret,
        refreshTokenToUse,
      );

      // Cập nhật token mới vào database
      const now = Date.now();
      const expiresInMs = response.expires_in * 1000; // Chuyển giây thành mili giây
      const newExpiresAt = now + expiresInMs;
      // Refresh token có hiệu lực 3 tháng (90 ngày)
      const newRefreshTokenExpiresAt = now + 90 * 24 * 60 * 60 * 1000;

      const updatedOA = await this.zaloOfficialAccountRepository.update(oa.id, {
        accessToken: response.access_token,
        refreshToken: response.refresh_token || refreshTokenToUse, // Giữ refresh token cũ nếu không có mới
        expiresAt: newExpiresAt,
        refreshTokenExpiresAt: response.refresh_token
          ? newRefreshTokenExpiresAt
          : undefined, // Chỉ cập nhật nếu có refresh token mới
        updatedAt: now,
      });

      if (!updatedOA) {
        throw new AppException(
          ErrorCode.DATABASE_ERROR,
          'Không thể cập nhật access token mới vào database',
        );
      }

      this.logger.log(
        `Successfully refreshed access token for OA ${oa.oaId} (Integration ID: ${integrationId})`,
      );
      return updatedOA;
    } catch (error) {
      this.logger.error(
        `Failed to refresh access token for Integration ${integrationId}: ${error.message}`,
        error.stack,
      );

      // Kiểm tra loại lỗi để xử lý phù hợp
      if (error.message?.includes('Không thể giải mã')) {
        throw new AppException(
          ErrorCode.CONFIGURATION_ERROR,
          'Lỗi khi làm mới access token: Dữ liệu mã hóa bị hỏng, vui lòng kết nối lại Zalo OA',
        );
      }

      if (error instanceof AppException) {
        throw error;
      }

      // Xử lý lỗi từ Zalo API
      if (error.response?.data?.error) {
        const zaloError = error.response.data;
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi từ Zalo API: ${zaloError.message || 'Refresh token không hợp lệ hoặc đã hết hạn'}`,
        );
      }

      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Không thể làm mới access token',
      );
    }
  }

  /**
   * Gửi tin nhắn văn bản đến người dùng Zalo
   * @param oaId ID của Official Account
   * @param userId ID của người dùng Zalo
   * @param message Nội dung tin nhắn
   * @returns Kết quả gửi tin nhắn
   */
  async sendTextMessage(
    oaId: string,
    userId: string,
    message: string,
  ): Promise<ZaloMessage> {
    try {
      // Làm mới token nếu cần
      const oa = await this.refreshAccessToken(oaId);

      // Gửi tin nhắn
      const response = await this.zaloApiClient.post(
        '/oa/message',
        oa.accessToken,
        {
          recipient: {
            user_id: userId,
          },
          message: {
            text: message,
          },
        },
      );

      // Lưu tin nhắn vào database
      const now = Date.now();
      return this.zaloMessageRepository.create({
        oaId,
        userId,
        messageId: response.data?.message_id,
        messageType: 'text',
        content: message,
        direction: 'outgoing',
        timestamp: now,
        createdAt: now,
      });
    } catch (error) {
      this.logger.error(`Failed to send text message: ${error.message}`);
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Không thể gửi tin nhắn văn bản',
      );
    }
  }

  /**
   * Gửi tin nhắn hình ảnh đến người dùng Zalo
   * @param oaId ID của Official Account
   * @param userId ID của người dùng Zalo
   * @param imageUrl URL của hình ảnh
   * @returns Kết quả gửi tin nhắn
   */
  async sendImageMessage(
    oaId: string,
    userId: string,
    imageUrl: string,
  ): Promise<ZaloMessage> {
    try {
      // Làm mới token nếu cần
      const oa = await this.refreshAccessToken(oaId);

      // Gửi tin nhắn
      const response = await this.zaloApiClient.post(
        '/oa/message',
        oa.accessToken,
        {
          recipient: {
            user_id: userId,
          },
          message: {
            attachment: {
              type: 'template',
              payload: {
                template_type: 'media',
                elements: [
                  {
                    media_type: 'image',
                    url: imageUrl,
                  },
                ],
              },
            },
          },
        },
      );

      // Lưu tin nhắn vào database
      const now = Date.now();
      return this.zaloMessageRepository.create({
        oaId,
        userId,
        messageId: response.data?.message_id,
        messageType: 'image',
        content: null,
        data: { imageUrl },
        direction: 'outgoing',
        timestamp: now,
        createdAt: now,
      });
    } catch (error) {
      this.logger.error(`Failed to send image message: ${error.message}`);
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Không thể gửi tin nhắn hình ảnh',
      );
    }
  }

  /**
   * Gửi tin nhắn file đến người dùng Zalo
   * @param oaId ID của Official Account
   * @param userId ID của người dùng Zalo
   * @param fileUrl URL của file
   * @returns Kết quả gửi tin nhắn
   */
  async sendFileMessage(
    oaId: string,
    userId: string,
    fileUrl: string,
  ): Promise<ZaloMessage> {
    try {
      // Làm mới token nếu cần
      const oa = await this.refreshAccessToken(oaId);

      // Gửi tin nhắn
      const response = await this.zaloApiClient.post(
        '/oa/message',
        oa.accessToken,
        {
          recipient: {
            user_id: userId,
          },
          message: {
            attachment: {
              type: 'file',
              payload: {
                url: fileUrl,
              },
            },
          },
        },
      );

      // Lưu tin nhắn vào database
      const now = Date.now();
      return this.zaloMessageRepository.create({
        oaId,
        userId,
        messageId: response.data?.message_id,
        messageType: 'file',
        content: null,
        data: { fileUrl },
        direction: 'outgoing',
        timestamp: now,
        createdAt: now,
      });
    } catch (error) {
      this.logger.error(`Failed to send file message: ${error.message}`);
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Không thể gửi tin nhắn file',
      );
    }
  }

  /**
   * Gửi tin nhắn template đến người dùng Zalo
   * @param oaId ID của Official Account
   * @param userId ID của người dùng Zalo
   * @param templateId ID của template
   * @param templateData Dữ liệu cho template
   * @returns Kết quả gửi tin nhắn
   */
  async sendTemplateMessage(
    oaId: string,
    userId: string,
    templateId: string,
    templateData: Record<string, any>,
  ): Promise<ZaloMessage> {
    try {
      // Làm mới token nếu cần
      const oa = await this.refreshAccessToken(oaId);

      // Gửi tin nhắn
      const response = await this.zaloApiClient.post(
        '/oa/message',
        oa.accessToken,
        {
          recipient: {
            user_id: userId,
          },
          message: {
            attachment: {
              type: 'template',
              payload: {
                template_type: 'list',
                elements: [
                  {
                    title: templateData.title || 'Thông báo',
                    subtitle: templateData.subtitle || '',
                    image_url: templateData.image_url,
                    default_action: templateData.default_action,
                    buttons: templateData.buttons || [],
                  },
                ],
              },
            },
          },
        },
      );

      // Lưu tin nhắn vào database
      const now = Date.now();
      return this.zaloMessageRepository.create({
        oaId,
        userId,
        messageId: response.data?.message_id,
        messageType: 'template',
        content: null,
        data: { templateId, templateData },
        direction: 'outgoing',
        timestamp: now,
        createdAt: now,
      });
    } catch (error) {
      this.logger.error(`Failed to send template message: ${error.message}`);
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Không thể gửi tin nhắn template',
      );
    }
  }

  /**
   * Gửi tin nhắn dựa trên loại tin nhắn
   * @param oaId ID của Official Account
   * @param userId ID của người dùng Zalo
   * @param messageType Loại tin nhắn
   * @param params Các tham số khác tùy thuộc vào loại tin nhắn
   * @returns Kết quả gửi tin nhắn
   */
  async sendMessage(
    oaId: string,
    userId: string,
    messageType: string,
    params: Record<string, any>,
  ): Promise<ZaloMessage> {
    switch (messageType) {
      case 'text':
        return this.sendTextMessage(oaId, userId, params.message);
      case 'image':
        return this.sendImageMessage(oaId, userId, params.imageUrl);
      case 'file':
        return this.sendFileMessage(oaId, userId, params.fileUrl);
      case 'template':
        return this.sendTemplateMessage(
          oaId,
          userId,
          params.templateId,
          params.templateData || {},
        );
      default:
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          `Loại tin nhắn không hợp lệ: ${messageType}`,
        );
    }
  }

  /**
   * Gửi tin nhắn OA với payload tùy chỉnh
   * @param accessToken Access token của Official Account
   * @param messagePayload Payload tin nhắn theo format Zalo API
   * @returns Kết quả gửi tin nhắn
   */
  async sendOaMessage(
    accessToken: string,
    messagePayload: {
      recipient: { user_id: string };
      message: {
        text?: string;
        attachment?: {
          type: string;
          payload: any;
        };
      };
    },
  ): Promise<{ message_id: string }> {
    try {
      this.logger.log(
        `Sending OA message to user ${messagePayload.recipient.user_id}`,
      );

      const response = await this.zaloApiClient.post(
        '/oa/message',
        accessToken,
        messagePayload,
      );

      if (response.error !== 0) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi gửi tin nhắn OA: ${response.message}`,
        );
      }

      return { message_id: response.data?.message_id || '' };
    } catch (error) {
      this.logger.error(`Failed to send OA message: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi gửi tin nhắn OA',
      );
    }
  }

  /**
   * Lấy danh sách người theo dõi của Official Account
   * @param oaId ID của Official Account
   * @param offset Vị trí bắt đầu
   * @param limit Số lượng tối đa
   * @returns Danh sách người theo dõi
   *
   * @deprecated API /oa/getfollowers không còn tồn tại trong Zalo API v3.0
   * Sử dụng getFollowersFromDatabase() để lấy dữ liệu từ database thay thế
   */
  async getFollowers(
    oaId: string,
    offset: number = 0,
    limit: number = 50,
  ): Promise<any> {
    // TODO: API /oa/getfollowers đã bị deprecated trong Zalo API v3.0
    // Cần tìm API thay thế hoặc sử dụng dữ liệu từ database
    this.logger.warn(
      'getFollowers API is deprecated. Use getFollowersFromDatabase instead.',
    );

    throw new AppException(
      ErrorCode.EXTERNAL_SERVICE_ERROR,
      'API lấy danh sách người theo dõi từ Zalo đã bị deprecated. Vui lòng sử dụng dữ liệu từ database.',
    );

    /* COMMENTED OUT - API không còn hoạt động
    try {
      // Làm mới token nếu cần
      const oa = await this.refreshAccessToken(oaId);

      // Lấy danh sách người theo dõi
      const response = await this.zaloApiClient.get('/oa/getfollowers', oa.accessToken, {
        offset,
        count: limit,
      });

      return response;
    } catch (error) {
      this.logger.error(`Failed to get followers: ${error.message}`);
      throw new AppException(ErrorCode.EXTERNAL_SERVICE_ERROR, 'Không thể lấy danh sách người theo dõi');
    }
    */
  }

  /**
   * Lấy danh sách người theo dõi từ database với phân trang và filter
   * @param oaId ID của Official Account
   * @param queryDto Tham số truy vấn
   * @returns Danh sách người theo dõi với phân trang
   */
  async getFollowersFromDatabase(
    oaId: string,
    queryDto: {
      page: number;
      limit: number;
      search?: string;
      sortBy?: string;
      sortDirection?: 'ASC' | 'DESC';
      tag?: string;
      status?: string;
      displayName?: string;
    },
  ): Promise<{
    items: ZaloFollower[];
    meta: {
      totalItems: number;
      itemCount: number;
      itemsPerPage: number;
      totalPages: number;
      currentPage: number;
    };
  }> {
    try {
      const {
        page,
        limit,
        search,
        sortBy,
        sortDirection,
        tag,
        status,
        displayName,
      } = queryDto;
      const skip = (page - 1) * limit;

      // Xây dựng điều kiện tìm kiếm
      const where: Record<string, any> = { oaId };

      if (status && status !== 'all') {
        where.status = status;
      }

      if (search) {
        // Sử dụng ILike của TypeORM thay vì $like
        where.displayName = search ? ILike(`%${search}%`) : undefined;
      }

      if (displayName) {
        // Sử dụng ILike của TypeORM thay vì $like
        where.displayName = displayName ? ILike(`%${displayName}%`) : undefined;
      }

      // Tìm kiếm người theo dõi
      let query = this.zaloFollowerRepository.find({
        where,
        skip,
        take: limit,
        order: {
          [sortBy || 'followedAt']: sortDirection || 'DESC',
        },
      });

      // Nếu có tag, cần xử lý đặc biệt
      if (tag) {
        query = this.zaloFollowerRepository.findByOaIdAndTag(oaId, tag, {
          skip,
          take: limit,
          order: {
            [sortBy || 'followedAt']: sortDirection || 'DESC',
          },
        });
      }

      const [items, totalItems] = await Promise.all([
        query,
        this.zaloFollowerRepository.count({ where }),
      ]);

      // Tính toán thông tin phân trang
      const totalPages = Math.ceil(totalItems / limit);

      return {
        items,
        meta: {
          totalItems,
          itemCount: items.length,
          itemsPerPage: limit,
          totalPages,
          currentPage: page,
        },
      };
    } catch (error) {
      this.logger.error(
        `Failed to get followers from database: ${error.message}`,
      );
      throw new AppException(
        ErrorCode.DATABASE_ERROR,
        'Không thể lấy danh sách người theo dõi từ database',
      );
    }
  }

  /**
   * Lấy lịch sử tin nhắn với một người dùng Zalo
   * @param oaId ID của Official Account
   * @param userId ID của người dùng Zalo
   * @param queryDto Tham số truy vấn
   * @returns Lịch sử tin nhắn với phân trang
   */
  async getMessages(oaId: string, userId: string, queryDto: any): Promise<any> {
    try {
      const {
        page,
        limit,
        search,
        sortBy,
        sortDirection,
        messageType,
        direction,
        content,
      } = queryDto;
      const skip = (page - 1) * limit;

      // Xây dựng điều kiện tìm kiếm
      const where: any = { oaId, userId };

      if (messageType && messageType !== 'all') {
        where.messageType = messageType;
      }

      if (direction && direction !== 'all') {
        where.direction = direction;
      }

      if (search) {
        where.content = search ? ILike(`%${search}%`) : undefined;
      }

      if (content) {
        where.content = content ? ILike(`%${content}%`) : undefined;
      }

      // Tìm kiếm tin nhắn
      const [items, totalItems] = await Promise.all([
        this.zaloMessageRepository.find({
          where,
          skip,
          take: limit,
          order: {
            [sortBy || 'timestamp']: sortDirection || 'DESC',
          },
        }),
        this.zaloMessageRepository.count({ where }),
      ]);

      // Tính toán thông tin phân trang
      const totalPages = Math.ceil(totalItems / limit);

      return {
        items,
        meta: {
          totalItems,
          itemCount: items.length,
          itemsPerPage: limit,
          totalPages,
          currentPage: page,
        },
      };
    } catch (error) {
      this.logger.error(`Failed to get messages: ${error.message}`);
      throw new AppException(
        ErrorCode.DATABASE_ERROR,
        'Không thể lấy lịch sử tin nhắn',
      );
    }
  }

  /**
   * Lấy danh sách Official Account của người dùng
   * @param userId ID của người dùng
   * @returns Danh sách Official Account
   */
  async getOfficialAccounts(userId: number): Promise<ZaloOfficialAccount[]> {
    try {
      return this.zaloOfficialAccountRepository.findByUserId(userId);
    } catch (error) {
      this.logger.error(`Failed to get official accounts: ${error.message}`);
      throw new AppException(
        ErrorCode.DATABASE_ERROR,
        'Không thể lấy danh sách Official Account',
      );
    }
  }

  /**
   * Lấy danh sách Official Account của người dùng có phân trang
   * @param userId ID của người dùng
   * @param queryDto Tham số truy vấn
   * @returns Kết quả phân trang
   */
  async getOfficialAccountsPaginated(
    userId: number,
    queryDto: OfficialAccountQueryDto,
  ): Promise<{
    items: ZaloOfficialAccount[];
    meta: {
      totalItems: number;
      itemCount: number;
      itemsPerPage: number;
      totalPages: number;
      currentPage: number;
    };
  }> {
    try {
      // Sử dụng method mới từ ZaloOALegacyWrapperService
      return await this.zaloOfficialAccountRepository.findWithPagination(
        userId,
        queryDto,
      );
    } catch (error) {
      this.logger.error(
        `Failed to get official accounts with pagination: ${error.message}`,
      );
      throw new AppException(
        ErrorCode.DATABASE_ERROR,
        'Không thể lấy danh sách Official Account có phân trang',
      );
    }
  }

  /**
   * Lấy thông tin chi tiết Official Account theo ID (khóa chính)
   * @param userId ID của người dùng
   * @param id ID của Official Account (khóa chính)
   * @returns Thông tin chi tiết Official Account
   */
  async getOfficialAccountDetailById(
    userId: number,
    id: string,
  ): Promise<ZaloOfficialAccount> {
    try {
      const oa = await this.zaloOfficialAccountRepository.findByIdAndUserId(
        id,
        userId,
      );
      if (!oa) {
        throw new AppException(
          ErrorCode.RESOURCE_NOT_FOUND,
          'Không tìm thấy Official Account',
        );
      }
      return oa;
    } catch (error) {
      this.logger.error(
        `Failed to get official account detail by ID: ${error.message}`,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.DATABASE_ERROR,
        'Không thể lấy thông tin chi tiết Official Account',
      );
    }
  }

  /**
   * Lấy thông tin chi tiết Official Account theo Integration ID
   * @param userId ID của người dùng
   * @param integrationId UUID của Integration
   * @returns Thông tin chi tiết Official Account với token đã giải mã và được refresh nếu cần
   */
  async getOfficialAccountByIntegrationId(
    userId: number,
    integrationId: string,
  ): Promise<ZaloOfficialAccount> {
    try {
      this.logger.debug(
        `Getting OA by integration ID: ${integrationId} for user: ${userId}`,
      );

      const oa =
        await this.zaloOfficialAccountRepository.findByIntegrationIdAndUserId(
          integrationId,
          userId,
        );
      if (!oa) {
        throw new AppException(
          ErrorCode.RESOURCE_NOT_FOUND,
          'Không tìm thấy Integration',
        );
      }

      // Kiểm tra trạng thái OA
      if (oa.status !== 'active') {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          `Official Account ${oa.oaId} không ở trạng thái active`,
        );
      }

      // Kiểm tra xem token có hết hạn không (thêm buffer 5 phút)
      const now = Date.now();
      const tokenExpiryBuffer = 5 * 60 * 1000; // 5 phút
      const isTokenExpired = oa.expiresAt <= now + tokenExpiryBuffer;

      this.logger.debug(
        `Token expiry check - Current: ${now}, Expires: ${oa.expiresAt}, Expired: ${isTokenExpired}`,
      );

      if (isTokenExpired) {
        this.logger.log(
          `Access token for OA ${oa.oaId} is expired or expiring soon, refreshing...`,
        );

        // Kiểm tra refresh token
        if (!oa.refreshToken) {
          throw new AppException(
            ErrorCode.EXTERNAL_SERVICE_ERROR,
            'Token đã hết hạn và không có refresh token để làm mới',
          );
        }

        // Refresh token
        return await this.refreshAccessTokenForOA(oa);
      }

      this.logger.debug(`Token is still valid for OA ${oa.oaId}`);
      return oa;
    } catch (error) {
      this.logger.error(
        `Failed to get official account by integration ID: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.DATABASE_ERROR,
        'Không thể lấy thông tin chi tiết Integration',
      );
    }
  }

  /**
   * Refresh access token cho Official Account
   * @param oa Official Account cần refresh token
   * @returns Official Account với token mới
   */
  private async refreshAccessTokenForOA(
    oa: ZaloOfficialAccount,
  ): Promise<ZaloOfficialAccount> {
    try {
      this.logger.debug(`Refreshing access token for OA: ${oa.oaId}`);

      // Kiểm tra cấu hình app
      if (!this.zaloAppId || !this.zaloAppSecret) {
        throw new AppException(
          ErrorCode.CONFIGURATION_ERROR,
          'ZALO_APP_ID hoặc ZALO_APP_SECRET không được định nghĩa',
        );
      }

      // Gọi API refresh token
      const response = await this.sharedZaloService.refreshOaAccessToken(
        this.zaloAppId,
        this.zaloAppSecret,
        oa.refreshToken,
      );

      this.logger.debug(`Refresh token response received for OA: ${oa.oaId}`);

      // Cập nhật token mới vào database
      const now = Date.now();
      const expiresInMs = response.expires_in * 1000; // Chuyển giây thành mili giây
      const newExpiresAt = now + expiresInMs;
      // Refresh token có hiệu lực 3 tháng (90 ngày)
      const newRefreshTokenExpiresAt = now + 90 * 24 * 60 * 60 * 1000;

      const updatedOA = await this.zaloOfficialAccountRepository.update(oa.id, {
        accessToken: response.access_token,
        refreshToken: response.refresh_token || oa.refreshToken, // Giữ refresh token cũ nếu không có mới
        expiresAt: newExpiresAt,
        refreshTokenExpiresAt: response.refresh_token
          ? newRefreshTokenExpiresAt
          : undefined, // Chỉ cập nhật nếu có refresh token mới
        updatedAt: now,
      });

      if (!updatedOA) {
        throw new AppException(
          ErrorCode.DATABASE_ERROR,
          'Không thể cập nhật access token mới vào database',
        );
      }

      this.logger.log(
        `Successfully refreshed access token for OA: ${oa.oaId}, new expiry: ${newExpiresAt}`,
      );
      return updatedOA;
    } catch (error) {
      this.logger.error(
        `Failed to refresh access token for OA ${oa.oaId}: ${error.message}`,
        error.stack,
      );

      // Nếu refresh token thất bại, có thể deactivate OA
      if (
        error.message?.includes('invalid_grant') ||
        error.message?.includes('refresh_token')
      ) {
        this.logger.warn(
          `Refresh token invalid for OA ${oa.oaId}, marking as inactive`,
        );
        await this.zaloOfficialAccountRepository.update(oa.id, {
          status: 'inactive',
          updatedAt: Date.now(),
        });

        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          'Refresh token không hợp lệ. Vui lòng kết nối lại Official Account',
        );
      }

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Không thể làm mới access token',
      );
    }
  }

  /**
   * Lấy thông tin chi tiết Official Account theo oaId (để tương thích ngược)
   * @param userId ID của người dùng
   * @param oaId ID của Official Account
   * @returns Thông tin chi tiết Official Account với token được refresh nếu cần
   */
  async getOfficialAccountDetail(
    userId: number,
    oaId: string,
  ): Promise<ZaloOfficialAccount> {
    try {
      this.logger.debug(
        `Getting OA detail by oaId: ${oaId} for user: ${userId}`,
      );

      const oa = await this.zaloOfficialAccountRepository.findByUserIdAndOaId(
        userId,
        oaId,
      );
      if (!oa) {
        throw new AppException(
          ErrorCode.RESOURCE_NOT_FOUND,
          'Không tìm thấy Official Account',
        );
      }

      // Kiểm tra trạng thái OA
      if (oa.status !== 'active') {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          `Official Account ${oa.oaId} không ở trạng thái active`,
        );
      }

      // Kiểm tra xem token có hết hạn không (thêm buffer 5 phút)
      const now = Date.now();
      const tokenExpiryBuffer = 5 * 60 * 1000; // 5 phút
      const isTokenExpired = oa.expiresAt <= now + tokenExpiryBuffer;

      this.logger.debug(
        `Token expiry check - Current: ${now}, Expires: ${oa.expiresAt}, Expired: ${isTokenExpired}`,
      );

      if (isTokenExpired) {
        this.logger.log(
          `Access token for OA ${oa.oaId} is expired or expiring soon, refreshing...`,
        );

        // Kiểm tra refresh token
        if (!oa.refreshToken) {
          throw new AppException(
            ErrorCode.EXTERNAL_SERVICE_ERROR,
            'Token đã hết hạn và không có refresh token để làm mới',
          );
        }

        // Refresh token
        return await this.refreshAccessTokenForOA(oa);
      }

      this.logger.debug(`Token is still valid for OA ${oa.oaId}`);
      return oa;
    } catch (error) {
      this.logger.error(
        `Failed to get official account detail: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.DATABASE_ERROR,
        'Không thể lấy thông tin chi tiết Official Account',
      );
    }
  }

  /**
   * Lấy thông tin chi tiết Official Account từ Zalo API
   * @param userId ID của người dùng
   * @param oaId ID của Official Account
   * @returns Thông tin chi tiết Official Account từ Zalo API
   */
  async getDetailedOfficialAccountInfo(
    userId: number,
    oaId: string,
  ): Promise<any> {
    try {
      // Kiểm tra quyền truy cập Official Account
      await this.getOfficialAccountDetail(userId, oaId);

      // Lấy access token hợp lệ (tự động refresh nếu hết hạn)
      const accessToken = await this.getOaToken(oaId);

      // Gọi Zalo API để lấy thông tin chi tiết Official Account
      const detailedOaInfo =
        await this.zaloOaService.getDetailedOaInfo(accessToken);

      return detailedOaInfo;
    } catch (error) {
      this.logger.error(
        `Failed to get detailed official account info: ${error.message}`,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Không thể lấy thông tin chi tiết Official Account từ Zalo API',
      );
    }
  }

  /**
   * Lấy thông tin chi tiết Official Account từ Zalo API theo integration ID
   * @param userId ID của người dùng
   * @param id ID của Integration
   * @returns Thông tin chi tiết Official Account từ Zalo API
   */
  async getDetailedOfficialAccountInfoById(
    userId: number,
    id: string,
  ): Promise<any> {
    try {
      // Kiểm tra quyền truy cập Official Account
      const oa = await this.getOfficialAccountDetailById(userId, id);

      // Lấy access token hợp lệ (tự động refresh nếu hết hạn)
      const accessToken = await this.getOaToken(oa.oaId);

      // Gọi Zalo API để lấy thông tin chi tiết Official Account
      const detailedOaInfo =
        await this.zaloOaService.getDetailedOaInfo(accessToken);

      return detailedOaInfo;
    } catch (error) {
      this.logger.error(
        `Failed to get detailed official account info by ID: ${error.message}`,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Không thể lấy thông tin chi tiết Official Account',
      );
    }
  }

  /**
   * Lấy thông tin quota message chi tiết của Official Account
   * @param userId ID của người dùng
   * @param oaId ID của Official Account
   * @param quotaOwner Thực thể sở hữu Quota muốn truy xuất
   * @param productType Loại sản phẩm (cs hoặc transaction)
   * @param quotaType Loại quota (sub_quota, purchase_quota, reward_quota)
   * @returns Thông tin quota message chi tiết
   */
  async getQuotaMessage(
    userId: number,
    oaId: string,
    quotaOwner: string,
    productType?: 'cs' | 'transaction',
    quotaType?: 'sub_quota' | 'purchase_quota' | 'reward_quota',
  ): Promise<any> {
    try {
      // Kiểm tra quyền truy cập Official Account
      await this.getOfficialAccountDetail(userId, oaId);

      // Lấy access token hợp lệ (tự động refresh nếu hết hạn)
      const accessToken = await this.getOaToken(oaId);

      // Tạo request body
      const request = {
        quota_owner: quotaOwner,
        ...(productType && { product_type: productType }),
        ...(quotaType && { quota_type: quotaType }),
      };

      // Gọi API Zalo để lấy thông tin quota message
      const result = await this.zaloOaService.getQuotaMessage(
        accessToken,
        request,
      );

      return result;
    } catch (error) {
      this.logger.error(`Failed to get quota message: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Không thể lấy thông tin quota message',
      );
    }
  }

  /**
   * Lấy thông tin quota message chi tiết của Official Account theo integration ID
   * @param userId ID của người dùng
   * @param id ID của Integration
   * @param quotaOwner Thực thể sở hữu Quota muốn truy xuất
   * @param productType Loại sản phẩm (cs hoặc transaction)
   * @param quotaType Loại quota (sub_quota, purchase_quota, reward_quota)
   * @returns Thông tin quota message chi tiết
   */
  async getQuotaMessageById(
    userId: number,
    id: string,
    quotaOwner: string,
    productType?: 'cs' | 'transaction',
    quotaType?: 'sub_quota' | 'purchase_quota' | 'reward_quota',
  ): Promise<any> {
    try {
      // Kiểm tra quyền truy cập Official Account
      const oa = await this.getOfficialAccountDetailById(userId, id);

      // Lấy access token hợp lệ (tự động refresh nếu hết hạn)
      const accessToken = await this.getOaToken(oa.oaId);

      // Tạo request body
      const request = {
        quota_owner: quotaOwner,
        ...(productType && { product_type: productType }),
        ...(quotaType && { quota_type: quotaType }),
      };

      // Gọi API Zalo để lấy thông tin quota message
      const result = await this.zaloOaService.getQuotaMessage(
        accessToken,
        request,
      );

      return result;
    } catch (error) {
      this.logger.error(`Failed to get quota message by ID: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Không thể lấy thông tin quota message',
      );
    }
  }

  /**
   * Lấy danh sách người dùng Zalo với các tùy chọn lọc nâng cao
   * @param userId ID của người dùng
   * @param oaId ID của Official Account
   * @param offset Thứ tự của người dùng đầu tiên trong danh sách trả về
   * @param count Số lượng người dùng muốn lấy (tối đa 50)
   * @param tagName Tên của nhãn được gắn cho người dùng (tùy chọn)
   * @param lastInteractionPeriod Khoảng thời gian tương tác gần nhất (tùy chọn)
   * @param isFollower Trạng thái quan tâm OA của người dùng (tùy chọn)
   * @returns Danh sách người dùng
   */
  async getUserList(
    userId: number,
    oaId: string,
    offset: number,
    count: number,
    tagName?: string,
    lastInteractionPeriod?: string,
    isFollower?: boolean,
  ): Promise<any> {
    try {
      // Kiểm tra quyền truy cập Official Account
      await this.getOfficialAccountDetail(userId, oaId);

      // Lấy access token hợp lệ (tự động refresh nếu hết hạn)
      const accessToken = await this.getOaToken(oaId);

      // Tạo request - đảm bảo offset >= 0 theo yêu cầu của Zalo API
      const request = {
        offset: Math.max(offset, 0),
        count: Math.min(Math.max(count, 1), 50),
        ...(tagName && { tag_name: tagName }),
        ...(lastInteractionPeriod && {
          last_interaction_period: lastInteractionPeriod,
        }),
        ...(isFollower !== undefined && { is_follower: isFollower }),
      };

      // Gọi API Zalo để lấy danh sách người dùng
      const result = await this.zaloUserManagementService.getUserList(
        accessToken,
        request,
      );

      return result;
    } catch (error) {
      this.logger.error(`Failed to get user list: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Không thể lấy danh sách người dùng',
      );
    }
  }

  /**
   * Ngắt kết nối Official Account
   * @param userId ID của người dùng
   * @param oaId ID của Official Account
   * @returns true nếu ngắt kết nối thành công
   */
  async disconnectOfficialAccount(
    userId: number,
    oaId: string,
  ): Promise<boolean> {
    try {
      const oa = await this.zaloOfficialAccountRepository.findByUserIdAndOaId(
        userId,
        oaId,
      );
      if (!oa) {
        throw new AppException(
          ErrorCode.RESOURCE_NOT_FOUND,
          'Không tìm thấy Official Account',
        );
      }

      // Cập nhật trạng thái
      await this.zaloOfficialAccountRepository.update(oa.id, {
        status: 'inactive',
        updatedAt: Date.now(),
      });

      return true;
    } catch (error) {
      this.logger.error(
        `Failed to disconnect official account: ${error.message}`,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.DATABASE_ERROR,
        'Không thể ngắt kết nối Official Account',
      );
    }
  }

  /**
   * Ngắt kết nối Official Account theo integration ID
   * @param userId ID của người dùng
   * @param id ID của Integration
   * @returns true nếu ngắt kết nối thành công
   */
  async disconnectOfficialAccountById(
    userId: number,
    id: string,
  ): Promise<boolean> {
    try {
      const oa = await this.zaloOfficialAccountRepository.findByIdAndUserId(
        id,
        userId,
      );
      if (!oa) {
        throw new AppException(
          ErrorCode.RESOURCE_NOT_FOUND,
          'Không tìm thấy Official Account',
        );
      }

      // Cập nhật trạng thái
      await this.zaloOfficialAccountRepository.update(oa.id, {
        status: 'inactive',
        updatedAt: Date.now(),
      });

      return true;
    } catch (error) {
      this.logger.error(
        `Failed to disconnect official account by ID: ${error.message}`,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.DATABASE_ERROR,
        'Không thể ngắt kết nối Official Account',
      );
    }
  }

  /**
   * Xóa nhiều Official Accounts
   * @param userId ID của người dùng
   * @param oaIds Danh sách ID của các Official Account cần xóa
   * @returns Kết quả xóa nhiều Official Accounts
   */
  async bulkDeleteOfficialAccounts(
    userId: number,
    oaIds: string[],
  ): Promise<{
    totalRequested: number;
    successCount: number;
    failureCount: number;
    successfulDeletes: string[];
    failedDeletes: Array<{ oaId: string; reason: string }>;
    message: string;
  }> {
    try {
      const totalRequested = oaIds.length;
      const successfulDeletes: string[] = [];
      const failedDeletes: Array<{ oaId: string; reason: string }> = [];

      this.logger.log(
        `Starting bulk delete for ${totalRequested} Official Accounts for user ${userId}`,
      );

      // Xử lý từng Official Account
      for (const oaId of oaIds) {
        try {
          // Kiểm tra Official Account có tồn tại và thuộc về user không
          const oa =
            await this.zaloOfficialAccountRepository.findByUserIdAndOaId(
              userId,
              oaId,
            );

          if (!oa) {
            failedDeletes.push({
              oaId,
              reason:
                'Không tìm thấy Official Account hoặc không có quyền truy cập',
            });
            continue;
          }

          // Kiểm tra xem Official Account có đang được sử dụng trong các chiến dịch hoặc tự động hóa không
          const [campaigns] =
            await this.zaloCampaignRepository.findWithPagination({
              where: { oaId },
              skip: 0,
              take: 1,
            });

          if (campaigns.length > 0) {
            failedDeletes.push({
              oaId,
              reason:
                'Official Account đang được sử dụng trong chiến dịch, không thể xóa',
            });
            continue;
          }

          const [automations] =
            await this.zaloAutomationRepository.findWithPagination({
              where: { oaId },
              skip: 0,
              take: 1,
            });

          if (automations.length > 0) {
            failedDeletes.push({
              oaId,
              reason:
                'Official Account đang được sử dụng trong tự động hóa, không thể xóa',
            });
            continue;
          }

          // Xóa các dữ liệu liên quan trước khi xóa Official Account
          await this.deleteRelatedData(oaId, userId);

          // Xóa Official Account
          const deleteResult = await this.zaloOfficialAccountRepository.delete(
            oa.id,
          );

          if (deleteResult) {
            successfulDeletes.push(oaId);
            this.logger.log(`Successfully deleted Official Account: ${oaId}`);
          } else {
            failedDeletes.push({
              oaId,
              reason: 'Lỗi khi xóa Official Account khỏi database',
            });
          }
        } catch (error) {
          this.logger.error(
            `Failed to delete Official Account ${oaId}: ${error.message}`,
          );
          failedDeletes.push({
            oaId,
            reason: error.message || 'Lỗi không xác định',
          });
        }
      }

      const successCount = successfulDeletes.length;
      const failureCount = failedDeletes.length;

      const message =
        failureCount === 0
          ? `Đã xóa thành công tất cả ${successCount} Official Account`
          : `Đã xóa thành công ${successCount}/${totalRequested} Official Account`;

      this.logger.log(
        `Bulk delete completed: ${successCount} success, ${failureCount} failed`,
      );

      return {
        totalRequested,
        successCount,
        failureCount,
        successfulDeletes,
        failedDeletes,
        message,
      };
    } catch (error) {
      this.logger.error(
        `Failed to bulk delete Official Accounts: ${error.message}`,
      );
      throw new AppException(
        ErrorCode.DATABASE_ERROR,
        'Không thể xóa nhiều Official Account',
      );
    }
  }

  /**
   * Xóa nhiều Official Accounts theo integration IDs
   * @param userId ID của người dùng
   * @param ids Danh sách ID của các Integration cần xóa
   * @returns Kết quả xóa nhiều Official Accounts
   */
  async bulkDeleteOfficialAccountsByIds(
    userId: number,
    ids: string[],
  ): Promise<{
    totalRequested: number;
    successCount: number;
    failureCount: number;
    successfulDeletes: string[];
    failedDeletes: Array<{ id: string; reason: string }>;
    message: string;
  }> {
    try {
      const totalRequested = ids.length;
      const successfulDeletes: string[] = [];
      const failedDeletes: Array<{ id: string; reason: string }> = [];

      this.logger.log(
        `Starting bulk delete for ${totalRequested} Official Accounts by IDs for user ${userId}`,
      );

      // Xử lý từng Official Account
      for (const id of ids) {
        try {
          // Kiểm tra Official Account có tồn tại và thuộc về user không
          const oa = await this.zaloOfficialAccountRepository.findByIdAndUserId(
            id,
            userId,
          );

          if (!oa) {
            failedDeletes.push({
              id,
              reason:
                'Không tìm thấy Official Account hoặc không có quyền truy cập',
            });
            continue;
          }

          // Kiểm tra xem Official Account có đang được sử dụng trong các chiến dịch hoặc tự động hóa không
          const [campaigns] =
            await this.zaloCampaignRepository.findWithPagination({
              where: { oaId: oa.oaId },
              skip: 0,
              take: 1,
            });

          if (campaigns.length > 0) {
            failedDeletes.push({
              id,
              reason:
                'Official Account đang được sử dụng trong chiến dịch, không thể xóa',
            });
            continue;
          }

          const [automations] =
            await this.zaloAutomationRepository.findWithPagination({
              where: { oaId: oa.oaId },
              skip: 0,
              take: 1,
            });

          if (automations.length > 0) {
            failedDeletes.push({
              id,
              reason:
                'Official Account đang được sử dụng trong tự động hóa, không thể xóa',
            });
            continue;
          }

          // Xóa các dữ liệu liên quan trước khi xóa Official Account
          await this.deleteRelatedData(oa.oaId, userId);

          // Xóa Official Account
          const deleteResult = await this.zaloOfficialAccountRepository.delete(
            oa.id,
          );

          if (deleteResult) {
            successfulDeletes.push(id);
            this.logger.log(`Successfully deleted Official Account: ${id}`);
          } else {
            failedDeletes.push({
              id,
              reason: 'Lỗi khi xóa Official Account khỏi database',
            });
          }
        } catch (error) {
          this.logger.error(
            `Failed to delete Official Account ${id}: ${error.message}`,
          );
          failedDeletes.push({
            id,
            reason: error.message || 'Lỗi không xác định',
          });
        }
      }

      const successCount = successfulDeletes.length;
      const failureCount = failedDeletes.length;

      const message =
        failureCount === 0
          ? `Đã xóa thành công tất cả ${successCount} Official Account`
          : `Đã xóa thành công ${successCount}/${totalRequested} Official Account`;

      this.logger.log(
        `Bulk delete by IDs completed: ${successCount} success, ${failureCount} failed`,
      );

      return {
        totalRequested,
        successCount,
        failureCount,
        successfulDeletes,
        failedDeletes,
        message,
      };
    } catch (error) {
      this.logger.error(
        `Failed to bulk delete Official Accounts by IDs: ${error.message}`,
      );
      throw new AppException(
        ErrorCode.DATABASE_ERROR,
        'Không thể xóa nhiều Official Account',
      );
    }
  }

  /**
   * Xóa nhiều followers
   * @param userId ID của người dùng
   * @param oaId ID của Official Account
   * @param userIds Danh sách User ID của followers cần xóa
   * @returns Kết quả xóa nhiều followers
   */
  async bulkDeleteFollowers(
    userId: number,
    oaId: string,
    userIds: string[],
  ): Promise<{
    totalRequested: number;
    successCount: number;
    failureCount: number;
    successfulDeletes: string[];
    failedDeletes: Array<{ userId: string; reason: string }>;
    message: string;
  }> {
    try {
      // Kiểm tra OA có tồn tại và thuộc về user không
      const officialAccount =
        await this.zaloOfficialAccountRepository.findByUserIdAndOaId(
          userId,
          oaId,
        );
      if (!officialAccount) {
        throw new AppException(
          ErrorCode.RESOURCE_NOT_FOUND,
          'Official Account không tồn tại hoặc không thuộc về bạn',
        );
      }

      const totalRequested = userIds.length;
      const successfulDeletes: string[] = [];
      const failedDeletes: Array<{ userId: string; reason: string }> = [];

      this.logger.log(
        `Starting bulk delete for ${totalRequested} followers for OA ${oaId} by user ${userId}`,
      );

      for (const userId of userIds) {
        try {
          // Kiểm tra follower có tồn tại không
          const follower =
            await this.zaloFollowerRepository.findByOaIdAndUserId(oaId, userId);
          if (!follower) {
            failedDeletes.push({
              userId,
              reason: 'Follower không tồn tại',
            });
            continue;
          }

          // Xóa follower
          await this.zaloFollowerRepository.delete(follower.id);
          successfulDeletes.push(userId);

          this.logger.log(
            `Successfully deleted follower ${userId} for OA ${oaId}`,
          );
        } catch (error) {
          this.logger.error(
            `Failed to delete follower ${userId}: ${error.message}`,
          );
          failedDeletes.push({
            userId,
            reason: error.message || 'Lỗi không xác định',
          });
        }
      }

      const successCount = successfulDeletes.length;
      const failureCount = failedDeletes.length;

      const message =
        failureCount === 0
          ? `Đã xóa thành công tất cả ${successCount} followers`
          : `Đã xóa thành công ${successCount}/${totalRequested} followers`;

      this.logger.log(
        `Bulk delete followers completed: ${successCount} success, ${failureCount} failed`,
      );

      return {
        totalRequested,
        successCount,
        failureCount,
        successfulDeletes,
        failedDeletes,
        message,
      };
    } catch (error) {
      this.logger.error(`Failed to bulk delete followers: ${error.message}`);
      throw new AppException(
        ErrorCode.DATABASE_ERROR,
        'Không thể xóa nhiều followers',
      );
    }
  }

  /**
   * Xóa nhiều messages
   * @param userId ID của người dùng
   * @param oaId ID của Official Account
   * @param messageIds Danh sách ID của messages cần xóa
   * @returns Kết quả xóa nhiều messages
   */
  async bulkDeleteMessages(
    userId: number,
    oaId: string,
    messageIds: string[],
  ): Promise<{
    totalRequested: number;
    successCount: number;
    failureCount: number;
    successfulDeletes: string[];
    failedDeletes: Array<{ messageId: string; reason: string }>;
    message: string;
  }> {
    try {
      // Kiểm tra OA có tồn tại và thuộc về user không
      const officialAccount =
        await this.zaloOfficialAccountRepository.findByUserIdAndOaId(
          userId,
          oaId,
        );
      if (!officialAccount) {
        throw new AppException(
          ErrorCode.RESOURCE_NOT_FOUND,
          'Official Account không tồn tại hoặc không thuộc về bạn',
        );
      }

      const totalRequested = messageIds.length;
      const successfulDeletes: string[] = [];
      const failedDeletes: Array<{ messageId: string; reason: string }> = [];

      this.logger.log(
        `Starting bulk delete for ${totalRequested} messages for OA ${oaId} by user ${userId}`,
      );

      for (const messageId of messageIds) {
        try {
          // Kiểm tra message có tồn tại không
          const message =
            await this.zaloMessageRepository.findByMessageId(messageId);
          if (!message || message.oaId !== oaId) {
            failedDeletes.push({
              messageId,
              reason: 'Message không tồn tại hoặc không thuộc về OA này',
            });
            continue;
          }

          // Xóa message
          await this.zaloMessageRepository.delete(message.id);
          successfulDeletes.push(messageId);

          this.logger.log(
            `Successfully deleted message ${messageId} for OA ${oaId}`,
          );
        } catch (error) {
          this.logger.error(
            `Failed to delete message ${messageId}: ${error.message}`,
          );
          failedDeletes.push({
            messageId,
            reason: error.message || 'Lỗi không xác định',
          });
        }
      }

      const successCount = successfulDeletes.length;
      const failureCount = failedDeletes.length;

      const message =
        failureCount === 0
          ? `Đã xóa thành công tất cả ${successCount} messages`
          : `Đã xóa thành công ${successCount}/${totalRequested} messages`;

      this.logger.log(
        `Bulk delete messages completed: ${successCount} success, ${failureCount} failed`,
      );

      return {
        totalRequested,
        successCount,
        failureCount,
        successfulDeletes,
        failedDeletes,
        message,
      };
    } catch (error) {
      this.logger.error(`Failed to bulk delete messages: ${error.message}`);
      throw new AppException(
        ErrorCode.DATABASE_ERROR,
        'Không thể xóa nhiều messages',
      );
    }
  }

  /**
   * Xóa nhiều segments
   * @param userId ID của người dùng
   * @param oaId ID của Official Account
   * @param segmentIds Danh sách ID của segments cần xóa
   * @returns Kết quả xóa nhiều segments
   */
  async bulkDeleteSegments(
    userId: number,
    oaId: string,
    segmentIds: number[],
  ): Promise<{
    totalRequested: number;
    successCount: number;
    failureCount: number;
    successfulDeletes: number[];
    failedDeletes: Array<{ segmentId: number; reason: string }>;
    message: string;
  }> {
    try {
      // Kiểm tra OA có tồn tại và thuộc về user không
      const officialAccount =
        await this.zaloOfficialAccountRepository.findByUserIdAndOaId(
          userId,
          oaId,
        );
      if (!officialAccount) {
        throw new AppException(
          ErrorCode.RESOURCE_NOT_FOUND,
          'Official Account không tồn tại hoặc không thuộc về bạn',
        );
      }

      const totalRequested = segmentIds.length;
      const successfulDeletes: number[] = [];
      const failedDeletes: Array<{ segmentId: number; reason: string }> = [];

      this.logger.log(
        `Starting bulk delete for ${totalRequested} segments for OA ${oaId} by user ${userId}`,
      );

      for (const segmentId of segmentIds) {
        try {
          // Kiểm tra segment có tồn tại không
          const segment = await this.zaloSegmentRepository.findById(segmentId);
          if (!segment || segment.oaId !== oaId || segment.userId !== userId) {
            failedDeletes.push({
              segmentId,
              reason: 'Segment không tồn tại hoặc không thuộc về bạn',
            });
            continue;
          }

          // Xóa segment (bỏ qua việc kiểm tra campaign sử dụng segment vì không có method phù hợp)
          await this.zaloSegmentRepository.delete(segmentId);
          successfulDeletes.push(segmentId);

          this.logger.log(
            `Successfully deleted segment ${segmentId} for OA ${oaId}`,
          );
        } catch (error) {
          this.logger.error(
            `Failed to delete segment ${segmentId}: ${error.message}`,
          );
          failedDeletes.push({
            segmentId,
            reason: error.message || 'Lỗi không xác định',
          });
        }
      }

      const successCount = successfulDeletes.length;
      const failureCount = failedDeletes.length;

      const message =
        failureCount === 0
          ? `Đã xóa thành công tất cả ${successCount} segments`
          : `Đã xóa thành công ${successCount}/${totalRequested} segments`;

      this.logger.log(
        `Bulk delete segments completed: ${successCount} success, ${failureCount} failed`,
      );

      return {
        totalRequested,
        successCount,
        failureCount,
        successfulDeletes,
        failedDeletes,
        message,
      };
    } catch (error) {
      this.logger.error(`Failed to bulk delete segments: ${error.message}`);
      throw new AppException(
        ErrorCode.DATABASE_ERROR,
        'Không thể xóa nhiều segments',
      );
    }
  }

  /**
   * Xóa các dữ liệu liên quan đến Official Account
   * @param oaId ID của Official Account
   * @param userId ID của người dùng
   */
  private async deleteRelatedData(oaId: string, userId: number): Promise<void> {
    try {
      this.logger.log(
        `Starting to delete related data for Official Account: ${oaId}`,
      );

      // Xóa segments
      const segments = await this.zaloSegmentRepository.findByUserIdAndOaId(
        userId,
        oaId,
      );
      for (const segment of segments) {
        await this.zaloSegmentRepository.delete(segment.id);
      }
      this.logger.log(`Deleted ${segments.length} segments for OA: ${oaId}`);

      // Lưu ý: Các dữ liệu khác như followers, messages, webhook logs
      // có thể được xóa bằng cascade delete hoặc soft delete
      // Tùy thuộc vào thiết kế database schema

      this.logger.log(
        `Successfully deleted related data for Official Account: ${oaId}`,
      );
    } catch (error) {
      this.logger.error(
        `Failed to delete related data for Official Account ${oaId}: ${error.message}`,
      );
      // Không throw error ở đây để không làm gián đoạn quá trình xóa chính
    }
  }

  /**
   * Lấy thông tin người theo dõi
   * @param oaId ID của Official Account
   * @param userId ID của người dùng Zalo
   * @returns Thông tin người theo dõi
   */
  async getFollowerProfile(
    oaId: string,
    userId: string,
  ): Promise<ZaloFollower> {
    try {
      // Làm mới token nếu cần
      const oa = await this.refreshAccessToken(oaId);

      // Lấy thông tin người theo dõi
      const response = await this.zaloApiClient.get(
        '/oa/getprofile',
        oa.accessToken,
        {
          user_id: userId,
        },
      );

      // Kiểm tra xem người theo dõi đã tồn tại trong database chưa
      const existingFollower =
        await this.zaloFollowerRepository.findByOaIdAndUserId(oaId, userId);

      const now = Date.now();
      const followerData = {
        oaId,
        userId,
        displayName: response.data.display_name,
        avatarUrl: response.data.avatar,
        phone: response.data.phone,
        gender: response.data.user_gender,
        birthDate: response.data.birth_date,
        status: 'active',
        updatedAt: now,
      };

      if (existingFollower) {
        // Cập nhật thông tin nếu đã tồn tại
        const updatedFollower = await this.zaloFollowerRepository.update(
          existingFollower.id,
          followerData,
        );

        // Đảm bảo không trả về null
        if (!updatedFollower) {
          throw new AppException(
            ErrorCode.DATABASE_ERROR,
            'Không thể cập nhật thông tin người theo dõi',
          );
        }

        return updatedFollower;
      }

      // Tạo mới nếu chưa tồn tại
      return this.zaloFollowerRepository.create({
        ...followerData,
        followedAt: now,
        tags: [],
        createdAt: now,
      });
    } catch (error) {
      this.logger.error(`Failed to get follower profile: ${error.message}`);
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Không thể lấy thông tin người theo dõi',
      );
    }
  }

  /**
   * Xử lý webhook từ Zalo
   * @param data Dữ liệu webhook
   * @returns Kết quả xử lý
   */
  async handleWebhook(data: any): Promise<any> {
    try {
      const now = Date.now();

      // Lưu log webhook
      const webhookLog = await this.zaloWebhookLogRepository.create({
        oaId: data.oa_id,
        eventName: data.event_name,
        eventId: data.event_id,
        data,
        processed: false,
        timestamp: now,
        createdAt: now,
      });

      // Xử lý sự kiện theo loại
      switch (data.event_name) {
        case 'follow':
          await this.handleFollowEvent(data);
          // Kích hoạt tự động hóa cho sự kiện follow
          await this.processAutomationTrigger(data.oa_id, 'follow', {
            userId: data.follower.id,
            displayName: data.follower.display_name,
            timestamp: data.timestamp,
          });
          break;
        case 'unfollow':
          await this.handleUnfollowEvent(data);
          // Kích hoạt tự động hóa cho sự kiện unfollow
          await this.processAutomationTrigger(data.oa_id, 'unfollow', {
            userId: data.follower.id,
            displayName: data.follower.display_name,
            timestamp: data.timestamp,
          });
          break;
        case 'user_send_text':
          await this.handleUserSendTextEvent(data);
          // Kích hoạt tự động hóa cho sự kiện message
          await this.processAutomationTrigger(data.oa_id, 'message', {
            userId: data.sender.id,
            displayName: data.sender.display_name,
            messageType: 'text',
            message: data.message.text,
            timestamp: data.timestamp,
          });
          break;
        case 'user_send_image':
          await this.handleUserSendImageEvent(data);
          // Kích hoạt tự động hóa cho sự kiện message
          await this.processAutomationTrigger(data.oa_id, 'message', {
            userId: data.sender.id,
            displayName: data.sender.display_name,
            messageType: 'image',
            attachments: data.message.attachments,
            timestamp: data.timestamp,
          });
          break;
        case 'zns_status':
          await this.handleZnsStatusEvent(data);
          break;
        // Thêm các sự kiện khác nếu cần
      }

      // Đánh dấu đã xử lý
      await this.zaloWebhookLogRepository.markAsProcessed(webhookLog.id);

      return { success: true };
    } catch (error) {
      this.logger.error(`Failed to handle webhook: ${error.message}`);
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Không thể xử lý webhook',
      );
    }
  }

  /**
   * Xử lý sự kiện người dùng theo dõi
   * @param data Dữ liệu sự kiện
   */
  private async handleFollowEvent(data: any): Promise<void> {
    const oaId = data.oa_id;
    const userId = data.follower.id;
    const now = Date.now();

    // Kiểm tra xem người theo dõi đã tồn tại trong database chưa
    const existingFollower =
      await this.zaloFollowerRepository.findByOaIdAndUserId(oaId, userId);

    if (existingFollower) {
      // Cập nhật trạng thái nếu đã tồn tại
      await this.zaloFollowerRepository.update(existingFollower.id, {
        status: 'active',
        followedAt: now,
        unfollowedAt: undefined, // Sử dụng undefined thay vì null
        updatedAt: now,
      });
    } else {
      // Tạo mới nếu chưa tồn tại
      await this.zaloFollowerRepository.create({
        oaId,
        userId,
        displayName: data.follower.display_name,
        avatarUrl: '',
        status: 'active',
        followedAt: now,
        tags: [],
        createdAt: now,
        updatedAt: now,
      });

      // Lấy thông tin chi tiết của người theo dõi
      await this.getFollowerProfile(oaId, userId);
    }
  }

  /**
   * Xử lý sự kiện người dùng hủy theo dõi
   * @param data Dữ liệu sự kiện
   */
  private async handleUnfollowEvent(data: any): Promise<void> {
    const oaId = data.oa_id;
    const userId = data.follower.id;
    const now = Date.now();

    // Cập nhật trạng thái người theo dõi
    const follower = await this.zaloFollowerRepository.findByOaIdAndUserId(
      oaId,
      userId,
    );
    if (follower) {
      await this.zaloFollowerRepository.update(follower.id, {
        status: 'unfollowed',
        unfollowedAt: now,
        updatedAt: now,
      });
    }
  }

  /**
   * Xử lý sự kiện người dùng gửi tin nhắn văn bản
   * @param data Dữ liệu sự kiện
   */
  private async handleUserSendTextEvent(data: any): Promise<void> {
    const oaId = data.oa_id;
    const userId = data.sender.id;
    const messageId = data.message.msg_id;
    const text = data.message.text;
    const timestamp = data.timestamp;

    // Lưu tin nhắn vào database
    await this.zaloMessageRepository.create({
      oaId,
      userId,
      messageId,
      messageType: 'text',
      content: text,
      direction: 'incoming',
      timestamp,
      createdAt: Date.now(),
    });
  }

  /**
   * Xử lý sự kiện người dùng gửi hình ảnh
   * @param data Dữ liệu sự kiện
   */
  private async handleUserSendImageEvent(data: any): Promise<void> {
    const oaId = data.oa_id;
    const userId = data.sender.id;
    const messageId = data.message.msg_id;
    const attachments = data.message.attachments;
    const timestamp = data.timestamp;

    // Lưu tin nhắn vào database
    await this.zaloMessageRepository.create({
      oaId,
      userId,
      messageId,
      messageType: 'image',
      content: '',
      data: attachments,
      direction: 'incoming',
      timestamp,
      createdAt: Date.now(),
    });
  }

  /**
   * Xử lý sự kiện cập nhật trạng thái tin nhắn ZNS
   * @param data Dữ liệu sự kiện
   */
  private async handleZnsStatusEvent(data: any): Promise<void> {
    const trackingId = data.tracking_id;
    const status = data.status.toLowerCase();
    const now = Date.now();

    // Cập nhật trạng thái tin nhắn ZNS
    const znsMessage =
      await this.zaloZnsMessageRepository.findByTrackingId(trackingId);
    if (znsMessage) {
      let updatedStatus = znsMessage.status;
      let deliveredTime = znsMessage.deliveredTime;

      if (status === 'success') {
        updatedStatus = 'delivered';
        deliveredTime = now;
      } else if (status === 'failed') {
        updatedStatus = 'failed';
      }

      await this.zaloZnsMessageRepository.updateByTrackingId(trackingId, {
        status: updatedStatus,
        deliveredTime,
        updatedAt: now,
      });
    }
  }

  /**
   * Lấy danh sách template ZNS
   * @param userId ID của người dùng
   * @param oaId ID của Official Account
   * @param queryDto Tham số truy vấn
   * @returns Danh sách template ZNS với phân trang
   */
  async getZnsTemplates(
    userId: number,
    oaId: string,
    queryDto: any,
  ): Promise<any> {
    try {
      const {
        page,
        limit,
        search,
        sortBy,
        sortDirection,
        templateName,
        status,
      } = queryDto;
      const skip = (page - 1) * limit;

      // Xây dựng điều kiện tìm kiếm
      const where: any = { userId, oaId };

      if (status && status !== 'all') {
        where.status = status;
      }

      if (search) {
        where.templateName = search ? ILike(`%${search}%`) : undefined;
      }

      if (templateName) {
        where.templateName = templateName
          ? ILike(`%${templateName}%`)
          : undefined;
      }

      // Tìm kiếm template ZNS
      const [items, totalItems] = await Promise.all([
        this.zaloZnsTemplateRepository.find({
          where,
          skip,
          take: limit,
          order: {
            [sortBy || 'createdAt']: sortDirection || 'DESC',
          },
        }),
        this.zaloZnsTemplateRepository.count({ where }),
      ]);

      // Tính toán thông tin phân trang
      const totalPages = Math.ceil(totalItems / limit);

      return {
        items,
        meta: {
          totalItems,
          itemCount: items.length,
          itemsPerPage: limit,
          totalPages,
          currentPage: page,
        },
      };
    } catch (error) {
      this.logger.error(`Failed to get ZNS templates: ${error.message}`);
      throw new AppException(
        ErrorCode.DATABASE_ERROR,
        'Không thể lấy danh sách template ZNS',
      );
    }
  }

  /**
   * Lấy thông tin chi tiết template ZNS
   * @param userId ID của người dùng
   * @param oaId ID của Official Account
   * @param templateId ID của template
   * @returns Thông tin chi tiết template ZNS
   */
  async getZnsTemplateDetail(
    userId: number,
    oaId: string,
    id: number,
  ): Promise<any> {
    try {
      const template = await this.zaloZnsTemplateRepository.findOne({
        where: { id, userId, oaId },
      });

      if (!template) {
        throw new AppException(
          ErrorCode.RESOURCE_NOT_FOUND,
          'Không tìm thấy template ZNS',
        );
      }

      return template;
    } catch (error) {
      this.logger.error(`Failed to get ZNS template detail: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.DATABASE_ERROR,
        'Không thể lấy thông tin chi tiết template ZNS',
      );
    }
  }

  /**
   * Đăng ký template ZNS
   * @param userId ID của người dùng
   * @param oaId ID của Official Account
   * @param registerDto Dữ liệu đăng ký
   * @returns Template ZNS đã đăng ký
   */
  async registerZnsTemplate(
    userId: number,
    oaId: string,
    registerDto: any,
  ): Promise<any> {
    try {
      // Làm mới token nếu cần
      const oa = await this.refreshAccessToken(oaId);

      // Đăng ký template ZNS với Zalo API
      const response = await this.zaloApiClient.post(
        '/oa/template/create',
        oa.accessToken,
        {
          name: registerDto.templateName,
          content: registerDto.templateContent,
          params: registerDto.params,
        },
      );

      // Lưu template vào database
      const now = Date.now();
      return this.zaloZnsTemplateRepository.create({
        userId,
        oaId,
        templateId: response.data.template_id,
        templateName: registerDto.templateName,
        templateContent: registerDto.templateContent,
        status: 'pending',
        createdAt: now,
        updatedAt: now,
      });
    } catch (error) {
      this.logger.error(`Failed to register ZNS template: ${error.message}`);
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Không thể đăng ký template ZNS',
      );
    }
  }

  /**
   * Cập nhật trạng thái template ZNS
   * @param userId ID của người dùng
   * @param oaId ID của Official Account
   * @param templateId ID của template
   * @param status Trạng thái mới
   * @returns Template ZNS đã cập nhật
   */
  async updateZnsTemplateStatus(
    userId: number,
    oaId: string,
    id: number,
    status: string,
  ): Promise<any> {
    try {
      const template = await this.zaloZnsTemplateRepository.findOne({
        where: { id, userId, oaId },
      });

      if (!template) {
        throw new AppException(
          ErrorCode.RESOURCE_NOT_FOUND,
          'Không tìm thấy template ZNS',
        );
      }

      // Cập nhật trạng thái
      return this.zaloZnsTemplateRepository.update(id, {
        status,
        updatedAt: Date.now(),
      });
    } catch (error) {
      this.logger.error(
        `Failed to update ZNS template status: ${error.message}`,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.DATABASE_ERROR,
        'Không thể cập nhật trạng thái template ZNS',
      );
    }
  }

  /**
   * Gửi tin nhắn ZNS
   * @param userId ID của người dùng
   * @param oaId ID của Official Account
   * @param sendDto Dữ liệu gửi tin nhắn
   * @returns Kết quả gửi tin nhắn
   */
  async sendZnsMessage(
    userId: number,
    oaId: string,
    sendDto: any,
  ): Promise<any> {
    try {
      // Làm mới token nếu cần
      const oa = await this.refreshAccessToken(oaId);

      // Kiểm tra template tồn tại
      const template = await this.zaloZnsTemplateRepository.findOne({
        where: { templateId: sendDto.templateId, oaId },
      });

      if (!template) {
        throw new AppException(
          ErrorCode.RESOURCE_NOT_FOUND,
          'Không tìm thấy template ZNS',
        );
      }

      if (template.status !== 'approved') {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          'Template ZNS chưa được phê duyệt',
        );
      }

      // Tạo tracking ID
      const trackingId = `zns_${Date.now()}_${Math.floor(Math.random() * 1000)}`;

      // Gửi tin nhắn ZNS
      const response = await this.zaloApiClient.post(
        '/oa/message/template',
        oa.accessToken,
        {
          phone: sendDto.phone,
          template_id: sendDto.templateId,
          template_data: sendDto.templateData,
          tracking_id: trackingId,
        },
      );

      // Lưu tin nhắn vào database
      const now = Date.now();
      return this.zaloZnsMessageRepository.create({
        userId,
        oaId,
        templateId: sendDto.templateId,
        phone: sendDto.phone,
        messageId: response.data?.message_id,
        trackingId,
        templateData: sendDto.templateData,
        status: 'pending',
        createdAt: now,
        updatedAt: now,
      });
    } catch (error) {
      this.logger.error(`Failed to send ZNS message: ${error.message}`);
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Không thể gửi tin nhắn ZNS',
      );
    }
  }

  /**
   * Lấy lịch sử tin nhắn ZNS
   * @param userId ID của người dùng
   * @param oaId ID của Official Account
   * @param queryDto Tham số truy vấn
   * @returns Lịch sử tin nhắn ZNS với phân trang
   */
  async getZnsMessages(
    userId: number,
    oaId: string,
    queryDto: any,
  ): Promise<any> {
    try {
      const {
        page,
        limit,
        search,
        sortBy,
        sortDirection,
        phone,
        templateId,
        status,
      } = queryDto;
      const skip = (page - 1) * limit;

      // Xây dựng điều kiện tìm kiếm
      const where: any = { userId, oaId };

      if (status && status !== 'all') {
        where.status = status;
      }

      if (templateId) {
        where.templateId = templateId;
      }

      if (phone) {
        where.phone = phone;
      }

      if (search) {
        where.phone = search ? ILike(`%${search}%`) : undefined;
      }

      // Tìm kiếm tin nhắn ZNS
      const [items, totalItems] = await Promise.all([
        this.zaloZnsMessageRepository.find({
          where,
          skip,
          take: limit,
          order: {
            [sortBy || 'createdAt']: sortDirection || 'DESC',
          },
        }),
        this.zaloZnsMessageRepository.count({ where }),
      ]);

      // Tính toán thông tin phân trang
      const totalPages = Math.ceil(totalItems / limit);

      return {
        items,
        meta: {
          totalItems,
          itemCount: items.length,
          itemsPerPage: limit,
          totalPages,
          currentPage: page,
        },
      };
    } catch (error) {
      this.logger.error(`Failed to get ZNS messages: ${error.message}`);
      throw new AppException(
        ErrorCode.DATABASE_ERROR,
        'Không thể lấy lịch sử tin nhắn ZNS',
      );
    }
  }

  /**
   * Lấy thông tin chi tiết tin nhắn ZNS
   * @param userId ID của người dùng
   * @param oaId ID của Official Account
   * @param id ID của tin nhắn
   * @returns Thông tin chi tiết tin nhắn ZNS
   */
  async getZnsMessageDetail(
    userId: number,
    oaId: string,
    id: number,
  ): Promise<any> {
    try {
      const message = await this.zaloZnsMessageRepository.findOne({
        where: { id, userId, oaId },
      });

      if (!message) {
        throw new AppException(
          ErrorCode.RESOURCE_NOT_FOUND,
          'Không tìm thấy tin nhắn ZNS',
        );
      }

      return message;
    } catch (error) {
      this.logger.error(`Failed to get ZNS message detail: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.DATABASE_ERROR,
        'Không thể lấy thông tin chi tiết tin nhắn ZNS',
      );
    }
  }

  /**
   * Lấy danh sách phân đoạn Zalo
   * @param userId ID của người dùng
   * @param oaId ID của Official Account
   * @param queryDto Tham số truy vấn
   * @returns Danh sách phân đoạn Zalo với phân trang
   */
  async getZaloSegments(
    userId: number,
    oaId: string,
    queryDto: any,
  ): Promise<any> {
    try {
      const { page, limit, search, sortBy, sortDirection, name } = queryDto;
      const skip = (page - 1) * limit;

      // Xây dựng điều kiện tìm kiếm
      const where: any = { userId, oaId };

      if (search) {
        where.name = search ? ILike(`%${search}%`) : undefined;
      }

      if (name) {
        where.name = name ? ILike(`%${name}%`) : undefined;
      }

      // Tìm kiếm phân đoạn
      const [items, totalItems] = await Promise.all([
        this.zaloSegmentRepository
          .findWithPagination({
            where,
            skip,
            take: limit,
            order: {
              [sortBy || 'createdAt']: sortDirection || 'DESC',
            },
          })
          .then(([items, _]) => items),
        this.zaloSegmentRepository
          .findWithPagination({ where, skip: 0, take: 1 })
          .then(([_, count]) => count),
      ]);

      // Tính toán thông tin phân trang
      const totalPages = Math.ceil(totalItems / limit);

      return {
        items,
        meta: {
          totalItems,
          itemCount: items.length,
          itemsPerPage: limit,
          totalPages,
          currentPage: page,
        },
      };
    } catch (error) {
      this.logger.error(`Failed to get Zalo segments: ${error.message}`);
      throw new AppException(
        ErrorCode.DATABASE_ERROR,
        'Không thể lấy danh sách phân đoạn Zalo',
      );
    }
  }

  /**
   * Lấy thông tin chi tiết phân đoạn Zalo
   * @param userId ID của người dùng
   * @param oaId ID của Official Account
   * @param id ID của phân đoạn
   * @returns Thông tin chi tiết phân đoạn Zalo
   */
  async getZaloSegmentDetail(
    userId: number,
    oaId: string,
    id: number,
  ): Promise<any> {
    try {
      const segment = await this.zaloSegmentRepository.findByIdAndUserIdAndOaId(
        id,
        userId,
        oaId,
      );

      if (!segment) {
        throw new AppException(
          ErrorCode.RESOURCE_NOT_FOUND,
          'Không tìm thấy phân đoạn Zalo',
        );
      }

      return segment;
    } catch (error) {
      this.logger.error(`Failed to get Zalo segment detail: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.DATABASE_ERROR,
        'Không thể lấy thông tin chi tiết phân đoạn Zalo',
      );
    }
  }

  /**
   * Tạo phân đoạn Zalo mới
   * @param userId ID của người dùng
   * @param oaId ID của Official Account
   * @param createDto Dữ liệu tạo phân đoạn
   * @returns Phân đoạn Zalo đã tạo
   */
  async createZaloSegment(
    userId: number,
    oaId: string,
    createDto: any,
  ): Promise<any> {
    try {
      // Kiểm tra quyền truy cập Official Account
      await this.getOfficialAccountDetail(userId, oaId);

      // Tạo phân đoạn mới
      const now = Date.now();
      const segment = await this.zaloSegmentRepository.create({
        userId,
        oaId,
        name: createDto.name,
        description: createDto.description,
        conditions: createDto.conditions,
        followerCount: 0,
        // lastUpdatedAt: now, // Thuộc tính này không tồn tại trong entity
        createdAt: now,
        updatedAt: now,
      });

      // Cập nhật số lượng người theo dõi thuộc phân đoạn
      await this.refreshZaloSegment(userId, oaId, segment.id);

      return segment;
    } catch (error) {
      this.logger.error(`Failed to create Zalo segment: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.DATABASE_ERROR,
        'Không thể tạo phân đoạn Zalo',
      );
    }
  }

  /**
   * Cập nhật phân đoạn Zalo
   * @param userId ID của người dùng
   * @param oaId ID của Official Account
   * @param id ID của phân đoạn
   * @param updateDto Dữ liệu cập nhật
   * @returns Phân đoạn Zalo đã cập nhật
   */
  async updateZaloSegment(
    userId: number,
    oaId: string,
    id: number,
    updateDto: any,
  ): Promise<any> {
    try {
      // Kiểm tra phân đoạn tồn tại
      const segment = await this.getZaloSegmentDetail(userId, oaId, id);

      // Cập nhật phân đoạn
      const updateData: any = {
        updatedAt: Date.now(),
      };

      if (updateDto.name !== undefined) {
        updateData.name = updateDto.name;
      }

      if (updateDto.description !== undefined) {
        updateData.description = updateDto.description;
      }

      if (updateDto.conditions !== undefined) {
        updateData.conditions = updateDto.conditions;
        // updateData.lastUpdatedAt = Date.now(); // Thuộc tính này không tồn tại trong entity
      }

      const updatedSegment = await this.zaloSegmentRepository.update(
        id,
        updateData,
      );

      // Nếu điều kiện thay đổi, cập nhật số lượng người theo dõi
      if (updateDto.conditions !== undefined) {
        await this.refreshZaloSegment(userId, oaId, id);
      }

      return updatedSegment;
    } catch (error) {
      this.logger.error(`Failed to update Zalo segment: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.DATABASE_ERROR,
        'Không thể cập nhật phân đoạn Zalo',
      );
    }
  }

  /**
   * Xóa phân đoạn Zalo
   * @param userId ID của người dùng
   * @param oaId ID của Official Account
   * @param id ID của phân đoạn
   * @returns true nếu xóa thành công
   */
  async deleteZaloSegment(
    userId: number,
    oaId: string,
    id: number,
  ): Promise<boolean> {
    try {
      // Kiểm tra phân đoạn tồn tại
      await this.getZaloSegmentDetail(userId, oaId, id);

      // Kiểm tra xem phân đoạn có đang được sử dụng trong chiến dịch hoặc tự động hóa không
      const [_, campaignCount] =
        await this.zaloCampaignRepository.findWithPagination({
          where: { segmentId: id },
          skip: 0,
          take: 1,
        });

      if (campaignCount > 0) {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          'Không thể xóa phân đoạn đang được sử dụng trong chiến dịch',
        );
      }

      // Xóa phân đoạn
      await this.zaloSegmentRepository.delete(id);

      return true;
    } catch (error) {
      this.logger.error(`Failed to delete Zalo segment: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.DATABASE_ERROR,
        'Không thể xóa phân đoạn Zalo',
      );
    }
  }

  /**
   * Cập nhật số lượng người theo dõi thuộc phân đoạn
   * @param userId ID của người dùng
   * @param oaId ID của Official Account
   * @param id ID của phân đoạn
   * @returns Phân đoạn Zalo đã cập nhật
   */
  async refreshZaloSegment(
    userId: number,
    oaId: string,
    id: number,
  ): Promise<any> {
    try {
      // Kiểm tra phân đoạn tồn tại
      const segment = await this.getZaloSegmentDetail(userId, oaId, id);

      // Lấy danh sách người theo dõi thuộc phân đoạn
      // Thay thế phương thức getZaloSegmentFollowers bằng cách lấy trực tiếp từ repository
      const followers = await this.zaloFollowerRepository.findByOaIdAndUserId(
        oaId,
        userId.toString(),
      );

      // Lọc followers theo điều kiện của segment nếu cần
      const filteredFollowers = Array.isArray(followers)
        ? followers
        : [followers];
      if (segment.conditions) {
        // Thực hiện lọc theo điều kiện
        // Đây chỉ là giải pháp tạm thời, cần triển khai đầy đủ sau
      }

      // Cập nhật số lượng người theo dõi
      const updatedSegment = await this.zaloSegmentRepository.update(id, {
        followerCount: filteredFollowers.length,
        // lastUpdatedAt: Date.now(), // Thuộc tính này không tồn tại trong entity
        updatedAt: Date.now(),
      });

      return updatedSegment;
    } catch (error) {
      this.logger.error(`Failed to refresh Zalo segment: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.DATABASE_ERROR,
        'Không thể cập nhật phân đoạn Zalo',
      );
    }
  }

  /**
   * Lấy danh sách người theo dõi thuộc phân đoạn
   * @param userId ID của người dùng
   * @param oaId ID của Official Account
   * @param id ID của phân đoạn
   * @param queryDto Tham số truy vấn
   * @returns Danh sách người theo dõi thuộc phân đoạn với phân trang
   */
  async getZaloSegmentFollowers(
    userId: number,
    oaId: string,
    id: number,
    queryDto: any,
  ): Promise<any> {
    try {
      // Kiểm tra phân đoạn tồn tại
      const segment = await this.getZaloSegmentDetail(userId, oaId, id);

      const { page, limit } = queryDto;
      const skip = (page - 1) * limit;

      // Lấy tất cả người theo dõi của Official Account
      const allFollowers = await this.zaloFollowerRepository.find({
        where: { oaId, status: 'active' },
      });

      // Lọc người theo dõi theo điều kiện của phân đoạn
      const filteredFollowers = this.filterFollowersBySegmentConditions(
        allFollowers,
        segment.conditions,
      );

      // Phân trang kết quả
      const startIndex = skip;
      const endIndex = Math.min(startIndex + limit, filteredFollowers.length);
      const items = filteredFollowers.slice(startIndex, endIndex);

      // Tính toán thông tin phân trang
      const totalItems = filteredFollowers.length;
      const totalPages = Math.ceil(totalItems / limit);

      return {
        items,
        meta: {
          totalItems,
          itemCount: items.length,
          itemsPerPage: limit,
          totalPages,
          currentPage: page,
        },
      };
    } catch (error) {
      this.logger.error(
        `Failed to get Zalo segment followers: ${error.message}`,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.DATABASE_ERROR,
        'Không thể lấy danh sách người theo dõi thuộc phân đoạn',
      );
    }
  }

  /**
   * Lọc người theo dõi theo điều kiện của phân đoạn
   * @param followers Danh sách người theo dõi
   * @param conditions Điều kiện phân đoạn
   * @returns Danh sách người theo dõi đã lọc
   */
  private filterFollowersBySegmentConditions(
    followers: any[],
    conditions: any[],
  ): any[] {
    if (!conditions || conditions.length === 0) {
      return followers;
    }

    return followers.filter((follower) => {
      // Kiểm tra tất cả các điều kiện
      return conditions.every((condition) => {
        const { field, operator, value } = condition;

        // Lấy giá trị của trường cần so sánh
        const fieldValue = this.getFollowerFieldValue(follower, field);

        // So sánh theo toán tử
        switch (operator) {
          case 'equals':
            return fieldValue === value;
          case 'not_equals':
            return fieldValue !== value;
          case 'contains':
            return typeof fieldValue === 'string' && fieldValue.includes(value);
          case 'not_contains':
            return (
              typeof fieldValue === 'string' && !fieldValue.includes(value)
            );
          case 'starts_with':
            return (
              typeof fieldValue === 'string' && fieldValue.startsWith(value)
            );
          case 'ends_with':
            return typeof fieldValue === 'string' && fieldValue.endsWith(value);
          case 'greater_than':
            return fieldValue > value;
          case 'less_than':
            return fieldValue < value;
          case 'in':
            return Array.isArray(value) && value.includes(fieldValue);
          case 'not_in':
            return Array.isArray(value) && !value.includes(fieldValue);
          case 'has_any_tag':
            return (
              Array.isArray(follower.tags) &&
              Array.isArray(value) &&
              value.some((tag) => follower.tags.includes(tag))
            );
          case 'has_all_tags':
            return (
              Array.isArray(follower.tags) &&
              Array.isArray(value) &&
              value.every((tag) => follower.tags.includes(tag))
            );
          case 'does_not_have_tag':
            return (
              Array.isArray(follower.tags) &&
              Array.isArray(value) &&
              !value.some((tag) => follower.tags.includes(tag))
            );
          default:
            return true;
        }
      });
    });
  }

  /**
   * Lấy giá trị của trường trong đối tượng người theo dõi
   * @param follower Đối tượng người theo dõi
   * @param field Tên trường
   * @returns Giá trị của trường
   */
  private getFollowerFieldValue(follower: any, field: string): any {
    // Xử lý các trường đặc biệt
    if (field === 'tags') {
      return follower.tags || [];
    }

    // Xử lý các trường lồng nhau (ví dụ: 'data.custom.field')
    const parts = field.split('.');
    let value = follower;

    for (const part of parts) {
      if (value === null || value === undefined) {
        return undefined;
      }
      value = value[part];
    }

    return value;
  }

  /**
   * Lấy danh sách chiến dịch Zalo theo userId (không cần oaId)
   * @param userId ID của người dùng
   * @param queryDto Tham số truy vấn
   * @returns Danh sách chiến dịch Zalo với phân trang
   */
  async getZaloCampaignsByUserId(userId: number, queryDto: any): Promise<any> {
    try {
      const {
        page = 1,
        limit = 10,
        search,
        sortBy = 'createdAt',
        sortDirection = 'DESC',
        name,
        type,
        status,
        oaId,
        integrationId,
      } = queryDto;
      const skip = (page - 1) * limit;

      // Xây dựng điều kiện tìm kiếm - chỉ cần userId
      const where: any = { userId };

      // Nếu có integrationId thì lookup oaId
      if (integrationId && integrationId.trim() !== '') {
        const oaId = await this.getOaIdFromIntegrationId(userId, integrationId);
        where.oaId = oaId;
      }
      // Nếu có oaId thì filter theo oaId (backward compatibility)
      else if (oaId && oaId.trim() !== '') {
        where.oaId = oaId;
      }

      if (search) {
        where.name = search ? ILike(`%${search}%`) : undefined;
      }

      if (name) {
        where.name = name ? ILike(`%${name}%`) : undefined;
      }

      if (type) {
        where.type = type;
      }

      if (status) {
        where.status = status;
      }

      // Xây dựng điều kiện sắp xếp
      const order: any = {};
      if (sortBy && sortDirection) {
        order[sortBy] = sortDirection.toUpperCase();
      } else {
        order.createdAt = 'DESC';
      }

      const [items, totalItems] =
        await this.zaloCampaignRepository.findWithPagination({
          where,
          skip,
          take: limit,
          order,
        });

      return {
        items,
        meta: {
          totalItems,
          itemCount: items.length,
          itemsPerPage: limit,
          totalPages: Math.ceil(totalItems / limit),
          currentPage: page,
        },
      };
    } catch (error) {
      this.logger.error(
        `Failed to get Zalo campaigns by userId: ${error.message}`,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        MARKETING_ERROR_CODES.ZALO_CAMPAIGN_FETCH_FAILED,
        'Không thể lấy danh sách chiến dịch Zalo',
      );
    }
  }

  /**
   * Lấy chi tiết chiến dịch Zalo theo userId và campaignId (không cần oaId)
   * @param userId ID của người dùng
   * @param campaignId ID của chiến dịch
   * @returns Chi tiết chiến dịch Zalo
   */
  async getZaloCampaignDetailByUserId(
    userId: number,
    campaignId: number,
  ): Promise<ZaloCampaign> {
    try {
      const campaign = await this.zaloCampaignRepository.findByIdAndUserId(
        campaignId,
        userId,
      );
      if (!campaign) {
        throw new AppException(
          MARKETING_ERROR_CODES.ZALO_CAMPAIGN_NOT_FOUND,
          'Không tìm thấy chiến dịch Zalo',
        );
      }
      return campaign;
    } catch (error) {
      this.logger.error(
        `Failed to get Zalo campaign detail by userId: ${error.message}`,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        MARKETING_ERROR_CODES.ZALO_CAMPAIGN_FETCH_FAILED,
        'Không thể lấy thông tin chi tiết chiến dịch Zalo',
      );
    }
  }

  /**
   * Lấy thống kê chiến dịch Zalo theo userId
   * @param userId ID của người dùng
   * @param oaId ID của Official Account (optional)
   * @returns Thống kê chiến dịch Zalo
   */
  async getZaloCampaignStatisticsByUserId(
    userId: number,
    oaId?: string,
  ): Promise<any> {
    try {
      // Xây dựng điều kiện where
      const where: any = { userId };
      if (oaId) {
        where.oaId = oaId;
      }

      // Lấy tất cả campaigns của user
      let campaigns: ZaloCampaign[];
      if (oaId) {
        campaigns = await this.zaloCampaignRepository.findByUserIdAndOaId(
          userId,
          oaId,
        );
      } else {
        // Nếu không có oaId, lấy tất cả campaigns của user
        const [allCampaigns] =
          await this.zaloCampaignRepository.findWithPagination({
            where: { userId },
            skip: 0,
            take: 10000, // Lấy tất cả
            order: { createdAt: 'DESC' },
          });
        campaigns = allCampaigns;
      }

      // Tính toán thống kê
      const statistics = {
        totalCampaigns: campaigns.length,
        draftCampaigns: campaigns.filter(
          (c) => c.status === ZaloCampaignStatus.DRAFT,
        ).length,
        scheduledCampaigns: campaigns.filter(
          (c) => c.status === ZaloCampaignStatus.SCHEDULED,
        ).length,
        runningCampaigns: campaigns.filter(
          (c) => c.status === ZaloCampaignStatus.RUNNING,
        ).length,
        completedCampaigns: campaigns.filter(
          (c) => c.status === ZaloCampaignStatus.COMPLETED,
        ).length,
        failedCampaigns: campaigns.filter(
          (c) => c.status === ZaloCampaignStatus.FAILED,
        ).length,
        totalRecipients: campaigns.reduce(
          (sum, c) => sum + (c.totalRecipients || 0),
          0,
        ),
        totalSuccessCount: campaigns.reduce(
          (sum, c) => sum + (c.successCount || 0),
          0,
        ),
        totalFailureCount: campaigns.reduce(
          (sum, c) => sum + (c.failureCount || 0),
          0,
        ),
      };

      return statistics;
    } catch (error) {
      this.logger.error(
        `Failed to get Zalo campaign statistics by userId: ${error.message}`,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        MARKETING_ERROR_CODES.ZALO_CAMPAIGN_FETCH_FAILED,
        'Không thể lấy thống kê chiến dịch Zalo',
      );
    }
  }

  /**
   * Lấy danh sách chiến dịch Zalo
   * @param userId ID của người dùng
   * @param oaId ID của Official Account
   * @param queryDto Tham số truy vấn
   * @returns Danh sách chiến dịch Zalo với phân trang
   */
  async getZaloCampaigns(
    userId: number,
    oaId: string,
    queryDto: any,
  ): Promise<any> {
    try {
      const { page, limit, search, sortBy, sortDirection, name, type, status } =
        queryDto;
      const skip = (page - 1) * limit;

      // Xây dựng điều kiện tìm kiếm
      const where: any = { userId, oaId };

      if (search) {
        where.name = search ? ILike(`%${search}%`) : undefined;
      }

      if (name) {
        where.name = name ? ILike(`%${name}%`) : undefined;
      }

      if (type) {
        where.type = type;
      }

      if (status) {
        where.status = status;
      }

      // Tìm kiếm chiến dịch
      const [items, totalItems] = await Promise.all([
        this.zaloCampaignRepository
          .findWithPagination({
            where,
            skip,
            take: limit,
            order: {
              [sortBy || 'createdAt']: sortDirection || 'DESC',
            },
          })
          .then(([items, _]) => items),
        this.zaloCampaignRepository
          .findWithPagination({ where, skip: 0, take: 1 })
          .then(([_, count]) => count),
      ]);

      // Tính toán thông tin phân trang
      const totalPages = Math.ceil(totalItems / limit);

      return {
        items,
        meta: {
          totalItems,
          itemCount: items.length,
          itemsPerPage: limit,
          totalPages,
          currentPage: page,
        },
      };
    } catch (error) {
      this.logger.error(`Failed to get Zalo campaigns: ${error.message}`);
      throw new AppException(
        ErrorCode.DATABASE_ERROR,
        'Không thể lấy danh sách chiến dịch Zalo',
      );
    }
  }

  /**
   * Lấy thông tin chi tiết chiến dịch Zalo
   * @param userId ID của người dùng
   * @param oaId ID của Official Account
   * @param id ID của chiến dịch
   * @returns Thông tin chi tiết chiến dịch Zalo
   */
  async getZaloCampaignDetail(
    userId: number,
    oaId: string,
    id: number,
  ): Promise<any> {
    try {
      const campaign =
        await this.zaloCampaignRepository.findByIdAndUserIdAndOaId(
          id,
          userId,
          oaId,
        );

      if (!campaign) {
        throw new AppException(
          ErrorCode.RESOURCE_NOT_FOUND,
          'Không tìm thấy chiến dịch Zalo',
        );
      }

      return campaign;
    } catch (error) {
      this.logger.error(`Failed to get Zalo campaign detail: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.DATABASE_ERROR,
        'Không thể lấy thông tin chi tiết chiến dịch Zalo',
      );
    }
  }

  /**
   * Tạo chiến dịch Zalo mới
   * @param userId ID của người dùng
   * @param oaId ID của Official Account
   * @param createDto Dữ liệu tạo chiến dịch
   * @returns Chiến dịch Zalo đã tạo
   */
  async createZaloCampaign(
    userId: number,
    oaId: string,
    createDto: any,
  ): Promise<any> {
    try {
      // Kiểm tra quyền truy cập Official Account
      await this.getOfficialAccountDetail(userId, oaId);

      // Không cần kiểm tra segment vì giờ sử dụng userIds trực tiếp

      // Tạo chiến dịch mới
      const now = Date.now();
      let messageContent: ZaloCampaignMessageContentDto | undefined = undefined;
      let znsContent: ZaloCampaignZnsContentDto | undefined = undefined;
      let consultationSequenceContent: any = undefined;
      let broadcastContent: any = undefined;
      let groupMessageContent: ZaloCampaignGroupMessageContentDto | undefined =
        undefined;

      if (createDto.type === 'message') {
        messageContent = createDto.messageContent;
      } else if (createDto.type === 'zns') {
        znsContent = createDto.znsContent;
      } else if (createDto.type === ZaloCampaignType.CONSULTATION_SEQUENCE) {
        consultationSequenceContent = createDto.consultationSequenceContent;
      } else if (createDto.type === ZaloCampaignType.GROUP_MESSAGE) {
        // Đối với GROUP_MESSAGE, bắt buộc phải có groupMessageContent
        if (!createDto.groupMessageContent) {
          throw new Error(
            'Đối với chiến dịch GROUP_MESSAGE, phải cung cấp groupMessageContent',
          );
        }
        groupMessageContent = createDto.groupMessageContent;
      } else if (createDto.type === ZaloCampaignType.BROADCAST) {
        // Đối với BROADCAST, bắt buộc phải có attachmentId và recipient
        if (!createDto.attachmentId || !createDto.recipient) {
          throw new Error(
            'Đối với chiến dịch BROADCAST, phải cung cấp cả attachmentId và recipient',
          );
        }

        // Xử lý cả string và array
        const attachmentIds = Array.isArray(createDto.attachmentId)
          ? createDto.attachmentId
          : [createDto.attachmentId];

        // Sử dụng recipient từ createDto
        const recipient = createDto.recipient;

        // Lưu thông tin về nhiều attachments để xử lý sau
        broadcastContent = {
          broadcastData: {
            recipient: recipient,
            message: {
              attachment: {
                type: 'template',
                payload: {
                  template_type: 'media',
                  elements: [
                    {
                      media_type: 'article',
                      attachment_id: attachmentIds[0], // Sử dụng attachment đầu tiên làm mặc định
                    },
                  ],
                },
              },
            },
          },
          // Lưu thông tin về tất cả attachments để xử lý khi gửi
          attachmentIds: attachmentIds,
        };
      }

      // Xác định status và totalRecipients dựa trên type
      let status = ZaloCampaignStatus.DRAFT;
      let totalRecipients = 0;
      let segment: any = null;

      if (createDto.type === ZaloCampaignType.CONSULTATION_SEQUENCE) {
        status = ZaloCampaignStatus.RUNNING;
        totalRecipients = createDto.userIds?.length || 0;
        // Lưu thông tin segment dạng JSON với userIds
        segment = {
          type: 'user_list',
          userIds: createDto.userIds,
          totalUsers: totalRecipients,
        };
      } else if (createDto.type === ZaloCampaignType.GROUP_MESSAGE) {
        // Đối với GROUP_MESSAGE, sử dụng groupIds thay vì userIds
        totalRecipients = createDto.groupMessageContent?.groupIds?.length || 0;
        segment = {
          type: 'group_list',
          groupIds: createDto.groupMessageContent?.groupIds,
          totalGroups: totalRecipients,
          messages: createDto.groupMessageContent?.messages,
        };
      } else if (createDto.type === ZaloCampaignType.BROADCAST) {
        // Đối với broadcast, không thể biết chính xác số người nhận
        // vì phụ thuộc vào target criteria (gender, cities, ages, etc.)
        totalRecipients = 0; // Hoặc có thể để undefined
        segment = {
          type: 'broadcast_target',
          recipient: createDto.recipient,
        };
      } else {
        // Đối với các loại khác (MESSAGE, ZNS)
        totalRecipients = createDto.userIds?.length || 0;
        segment = {
          type: 'user_list',
          userIds: createDto.userIds,
          totalUsers: totalRecipients,
        };
      }

      const campaign = await this.zaloCampaignRepository.create({
        userId,
        oaId,
        name: createDto.name,
        description: createDto.description,
        tags: createDto.tags,
        type: createDto.type,
        segment,
        status,
        scheduledAt: createDto.scheduledAt,
        startedAt:
          createDto.type === ZaloCampaignType.CONSULTATION_SEQUENCE
            ? now
            : undefined,
        messageContent,
        znsContent,
        consultationSequenceContent,
        broadcastContent,
        groupMessageContent,
        totalRecipients,
        successCount: 0,
        failureCount: 0,
        createdAt: now,
        updatedAt: now,
      });

      // Nếu là consultation_sequence, tạo jobs cho từng user thay vì xử lý đồng bộ
      if (createDto.type === ZaloCampaignType.CONSULTATION_SEQUENCE) {
        const jobResult = await this.createConsultationSequenceJobs(
          userId,
          oaId,
          campaign.id,
          {
            userIds: createDto.userIds,
            messages: createDto.consultationSequenceContent?.messages || [],
          },
        );

        // Cập nhật campaign status thành RUNNING và lưu jobIds
        await this.zaloCampaignRepository.update(campaign.id, {
          status: ZaloCampaignStatus.RUNNING,
          startedAt: Date.now(),
          jobIds: jobResult.jobIds,
          updatedAt: Date.now(),
        });

        // Lấy campaign đã được cập nhật
        const updatedCampaign = await this.zaloCampaignRepository.findById(
          campaign.id,
        );

        if (!updatedCampaign) {
          throw new AppException(
            ErrorCode.DATABASE_ERROR,
            'Không thể lấy thông tin chiến dịch sau khi cập nhật',
          );
        }

        // Trả về response với thông tin jobs đã được tạo
        return {
          campaign: {
            id: updatedCampaign.id,
            name: updatedCampaign.name,
            description: updatedCampaign.description,
            tags: updatedCampaign.tags,
            type: updatedCampaign.type,
            status: updatedCampaign.status,
            totalRecipients: updatedCampaign.totalRecipients,
            successCount: updatedCampaign.successCount,
            failureCount: updatedCampaign.failureCount,
            createdAt: updatedCampaign.createdAt,
            completedAt: updatedCampaign.completedAt,
          },
          messageResults: {
            totalSent: 0, // Sẽ được cập nhật bởi worker
            successCount: 0, // Sẽ được cập nhật bởi worker
            failureCount: 0, // Sẽ được cập nhật bởi worker
            results: [], // Jobs đang được xử lý bởi worker
            message: `Đã tạo ${createDto.userIds?.length || 0} jobs để xử lý chuỗi tin nhắn tư vấn`,
          },
        };
      }

      return campaign;
    } catch (error) {
      this.logger.error(`Failed to create Zalo campaign: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.DATABASE_ERROR,
        'Không thể tạo chiến dịch Zalo',
      );
    }
  }

  /**
   * Tạo jobs cho chiến dịch chuỗi tin nhắn tư vấn (mỗi user = 1 job)
   * @param systemUserId ID của người dùng hệ thống
   * @param oaId ID của Official Account
   * @param campaignId ID của chiến dịch
   * @param consultationSequenceContent Nội dung chuỗi tin nhắn
   */
  private async createConsultationSequenceJobs(
    systemUserId: number,
    oaId: string,
    campaignId: number,
    consultationSequenceContent: any,
  ): Promise<{
    totalJobs: number;
    jobIds: string[];
  }> {
    try {
      const userIds = consultationSequenceContent.userIds || [];
      const messages = consultationSequenceContent.messages || [];

      if (userIds.length === 0) {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          'Danh sách người dùng không được để trống',
        );
      }

      if (messages.length === 0) {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          'Danh sách tin nhắn không được để trống',
        );
      }

      const jobIds: string[] = [];
      const timestamp = Date.now();

      // Tạo 1 job cho mỗi user
      for (const userId of userIds) {
        const jobData = {
          campaignId,
          oaId,
          userId,
          messages,
          timestamp,
          trackingId: `consultation_${campaignId}_${userId}_${timestamp}`,
        };

        const jobId =
          await this.queueService.addConsultationSequenceUserJob(jobData);
        jobIds.push(jobId);

        this.logger.debug(
          `Created consultation sequence job ${jobId} for user ${userId}, campaign ${campaignId}`,
        );
      }

      this.logger.log(
        `Created ${jobIds.length} consultation sequence jobs for campaign ${campaignId}`,
      );

      return {
        totalJobs: jobIds.length,
        jobIds,
      };
    } catch (error) {
      this.logger.error(
        `Failed to create consultation sequence jobs for campaign ${campaignId}: ${error.message}`,
      );

      // Cập nhật status thành failed
      await this.zaloCampaignRepository.update(campaignId, {
        status: ZaloCampaignStatus.FAILED,
        updatedAt: Date.now(),
      });

      throw error;
    }
  }

  /**
   * Thực thi chiến dịch chuỗi tin nhắn tư vấn (DEPRECATED - chỉ giữ lại để backward compatibility)
   * @param systemUserId ID của người dùng hệ thống
   * @param oaId ID của Official Account
   * @param campaignId ID của chiến dịch
   * @param consultationSequenceContent Nội dung chuỗi tin nhắn
   * @deprecated Sử dụng createConsultationSequenceJobs thay thế
   */
  private async executeConsultationSequenceCampaign(
    systemUserId: number,
    oaId: string,
    campaignId: number,
    consultationSequenceContent: any,
  ): Promise<{
    successCount: number;
    failureCount: number;
    results: Array<{
      userId: string;
      messageId?: string;
      status: 'sent' | 'failed';
      error?: string;
    }>;
  }> {
    try {
      // Lấy access token
      const accessToken = await this.getOaToken(oaId);

      const userIds = consultationSequenceContent.userIds || [];
      const messages = consultationSequenceContent.messages || [];

      let successCount = 0;
      let failureCount = 0;
      const results: Array<{
        userId: string;
        messageId?: string;
        status: 'sent' | 'failed';
        error?: string;
      }> = [];

      // Gửi tin nhắn cho từng user
      for (const zaloUserId of userIds) {
        for (let i = 0; i < messages.length; i++) {
          const message = messages[i];

          // Delay giữa các tin nhắn
          if (message.delaySeconds && message.delaySeconds > 0) {
            await new Promise((resolve) =>
              setTimeout(resolve, (message.delaySeconds || 0) * 1000),
            );
          }

          try {
            let result: { message_id: string };

            // Gửi tin nhắn dựa trên messageType (sử dụng enum values)
            switch (message.messageType) {
              case ConsultationMessageType.TEXT:
                result =
                  await this.zaloConsultationService.sendConsultationTextMessage(
                    accessToken,
                    zaloUserId,
                    message.text!,
                  );
                break;

              case ConsultationMessageType.IMAGE:
                result =
                  await this.zaloConsultationService.sendConsultationImageMessage(
                    accessToken,
                    zaloUserId,
                    message.imageUrl,
                    message.attachmentId,
                    message.imageMessage,
                  );
                break;

              case ConsultationMessageType.STICKER:
                result =
                  await this.zaloConsultationService.sendConsultationStickerMessage(
                    accessToken,
                    zaloUserId,
                    message.stickerId!,
                  );
                break;

              case ConsultationMessageType.FILE:
                result =
                  await this.zaloConsultationService.sendConsultationFileMessage(
                    accessToken,
                    zaloUserId,
                    message.fileUrl!,
                    message.filename!,
                    message.fileMessage,
                  );
                break;

              case ConsultationMessageType.REQUEST_INFO:
                // Sử dụng elements mặc định cho form yêu cầu thông tin
                const defaultElements = [
                  {
                    title: 'Họ và tên',
                    type: 'text' as const,
                    required: true,
                    placeholder: 'Nhập họ và tên của bạn',
                  },
                  {
                    title: 'Số điện thoại',
                    type: 'phone' as const,
                    required: true,
                    placeholder: 'Nhập số điện thoại',
                  },
                  {
                    title: 'Email',
                    type: 'email' as const,
                    required: false,
                    placeholder: 'Nhập địa chỉ email',
                  },
                ];

                result =
                  await this.zaloConsultationService.sendConsultationRequestInfoMessage(
                    accessToken,
                    zaloUserId,
                    message.requestTitle!,
                    defaultElements,
                    message.requestSubtitle,
                    message.requestImageUrl,
                  );
                break;

              default:
                throw new Error(
                  'Loại tin nhắn tư vấn không hỗ trợ gửi hàng loạt',
                );
            }

            results.push({
              userId: zaloUserId,
              messageId: result.message_id,
              status: 'sent',
            });
            successCount++;
            this.logger.debug(
              `Message sent successfully to user ${zaloUserId}, message ID: ${result.message_id}`,
            );
          } catch (error) {
            // Kiểm tra nếu là lỗi "User has not interacted with the OA in the past 7 days"
            if (
              error.message &&
              error.message.includes(
                'User has not interacted with the OA in the past 7 days',
              )
            ) {
              // Cập nhật user_audience với thông tin tương tác gần nhất
              await this.handleUserInteractionError(
                systemUserId,
                zaloUserId,
                accessToken,
              );
            }

            results.push({
              userId: zaloUserId,
              status: 'failed',
              error: error.message || 'Lỗi không xác định',
            });
            failureCount++;
            this.logger.error(
              `Failed to send message to user ${zaloUserId}: ${error.message}`,
            );

            // Dừng gửi các tin nhắn tiếp theo cho user này nếu gặp lỗi
            break;
          }
        }
      }

      // Cập nhật thống kê campaign
      await this.zaloCampaignRepository.update(campaignId, {
        successCount,
        failureCount,
        status: ZaloCampaignStatus.COMPLETED,
        completedAt: Date.now(),
        updatedAt: Date.now(),
      });

      this.logger.log(
        `Consultation sequence campaign ${campaignId} completed. Success: ${successCount}, Failure: ${failureCount}`,
      );

      return {
        successCount,
        failureCount,
        results,
      };
    } catch (error) {
      this.logger.error(
        `Failed to execute consultation sequence campaign: ${error.message}`,
      );

      // Cập nhật status thành failed
      await this.zaloCampaignRepository.update(campaignId, {
        status: ZaloCampaignStatus.FAILED,
        updatedAt: Date.now(),
      });

      throw error;
    }
  }

  /**
   * Xử lý lỗi tương tác người dùng
   * @param userId ID người dùng hệ thống
   * @param zaloUserId ID người dùng Zalo
   * @param accessToken Access token
   */
  private async handleUserInteractionError(
    userId: number,
    zaloUserId: string,
    accessToken: string,
  ): Promise<void> {
    try {
      // Lấy thông tin chi tiết user từ Zalo API
      const userDetail = await this.zaloUserManagementService.getUserDetail(
        accessToken,
        zaloUserId,
      );

      // Tìm user_audience theo zaloSocialId
      const audience = await this.userAudienceRepository.findOne({
        where: {
          zaloSocialId: zaloUserId,
          userId: userId,
        },
      });

      if (audience) {
        // Convert date từ format dd/MM/yyyy sang Unix timestamp
        let lastInteractionTimestamp: number | null = null;
        if (userDetail.user_last_interaction_date) {
          const [day, month, year] =
            userDetail.user_last_interaction_date.split('/');
          const date = new Date(
            parseInt(year),
            parseInt(month) - 1,
            parseInt(day),
          );
          lastInteractionTimestamp = Math.floor(date.getTime() / 1000) * 1000; // Convert to milliseconds
        }

        // Cập nhật user_last_interaction_date và user_is_follower
        await this.userAudienceRepository.save({
          ...audience,
          userLastInteractionDate: lastInteractionTimestamp,
          zaloUserIsFollower: userDetail.user_is_follower,
          updatedAt: Date.now(),
        });

        this.logger.log(
          `Updated user_audience for zaloSocialId: ${zaloUserId}, lastInteractionDate: ${lastInteractionTimestamp}`,
        );
      }
    } catch (error) {
      this.logger.error(
        `Failed to update user_audience for zaloSocialId: ${zaloUserId}`,
        error,
      );
      // Không throw error để không ảnh hưởng đến flow chính
    }
  }

  /**
   * Cập nhật chiến dịch Zalo
   * @param userId ID của người dùng
   * @param oaId ID của Official Account
   * @param id ID của chiến dịch
   * @param updateDto Dữ liệu cập nhật
   * @returns Chiến dịch Zalo đã cập nhật
   */
  async updateZaloCampaign(
    userId: number,
    oaId: string,
    id: number,
    updateDto: any,
  ): Promise<any> {
    try {
      // Kiểm tra chiến dịch tồn tại
      const campaign = await this.getZaloCampaignDetail(userId, oaId, id);

      // Kiểm tra trạng thái chiến dịch
      if (campaign.status !== 'draft' && campaign.status !== 'scheduled') {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          'Không thể cập nhật chiến dịch đang chạy hoặc đã hoàn thành',
        );
      }

      // Kiểm tra phân đoạn tồn tại nếu có cập nhật
      if (updateDto.segmentId) {
        await this.getZaloSegmentDetail(userId, oaId, updateDto.segmentId);
      }

      // Cập nhật chiến dịch
      const updateData: any = {
        updatedAt: Date.now(),
      };

      if (updateDto.name !== undefined) {
        updateData.name = updateDto.name;
      }

      if (updateDto.description !== undefined) {
        updateData.description = updateDto.description;
      }

      if (updateDto.segmentId !== undefined) {
        updateData.segmentId = updateDto.segmentId;
      }

      if (updateDto.scheduledAt !== undefined) {
        updateData.scheduledAt = updateDto.scheduledAt;
      }

      if (
        updateDto.messageContent !== undefined &&
        campaign.type === 'message'
      ) {
        updateData.messageContent = updateDto.messageContent;
      }

      if (updateDto.znsContent !== undefined && campaign.type === 'zns') {
        updateData.znsContent = updateDto.znsContent;
      }

      const updatedCampaign = await this.zaloCampaignRepository.update(
        id,
        updateData,
      );

      return updatedCampaign;
    } catch (error) {
      this.logger.error(`Failed to update Zalo campaign: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.DATABASE_ERROR,
        'Không thể cập nhật chiến dịch Zalo',
      );
    }
  }

  /**
   * Xóa chiến dịch Zalo
   * @param userId ID của người dùng
   * @param oaId ID của Official Account
   * @param id ID của chiến dịch
   * @returns true nếu xóa thành công
   */
  async deleteZaloCampaign(
    userId: number,
    oaId: string,
    id: number,
  ): Promise<boolean> {
    try {
      // Kiểm tra chiến dịch tồn tại
      const campaign = await this.getZaloCampaignDetail(userId, oaId, id);

      // Kiểm tra trạng thái chiến dịch
      if (campaign.status === 'running') {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          'Không thể xóa chiến dịch đang chạy',
        );
      }

      // Xóa lịch sử chiến dịch
      // Lấy tất cả log của chiến dịch
      const logs = await this.zaloCampaignLogRepository.findByCampaignId(id);

      // Xóa từng log một
      for (const log of logs) {
        await this.zaloCampaignLogRepository.update(log.id, {
          status: 'deleted',
        });
      }

      // Xóa chiến dịch
      await this.zaloCampaignRepository.delete(id);

      return true;
    } catch (error) {
      this.logger.error(`Failed to delete Zalo campaign: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.DATABASE_ERROR,
        'Không thể xóa chiến dịch Zalo',
      );
    }
  }

  /**
   * Xóa nhiều chiến dịch Zalo
   * @param userId ID của người dùng
   * @param ids Danh sách ID chiến dịch cần xóa
   * @returns Kết quả xóa nhiều chiến dịch
   */
  async bulkDeleteZaloCampaigns(
    userId: number,
    ids: number[],
  ): Promise<{
    totalRequested: number;
    successCount: number;
    failureCount: number;
    successfulDeletes: number[];
    failedDeletes: Array<{ id: number; reason: string }>;
    message: string;
  }> {
    try {
      if (!ids || ids.length === 0) {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          'Danh sách ID không được rỗng',
        );
      }

      const totalRequested = ids.length;
      const successfulDeletes: number[] = [];
      const failedDeletes: Array<{ id: number; reason: string }> = [];

      this.logger.log(
        `Starting bulk delete for ${totalRequested} Zalo campaigns for user ${userId}`,
      );

      // Xử lý từng chiến dịch
      for (const id of ids) {
        try {
          // Kiểm tra chiến dịch có tồn tại và thuộc về user không
          const campaign = await this.zaloCampaignRepository.findByUserIdAndId(
            userId,
            id,
          );

          if (!campaign) {
            failedDeletes.push({
              id,
              reason: 'Không tìm thấy chiến dịch hoặc không có quyền truy cập',
            });
            continue;
          }

          // Kiểm tra trạng thái có thể xóa không
          if (campaign.status === ZaloCampaignStatus.RUNNING) {
            failedDeletes.push({
              id,
              reason: 'Không thể xóa chiến dịch đang chạy',
            });
            continue;
          }

          // Xóa lịch sử chiến dịch
          const logs =
            await this.zaloCampaignLogRepository.findByCampaignId(id);
          for (const log of logs) {
            await this.zaloCampaignLogRepository.update(log.id, {
              status: 'deleted',
            });
          }

          // Xóa chiến dịch
          const deleted = await this.zaloCampaignRepository.delete(id);
          if (deleted) {
            successfulDeletes.push(id);
            this.logger.log(`Successfully deleted Zalo campaign ${id}`);
          } else {
            failedDeletes.push({
              id,
              reason: 'Không thể xóa chiến dịch từ database',
            });
          }
        } catch (error) {
          this.logger.error(
            `Failed to delete Zalo campaign ${id}: ${error.message}`,
          );
          failedDeletes.push({
            id,
            reason: error.message || 'Lỗi không xác định',
          });
        }
      }

      const successCount = successfulDeletes.length;
      const failureCount = failedDeletes.length;

      const message = `Đã xóa ${successCount}/${totalRequested} chiến dịch thành công`;

      this.logger.log(
        `Bulk delete completed for user ${userId}. Success: ${successCount}, Failed: ${failureCount}`,
      );

      return {
        totalRequested,
        successCount,
        failureCount,
        successfulDeletes,
        failedDeletes,
        message,
      };
    } catch (error) {
      this.logger.error(
        `Failed to bulk delete Zalo campaigns: ${error.message}`,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.DATABASE_ERROR,
        'Không thể xóa nhiều chiến dịch Zalo',
      );
    }
  }

  /**
   * Thực thi chiến dịch Zalo
   * @param userId ID của người dùng
   * @param oaId ID của Official Account
   * @param id ID của chiến dịch
   * @returns Chiến dịch Zalo đã cập nhật
   */
  async executeZaloCampaign(
    userId: number,
    oaId: string,
    id: number,
  ): Promise<any> {
    try {
      // Kiểm tra chiến dịch tồn tại
      const campaign = await this.getZaloCampaignDetail(userId, oaId, id);

      // Kiểm tra trạng thái chiến dịch
      if (campaign.status === 'running') {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          'Chiến dịch đang chạy',
        );
      }

      if (campaign.status === 'completed') {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          'Chiến dịch đã hoàn thành',
        );
      }

      // Cập nhật trạng thái chiến dịch
      const now = Date.now();
      const updatedCampaign = await this.zaloCampaignRepository.update(id, {
        status: ZaloCampaignStatus.RUNNING,
        startedAt: now,
        updatedAt: now,
      });

      // Xử lý riêng cho GROUP_MESSAGE
      if (campaign.type === ZaloCampaignType.GROUP_MESSAGE) {
        return await this.executeGroupMessageCampaign(userId, oaId, campaign);
      }

      // Lấy danh sách người theo dõi thuộc phân đoạn
      const followers = await this.getZaloSegmentFollowers(
        userId,
        oaId,
        campaign.segmentId,
        {
          page: 1,
          limit: 1000, // Lấy tối đa 1000 người theo dõi
        },
      );

      // Cập nhật tổng số người nhận
      await this.zaloCampaignRepository.update(id, {
        totalRecipients: followers.meta.totalItems,
      });

      // Thực thi chiến dịch (gửi tin nhắn)
      this.processCampaign(userId, oaId, campaign, followers.items);

      return updatedCampaign;
    } catch (error) {
      this.logger.error(`Failed to execute Zalo campaign: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.DATABASE_ERROR,
        'Không thể thực thi chiến dịch Zalo',
      );
    }
  }

  /**
   * Thực thi chiến dịch GROUP_MESSAGE
   * @param userId ID của người dùng
   * @param oaId ID của Official Account
   * @param campaign Thông tin chiến dịch
   * @returns Chiến dịch đã được cập nhật
   */
  private async executeGroupMessageCampaign(
    userId: number,
    oaId: string,
    campaign: any,
  ): Promise<any> {
    try {
      // Lấy integrationId từ oaId
      const integrationId = await this.getIntegrationIdFromOaId(userId, oaId);

      // Lấy thông tin group message từ campaign
      const groupMessageContent = campaign.groupMessageContent;
      if (
        !groupMessageContent ||
        !groupMessageContent.groupIds ||
        !groupMessageContent.messages
      ) {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          'Thiếu thông tin groupIds hoặc messages trong chiến dịch GROUP_MESSAGE',
        );
      }

      // Tạo job data cho queue
      const jobData = {
        userId: userId.toString(),
        integrationId,
        groupIds: groupMessageContent.groupIds,
        messages: groupMessageContent.messages,
        timestamp: Date.now(),
        trackingId: `campaign_${campaign.id}_${Date.now()}`,
      };

      // Thêm job vào queue
      const jobId = await this.queueService.addGroupMessageSequenceJob(jobData);

      // Cập nhật campaign với jobId và status
      const updatedCampaign = await this.zaloCampaignRepository.update(
        campaign.id,
        {
          status: ZaloCampaignStatus.RUNNING,
          jobIds: [jobId],
          totalRecipients: groupMessageContent.groupIds.length,
          updatedAt: Date.now(),
        },
      );

      this.logger.log(
        `Đã tạo job group message sequence cho campaign ${campaign.id}: ${jobId}`,
      );

      return updatedCampaign;
    } catch (error) {
      this.logger.error(
        `Lỗi khi thực thi chiến dịch GROUP_MESSAGE ${campaign.id}: ${error.message}`,
        error.stack,
      );

      // Cập nhật status thành failed
      await this.zaloCampaignRepository.update(campaign.id, {
        status: ZaloCampaignStatus.FAILED,
        updatedAt: Date.now(),
      });

      throw error;
    }
  }

  /**
   * Dừng chiến dịch Zalo
   * @param userId ID của người dùng
   * @param oaId ID của Official Account
   * @param id ID của chiến dịch
   * @returns Chiến dịch Zalo đã cập nhật
   */
  async stopZaloCampaign(
    userId: number,
    oaId: string,
    id: number,
  ): Promise<any> {
    try {
      // Kiểm tra chiến dịch tồn tại
      const campaign = await this.getZaloCampaignDetail(userId, oaId, id);

      // Kiểm tra trạng thái chiến dịch
      if (campaign.status !== 'running' && campaign.status !== 'scheduled') {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          'Chiến dịch không đang chạy hoặc đã lên lịch',
        );
      }

      // Cập nhật trạng thái chiến dịch
      const updatedCampaign = await this.zaloCampaignRepository.update(id, {
        status: ZaloCampaignStatus.CANCELLED,
        updatedAt: Date.now(),
      });

      return updatedCampaign;
    } catch (error) {
      this.logger.error(`Failed to stop Zalo campaign: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.DATABASE_ERROR,
        'Không thể dừng chiến dịch Zalo',
      );
    }
  }

  /**
   * Lấy lịch sử thực thi chiến dịch Zalo
   * @param userId ID của người dùng
   * @param oaId ID của Official Account
   * @param id ID của chiến dịch
   * @param queryDto Tham số truy vấn
   * @returns Lịch sử thực thi chiến dịch Zalo với phân trang
   */
  async getZaloCampaignLogs(
    userId: number,
    oaId: string,
    id: number,
    queryDto: any,
  ): Promise<any> {
    try {
      // Kiểm tra chiến dịch tồn tại
      await this.getZaloCampaignDetail(userId, oaId, id);

      const { page, limit, sortBy, sortDirection, status, followerId } =
        queryDto;
      const skip = (page - 1) * limit;

      // Xây dựng điều kiện tìm kiếm
      const where: any = { campaignId: id };

      if (status) {
        where.status = status;
      }

      if (followerId) {
        where.followerId = followerId;
      }

      // Tìm kiếm lịch sử chiến dịch
      const [items, totalItems] = await Promise.all([
        this.zaloCampaignLogRepository
          .findWithPagination({
            where,
            skip,
            take: limit,
            order: {
              [sortBy || 'createdAt']: sortDirection || 'DESC',
            },
          })
          .then(([items, _]) => items),
        this.zaloCampaignLogRepository
          .findWithPagination({ where, skip: 0, take: 1 })
          .then(([_, count]) => count),
      ]);

      // Tính toán thông tin phân trang
      const totalPages = Math.ceil(totalItems / limit);

      return {
        items,
        meta: {
          totalItems,
          itemCount: items.length,
          itemsPerPage: limit,
          totalPages,
          currentPage: page,
        },
      };
    } catch (error) {
      this.logger.error(`Failed to get Zalo campaign logs: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.DATABASE_ERROR,
        'Không thể lấy lịch sử thực thi chiến dịch Zalo',
      );
    }
  }

  /**
   * Xử lý chiến dịch Zalo (gửi tin nhắn)
   * @param userId ID của người dùng
   * @param oaId ID của Official Account
   * @param campaign Thông tin chiến dịch
   * @param followers Danh sách người theo dõi
   */
  private async processCampaign(
    userId: number,
    oaId: string,
    campaign: any,
    followers: any[],
  ): Promise<void> {
    try {
      let successCount = 0;
      let failureCount = 0;

      // Xử lý từng người theo dõi
      for (const follower of followers) {
        let log;
        try {
          // Tạo log chiến dịch
          const now = Date.now();
          log = await this.zaloCampaignLogRepository.create({
            campaignId: campaign.id,
            followerId: follower.userId,
            // followerName: follower.displayName, // Thuộc tính này không tồn tại trong entity
            status: 'pending',
            createdAt: now,
          });

          // Gửi tin nhắn tùy theo loại chiến dịch
          if (campaign.type === 'message') {
            await this.sendCampaignMessage(
              oaId,
              follower.userId,
              campaign.messageContent,
              log.id,
            );
          } else if (campaign.type === 'zns') {
            await this.sendCampaignZns(
              userId,
              oaId,
              follower,
              campaign.znsContent,
              log.id,
            );
          } else if (campaign.type === ZaloCampaignType.BROADCAST) {
            await this.sendCampaignBroadcast(
              userId,
              oaId,
              campaign.broadcastContent,
              log.id,
            );
          }

          successCount++;
        } catch (error) {
          this.logger.error(
            `Failed to process follower ${follower.userId}: ${error.message}`,
          );
          failureCount++;

          // Cập nhật log chiến dịch với lỗi nếu log đã được tạo
          if (log) {
            await this.zaloCampaignLogRepository.update(log.id, {
              status: 'failed',
              error: error.message,
            });
          }
        }
      }

      // Cập nhật thống kê chiến dịch
      await this.zaloCampaignRepository.update(campaign.id, {
        successCount,
        failureCount,
        status: ZaloCampaignStatus.COMPLETED,
        completedAt: Date.now(),
        updatedAt: Date.now(),
      });
    } catch (error) {
      this.logger.error(`Failed to process campaign: ${error.message}`);

      // Cập nhật trạng thái chiến dịch thành thất bại
      await this.zaloCampaignRepository.update(campaign.id, {
        status: ZaloCampaignStatus.FAILED,
        updatedAt: Date.now(),
      });
    }
  }

  /**
   * Gửi tin nhắn chiến dịch
   * @param oaId ID của Official Account
   * @param followerId ID của người theo dõi
   * @param messageContent Nội dung tin nhắn
   * @param logId ID của log chiến dịch
   */
  private async sendCampaignMessage(
    oaId: string,
    followerId: string,
    messageContent: any,
    logId: number,
  ): Promise<void> {
    try {
      // Gửi tin nhắn tùy theo loại
      const { type, text, imageUrl, fileUrl, templateId, templateData } =
        messageContent;

      switch (type) {
        case 'text':
          await this.sendTextMessage(oaId, followerId, text);
          break;
        case 'image':
          await this.sendImageMessage(oaId, followerId, imageUrl);
          break;
        case 'file':
          await this.sendFileMessage(oaId, followerId, fileUrl);
          break;
        case 'template':
          await this.sendTemplateMessage(
            oaId,
            followerId,
            templateId,
            templateData,
          );
          break;
        default:
          throw new Error(`Loại tin nhắn không hợp lệ: ${type}`);
      }

      // Cập nhật log chiến dịch
      await this.zaloCampaignLogRepository.update(logId, {
        // Chỉ cập nhật các trường có trong entity
        status: 'success',
        messageId: type === 'text' ? text.substring(0, 20) : 'non-text-message', // Lưu một phần của tin nhắn vào messageId
      });
    } catch (error) {
      this.logger.error(`Failed to send campaign message: ${error.message}`);

      // Cập nhật log chiến dịch với lỗi
      await this.zaloCampaignLogRepository.update(logId, {
        status: 'failed',
        error: error.message,
      });

      throw error;
    }
  }

  /**
   * Gửi tin nhắn broadcast cho chiến dịch
   * @param userId ID của người dùng
   * @param oaId ID của Official Account
   * @param broadcastContent Nội dung broadcast
   * @param logId ID của log chiến dịch
   */
  private async sendCampaignBroadcast(
    userId: number,
    oaId: string,
    broadcastContent: any,
    logId: number,
  ): Promise<void> {
    try {
      // Lấy integration từ oaId
      const oa = await this.getOfficialAccountDetail(userId, oaId);
      if (!oa) {
        throw new Error('Không tìm thấy Official Account');
      }

      // Kiểm tra xem có nhiều attachmentId không
      const attachmentIds = broadcastContent.attachmentIds || [
        broadcastContent.broadcastData?.message?.attachment?.payload
          ?.elements?.[0]?.attachment_id,
      ];
      const messageIds: string[] = [];
      let lastError: any = null;

      // Gửi broadcast message cho từng attachment
      for (const attachmentId of attachmentIds) {
        if (!attachmentId) continue;

        try {
          // Tạo broadcastData cho từng attachment
          const currentBroadcastData = {
            ...broadcastContent.broadcastData,
            message: {
              ...broadcastContent.broadcastData.message,
              attachment: {
                ...broadcastContent.broadcastData.message.attachment,
                payload: {
                  ...broadcastContent.broadcastData.message.attachment.payload,
                  elements: [
                    {
                      media_type: 'article',
                      attachment_id: attachmentId,
                    },
                  ],
                },
              },
            },
          };

          const result = await this.sendBroadcastMessage(
            userId,
            oa.id, // sử dụng integration ID
            currentBroadcastData,
          );

          messageIds.push(result.message_id);
          this.logger.log(
            `Broadcast sent successfully for attachment ${attachmentId}: ${result.message_id}`,
          );
        } catch (error) {
          this.logger.error(
            `Failed to send broadcast for attachment ${attachmentId}: ${error.message}`,
          );
          lastError = error;
          // Tiếp tục gửi các attachment khác thay vì dừng lại
        }
      }

      // Cập nhật log chiến dịch
      if (messageIds.length > 0) {
        const status =
          messageIds.length === attachmentIds.length
            ? 'success'
            : 'partial_success';
        const messageId = messageIds.join(', ');

        // Tạo thông tin chi tiết để lưu trong error field (dưới dạng JSON)
        const detailInfo = JSON.stringify({
          totalAttachments: attachmentIds.length,
          successfulAttachments: messageIds.length,
          messageIds: messageIds,
          status: status,
        });

        await this.zaloCampaignLogRepository.update(logId, {
          status: status,
          messageId: messageId,
          error: status === 'partial_success' ? detailInfo : undefined,
        });
      } else {
        throw (
          lastError ||
          new Error('Không thể gửi broadcast cho bất kỳ attachment nào')
        );
      }
    } catch (error) {
      this.logger.error(`Failed to send campaign broadcast: ${error.message}`);

      // Cập nhật log chiến dịch với lỗi
      await this.zaloCampaignLogRepository.update(logId, {
        status: 'failed',
        error: error.message,
      });

      throw error;
    }
  }

  /**
   * Gửi ZNS chiến dịch
   * @param userId ID của người dùng
   * @param oaId ID của Official Account
   * @param follower Thông tin người theo dõi
   * @param znsContent Nội dung ZNS
   * @param logId ID của log chiến dịch
   */
  private async sendCampaignZns(
    userId: number,
    oaId: string,
    follower: any,
    znsContent: any,
    logId: number,
  ): Promise<void> {
    try {
      const { templateId, templateData } = znsContent;

      // Thay thế các placeholder trong templateData
      const processedTemplateData = this.processTemplateData(
        templateData,
        follower,
      );

      // Gửi ZNS
      const sendDto = {
        templateId,
        phone: follower.phone,
        templateData: processedTemplateData,
      };

      await this.sendZnsMessage(userId, oaId, sendDto);

      // Cập nhật log chiến dịch
      await this.zaloCampaignLogRepository.update(logId, {
        // Chỉ cập nhật các trường có trong entity
        status: 'success',
        messageId: templateId.substring(0, 20), // Lưu một phần của templateId vào messageId
      });
    } catch (error) {
      this.logger.error(`Failed to send campaign ZNS: ${error.message}`);

      // Cập nhật log chiến dịch với lỗi
      await this.zaloCampaignLogRepository.update(logId, {
        status: 'failed',
        error: error.message,
      });

      throw error;
    }
  }

  /**
   * Xử lý dữ liệu template
   * @param templateData Dữ liệu template gốc
   * @param follower Thông tin người theo dõi
   * @returns Dữ liệu template đã xử lý
   */
  private processTemplateData(
    templateData: Record<string, any>,
    follower: any,
  ): Record<string, any> {
    const result = { ...templateData };

    // Duyệt qua tất cả các trường trong templateData
    for (const key in result) {
      if (typeof result[key] === 'string') {
        // Thay thế các placeholder
        result[key] = this.replacePlaceholders(result[key], follower);
      }
    }

    return result;
  }

  /**
   * Thay thế các placeholder trong chuỗi
   * @param text Chuỗi gốc
   * @param follower Thông tin người theo dõi
   * @returns Chuỗi đã thay thế placeholder
   */
  private replacePlaceholders(text: string, follower: any): string {
    // Thay thế các placeholder cơ bản
    const result = text
      .replace(/{displayName}/g, follower.displayName || '')
      .replace(/{name}/g, follower.displayName || '')
      .replace(/{phone}/g, follower.phone || '');

    // Thay thế các placeholder khác nếu cần

    return result;
  }

  /**
   * Lấy danh sách tự động hóa Zalo
   * @param userId ID của người dùng
   * @param oaId ID của Official Account
   * @param queryDto Tham số truy vấn
   * @returns Danh sách tự động hóa Zalo với phân trang
   */
  async getZaloAutomations(
    userId: number,
    oaId: string,
    queryDto: any,
  ): Promise<any> {
    try {
      const {
        page,
        limit,
        search,
        sortBy,
        sortDirection,
        name,
        triggerType,
        status,
      } = queryDto;
      const skip = (page - 1) * limit;

      // Xây dựng điều kiện tìm kiếm
      const where: any = { userId, oaId };

      if (search) {
        where.name = search ? ILike(`%${search}%`) : undefined;
      }

      if (name) {
        where.name = name ? ILike(`%${name}%`) : undefined;
      }

      if (triggerType) {
        where['trigger.type'] = triggerType;
      }

      if (status) {
        where.status = status;
      }

      // Tìm kiếm tự động hóa
      const [items, totalItems] =
        await this.zaloAutomationRepository.findWithPagination({
          where,
          skip,
          take: limit,
          order: {
            [sortBy || 'createdAt']: sortDirection || 'DESC',
          },
        });

      // Tính toán thông tin phân trang
      const totalPages = Math.ceil(totalItems / limit);

      return {
        items,
        meta: {
          totalItems,
          itemCount: items.length,
          itemsPerPage: limit,
          totalPages,
          currentPage: page,
        },
      };
    } catch (error) {
      this.logger.error(`Failed to get Zalo automations: ${error.message}`);
      throw new AppException(
        ErrorCode.DATABASE_ERROR,
        'Không thể lấy danh sách tự động hóa Zalo',
      );
    }
  }

  /**
   * Lấy thông tin chi tiết tự động hóa Zalo
   * @param userId ID của người dùng
   * @param oaId ID của Official Account
   * @param id ID của tự động hóa
   * @returns Thông tin chi tiết tự động hóa Zalo
   */
  async getZaloAutomationDetail(
    userId: number,
    oaId: string,
    id: number,
  ): Promise<any> {
    try {
      const automation =
        await this.zaloAutomationRepository.findByIdAndUserIdAndOaId(
          id,
          userId,
          oaId,
        );

      if (!automation) {
        throw new AppException(
          ErrorCode.RESOURCE_NOT_FOUND,
          'Không tìm thấy tự động hóa Zalo',
        );
      }

      return automation;
    } catch (error) {
      this.logger.error(
        `Failed to get Zalo automation detail: ${error.message}`,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.DATABASE_ERROR,
        'Không thể lấy thông tin chi tiết tự động hóa Zalo',
      );
    }
  }

  /**
   * Tạo tự động hóa Zalo mới
   * @param userId ID của người dùng
   * @param oaId ID của Official Account
   * @param createDto Dữ liệu tạo tự động hóa
   * @returns Tự động hóa Zalo đã tạo
   */
  async createZaloAutomation(
    userId: number,
    oaId: string,
    createDto: any,
  ): Promise<any> {
    try {
      // Kiểm tra quyền truy cập Official Account
      await this.getOfficialAccountDetail(userId, oaId);

      // Tạo tự động hóa mới
      const now = Date.now();
      const automation = await this.zaloAutomationRepository.create({
        userId,
        oaId,
        name: createDto.name,
        description: createDto.description,
        trigger: createDto.trigger,
        actions: createDto.actions,
        status: createDto.status || ZaloAutomationStatus.INACTIVE,
        triggerCount: 0,
        // Loại bỏ các thuộc tính không tồn tại
        // successCount: 0,
        // failedCount: 0,
        createdAt: now,
        updatedAt: now,
      });

      return automation;
    } catch (error) {
      this.logger.error(`Failed to create Zalo automation: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.DATABASE_ERROR,
        'Không thể tạo tự động hóa Zalo',
      );
    }
  }

  /**
   * Cập nhật tự động hóa Zalo
   * @param userId ID của người dùng
   * @param oaId ID của Official Account
   * @param id ID của tự động hóa
   * @param updateDto Dữ liệu cập nhật
   * @returns Tự động hóa Zalo đã cập nhật
   */
  async updateZaloAutomation(
    userId: number,
    oaId: string,
    id: number,
    updateDto: any,
  ): Promise<any> {
    try {
      // Kiểm tra tự động hóa tồn tại
      await this.getZaloAutomationDetail(userId, oaId, id);

      // Cập nhật tự động hóa
      const updateData: any = {
        updatedAt: Date.now(),
      };

      if (updateDto.name !== undefined) {
        updateData.name = updateDto.name;
      }

      if (updateDto.description !== undefined) {
        updateData.description = updateDto.description;
      }

      if (updateDto.trigger !== undefined) {
        updateData.trigger = updateDto.trigger;
      }

      if (updateDto.actions !== undefined) {
        updateData.actions = updateDto.actions;
      }

      if (updateDto.status !== undefined) {
        updateData.status = updateDto.status;
      }

      const updatedAutomation = await this.zaloAutomationRepository.update(
        id,
        updateData,
      );

      return updatedAutomation;
    } catch (error) {
      this.logger.error(`Failed to update Zalo automation: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.DATABASE_ERROR,
        'Không thể cập nhật tự động hóa Zalo',
      );
    }
  }

  /**
   * Xóa tự động hóa Zalo
   * @param userId ID của người dùng
   * @param oaId ID của Official Account
   * @param id ID của tự động hóa
   * @returns true nếu xóa thành công
   */
  async deleteZaloAutomation(
    userId: number,
    oaId: string,
    id: number,
  ): Promise<boolean> {
    try {
      // Kiểm tra tự động hóa tồn tại
      await this.getZaloAutomationDetail(userId, oaId, id);

      // Xóa lịch sử tự động hóa
      // Xóa lịch sử tự động hóa
      // Phương thức deleteMany và delete có thể không tồn tại trong repository
      // Cần triển khai phương thức này trong repository
      // Tạm thời comment lại để tránh lỗi TypeScript
      // await this.zaloAutomationLogRepository.delete({ automationId: id });

      // Xóa tự động hóa
      await this.zaloAutomationRepository.delete(id);

      return true;
    } catch (error) {
      this.logger.error(`Failed to delete Zalo automation: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.DATABASE_ERROR,
        'Không thể xóa tự động hóa Zalo',
      );
    }
  }

  /**
   * Kích hoạt tự động hóa Zalo
   * @param userId ID của người dùng
   * @param oaId ID của Official Account
   * @param id ID của tự động hóa
   * @returns Tự động hóa Zalo đã cập nhật
   */
  async activateZaloAutomation(
    userId: number,
    oaId: string,
    id: number,
  ): Promise<any> {
    try {
      // Kiểm tra tự động hóa tồn tại
      await this.getZaloAutomationDetail(userId, oaId, id);

      // Cập nhật trạng thái tự động hóa
      const updatedAutomation = await this.zaloAutomationRepository.update(id, {
        status: ZaloAutomationStatus.ACTIVE,
        updatedAt: Date.now(),
      });

      return updatedAutomation;
    } catch (error) {
      this.logger.error(`Failed to activate Zalo automation: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.DATABASE_ERROR,
        'Không thể kích hoạt tự động hóa Zalo',
      );
    }
  }

  /**
   * Vô hiệu hóa tự động hóa Zalo
   * @param userId ID của người dùng
   * @param oaId ID của Official Account
   * @param id ID của tự động hóa
   * @returns Tự động hóa Zalo đã cập nhật
   */
  async deactivateZaloAutomation(
    userId: number,
    oaId: string,
    id: number,
  ): Promise<any> {
    try {
      // Kiểm tra tự động hóa tồn tại
      await this.getZaloAutomationDetail(userId, oaId, id);

      // Cập nhật trạng thái tự động hóa
      const updatedAutomation = await this.zaloAutomationRepository.update(id, {
        status: ZaloAutomationStatus.INACTIVE,
        updatedAt: Date.now(),
      });

      return updatedAutomation;
    } catch (error) {
      this.logger.error(
        `Failed to deactivate Zalo automation: ${error.message}`,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.DATABASE_ERROR,
        'Không thể vô hiệu hóa tự động hóa Zalo',
      );
    }
  }

  /**
   * Lấy lịch sử thực thi tự động hóa Zalo
   * @param userId ID của người dùng
   * @param oaId ID của Official Account
   * @param id ID của tự động hóa
   * @param queryDto Tham số truy vấn
   * @returns Lịch sử thực thi tự động hóa Zalo với phân trang
   */
  async getZaloAutomationLogs(
    userId: number,
    oaId: string,
    id: number,
    queryDto: any,
  ): Promise<any> {
    try {
      // Kiểm tra tự động hóa tồn tại
      await this.getZaloAutomationDetail(userId, oaId, id);

      const { page, limit, sortBy, sortDirection, status, followerId } =
        queryDto;
      const skip = (page - 1) * limit;

      // Xây dựng điều kiện tìm kiếm
      const where: any = { automationId: id };

      if (status) {
        where.status = status;
      }

      if (followerId) {
        where.followerId = followerId;
      }

      // Tìm kiếm lịch sử tự động hóa
      const [items, totalItems] =
        await this.zaloAutomationLogRepository.findWithPagination({
          where,
          skip,
          take: limit,
          order: {
            [sortBy || 'createdAt']: sortDirection || 'DESC',
          },
        });

      // Tính toán thông tin phân trang
      const totalPages = Math.ceil(totalItems / limit);

      return {
        items,
        meta: {
          totalItems,
          itemCount: items.length,
          itemsPerPage: limit,
          totalPages,
          currentPage: page,
        },
      };
    } catch (error) {
      this.logger.error(`Failed to get Zalo automation logs: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.DATABASE_ERROR,
        'Không thể lấy lịch sử thực thi tự động hóa Zalo',
      );
    }
  }

  /**
   * Xử lý sự kiện để kích hoạt tự động hóa
   * @param oaId ID của Official Account
   * @param eventType Loại sự kiện
   * @param data Dữ liệu sự kiện
   */
  async processAutomationTrigger(
    oaId: string,
    eventType: string,
    data: any,
  ): Promise<void> {
    try {
      // Tìm các tự động hóa phù hợp với sự kiện
      // Chuyển đổi eventType thành ZaloAutomationTriggerType
      const triggerType = eventType as ZaloAutomationTriggerType;
      const automations =
        await this.zaloAutomationRepository.findByOaIdAndTriggerTypeAndStatus(
          oaId,
          triggerType,
          ZaloAutomationStatus.ACTIVE,
        );

      if (!automations || automations.length === 0) {
        return;
      }

      // Xử lý từng tự động hóa
      for (const automation of automations) {
        try {
          // Kiểm tra điều kiện bổ sung nếu có
          if (
            automation.trigger.conditions &&
            automation.trigger.conditions.length > 0
          ) {
            // Kiểm tra điều kiện
            const follower =
              await this.zaloFollowerRepository.findByOaIdAndUserId(
                oaId,
                data.userId,
              );
            if (!follower) {
              continue;
            }

            const matchesConditions = this.checkAutomationConditions(
              follower,
              automation.trigger.conditions,
            );
            if (!matchesConditions) {
              continue;
            }
          }

          // Tăng số lần kích hoạt
          await this.zaloAutomationRepository.update(automation.id, {
            triggerCount: automation.triggerCount + 1,
            updatedAt: Date.now(),
          });

          // Thực thi các hành động
          await this.executeAutomationActions(automation, data);
        } catch (error) {
          this.logger.error(
            `Failed to process automation ${automation.id}: ${error.message}`,
          );
        }
      }
    } catch (error) {
      this.logger.error(
        `Failed to process automation trigger: ${error.message}`,
      );
    }
  }

  /**
   * Kiểm tra điều kiện tự động hóa
   * @param follower Thông tin người theo dõi
   * @param conditions Danh sách điều kiện
   * @returns true nếu thỏa mãn tất cả điều kiện
   */
  private checkAutomationConditions(follower: any, conditions: any[]): boolean {
    return (
      this.filterFollowersBySegmentConditions([follower], conditions).length > 0
    );
  }

  /**
   * Thực thi các hành động của tự động hóa
   * @param automation Thông tin tự động hóa
   * @param data Dữ liệu sự kiện
   */
  private async executeAutomationActions(
    automation: any,
    data: any,
  ): Promise<void> {
    const { userId, oaId, id, actions } = automation;
    const followerId = data.userId;
    const follower = await this.zaloFollowerRepository.findByOaIdAndUserId(
      oaId,
      followerId,
    );

    if (!follower) {
      return;
    }

    let successCount = 0;
    let failedCount = 0;

    // Thực thi từng hành động
    for (const action of actions) {
      try {
        // Tạo log tự động hóa
        const now = Date.now();
        const log = await this.zaloAutomationLogRepository.create({
          automationId: id,
          userId: automation.userId,
          oaId: automation.oaId,
          followerId,
          followerUserId: follower.userId,
          triggerType: automation.trigger.type,
          actionType: action.type,
          status: 'pending',
          createdAt: now,
        });

        // Thực thi hành động sau khoảng thời gian delay (nếu có)
        const delay = action.delay || 0;
        if (delay > 0) {
          await new Promise((resolve) => setTimeout(resolve, delay * 1000));
        }

        // Thực thi hành động tùy theo loại
        await this.executeAutomationAction(
          userId,
          oaId,
          follower,
          action,
          log.id,
        );

        successCount++;
      } catch (error) {
        this.logger.error(`Failed to execute action: ${error.message}`);
        failedCount++;
      }
    }

    // Cập nhật thống kê tự động hóa
    await this.zaloAutomationRepository.update(id, {
      // Loại bỏ các thuộc tính không tồn tại
      // successCount: automation.successCount + successCount,
      // failedCount: automation.failedCount + failedCount,
      updatedAt: Date.now(),
    });
  }

  /**
   * Thực thi một hành động cụ thể của tự động hóa
   * @param userId ID của người dùng
   * @param oaId ID của Official Account
   * @param follower Thông tin người theo dõi
   * @param action Thông tin hành động
   * @param logId ID của log tự động hóa
   */
  private async executeAutomationAction(
    userId: number,
    oaId: string,
    follower: any,
    action: any,
    logId: number,
  ): Promise<void> {
    try {
      const followerId = follower.userId;

      switch (action.type) {
        case 'send_message':
          if (action.messageContent) {
            await this.sendCampaignMessage(
              oaId,
              followerId,
              action.messageContent,
              logId,
            );
          }
          break;

        case 'send_zns':
          if (action.znsContent) {
            await this.sendCampaignZns(
              userId,
              oaId,
              follower,
              action.znsContent,
              logId,
            );
          }
          break;

        case 'add_tag':
          if (action.tag) {
            await this.addTagToFollowerByOaId(oaId, followerId, action.tag);
            await this.zaloAutomationLogRepository.update(logId, {
              status: 'success',
            });
          }
          break;

        case 'remove_tag':
          if (action.tag) {
            await this.removeTagFromFollowerByOaId(
              oaId,
              followerId,
              action.tag,
            );
            await this.zaloAutomationLogRepository.update(logId, {
              status: 'success',
            });
          }
          break;

        default:
          throw new Error(`Loại hành động không hỗ trợ: ${action.type}`);
      }
    } catch (error) {
      this.logger.error(
        `Failed to execute automation action: ${error.message}`,
      );

      // Cập nhật log tự động hóa với lỗi
      await this.zaloAutomationLogRepository.update(logId, {
        status: 'failed',
        error: error.message,
      });

      throw error;
    }
  }

  /**
   * Lấy danh sách tag của Zalo OA
   * @param userId ID của người dùng
   * @param integrationId ID của Integration
   * @returns Danh sách tag
   */
  async getZaloTags(userId: number, integrationId: string): Promise<any[]> {
    try {
      // Lấy thông tin Official Account từ Integration ID
      const oa = await this.getOfficialAccountByIntegrationId(
        userId,
        integrationId,
      );

      // Lấy danh sách tag từ Zalo API sử dụng service mới
      const response = await this.zaloTagService.getTagsOfOA(oa.accessToken);

      if (!response.data || response.data.length === 0) {
        return [];
      }

      // Lấy số lượng người theo dõi cho mỗi tag
      const tags = response.data;
      const tagStats = await Promise.all(
        tags.map(async (tagName: string) => {
          const followerCount = await this.zaloFollowerRepository.count({
            where: {
              oaId: oa.oaId,
              tags: ArrayContains([tagName]),
            },
          });

          return {
            name: tagName,
            followerCount,
          };
        }),
      );

      return tagStats;
    } catch (error) {
      this.logger.error(`Failed to get Zalo tags: ${error.message}`);
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Không thể lấy danh sách tag Zalo',
      );
    }
  }

  /**
   * Lấy danh sách người theo dõi có tag
   * @param userId ID của người dùng
   * @param integrationId ID của Integration
   * @param tagName Tên tag
   * @param queryDto Tham số truy vấn
   * @returns Danh sách người theo dõi có tag với phân trang
   */
  async getZaloFollowersWithTag(
    userId: number,
    integrationId: string,
    tagName: string,
    queryDto: any,
  ): Promise<any> {
    try {
      // Lấy thông tin Official Account từ Integration ID
      const oa = await this.getOfficialAccountByIntegrationId(
        userId,
        integrationId,
      );

      const { page, limit, search, sortBy, sortDirection } = queryDto;
      const skip = (page - 1) * limit;

      // Xây dựng điều kiện tìm kiếm
      const where: any = {
        oaId: oa.oaId,
        tags: { $contains: tagName },
      };

      if (search) {
        where.$or = [
          { displayName: { $like: `%${search}%` } },
          { phone: { $like: `%${search}%` } },
        ];
      }

      // Tìm kiếm người theo dõi
      const [items, totalItems] = await Promise.all([
        this.zaloFollowerRepository.find({
          where,
          skip,
          take: limit,
          order: {
            [sortBy || 'createdAt']: sortDirection || 'DESC',
          },
        }),
        this.zaloFollowerRepository.count({ where }),
      ]);

      // Tính toán thông tin phân trang
      const totalPages = Math.ceil(totalItems / limit);

      return {
        items,
        meta: {
          totalItems,
          itemCount: items.length,
          itemsPerPage: limit,
          totalPages,
          currentPage: page,
        },
      };
    } catch (error) {
      this.logger.error(
        `Failed to get Zalo followers with tag: ${error.message}`,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.DATABASE_ERROR,
        'Không thể lấy danh sách người theo dõi có tag',
      );
    }
  }

  /**
   * Thêm tag cho người theo dõi (dành cho admin - sử dụng oaId trực tiếp)
   * @param oaId ID của Official Account
   * @param followerId ID của người theo dõi
   * @param tagName Tên tag
   * @returns true nếu thành công
   */
  async addTagToFollowerByOaId(
    oaId: string,
    followerId: string,
    tagName: string,
  ): Promise<boolean> {
    try {
      // Lấy thông tin người theo dõi
      const follower = await this.zaloFollowerRepository.findByOaIdAndUserId(
        oaId,
        followerId,
      );
      if (!follower) {
        throw new AppException(
          ErrorCode.RESOURCE_NOT_FOUND,
          'Không tìm thấy người theo dõi',
        );
      }

      // Lấy access token
      const accessToken = await this.getOaToken(oaId);

      // Thêm tag vào Zalo API sử dụng service mới
      const response = await this.zaloTagService.tagFollower(
        accessToken,
        followerId,
        tagName,
      );

      if (response.error !== 0) {
        throw new Error(`Zalo API error: ${response.message}`);
      }

      // Cập nhật thông tin người theo dõi trong database
      const tags = follower.tags || [];
      if (!tags.includes(tagName)) {
        tags.push(tagName);
        await this.zaloFollowerRepository.update(follower.id, {
          tags,
          updatedAt: Date.now(),
        });
      }

      return true;
    } catch (error) {
      this.logger.error(`Failed to add tag to follower: ${error.message}`);
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Không thể thêm tag cho người theo dõi',
      );
    }
  }

  /**
   * Thêm tag cho người theo dõi
   * @param userId ID của người dùng
   * @param integrationId ID của Integration
   * @param followerId ID của người theo dõi
   * @param tagName Tên tag
   * @returns true nếu thành công
   */
  async addTagToFollower(
    userId: number,
    integrationId: string,
    followerId: string,
    tagName: string,
  ): Promise<boolean> {
    try {
      // Lấy thông tin Official Account từ Integration ID
      const oa = await this.getOfficialAccountByIntegrationId(
        userId,
        integrationId,
      );

      // Lấy thông tin người theo dõi
      const follower = await this.zaloFollowerRepository.findByOaIdAndUserId(
        oa.oaId,
        followerId,
      );
      if (!follower) {
        throw new AppException(
          ErrorCode.RESOURCE_NOT_FOUND,
          'Không tìm thấy người theo dõi',
        );
      }

      // Thêm tag vào Zalo API
      const response = await this.zaloApiClient.post(
        '/tag/tagfollower',
        oa.accessToken,
        {
          user_id: followerId,
          tag_name: tagName,
        },
      );

      if (response.error !== 0) {
        throw new Error(`Zalo API error: ${response.message}`);
      }

      // Cập nhật thông tin người theo dõi trong database
      const tags = follower.tags || [];
      if (!tags.includes(tagName)) {
        tags.push(tagName);
        await this.zaloFollowerRepository.update(follower.id, {
          tags,
          updatedAt: Date.now(),
        });
      }

      return true;
    } catch (error) {
      this.logger.error(`Failed to add tag to follower: ${error.message}`);
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Không thể thêm tag cho người theo dõi',
      );
    }
  }

  /**
   * Xóa tag của người theo dõi (dành cho admin - sử dụng oaId trực tiếp)
   * @param oaId ID của Official Account
   * @param followerId ID của người theo dõi
   * @param tagName Tên tag
   * @returns true nếu thành công
   */
  async removeTagFromFollowerByOaId(
    oaId: string,
    followerId: string,
    tagName: string,
  ): Promise<boolean> {
    try {
      // Lấy thông tin người theo dõi
      const follower = await this.zaloFollowerRepository.findByOaIdAndUserId(
        oaId,
        followerId,
      );
      if (!follower) {
        throw new AppException(
          ErrorCode.RESOURCE_NOT_FOUND,
          'Không tìm thấy người theo dõi',
        );
      }

      // Lấy access token
      const accessToken = await this.getOaToken(oaId);

      // Xóa tag từ Zalo API sử dụng service mới
      const response = await this.zaloTagService.removeTagFromFollower(
        accessToken,
        followerId,
        tagName,
      );

      if (response.error !== 0) {
        throw new Error(`Zalo API error: ${response.message}`);
      }

      // Cập nhật thông tin người theo dõi trong database
      const tags = follower.tags || [];
      const tagIndex = tags.indexOf(tagName);
      if (tagIndex !== -1) {
        tags.splice(tagIndex, 1);
        await this.zaloFollowerRepository.update(follower.id, {
          tags,
          updatedAt: Date.now(),
        });
      }

      return true;
    } catch (error) {
      this.logger.error(`Failed to remove tag from follower: ${error.message}`);
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Không thể xóa tag của người theo dõi',
      );
    }
  }

  /**
   * Xóa tag của người theo dõi
   * @param userId ID của người dùng
   * @param integrationId ID của Integration
   * @param followerId ID của người theo dõi
   * @param tagName Tên tag
   * @returns true nếu thành công
   */
  async removeTagFromFollower(
    userId: number,
    integrationId: string,
    followerId: string,
    tagName: string,
  ): Promise<boolean> {
    try {
      // Lấy thông tin Official Account từ Integration ID
      const oa = await this.getOfficialAccountByIntegrationId(
        userId,
        integrationId,
      );

      // Lấy thông tin người theo dõi
      const follower = await this.zaloFollowerRepository.findByOaIdAndUserId(
        oa.oaId,
        followerId,
      );
      if (!follower) {
        throw new AppException(
          ErrorCode.RESOURCE_NOT_FOUND,
          'Không tìm thấy người theo dõi',
        );
      }

      // Xóa tag từ Zalo API sử dụng service mới
      const response = await this.zaloTagService.removeTagFromFollower(
        oa.accessToken,
        followerId,
        tagName,
      );

      if (response.error !== 0) {
        throw new Error(`Zalo API error: ${response.message}`);
      }

      // Cập nhật thông tin người theo dõi trong database
      const tags = follower.tags || [];
      const tagIndex = tags.indexOf(tagName);
      if (tagIndex !== -1) {
        tags.splice(tagIndex, 1);
        await this.zaloFollowerRepository.update(follower.id, {
          tags,
          updatedAt: Date.now(),
        });
      }

      return true;
    } catch (error) {
      this.logger.error(`Failed to remove tag from follower: ${error.message}`);
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Không thể xóa tag của người theo dõi',
      );
    }
  }

  /**
   * Thêm tag cho nhiều người theo dõi
   * @param userId ID của người dùng
   * @param integrationId ID của Integration
   * @param followerIds Danh sách ID người theo dõi
   * @param tagName Tên tag
   * @returns Kết quả thực hiện
   */
  async batchAddTag(
    userId: number,
    integrationId: string,
    followerIds: string[],
    tagName: string,
  ): Promise<{ success: number; failed: number }> {
    let success = 0;
    let failed = 0;

    for (const followerId of followerIds) {
      try {
        await this.addTagToFollower(userId, integrationId, followerId, tagName);
        success++;
      } catch (error) {
        this.logger.error(
          `Failed to add tag to follower ${followerId}: ${error.message}`,
        );
        failed++;
      }
    }

    return { success, failed };
  }

  /**
   * Xóa tag của nhiều người theo dõi
   * @param userId ID của người dùng
   * @param integrationId ID của Integration
   * @param followerIds Danh sách ID người theo dõi
   * @param tagName Tên tag
   * @returns Kết quả thực hiện
   */
  async batchRemoveTag(
    userId: number,
    integrationId: string,
    followerIds: string[],
    tagName: string,
  ): Promise<{ success: number; failed: number }> {
    let success = 0;
    let failed = 0;

    for (const followerId of followerIds) {
      try {
        await this.removeTagFromFollower(
          userId,
          integrationId,
          followerId,
          tagName,
        );
        success++;
      } catch (error) {
        this.logger.error(
          `Failed to remove tag from follower ${followerId}: ${error.message}`,
        );
        failed++;
      }
    }

    return { success, failed };
  }

  /**
   * Lấy danh sách mẫu tin nhắn Zalo
   * @param userId ID của người dùng
   * @param queryDto Tham số truy vấn (bao gồm oaId tùy chọn)
   * @returns Danh sách mẫu tin nhắn với phân trang
   */
  async getZaloMessageTemplates(userId: number, queryDto: any): Promise<any> {
    try {
      const { page, limit, search, sortBy, sortDirection, type, oaId } =
        queryDto;

      // Nếu có oaId thì kiểm tra quyền truy cập Official Account
      if (oaId && oaId.trim() !== '') {
        await this.getOfficialAccountDetail(userId, oaId);
      }

      const skip = (page - 1) * limit;

      // Xây dựng điều kiện tìm kiếm
      const where: any = {
        userId,
      };

      // Chỉ thêm oaId vào điều kiện nếu có giá trị hợp lệ
      if (oaId && oaId.trim() !== '') {
        where.oaId = oaId;
      }

      if (search) {
        where.name = { $like: `%${search}%` };
      }

      if (type) {
        where.type = type;
      }

      // Tìm kiếm mẫu tin nhắn
      const [items, totalItems] = await Promise.all([
        this.zaloMessageTemplateRepository.find({
          where,
          skip,
          take: limit,
          order: {
            [sortBy || 'createdAt']: sortDirection || 'DESC',
          },
        }),
        this.zaloMessageTemplateRepository.count({ where }),
      ]);

      // Tính toán thông tin phân trang
      const totalPages = Math.ceil(totalItems / limit);

      return {
        items,
        meta: {
          totalItems,
          itemCount: items.length,
          itemsPerPage: limit,
          totalPages,
          currentPage: page,
        },
      };
    } catch (error) {
      this.logger.error(
        `Failed to get Zalo message templates: ${error.message}`,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.DATABASE_ERROR,
        'Không thể lấy danh sách mẫu tin nhắn Zalo',
      );
    }
  }

  /**
   * Lấy thông tin chi tiết mẫu tin nhắn Zalo theo ID Official Account
   * @param userId ID của người dùng
   * @param officialAccountId ID của Official Account (khóa chính)
   * @param templateId ID của mẫu tin nhắn
   * @returns Thông tin chi tiết mẫu tin nhắn
   */
  async getZaloMessageTemplateDetailById(
    userId: number,
    officialAccountId: string,
    templateId: number,
  ): Promise<any> {
    try {
      // Kiểm tra quyền truy cập Official Account
      const oa = await this.getOfficialAccountDetailById(
        userId,
        officialAccountId,
      );

      // Lấy thông tin mẫu tin nhắn
      const template = await this.zaloMessageTemplateRepository.findOne({
        where: { id: templateId, userId, oaId: oa.oaId },
      });

      if (!template) {
        throw new AppException(
          ErrorCode.RESOURCE_NOT_FOUND,
          'Không tìm thấy mẫu tin nhắn Zalo',
        );
      }

      return template;
    } catch (error) {
      this.logger.error(
        `Failed to get Zalo message template detail by ID: ${error.message}`,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.DATABASE_ERROR,
        'Không thể lấy thông tin chi tiết mẫu tin nhắn Zalo',
      );
    }
  }

  /**
   * Lấy thông tin chi tiết mẫu tin nhắn Zalo (để tương thích ngược)
   * @param userId ID của người dùng
   * @param oaId ID của Official Account
   * @param id ID của mẫu tin nhắn
   * @returns Thông tin chi tiết mẫu tin nhắn
   */
  async getZaloMessageTemplateDetail(
    userId: number,
    oaId: string,
    id: number,
  ): Promise<any> {
    try {
      // Kiểm tra quyền truy cập Official Account
      await this.getOfficialAccountDetail(userId, oaId);

      // Lấy thông tin mẫu tin nhắn
      const template = await this.zaloMessageTemplateRepository.findOne({
        where: { id, userId, oaId },
      });

      if (!template) {
        throw new AppException(
          ErrorCode.RESOURCE_NOT_FOUND,
          'Không tìm thấy mẫu tin nhắn Zalo',
        );
      }

      return template;
    } catch (error) {
      this.logger.error(
        `Failed to get Zalo message template detail: ${error.message}`,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.DATABASE_ERROR,
        'Không thể lấy thông tin chi tiết mẫu tin nhắn Zalo',
      );
    }
  }

  /**
   * Tạo mẫu tin nhắn Zalo mới
   * @param userId ID của người dùng
   * @param oaId ID của Official Account
   * @param createDto Dữ liệu tạo mẫu tin nhắn
   * @returns Mẫu tin nhắn đã tạo
   */
  async createZaloMessageTemplate(
    userId: number,
    oaId: string,
    createDto: any,
  ): Promise<any> {
    try {
      // Kiểm tra quyền truy cập Official Account
      await this.getOfficialAccountDetail(userId, oaId);

      // Tạo mẫu tin nhắn mới
      const now = Date.now();
      const template = await this.zaloMessageTemplateRepository.create({
        userId,
        oaId,
        templateId: createDto.templateId || `template_${now}`,
        templateName: createDto.name,
        templateContent: createDto.content,
        status: 'pending',
        createdAt: now,
        updatedAt: now,
      });

      return template;
    } catch (error) {
      this.logger.error(
        `Failed to create Zalo message template: ${error.message}`,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.DATABASE_ERROR,
        'Không thể tạo mẫu tin nhắn Zalo',
      );
    }
  }

  /**
   * Cập nhật mẫu tin nhắn Zalo
   * @param userId ID của người dùng
   * @param oaId ID của Official Account
   * @param id ID của mẫu tin nhắn
   * @param updateDto Dữ liệu cập nhật
   * @returns Mẫu tin nhắn đã cập nhật
   */
  async updateZaloMessageTemplate(
    userId: number,
    oaId: string,
    id: number,
    updateDto: any,
  ): Promise<any> {
    try {
      // Kiểm tra mẫu tin nhắn tồn tại
      await this.getZaloMessageTemplateDetail(userId, oaId, id);

      // Cập nhật mẫu tin nhắn
      const updateData: any = {
        updatedAt: Date.now(),
      };

      if (updateDto.name !== undefined) {
        updateData.name = updateDto.name;
      }

      if (updateDto.content !== undefined) {
        updateData.content = updateDto.content;
      }

      const updatedTemplate = await this.zaloMessageTemplateRepository.update(
        id,
        updateData,
      );

      return updatedTemplate;
    } catch (error) {
      this.logger.error(
        `Failed to update Zalo message template: ${error.message}`,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.DATABASE_ERROR,
        'Không thể cập nhật mẫu tin nhắn Zalo',
      );
    }
  }

  /**
   * Xóa mẫu tin nhắn Zalo
   * @param userId ID của người dùng
   * @param oaId ID của Official Account
   * @param id ID của mẫu tin nhắn
   * @returns true nếu xóa thành công
   */
  async deleteZaloMessageTemplate(
    userId: number,
    oaId: string,
    id: number,
  ): Promise<boolean> {
    try {
      // Kiểm tra mẫu tin nhắn tồn tại
      await this.getZaloMessageTemplateDetail(userId, oaId, id);

      // Xóa mẫu tin nhắn
      await this.zaloMessageTemplateRepository.delete(id);

      return true;
    } catch (error) {
      this.logger.error(
        `Failed to delete Zalo message template: ${error.message}`,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.DATABASE_ERROR,
        'Không thể xóa mẫu tin nhắn Zalo',
      );
    }
  }

  /**
   * Gửi tin nhắn sử dụng mẫu
   * @param userId ID của người dùng
   * @param oaId ID của Official Account
   * @param templateId ID của mẫu tin nhắn
   * @param followerId ID của người theo dõi
   * @param data Dữ liệu thay thế
   * @returns Kết quả gửi tin nhắn
   */
  async sendZaloTemplateMessage(
    userId: number,
    oaId: string,
    templateId: number,
    followerId: string,
    data?: Record<string, any>,
  ): Promise<any> {
    try {
      // Kiểm tra mẫu tin nhắn tồn tại
      const template = await this.getZaloMessageTemplateDetail(
        userId,
        oaId,
        templateId,
      );

      // Lấy thông tin người theo dõi
      const follower = await this.zaloFollowerRepository.findByOaIdAndUserId(
        oaId,
        followerId,
      );
      if (!follower) {
        throw new AppException(
          ErrorCode.RESOURCE_NOT_FOUND,
          'Không tìm thấy người theo dõi',
        );
      }

      // Xử lý dữ liệu thay thế
      const processedData = this.processTemplateData(data || {}, follower);

      // Gửi tin nhắn tùy theo loại mẫu
      const accessToken = await this.getOaToken(oaId);

      switch (template.type) {
        case 'text':
          // Thay thế các placeholder trong nội dung văn bản
          let textContent = template.content.text;
          for (const key in processedData) {
            textContent = textContent.replace(
              new RegExp(`{${key}}`, 'g'),
              processedData[key],
            );
          }
          await this.sendTextMessage(oaId, followerId, textContent);
          break;

        case 'image':
          await this.sendImageMessage(
            oaId,
            followerId,
            template.content.imageUrl,
          );
          break;

        case 'file':
          await this.sendFileMessage(
            oaId,
            followerId,
            template.content.fileUrl,
          );
          break;

        case 'list':
          // Xử lý danh sách các phần tử
          const elements = template.content.elements.map((element: any) => {
            let title = element.title;
            let subtitle = element.subtitle;

            // Thay thế các placeholder trong tiêu đề và phụ đề
            for (const key in processedData) {
              title = title.replace(
                new RegExp(`{${key}}`, 'g'),
                processedData[key],
              );
              if (subtitle) {
                subtitle = subtitle.replace(
                  new RegExp(`{${key}}`, 'g'),
                  processedData[key],
                );
              }
            }

            return {
              ...element,
              title,
              subtitle,
            };
          });

          await this.zaloApiClient.post('/message/cs', accessToken, {
            recipient: { user_id: followerId },
            message: {
              attachment: {
                type: 'template',
                payload: {
                  template_type: 'list',
                  elements,
                },
              },
            },
          });
          break;

        default:
          throw new Error(`Loại mẫu tin nhắn không hỗ trợ: ${template.type}`);
      }

      // Lưu lịch sử tin nhắn
      const now = Date.now();
      const message = await this.zaloMessageRepository.create({
        userId: userId.toString(),
        oaId,
        direction: 'outgoing',
        messageType: template.type,
        content: JSON.stringify(template.content),
        data: template.content,
        timestamp: now,
        createdAt: now,
      });

      return {
        messageId: message.id,
        status: 'sent',
        sentAt: now,
      };
    } catch (error) {
      this.logger.error(
        `Failed to send Zalo template message: ${error.message}`,
      );
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Không thể gửi tin nhắn sử dụng mẫu',
      );
    }
  }

  /**
   * Kiểm tra thời gian gửi tin broadcast (8:00 - 22:00)
   * @private
   */
  private validateBroadcastSendingTime(): void {
    const now = new Date();
    const hour = now.getHours();

    if (hour < 8 || hour >= 22) {
      throw new AppException(
        ErrorCode.VALIDATION_ERROR,
        'Chỉ được gửi tin broadcast trong khung giờ từ 8:00 đến 22:00',
      );
    }
  }

  /**
   * Gửi tin Truyền thông Broadcast
   * @param userId ID của người dùng
   * @param integrationId ID của Integration (Official Account)
   * @param broadcastData Dữ liệu broadcast message
   * @returns Kết quả gửi tin broadcast
   */
  async sendBroadcastMessage(
    userId: number,
    integrationId: string,
    broadcastData: any,
  ): Promise<{ message_id: string }> {
    try {
      this.logger.log(
        `Sending broadcast message for user ${userId}, integration ${integrationId}`,
      );

      // Kiểm tra thời gian gửi
      this.validateBroadcastSendingTime();

      // Lấy thông tin Official Account từ Integration
      const oa = await this.getOfficialAccountByIntegrationId(
        userId,
        integrationId,
      );
      if (!oa) {
        throw new AppException(
          ErrorCode.RESOURCE_NOT_FOUND,
          'Không tìm thấy Official Account',
        );
      }

      // Kiểm tra và làm mới token nếu cần
      if (oa.expiresAt && oa.expiresAt <= Date.now()) {
        // Token đã hết hạn, cần refresh
        if (!this.zaloAppId || !this.zaloAppSecret) {
          throw new AppException(
            ErrorCode.CONFIGURATION_ERROR,
            'ZALO_APP_ID hoặc ZALO_APP_SECRET không được định nghĩa',
          );
        }

        const now = Date.now();
        const response = await this.sharedZaloService.refreshOaAccessToken(
          this.zaloAppId,
          this.zaloAppSecret,
          oa.refreshToken,
        );

        // Cập nhật token mới
        await this.zaloOfficialAccountRepository.update(oa.id, {
          accessToken: response.access_token,
          refreshToken: response.refresh_token,
          expiresAt: now + response.expires_in * 1000,
          updatedAt: now,
        });

        // Cập nhật oa object với token mới
        oa.accessToken = response.access_token;
      }

      // Gửi broadcast message qua Zalo API
      const response = await this.zaloApiClient.post(
        '/oa/message',
        oa.accessToken,
        broadcastData,
      );

      if (response.error !== 0) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi gửi tin broadcast: ${response.message}`,
        );
      }

      this.logger.log(
        `Broadcast message sent successfully: ${response.data?.message_id}`,
      );
      return { message_id: response.data?.message_id || '' };
    } catch (error) {
      this.logger.error(`Failed to send broadcast message: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi gửi tin broadcast',
      );
    }
  }

  /**
   * Gửi tin nhắn sử dụng mẫu cho nhiều người theo dõi
   * @param userId ID của người dùng
   * @param oaId ID của Official Account
   * @param templateId ID của mẫu tin nhắn
   * @param followerIds Danh sách ID người theo dõi
   * @param data Dữ liệu thay thế
   * @returns Kết quả gửi tin nhắn
   */
  async batchSendZaloTemplateMessage(
    userId: number,
    oaId: string,
    templateId: number,
    followerIds: string[],
    data?: Record<string, any>,
  ): Promise<{
    totalRecipients: number;
    successCount: number;
    failedCount: number;
  }> {
    const totalRecipients = followerIds.length;
    let successCount = 0;
    let failedCount = 0;

    for (const followerId of followerIds) {
      try {
        await this.sendZaloTemplateMessage(
          userId,
          oaId,
          templateId,
          followerId,
          data,
        );
        successCount++;
      } catch (error) {
        this.logger.error(
          `Failed to send template message to follower ${followerId}: ${error.message}`,
        );
        failedCount++;
      }
    }

    return { totalRecipients, successCount, failedCount };
  }

  /**
   * Đồng bộ người theo dõi Zalo vào danh sách khách hàng
   * @param userId ID của người dùng
   * @param oaId ID của Official Account
   * @param audienceId ID của danh sách khách hàng
   * @param segmentId ID của phân đoạn (tùy chọn)
   * @param tagName Tên tag (tùy chọn)
   * @returns Kết quả đồng bộ
   */
  async syncFollowersToAudience(
    userId: number,
    oaId: string,
    audienceId: number,
    segmentId?: number,
    tagName?: string,
  ): Promise<{
    totalFollowers: number;
    syncedCount: number;
    audienceId: number;
  }> {
    try {
      // Kiểm tra quyền truy cập Official Account
      await this.getOfficialAccountDetail(userId, oaId);

      // Kiểm tra danh sách khách hàng tồn tại
      const audience = await this.userAudienceRepository.findOne({
        where: { id: audienceId, userId },
      });

      if (!audience) {
        throw new AppException(
          ErrorCode.RESOURCE_NOT_FOUND,
          'Không tìm thấy danh sách khách hàng',
        );
      }

      // Lấy danh sách người theo dõi
      let followers: any[];
      if (segmentId) {
        // Lấy người theo dõi từ phân đoạn
        const segment = await this.getZaloSegmentDetail(
          userId,
          oaId,
          segmentId,
        );

        // Thay thế phương thức getZaloSegmentFollowers bằng cách lấy trực tiếp từ repository
        // Giả sử chúng ta có điều kiện lọc từ segment.conditions
        const follower = await this.zaloFollowerRepository.findByOaIdAndUserId(
          oaId,
          userId.toString(),
        );
        followers = follower ? [follower] : [];

        // Lọc followers theo điều kiện của segment nếu cần
        // Đây chỉ là giải pháp tạm thời, cần triển khai đầy đủ sau
        if (segment.conditions) {
          // Thực hiện lọc theo điều kiện
        }
      } else if (tagName) {
        // Lấy người theo dõi có tag
        // Thay thế phương thức getZaloFollowersWithTag bằng cách lấy trực tiếp từ repository
        const follower = await this.zaloFollowerRepository.findByOaIdAndUserId(
          oaId,
          userId.toString(),
        );
        followers = follower ? [follower] : [];

        // Lọc followers theo tag nếu cần
        // Đây chỉ là giải pháp tạm thời, cần triển khai đầy đủ sau
        followers = followers.filter(
          (follower: any) => follower.tags && follower.tags.includes(tagName),
        );
      } else {
        // Lấy tất cả người theo dõi
        // Sử dụng phương thức find thay vì findWithPagination
        const follower = await this.zaloFollowerRepository.findByOaIdAndUserId(
          oaId,
          userId.toString(),
        );
        followers = follower ? [follower] : [];
        // Nếu không có phương thức findByOaIdAndUserId, có thể sử dụng đoạn code sau:
        // const result = await this.zaloFollowerRepository.find({
        //   where: { oaId, userId: userId.toString() },
        //   skip: 0,
        //   take: 1000,
        // });
        // followers = result;
      }

      // Đồng bộ người theo dõi vào danh sách khách hàng
      let syncedCount = 0;
      for (const follower of followers) {
        try {
          // Parse số điện thoại
          if (!follower.phone) {
            continue;
          }

          const phoneData = this.parseFollowerPhone(follower.phone);
          if (!phoneData) {
            continue;
          }

          // Kiểm tra xem khách hàng đã tồn tại trong danh sách chưa
          const existingAudience =
            await this.userAudienceRepository.findByPhoneNumber(
              phoneData.countryCode,
              phoneData.phoneNumber,
              userId,
            );

          if (!existingAudience) {
            // Tạo khách hàng mới
            await this.userAudienceRepository.create({
              userId,
              email: follower.email,
              countryCode: phoneData.countryCode,
              phoneNumber: phoneData.phoneNumber,
              createdAt: Date.now(),
              updatedAt: Date.now(),
            });
            syncedCount++;
          }
        } catch (error) {
          this.logger.error(
            `Failed to sync follower ${follower.userId}: ${error.message}`,
          );
        }
      }

      return {
        totalFollowers: followers.length,
        syncedCount,
        audienceId,
      };
    } catch (error) {
      this.logger.error(
        `Failed to sync Zalo followers to audience: ${error.message}`,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.DATABASE_ERROR,
        'Không thể đồng bộ người theo dõi Zalo vào danh sách khách hàng',
      );
    }
  }

  /**
   * Lấy thống kê tích hợp Zalo
   * @param userId ID của người dùng
   * @param oaId ID của Official Account
   * @param startDate Ngày bắt đầu (tùy chọn)
   * @param endDate Ngày kết thúc (tùy chọn)
   * @returns Thống kê tích hợp Zalo
   */
  async getZaloIntegrationStatistics(
    userId: number,
    oaId: string,
    startDate?: string,
    endDate?: string,
  ): Promise<any> {
    try {
      // Kiểm tra quyền truy cập Official Account
      await this.getOfficialAccountDetail(userId, oaId);

      // Xử lý ngày bắt đầu và kết thúc
      const now = new Date();
      const start = startDate
        ? new Date(startDate)
        : new Date(now.getFullYear(), now.getMonth(), now.getDate() - 30);
      const end = endDate ? new Date(endDate) : now;

      const startTimestamp = start.getTime();
      const endTimestamp = end.getTime();

      // Lấy tổng số tài khoản OA của người dùng
      const totalOfficialAccounts =
        await this.zaloOfficialAccountRepository.count({
          where: { userId },
        });

      // Lấy tổng số người theo dõi
      const totalFollowers = await this.zaloFollowerRepository.count({
        where: { oaId, userId: userId.toString() },
      });

      // Lấy tổng số chiến dịch
      const [_, totalCampaigns] =
        await this.zaloCampaignRepository.findWithPagination({
          where: { oaId, userId },
          skip: 0,
          take: 1,
        });

      // Lấy tổng số tin nhắn đã gửi
      const totalMessagesSent = await this.zaloMessageRepository.count({
        where: {
          oaId,
          userId: userId.toString(),
          direction: 'outgoing',
          createdAt: Between(startTimestamp, endTimestamp),
        },
      });

      // Lấy tổng số ZNS đã gửi
      const totalZnsSent = await this.zaloZnsMessageRepository.count({
        where: {
          oaId,
          userId,
          createdAt: Between(startTimestamp, endTimestamp),
        },
      });

      // Tính phần trăm tương tác
      const interactionRate = await this.calculateInteractionRate(
        userId,
        oaId,
        startTimestamp,
        endTimestamp,
      );

      // Lấy thống kê tăng trưởng người theo dõi theo ngày
      const followerGrowth = await this.getFollowerGrowthByDay(
        userId,
        oaId,
        startTimestamp,
        endTimestamp,
      );

      // Lấy thống kê tin nhắn
      const messageStats = await this.getMessageStats(
        userId,
        oaId,
        startTimestamp,
        endTimestamp,
      );

      // Lấy thống kê ZNS
      const znsStats = await this.getZnsStats(
        userId,
        oaId,
        startTimestamp,
        endTimestamp,
      );

      return {
        totalOfficialAccounts,
        totalFollowers,
        totalCampaigns,
        totalMessagesSent,
        totalZnsSent,
        interactionRate,
        followerGrowth,
        messageStats,
        znsStats,
      };
    } catch (error) {
      this.logger.error(
        `Failed to get Zalo integration statistics: ${error.message}`,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.DATABASE_ERROR,
        'Không thể lấy thống kê tích hợp Zalo',
      );
    }
  }

  /**
   * Lấy thống kê tăng trưởng người theo dõi theo ngày
   * @param userId ID của người dùng
   * @param oaId ID của Official Account
   * @param startTimestamp Thời gian bắt đầu
   * @param endTimestamp Thời gian kết thúc
   * @returns Thống kê tăng trưởng người theo dõi theo ngày
   */
  private async getFollowerGrowthByDay(
    userId: number,
    oaId: string,
    startTimestamp: number,
    endTimestamp: number,
  ): Promise<any[]> {
    // Lấy danh sách người theo dõi mới trong khoảng thời gian
    const followers = await this.zaloFollowerRepository.find({
      where: {
        oaId,
        userId: userId.toString(),
        createdAt: Between(startTimestamp, endTimestamp),
      },
      select: ['createdAt'],
    });

    // Nhóm người theo dõi theo ngày
    const growthByDay = {};
    for (const follower of followers) {
      const date = new Date(follower.createdAt);
      const dateString = `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`;

      if (!growthByDay[dateString]) {
        growthByDay[dateString] = 0;
      }

      growthByDay[dateString]++;
    }

    // Chuyển đổi thành mảng
    const result = Object.keys(growthByDay).map((date) => ({
      date,
      count: growthByDay[date],
    }));

    // Sắp xếp theo ngày
    result.sort((a, b) => a.date.localeCompare(b.date));

    return result;
  }

  /**
   * Lấy thống kê tin nhắn
   * @param userId ID của người dùng
   * @param oaId ID của Official Account
   * @param startTimestamp Thời gian bắt đầu
   * @param endTimestamp Thời gian kết thúc
   * @returns Thống kê tin nhắn
   */
  private async getMessageStats(
    userId: number,
    oaId: string,
    startTimestamp: number,
    endTimestamp: number,
  ): Promise<any> {
    // Lấy tổng số tin nhắn đã gửi
    const sent = await this.zaloMessageRepository.count({
      where: {
        oaId,
        userId: userId.toString(),
        direction: 'outgoing',
        createdAt: Between(startTimestamp, endTimestamp),
      },
    });

    // Lấy số tin nhắn đã gửi thành công
    const delivered = await this.zaloMessageRepository.count({
      where: {
        oaId,
        userId: userId.toString(),
        direction: 'outgoing',
        createdAt: Between(startTimestamp, endTimestamp),
      },
    });

    // Lấy số tin nhắn đã đọc
    const read = await this.zaloMessageRepository.count({
      where: {
        oaId,
        userId: userId.toString(),
        direction: 'outgoing',
        createdAt: Between(startTimestamp, endTimestamp),
      },
    });

    // Lấy số tin nhắn gửi thất bại
    const failed = await this.zaloMessageRepository.count({
      where: {
        oaId,
        userId: userId.toString(),
        direction: 'outgoing',
        createdAt: Between(startTimestamp, endTimestamp),
      },
    });

    return {
      sent,
      delivered,
      read,
      failed,
    };
  }

  /**
   * Lấy thống kê ZNS
   * @param userId ID của người dùng
   * @param oaId ID của Official Account
   * @param startTimestamp Thời gian bắt đầu
   * @param endTimestamp Thời gian kết thúc
   * @returns Thống kê ZNS
   */
  private async getZnsStats(
    userId: number,
    oaId: string,
    startTimestamp: number,
    endTimestamp: number,
  ): Promise<any> {
    // Lấy tổng số ZNS đã gửi
    const sent = await this.zaloZnsMessageRepository.count({
      where: {
        oaId,
        userId,
        createdAt: Between(startTimestamp, endTimestamp),
      },
    });

    // Lấy số ZNS đã gửi thành công
    const delivered = await this.zaloZnsMessageRepository.count({
      where: {
        oaId,
        userId,
        status: 'success',
        createdAt: Between(startTimestamp, endTimestamp),
      },
    });

    // Lấy số ZNS đã đọc
    const read = await this.zaloZnsMessageRepository.count({
      where: {
        oaId,
        userId,
        status: 'read',
        createdAt: Between(startTimestamp, endTimestamp),
      },
    });

    // Lấy số ZNS gửi thất bại
    const failed = await this.zaloZnsMessageRepository.count({
      where: {
        oaId,
        userId,
        status: 'failed',
        createdAt: Between(startTimestamp, endTimestamp),
      },
    });

    return {
      sent,
      delivered,
      read,
      failed,
    };
  }

  /**
   * Tính phần trăm tương tác
   * @param userId ID của người dùng
   * @param oaId ID của Official Account
   * @param startTimestamp Thời gian bắt đầu
   * @param endTimestamp Thời gian kết thúc
   * @returns Phần trăm tương tác
   */
  private async calculateInteractionRate(
    userId: number,
    oaId: string,
    startTimestamp: number,
    endTimestamp: number,
  ): Promise<number> {
    try {
      // Lấy tổng số tin nhắn đã gửi
      const totalMessagesSent = await this.zaloMessageRepository.count({
        where: {
          oaId,
          userId: userId.toString(),
          direction: 'outgoing',
          createdAt: Between(startTimestamp, endTimestamp),
        },
      });

      // Lấy tổng số tin nhắn phản hồi từ người dùng
      const totalMessagesReceived = await this.zaloMessageRepository.count({
        where: {
          oaId,
          userId: userId.toString(),
          direction: 'incoming',
          createdAt: Between(startTimestamp, endTimestamp),
        },
      });

      // Tính phần trăm tương tác
      if (totalMessagesSent === 0) {
        return 0;
      }

      const interactionRate = (totalMessagesReceived / totalMessagesSent) * 100;
      return Math.round(interactionRate * 100) / 100; // Làm tròn 2 chữ số thập phân
    } catch (error) {
      this.logger.error(
        `Failed to calculate interaction rate: ${error.message}`,
      );
      return 0;
    }
  }

  /**
   * Lấy thống kê tổng quan Zalo Ads
   * @param userId ID của người dùng
   * @param startDate Ngày bắt đầu (tùy chọn)
   * @param endDate Ngày kết thúc (tùy chọn)
   * @param adsAccountId ID tài khoản Ads cụ thể (tùy chọn)
   * @returns Thống kê tổng quan Zalo Ads
   */
  async getZaloAdsOverview(
    userId: number,
    startDate?: string,
    endDate?: string,
    adsAccountId?: string,
  ): Promise<any> {
    try {
      // Xử lý ngày bắt đầu và kết thúc
      const now = new Date();
      const start = startDate
        ? new Date(startDate)
        : new Date(now.getFullYear(), now.getMonth(), now.getDate() - 30);
      const end = endDate ? new Date(endDate) : now;

      const startDateStr = start.toISOString().split('T')[0];
      const endDateStr = end.toISOString().split('T')[0];

      // Xây dựng điều kiện tìm kiếm
      const whereConditions: any = {
        userId,
        dateStart: MoreThanOrEqual(startDateStr),
        dateEnd: LessThanOrEqual(endDateStr),
      };

      if (adsAccountId) {
        whereConditions.adsAccountId = adsAccountId;
      }

      // Lấy tổng số tài khoản Zalo Ads của người dùng
      const totalAdsAccounts = await this.zaloAdsAccountRepository.count({
        where: { userId },
      });

      // Lấy tổng số chiến dịch Ads
      const totalAdsCampaigns = await this.zaloAdsCampaignRepository.count({
        where: adsAccountId ? { userId, adsAccountId } : { userId },
      });

      // Lấy tổng các chỉ số performance
      const performanceTotals =
        await this.zaloAdsPerformanceRepository.calculateTotals({
          where: whereConditions,
        });

      // Tính các chỉ số
      const ctr =
        performanceTotals.totalImpressions > 0
          ? (performanceTotals.totalClicks /
              performanceTotals.totalImpressions) *
            100
          : 0;

      const avgCpc =
        performanceTotals.totalClicks > 0
          ? performanceTotals.totalSpend / performanceTotals.totalClicks
          : 0;

      const avgCpm =
        performanceTotals.totalImpressions > 0
          ? (performanceTotals.totalSpend /
              performanceTotals.totalImpressions) *
            1000
          : 0;

      const roas =
        performanceTotals.totalSpend > 0
          ? performanceTotals.totalRevenue / performanceTotals.totalSpend
          : 0;

      return {
        totalAdsAccounts,
        totalAdsCampaigns,
        totalSpent: performanceTotals.totalSpend,
        totalRevenue: performanceTotals.totalRevenue,
        totalImpressions: performanceTotals.totalImpressions,
        totalClicks: performanceTotals.totalClicks,
        roas: Math.round(roas * 100) / 100,
        ctr: Math.round(ctr * 100) / 100,
        avgCpc: Math.round(avgCpc),
        avgCpm: Math.round(avgCpm),
      };
    } catch (error) {
      this.logger.error(`Failed to get Zalo Ads overview: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.DATABASE_ERROR,
        'Không thể lấy thống kê tổng quan Zalo Ads',
      );
    }
  }

  /**
   * Lấy thống kê tổng quan template Zalo theo ID Official Account
   * @param userId ID của người dùng
   * @param officialAccountId ID của Official Account (khóa chính)
   * @returns Thống kê tổng quan template
   */
  async getTemplateOverviewById(
    userId: number,
    officialAccountId: string,
  ): Promise<any> {
    try {
      // Kiểm tra quyền truy cập Official Account
      const oa = await this.getOfficialAccountDetailById(
        userId,
        officialAccountId,
      );

      const now = Date.now();
      const threeDaysAgo = now - 3 * 24 * 60 * 60 * 1000; // 3 ngày trước

      // 1. Tổng số templates
      const totalTemplates = await this.zaloZnsTemplateRepository.count({
        where: { userId, oaId: oa.oaId },
      });

      // 2. Templates đã duyệt
      const approvedTemplates = await this.zaloZnsTemplateRepository.count({
        where: { userId, oaId: oa.oaId, status: 'approved' },
      });

      // 3. Templates chờ duyệt
      const pendingTemplates = await this.zaloZnsTemplateRepository.count({
        where: { userId, oaId: oa.oaId, status: 'pending' },
      });

      // 4. Templates bị từ chối
      const rejectedTemplates = await this.zaloZnsTemplateRepository.count({
        where: { userId, oaId: oa.oaId, status: 'rejected' },
      });

      // 5. Templates mới trong 3 ngày
      const newTemplatesLast3Days = await this.zaloZnsTemplateRepository.count({
        where: {
          userId,
          oaId: oa.oaId,
          createdAt: MoreThanOrEqual(threeDaysAgo),
        },
      });

      // 6. Tổng số tin nhắn ZNS đã gửi
      const totalMessagesSent = await this.zaloZnsMessageRepository.count({
        where: { userId, oaId: oa.oaId },
      });

      // 7. Tính tổng chi phí (giả sử mỗi tin nhắn ZNS có cost 1500 VND)
      const costPerMessage = 1500; // VND
      const totalCostSpent = totalMessagesSent * costPerMessage;

      // 8. Chi phí trung bình mỗi tin nhắn
      const averageCostPerMessage =
        totalMessagesSent > 0 ? totalCostSpent / totalMessagesSent : 0;

      return {
        totalTemplates,
        approvedTemplates,
        pendingTemplates,
        rejectedTemplates,
        averageCostPerMessage,
        newTemplatesLast3Days,
        totalMessagesSent,
        totalCostSpent,
        updatedAt: now,
      };
    } catch (error) {
      this.logger.error(
        `Failed to get template overview by ID: ${error.message}`,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.DATABASE_ERROR,
        'Không thể lấy thống kê tổng quan template',
      );
    }
  }

  /**
   * Lấy thống kê tổng quan template Zalo (để tương thích ngược)
   * @param userId ID của người dùng
   * @param oaId ID của Official Account
   * @returns Thống kê tổng quan template
   */
  async getTemplateOverview(userId: number, oaId: string): Promise<any> {
    try {
      // Kiểm tra quyền truy cập Official Account
      await this.getOfficialAccountDetail(userId, oaId);

      const now = Date.now();
      const threeDaysAgo = now - 3 * 24 * 60 * 60 * 1000; // 3 ngày trước

      // 1. Tổng số templates
      const totalTemplates = await this.zaloZnsTemplateRepository.count({
        where: { userId, oaId },
      });

      // 2. Templates đã duyệt
      const approvedTemplates = await this.zaloZnsTemplateRepository.count({
        where: { userId, oaId, status: 'approved' },
      });

      // 3. Templates chờ duyệt
      const pendingTemplates = await this.zaloZnsTemplateRepository.count({
        where: { userId, oaId, status: 'pending' },
      });

      // 4. Templates bị từ chối
      const rejectedTemplates = await this.zaloZnsTemplateRepository.count({
        where: { userId, oaId, status: 'rejected' },
      });

      // 5. Templates mới trong 3 ngày
      const newTemplatesLast3Days = await this.zaloZnsTemplateRepository.count({
        where: {
          userId,
          oaId,
          createdAt: MoreThanOrEqual(threeDaysAgo),
        },
      });

      // 6. Tổng số tin nhắn ZNS đã gửi
      const totalMessagesSent = await this.zaloZnsMessageRepository.count({
        where: { userId, oaId },
      });

      // 7. Tính tổng chi phí (giả sử mỗi tin nhắn ZNS có cost 1500 VND)
      const costPerMessage = 1500; // VND
      const totalCostSpent = totalMessagesSent * costPerMessage;

      // 8. Chi phí trung bình mỗi tin nhắn
      const averageCostPerMessage =
        totalMessagesSent > 0 ? totalCostSpent / totalMessagesSent : 0;

      return {
        totalTemplates,
        approvedTemplates,
        pendingTemplates,
        rejectedTemplates,
        averageCostPerMessage,
        newTemplatesLast3Days,
        totalMessagesSent,
        totalCostSpent,
        updatedAt: now,
      };
    } catch (error) {
      this.logger.error(`Failed to get template overview: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.DATABASE_ERROR,
        'Không thể lấy thống kê tổng quan template',
      );
    }
  }

  /**
   * Tạo video upload record
   * @param data Dữ liệu video upload
   * @returns Video upload record đã tạo
   */
  async createVideoUploadRecord(data: any): Promise<any> {
    try {
      return await this.zaloVideoUploadRepository.create(data);
    } catch (error) {
      this.logger.error(
        `Failed to create video upload record: ${error.message}`,
      );
      throw new AppException(
        ErrorCode.DATABASE_ERROR,
        'Không thể tạo video upload record',
      );
    }
  }

  /**
   * Cập nhật trạng thái video upload
   * @param token Token của video upload
   * @param status Trạng thái mới
   * @param statusMessage Thông báo trạng thái
   * @param convertPercent Phần trăm chuyển đổi
   * @param videoId Video ID từ Zalo
   * @returns Video upload record đã cập nhật
   */
  async updateVideoUploadStatus(
    token: string,
    status: number,
    statusMessage?: string,
    convertPercent?: number,
    videoId?: string,
  ): Promise<any> {
    try {
      return await this.zaloVideoUploadRepository.updateStatusByToken(
        token,
        status,
        statusMessage,
        convertPercent,
        videoId,
      );
    } catch (error) {
      this.logger.error(
        `Failed to update video upload status: ${error.message}`,
      );
      throw new AppException(
        ErrorCode.DATABASE_ERROR,
        'Không thể cập nhật trạng thái video upload',
      );
    }
  }

  /**
   * Kiểm tra và cập nhật trạng thái tất cả các chiến dịch Zalo đang chạy
   * @param userId ID của người dùng
   * @returns Kết quả cập nhật trạng thái
   */
  async syncAllCampaignStatus(userId: number): Promise<any> {
    try {
      this.logger.log(
        `Bắt đầu kiểm tra trạng thái chiến dịch cho user ${userId}`,
      );

      const result = {
        totalCampaigns: 0,
        updatedCampaigns: 0,
        zaloCampaigns: 0,
        oaMessageCampaigns: 0,
        znsCampaigns: 0,
        details: [] as Array<{
          campaignId: number;
          campaignType: string;
          oldStatus: string;
          newStatus: string;
          updated: boolean;
        }>,
      };

      // 1. Kiểm tra ZaloCampaign (CONSULTATION_SEQUENCE)
      const runningZaloCampaigns =
        await this.zaloCampaignRepository.findByUserIdAndStatuses(userId, [
          ZaloCampaignStatus.RUNNING,
          ZaloCampaignStatus.SCHEDULED,
        ]);

      result.zaloCampaigns = runningZaloCampaigns.length;
      result.totalCampaigns += runningZaloCampaigns.length;

      for (const campaign of runningZaloCampaigns) {
        const statusResult = await this.checkZaloCampaignJobStatus(campaign);
        result.details.push({
          campaignId: campaign.id,
          campaignType: 'ZaloCampaign',
          oldStatus: campaign.status,
          newStatus: statusResult.newStatus,
          updated: statusResult.updated,
        });
        if (statusResult.updated) {
          result.updatedCampaigns++;
        }
      }

      // 2. Kiểm tra ZaloOaMessageCampaign
      const runningOaCampaigns =
        await this.zaloOaMessageCampaignRepository.findByStatus(
          ZaloOaMessageCampaignStatus.RUNNING,
        );

      const userOaCampaigns = runningOaCampaigns.filter(
        (c) => c.userId === userId,
      );
      result.oaMessageCampaigns = userOaCampaigns.length;
      result.totalCampaigns += userOaCampaigns.length;

      for (const campaign of userOaCampaigns) {
        const statusResult =
          await this.checkOaMessageCampaignJobStatus(campaign);
        result.details.push({
          campaignId: campaign.id,
          campaignType: 'ZaloOaMessageCampaign',
          oldStatus: campaign.status,
          newStatus: statusResult.newStatus,
          updated: statusResult.updated,
        });
        if (statusResult.updated) {
          result.updatedCampaigns++;
        }
      }

      // 3. Kiểm tra ZaloZnsCampaign
      const runningZnsCampaigns =
        await this.zaloZnsCampaignRepository.findByStatus(
          ZaloZnsCampaignStatus.SCHEDULED,
        );

      const userZnsCampaigns = runningZnsCampaigns.filter(
        (c) => c.userId === userId,
      );
      result.znsCampaigns = userZnsCampaigns.length;
      result.totalCampaigns += userZnsCampaigns.length;

      for (const campaign of userZnsCampaigns) {
        const statusResult = await this.checkZnsCampaignJobStatus(campaign);
        result.details.push({
          campaignId: campaign.id,
          campaignType: 'ZaloZnsCampaign',
          oldStatus: campaign.status,
          newStatus: statusResult.newStatus,
          updated: statusResult.updated,
        });
        if (statusResult.updated) {
          result.updatedCampaigns++;
        }
      }

      this.logger.log(
        `Hoàn thành kiểm tra trạng thái chiến dịch cho user ${userId}. ` +
          `Tổng: ${result.totalCampaigns}, Cập nhật: ${result.updatedCampaigns}`,
      );

      return result;
    } catch (error) {
      this.logger.error(
        `Lỗi khi kiểm tra trạng thái chiến dịch cho user ${userId}: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Không thể kiểm tra trạng thái chiến dịch',
      );
    }
  }

  /**
   * Kiểm tra trạng thái job của ZaloCampaign
   * @param campaign Chiến dịch Zalo
   * @returns Kết quả kiểm tra trạng thái
   */
  private async checkZaloCampaignJobStatus(campaign: ZaloCampaign): Promise<{
    newStatus: string;
    updated: boolean;
  }> {
    try {
      // ZaloCampaign (CONSULTATION_SEQUENCE) sử dụng queue ZALO_CONSULTATION_SEQUENCE
      // Kiểm tra thông qua jobIds nếu có, nếu không thì kiểm tra thống kê

      if (campaign.jobIds && campaign.jobIds.length > 0) {
        // Có jobIds - kiểm tra trạng thái job trong queue
        let completedJobs = 0;
        let failedJobs = 0;
        let activeJobs = 0;

        for (const jobId of campaign.jobIds) {
          try {
            const jobStatus = await this.queueService.getJobStatus(
              QueueName.ZALO_CONSULTATION_SEQUENCE,
              jobId,
            );

            switch (jobStatus.state) {
              case 'completed':
                completedJobs++;
                break;
              case 'failed':
                failedJobs++;
                break;
              case 'waiting':
              case 'active':
              case 'delayed':
                activeJobs++;
                break;
            }
          } catch (error) {
            // Job không tồn tại, coi như failed
            failedJobs++;
          }
        }

        const totalJobs = campaign.jobIds.length;
        let newStatus = campaign.status;

        if (completedJobs === totalJobs) {
          // Tất cả job hoàn thành
          newStatus = ZaloCampaignStatus.COMPLETED;
        } else if (failedJobs === totalJobs) {
          // Tất cả job thất bại
          newStatus = ZaloCampaignStatus.FAILED;
        } else if (
          activeJobs === 0 &&
          completedJobs + failedJobs === totalJobs
        ) {
          // Không còn job active, một số thành công một số thất bại
          newStatus =
            completedJobs > 0
              ? ZaloCampaignStatus.COMPLETED
              : ZaloCampaignStatus.FAILED;
        }

        if (newStatus !== campaign.status) {
          const updateData: any = {
            status: newStatus,
            updatedAt: Date.now(),
          };

          if (newStatus === ZaloCampaignStatus.COMPLETED) {
            updateData.completedAt = Date.now();
          }

          await this.zaloCampaignRepository.update(campaign.id, updateData);
          return { newStatus, updated: true };
        }
      } else {
        // Không có jobIds - kiểm tra thông qua thống kê (fallback method)
        const totalRecipients = campaign.totalRecipients || 0;
        const processedCount =
          (campaign.successCount || 0) + (campaign.failureCount || 0);

        if (totalRecipients > 0 && processedCount >= totalRecipients) {
          // Tất cả đã được xử lý
          const newStatus = ZaloCampaignStatus.COMPLETED;
          if (campaign.status !== newStatus) {
            await this.zaloCampaignRepository.update(campaign.id, {
              status: newStatus,
              completedAt: Date.now(),
              updatedAt: Date.now(),
            });
            return { newStatus, updated: true };
          }
        }
      }

      return { newStatus: campaign.status, updated: false };
    } catch (error) {
      this.logger.error(
        `Lỗi khi kiểm tra trạng thái ZaloCampaign ${campaign.id}: ${error.message}`,
      );
      return { newStatus: campaign.status, updated: false };
    }
  }

  /**
   * Kiểm tra trạng thái job của ZaloOaMessageCampaign
   * @param campaign Chiến dịch tin nhắn OA
   * @returns Kết quả kiểm tra trạng thái
   */
  private async checkOaMessageCampaignJobStatus(
    campaign: ZaloOaMessageCampaign,
  ): Promise<{
    newStatus: string;
    updated: boolean;
  }> {
    try {
      if (!campaign.jobIds || campaign.jobIds.length === 0) {
        return { newStatus: campaign.status, updated: false };
      }

      let completedJobs = 0;
      let failedJobs = 0;
      let activeJobs = 0;

      // Kiểm tra trạng thái từng job
      for (const jobId of campaign.jobIds) {
        try {
          const jobStatus = await this.queueService.getJobStatus(
            QueueName.ZALO_ZNS,
            jobId,
          );

          switch (jobStatus.state) {
            case 'completed':
              completedJobs++;
              break;
            case 'failed':
              failedJobs++;
              break;
            case 'waiting':
            case 'active':
            case 'delayed':
              activeJobs++;
              break;
          }
        } catch (error) {
          // Job không tồn tại, coi như failed
          failedJobs++;
        }
      }

      const totalJobs = campaign.jobIds.length;
      let newStatus = campaign.status;
      let updated = false;

      if (completedJobs === totalJobs) {
        // Tất cả job hoàn thành
        newStatus = ZaloOaMessageCampaignStatus.COMPLETED;
      } else if (failedJobs === totalJobs) {
        // Tất cả job thất bại
        newStatus = ZaloOaMessageCampaignStatus.FAILED;
      } else if (activeJobs === 0 && completedJobs + failedJobs === totalJobs) {
        // Không còn job active, một số thành công một số thất bại
        newStatus =
          completedJobs > 0
            ? ZaloOaMessageCampaignStatus.COMPLETED
            : ZaloOaMessageCampaignStatus.FAILED;
      }

      if (newStatus !== campaign.status) {
        const updateData: any = {
          status: newStatus,
          updatedAt: Date.now(),
        };

        if (newStatus === ZaloOaMessageCampaignStatus.COMPLETED) {
          updateData.completedAt = Date.now();
        }

        await this.zaloOaMessageCampaignRepository.updateStatus(
          campaign.id,
          newStatus,
          updateData,
        );
        updated = true;
      }

      return { newStatus, updated };
    } catch (error) {
      this.logger.error(
        `Lỗi khi kiểm tra trạng thái ZaloOaMessageCampaign ${campaign.id}: ${error.message}`,
      );
      return { newStatus: campaign.status, updated: false };
    }
  }

  /**
   * Kiểm tra trạng thái job của ZaloZnsCampaign
   * @param campaign Chiến dịch ZNS
   * @returns Kết quả kiểm tra trạng thái
   */
  private async checkZnsCampaignJobStatus(campaign: ZaloZnsCampaign): Promise<{
    newStatus: string;
    updated: boolean;
  }> {
    try {
      if (!campaign.jobIds || campaign.jobIds.length === 0) {
        return { newStatus: campaign.status, updated: false };
      }

      let completedJobs = 0;
      let failedJobs = 0;
      let activeJobs = 0;

      // Kiểm tra trạng thái từng job
      for (const jobId of campaign.jobIds) {
        try {
          const jobStatus = await this.queueService.getJobStatus(
            QueueName.ZALO_ZNS,
            jobId,
          );

          switch (jobStatus.state) {
            case 'completed':
              completedJobs++;
              break;
            case 'failed':
              failedJobs++;
              break;
            case 'waiting':
            case 'active':
            case 'delayed':
              activeJobs++;
              break;
          }
        } catch (error) {
          // Job không tồn tại, coi như failed
          failedJobs++;
        }
      }

      const totalJobs = campaign.jobIds.length;
      let newStatus = campaign.status;
      let updated = false;

      if (completedJobs === totalJobs) {
        // Tất cả job hoàn thành
        newStatus = ZaloZnsCampaignStatus.SENT;
      } else if (failedJobs === totalJobs) {
        // Tất cả job thất bại
        newStatus = ZaloZnsCampaignStatus.FAILED;
      } else if (activeJobs === 0 && completedJobs + failedJobs === totalJobs) {
        // Không còn job active, một số thành công một số thất bại
        newStatus =
          completedJobs > 0
            ? ZaloZnsCampaignStatus.SENT
            : ZaloZnsCampaignStatus.FAILED;
      }

      if (newStatus !== campaign.status) {
        const updateData: any = {
          status: newStatus,
          updatedAt: Date.now(),
        };

        if (newStatus === ZaloZnsCampaignStatus.SENT) {
          updateData.completedAt = Date.now();
        }

        await this.zaloZnsCampaignRepository.update(campaign.id, updateData);
        updated = true;
      }

      return { newStatus, updated };
    } catch (error) {
      this.logger.error(
        `Lỗi khi kiểm tra trạng thái ZaloZnsCampaign ${campaign.id}: ${error.message}`,
      );
      return { newStatus: campaign.status, updated: false };
    }
  }

  /**
   * Lấy danh sách video upload
   * @param userId ID của người dùng
   * @param queryDto Tham số truy vấn
   * @returns Danh sách video upload với phân trang
   */
  async getVideoUploadList(userId: number, queryDto: any): Promise<any> {
    try {
      const { page = 1, limit = 10, oaId, status, search } = queryDto;

      // Nếu có oaId thì kiểm tra quyền truy cập Official Account
      if (oaId && oaId.trim() !== '') {
        await this.getOfficialAccountDetail(userId, oaId);
      }

      const { items, total } =
        await this.zaloVideoUploadRepository.findWithPagination(
          userId,
          oaId,
          page,
          limit,
        );

      // Filter theo status nếu có
      let filteredItems = items;
      if (status !== undefined) {
        filteredItems = items.filter((item) => item.status === status);
      }

      // Filter theo search nếu có
      if (search && search.trim() !== '') {
        filteredItems = filteredItems.filter(
          (item) =>
            item.videoName.toLowerCase().includes(search.toLowerCase()) ||
            (item.description &&
              item.description.toLowerCase().includes(search.toLowerCase())),
        );
      }

      const totalPages = Math.ceil(total / limit);

      return {
        items: filteredItems,
        meta: {
          totalItems: total,
          itemCount: filteredItems.length,
          itemsPerPage: limit,
          totalPages,
          currentPage: page,
        },
      };
    } catch (error) {
      this.logger.error(`Failed to get video upload list: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.DATABASE_ERROR,
        'Không thể lấy danh sách video upload',
      );
    }
  }

  /**
   * Lấy thông tin chi tiết video upload
   * @param userId ID của người dùng
   * @param id ID của video upload
   * @returns Thông tin chi tiết video upload
   */
  async getVideoUploadDetail(userId: number, id: number): Promise<any> {
    try {
      const videoUpload = await this.zaloVideoUploadRepository.findById(id);

      if (!videoUpload || videoUpload.userId !== userId) {
        throw new AppException(
          ErrorCode.RESOURCE_NOT_FOUND,
          'Không tìm thấy video upload',
        );
      }

      return videoUpload;
    } catch (error) {
      this.logger.error(`Failed to get video upload detail: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.DATABASE_ERROR,
        'Không thể lấy thông tin chi tiết video upload',
      );
    }
  }

  /**
   * Xóa video upload
   * @param userId ID của người dùng
   * @param id ID của video upload
   * @returns Kết quả xóa
   */
  async deleteVideoUpload(userId: number, id: number): Promise<boolean> {
    try {
      // Kiểm tra quyền sở hữu
      const videoUpload = await this.getVideoUploadDetail(userId, id);

      return await this.zaloVideoUploadRepository.delete(id);
    } catch (error) {
      this.logger.error(`Failed to delete video upload: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.DATABASE_ERROR,
        'Không thể xóa video upload',
      );
    }
  }

  /**
   * Parse số điện thoại từ Zalo follower thành countryCode và phoneNumber
   * @param phone Số điện thoại từ Zalo (có thể có hoặc không có mã quốc gia)
   * @returns Object chứa countryCode và phoneNumber hoặc null nếu không hợp lệ
   */
  private parseFollowerPhone(
    phone: string,
  ): { countryCode: number; phoneNumber: string } | null {
    if (!phone || !phone.trim()) {
      return null;
    }

    // Nếu số điện thoại đã có mã quốc gia (+84, +1, etc.)
    if (phone.startsWith('+')) {
      const parsed = parseInternationalPhone(phone);
      if (parsed) {
        return parsed;
      }
    }

    // Nếu số điện thoại không có mã quốc gia, giả định là Việt Nam
    if (phone.startsWith('0')) {
      // Số điện thoại Việt Nam bắt đầu bằng 0, loại bỏ số 0 đầu
      const phoneWithoutZero = phone.substring(1);
      return {
        countryCode: 84,
        phoneNumber: phoneWithoutZero,
      };
    }

    // Nếu số điện thoại không bắt đầu bằng 0 hoặc +, giả định là đã loại bỏ mã quốc gia
    return {
      countryCode: 84,
      phoneNumber: phone,
    };
  }

  /**
   * Lấy oaId từ integrationId
   * @param userId ID của người dùng
   * @param integrationId ID của Integration
   * @returns oaId từ metadata của Integration
   */
  async getOaIdFromIntegrationId(
    userId: number,
    integrationId: string,
  ): Promise<string> {
    try {
      const integration =
        await this.zaloOAIntegrationService.getZaloOAIntegrationById(
          integrationId,
        );

      // Kiểm tra quyền truy cập
      if (integration.userId !== userId) {
        throw new AppException(
          MARKETING_ERROR_CODES.ZALO_OA_ACCESS_DENIED,
          'Không có quyền truy cập Integration này',
        );
      }

      // Lấy oaId từ metadata
      const metadata = integration.metadata as ZaloOAMetadata;
      if (!metadata?.oaId) {
        throw new AppException(
          MARKETING_ERROR_CODES.ZALO_OA_NOT_FOUND,
          'Không tìm thấy oaId trong metadata của Integration',
        );
      }

      return metadata.oaId;
    } catch (error) {
      this.logger.error(
        `Failed to get oaId from integrationId ${integrationId}: ${error.message}`,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        MARKETING_ERROR_CODES.ZALO_OA_NOT_FOUND,
        'Không tìm thấy Integration hoặc Integration không hợp lệ',
      );
    }
  }

  /**
   * Lấy integrationId từ oaId
   * @param userId ID của người dùng
   * @param oaId ID của Official Account
   * @returns integrationId từ Integration có metadata chứa oaId
   */
  async getIntegrationIdFromOaId(
    userId: number,
    oaId: string,
  ): Promise<string> {
    try {
      const integrations =
        await this.zaloOAIntegrationService.getZaloOAIntegrationsByUserId(
          userId,
        );

      // Tìm integration có metadata.oaId khớp với oaId
      const matchingIntegration = integrations.find((integration) => {
        const metadata = integration.metadata as ZaloOAMetadata;
        return metadata?.oaId === oaId;
      });

      if (!matchingIntegration) {
        throw new AppException(
          MARKETING_ERROR_CODES.ZALO_OA_NOT_FOUND,
          `Không tìm thấy Integration cho oaId ${oaId}`,
        );
      }

      return matchingIntegration.id;
    } catch (error) {
      this.logger.error(
        `Failed to get integrationId from oaId ${oaId}: ${error.message}`,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        MARKETING_ERROR_CODES.ZALO_OA_NOT_FOUND,
        'Không tìm thấy Integration cho oaId này',
      );
    }
  }

  /**
   * Đồng bộ danh sách nhãn từ Zalo OA về hệ thống
   * @param userId ID của người dùng
   * @param integrationId ID của Integration
   * @returns Kết quả đồng bộ
   */
  async syncZaloTagsToSystem(
    userId: number,
    integrationId: string,
  ): Promise<{
    totalTags: number;
    newTags: number;
    updatedTags: number;
    syncedFollowers: number;
    tags: Array<{ name: string; followerCount: number; isNew: boolean }>;
  }> {
    try {
      this.logger.log(
        `Starting sync tags for user ${userId}, Integration ${integrationId}`,
      );

      // Lấy thông tin Official Account từ Integration ID
      const oa = await this.getOfficialAccountByIntegrationId(
        userId,
        integrationId,
      );

      // Lấy danh sách tag từ Zalo API sử dụng service mới
      const response = await this.zaloTagService.getTagsOfOA(oa.accessToken);

      if (!response.data || response.data.length === 0) {
        return {
          totalTags: 0,
          newTags: 0,
          updatedTags: 0,
          syncedFollowers: 0,
          tags: [],
        };
      }

      const zaloTags = response.data;
      let newTags = 0;
      let updatedTags = 0;
      let syncedFollowers = 0;
      const syncedTagsInfo: Array<{
        name: string;
        followerCount: number;
        isNew: boolean;
      }> = [];

      // Lấy danh sách tag hiện có trong hệ thống (từ followers)
      const allFollowers = await this.zaloFollowerRepository.find({
        where: { oaId: oa.oaId },
        select: ['tags'],
      });

      const existingTags = new Set<string>();
      allFollowers.forEach((follower) => {
        if (follower.tags && Array.isArray(follower.tags)) {
          follower.tags.forEach((tag) => existingTags.add(tag));
        }
      });

      // Xử lý từng tag từ Zalo
      for (const tagName of zaloTags) {
        const isNewTag = !existingTags.has(tagName);

        if (isNewTag) {
          newTags++;
        } else {
          updatedTags++;
        }

        // Lấy danh sách followers có tag này từ Zalo API
        try {
          const followersWithTag = await this.getFollowersWithTagFromZalo(
            oa.accessToken,
            tagName,
          );

          // Cập nhật tag cho followers trong database
          let tagSyncCount = 0;
          for (const followerData of followersWithTag) {
            const follower = await this.zaloFollowerRepository.findOne({
              where: { oaId: oa.oaId, userId: followerData.user_id },
            });

            if (follower) {
              const currentTags = follower.tags || [];
              if (!currentTags.includes(tagName)) {
                currentTags.push(tagName);
                await this.zaloFollowerRepository.update(follower.id, {
                  tags: currentTags,
                  updatedAt: Date.now(),
                });
                tagSyncCount++;
              }
            }
          }

          syncedFollowers += tagSyncCount;

          // Đếm số followers có tag này trong database
          const followerCount = await this.zaloFollowerRepository.count({
            where: {
              oaId: oa.oaId,
              tags: ArrayContains([tagName]),
            },
          });

          syncedTagsInfo.push({
            name: tagName,
            followerCount,
            isNew: isNewTag,
          });

          this.logger.debug(
            `Synced tag "${tagName}": ${tagSyncCount} followers updated`,
          );
        } catch (error) {
          this.logger.error(
            `Failed to sync tag "${tagName}": ${error.message}`,
          );
          // Vẫn thêm tag vào kết quả nhưng với followerCount = 0
          syncedTagsInfo.push({
            name: tagName,
            followerCount: 0,
            isNew: isNewTag,
          });
        }
      }

      const result = {
        totalTags: zaloTags.length,
        newTags,
        updatedTags,
        syncedFollowers,
        tags: syncedTagsInfo,
      };

      this.logger.log(
        `Completed sync tags for user ${userId}, OA ${oa.oaId}: ${result.totalTags} total, ${result.newTags} new, ${result.updatedTags} updated, ${result.syncedFollowers} followers synced`,
      );

      return result;
    } catch (error) {
      this.logger.error(`Failed to sync Zalo tags: ${error.message}`);
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Không thể đồng bộ nhãn từ Zalo về hệ thống',
      );
    }
  }

  /**
   * Lấy danh sách followers có tag từ Zalo API
   * @param accessToken Access token
   * @param tagName Tên tag
   * @returns Danh sách followers
   */
  private async getFollowersWithTagFromZalo(
    accessToken: string,
    tagName: string,
  ): Promise<any[]> {
    try {
      const response = await this.zaloApiClient.get(
        '/tag/getfollowersoftag',
        accessToken,
        { tag_name: tagName, count: 50 }, // Lấy tối đa 50 followers mỗi lần
      );

      return response.data?.followers || [];
    } catch (error) {
      this.logger.warn(
        `Failed to get followers for tag "${tagName}": ${error.message}`,
      );
      return [];
    }
  }

  /**
   * Làm mới danh sách nhãn từ Zalo (nhanh hơn sync đầy đủ)
   * @param userId ID của người dùng
   * @param integrationId ID của Integration
   * @returns Danh sách nhãn đã làm mới
   */
  async refreshZaloTags(
    userId: number,
    integrationId: string,
  ): Promise<Array<{ name: string; followerCount: number; isNew: boolean }>> {
    try {
      this.logger.log(
        `Refreshing tags for user ${userId}, Integration ${integrationId}`,
      );

      // Lấy thông tin Official Account từ Integration ID
      const oa = await this.getOfficialAccountByIntegrationId(
        userId,
        integrationId,
      );

      // Lấy danh sách tag từ Zalo API sử dụng service mới
      const response = await this.zaloTagService.getTagsOfOA(oa.accessToken);

      if (!response.data || response.data.length === 0) {
        return [];
      }

      const zaloTags = response.data;

      // Lấy danh sách tag hiện có trong hệ thống
      const allFollowers = await this.zaloFollowerRepository.find({
        where: { oaId: oa.oaId },
        select: ['tags'],
      });

      const existingTags = new Set<string>();
      allFollowers.forEach((follower) => {
        if (follower.tags && Array.isArray(follower.tags)) {
          follower.tags.forEach((tag) => existingTags.add(tag));
        }
      });

      // Xử lý từng tag từ Zalo và tính số lượng followers
      const refreshedTags = await Promise.all(
        zaloTags.map(async (tagName: string) => {
          const isNew = !existingTags.has(tagName);

          // Đếm số followers có tag này trong database
          const followerCount = await this.zaloFollowerRepository.count({
            where: {
              oaId: oa.oaId,
              tags: ArrayContains([tagName]),
            },
          });

          return {
            name: tagName,
            followerCount,
            isNew,
          };
        }),
      );

      this.logger.log(
        `Refreshed ${refreshedTags.length} tags for user ${userId}, OA ${oa.oaId}`,
      );

      return refreshedTags;
    } catch (error) {
      this.logger.error(`Failed to refresh Zalo tags: ${error.message}`);
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Không thể làm mới danh sách nhãn từ Zalo',
      );
    }
  }

  /**
   * Xóa nhãn hoàn toàn khỏi Zalo OA
   * @param userId ID của người dùng
   * @param oaId ID của Official Account
   * @param tagName Tên nhãn cần xóa
   * @returns Kết quả xóa nhãn
   */
  async deleteZaloTag(
    userId: number,
    oaId: string,
    tagName: string,
  ): Promise<{
    tagName: string;
    removedFromFollowers: number;
    success: boolean;
  }> {
    try {
      this.logger.log(
        `Deleting tag "${tagName}" for user ${userId}, OA ${oaId}`,
      );

      // Kiểm tra quyền truy cập Official Account
      await this.getOfficialAccountDetail(userId, oaId);

      // Xóa nhãn từ Zalo API
      const accessToken = await this.getOaToken(oaId);
      const response = await this.zaloApiClient.post(
        '/tag/rmtag',
        accessToken,
        {
          tag_name: tagName,
        },
      );

      if (response.error !== 0) {
        throw new Error(`Zalo API error: ${response.message}`);
      }

      // Đếm số followers có nhãn này trước khi xóa
      const followersWithTag = await this.zaloFollowerRepository.find({
        where: {
          oaId,
          tags: ArrayContains([tagName]),
        },
      });

      let removedFromFollowers = 0;

      // Xóa nhãn khỏi tất cả followers trong database
      for (const follower of followersWithTag) {
        try {
          const currentTags = follower.tags || [];
          const tagIndex = currentTags.indexOf(tagName);

          if (tagIndex !== -1) {
            currentTags.splice(tagIndex, 1);
            await this.zaloFollowerRepository.update(follower.id, {
              tags: currentTags,
              updatedAt: Date.now(),
            });
            removedFromFollowers++;
          }
        } catch (error) {
          this.logger.error(
            `Failed to remove tag from follower ${follower.id}: ${error.message}`,
          );
        }
      }

      const result = {
        tagName,
        removedFromFollowers,
        success: true,
      };

      this.logger.log(
        `Successfully deleted tag "${tagName}" for user ${userId}, OA ${oaId}. Removed from ${removedFromFollowers} followers`,
      );

      return result;
    } catch (error) {
      this.logger.error(
        `Failed to delete Zalo tag "${tagName}": ${error.message}`,
      );

      // Kiểm tra lỗi cụ thể từ Zalo API
      if (
        error.message.includes('tag not found') ||
        error.message.includes('không tìm thấy')
      ) {
        throw new AppException(
          ErrorCode.RESOURCE_NOT_FOUND,
          `Không tìm thấy nhãn "${tagName}" trong Zalo OA`,
        );
      }

      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        `Không thể xóa nhãn "${tagName}" từ Zalo OA`,
      );
    }
  }

  /**
   * Đếm số lượng user-audience có zaloSocialId
   * @param userId ID của người dùng
   * @param integrationId ID của integration (optional)
   * @returns Số lượng user-audience có zaloSocialId
   */
  async countUserAudienceWithZaloSocialId(
    userId: number,
    integrationId?: string,
  ): Promise<number> {
    try {
      const whereCondition: any = {
        userId,
        zaloSocialId: Not(IsNull()),
      };

      // Nếu có integrationId thì filter theo integration
      if (integrationId) {
        whereCondition.integrationId = integrationId;
      }

      const count = await this.userAudienceRepository.count({
        where: whereCondition,
      });

      this.logger.log(
        `Đếm user-audience có zaloSocialId: ${count} (userId: ${userId}, integrationId: ${integrationId})`,
      );
      return count;
    } catch (error) {
      this.logger.error(
        `Lỗi khi đếm user-audience có zaloSocialId: ${error.message}`,
        error.stack,
      );
      return 0;
    }
  }
}

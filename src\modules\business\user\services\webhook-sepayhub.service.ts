import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import {
  WebhooksSepayHubRequestDto,
  WebhookSepayHubResponseDto,
} from '../dto/webhook-sepayhub-request.dto';
import { UserOrderRepository } from '../../repositories';
import { OrderStatusEnum, PaymentStatusEnum } from '../../enums';
import { BillInfo } from '../../interfaces';
import { Transactional } from 'typeorm-transactional';

@Injectable()
export class WebhookSepayHubService {
  private readonly logger = new Logger(WebhookSepayHubService.name);
  private readonly webhookApiKey: string | undefined;

  constructor(
    private readonly configService: ConfigService,
    private readonly userOrderRepository: UserOrderRepository,
  ) {
    this.webhookApiKey = this.configService.get<string>(
      'SEPAYHUB_WEBHOOK_API_KEY',
    );
  }

  /**
   * <PERSON>ác thực API key từ header
   * @param apiKey API key từ header
   * @returns true nếu API key hợp lệ, false nếu không
   */
  validateApiKey(apiKey: string): boolean {
    if (!this.webhookApiKey) {
      this.logger.warn('SEPAYHUB_WEBHOOK_API_KEY is not configured');
      return false;
    }
    return apiKey === this.webhookApiKey;
  }

  /**
   * Xử lý webhook từ SepayHub
   * @param webhookRequest Dữ liệu webhook
   * @returns Kết quả xử lý
   */
  @Transactional()
  async processWebhook(
    webhookRequest: WebhooksSepayHubRequestDto,
  ): Promise<WebhookSepayHubResponseDto> {
    try {
      this.logger.log(
        `Processing SepayHub webhook: ${JSON.stringify(webhookRequest)}`,
      );

      // Kiểm tra định dạng nội dung với tiền tố REDAI + Mã đơn hàng + HUB
      const orderPattern = /REDAI(\d+)HUB/;
      const orderMatch = webhookRequest.content.match(orderPattern);

      if (!orderMatch) {
        this.logger.warn(
          `Invalid content format for SepayHub: ${webhookRequest.content}`,
        );
        return {
          success: false,
          message: 'Invalid content format. Expected format: REDAI{orderId}HUB',
        };
      }

      // Lấy orderId từ nội dung
      const orderId = parseInt(orderMatch[1]);
      this.logger.log(`Processing order ID: ${orderId}`);

      // Kiểm tra loại giao dịch phải là credit (tiền vào)
      if (webhookRequest.transfer_type !== 'credit') {
        this.logger.warn(
          `Invalid transfer type: ${webhookRequest.transfer_type}, expected: credit`,
        );
        return {
          success: false,
          message:
            'Invalid transfer type. Only credit transactions are accepted',
        };
      }

      // Kiểm tra số tiền phải lớn hơn 0
      if (webhookRequest.amount <= 0) {
        this.logger.warn(`Invalid amount: ${webhookRequest.amount}`);
        return {
          success: false,
          message: 'Invalid amount. Amount must be greater than 0',
        };
      }

      // Xử lý logic đơn hàng
      const orderProcessResult = await this.processOrderPayment(
        orderId,
        webhookRequest,
      );
      if (!orderProcessResult.success) {
        return orderProcessResult;
      }

      this.logger.log(
        `SepayHub webhook processed successfully for order: ${orderId}`,
      );

      return {
        success: true,
        message: 'SepayHub webhook processed successfully',
      };
    } catch (error) {
      this.logger.error(
        `Error processing SepayHub webhook: ${error.message}`,
        error.stack,
      );
      return {
        success: false,
        message: `Error processing webhook: ${error.message}`,
      };
    }
  }

  /**
   * Xử lý thanh toán đơn hàng
   * @param orderId ID đơn hàng
   * @param webhookRequest Dữ liệu webhook
   * @returns Kết quả xử lý
   */
  private async processOrderPayment(
    orderId: number,
    webhookRequest: WebhooksSepayHubRequestDto,
  ): Promise<WebhookSepayHubResponseDto> {
    try {
      // 1. Tìm đơn hàng theo orderId
      const order = await this.userOrderRepository.findById(orderId.toString());
      if (!order) {
        this.logger.warn(`Order not found: ${orderId}`);
        return {
          success: false,
          message: 'Order not found',
        };
      }

      // 2. Kiểm tra trạng thái đơn hàng
      if (order.orderStatus !== OrderStatusEnum.PENDING) {
        this.logger.warn(
          `Order is not in PENDING status: ${orderId}, current status: ${order.orderStatus}`,
        );
        return {
          success: false,
          message: 'Order is not in PENDING status',
        };
      }

      // 3. Kiểm tra số tiền thanh toán
      const billInfo = order.billInfo as BillInfo;
      if (!billInfo || !billInfo.total) {
        this.logger.warn(`Order ${orderId} does not have valid bill info`);
        return {
          success: false,
          message: 'Order does not have valid bill info',
        };
      }

      const expectedAmount = billInfo.total;
      if (!this.isValidAmount(webhookRequest.amount, expectedAmount)) {
        this.logger.warn(
          `Invalid payment amount: paid=${webhookRequest.amount}, expected=${expectedAmount}`,
        );
        return {
          success: false,
          message: 'Invalid payment amount',
        };
      }

      // 4. Cập nhật trạng thái đơn hàng và thanh toán
      const updatedBillInfo = {
        ...billInfo,
        paymentStatus: PaymentStatusEnum.PAID,
        paymentInfo: {
          ...billInfo.paymentInfo,
          method: 'BANK_TRANSFER',
          validatedAt: new Date().toISOString(),
          transactionId: webhookRequest.transaction_id,
          referenceCode: webhookRequest.reference_code,
          gateway: webhookRequest.gateway,
        },
      };

      await this.userOrderRepository.update(order.id, {
        orderStatus: OrderStatusEnum.CONFIRMED,
        billInfo: updatedBillInfo as any, // Cast to any for TypeORM JSONB update
        updatedAt: Math.floor(Date.now() / 1000),
      });

      this.logger.log(
        `Order ${orderId} payment processed successfully. Status updated to CONFIRMED`,
      );

      return {
        success: true,
        message: 'Order payment processed successfully',
      };
    } catch (error) {
      this.logger.error(
        `Error processing order payment for order ${orderId}: ${error.message}`,
        error.stack,
      );
      return {
        success: false,
        message: `Error processing order payment: ${error.message}`,
      };
    }
  }

  /**
   * Kiểm tra số tiền thanh toán có hợp lệ không
   * @param paidAmount Số tiền đã thanh toán
   * @param expectedAmount Số tiền cần thanh toán
   * @returns true nếu số tiền hợp lệ, false nếu không
   */
  private isValidAmount(paidAmount: number, expectedAmount: number): boolean {
    // Cho phép sai số 1% hoặc 1,000 VND (lấy giá trị nhỏ hơn)
    const tolerance = Math.min(expectedAmount * 0.01, 1000);
    return Math.abs(paidAmount - expectedAmount) <= tolerance;
  }
}

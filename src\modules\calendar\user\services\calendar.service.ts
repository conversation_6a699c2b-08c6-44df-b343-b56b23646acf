import { Injectable, Logger } from '@nestjs/common';
import { CalendarEventRepository } from '../../repositories';
import { QueueService } from '@/shared/queue';
import {
  CalendarEventQueryDto,
  CreateCalendarEventDto,
  UpdateCalendarEventDto,
} from '../../dto';
import {
  CalendarEvent,
  CalendarActionType,
  ExecutionStatus,
  CalendarEventStatus,
} from '../../entities';
import { AppException } from '@/common';
import { CALENDAR_ERROR_CODES } from '../../constants';
import { PaginatedResult } from '@/common/response';

@Injectable()
export class CalendarService {
  private readonly logger = new Logger(CalendarService.name);

  constructor(
    private readonly calendarEventRepository: CalendarEventRepository,
    private readonly queueService: QueueService,
  ) {}

  /**
   * Tạo sự kiện calendar mới
   */
  async createEvent(
    userId: number,
    createEventDto: CreateCalendarEventDto,
    attendees?: any[],
    resources?: any[],
    reminders?: any[],
  ): Promise<CalendarEvent> {
    try {
      // Kiểm tra xung đột thời gian
      if (createEventDto.endTime) {
        const conflictingEvents =
          await this.calendarEventRepository.findConflictingEvents(
            userId,
            new Date(createEventDto.startTime),
            new Date(createEventDto.endTime),
          );

        if (conflictingEvents.length > 0) {
          throw new AppException(
            CALENDAR_ERROR_CODES.EVENT_CONFLICT,
            CALENDAR_ERROR_CODES.EVENT_CONFLICT.message,
          );
        }
      }

      // Tạo sự kiện
      const eventData = {
        ...createEventDto,
        userId,
        startTime: new Date(createEventDto.startTime),
        endTime: createEventDto.endTime
          ? new Date(createEventDto.endTime)
          : undefined,
        status: CalendarEventStatus.SCHEDULED,
      };

      const event = this.calendarEventRepository.create(eventData);
      const savedEvent = await this.calendarEventRepository.save(event);

      // NOTE: Trong version tối ưu, attendees và resources được lưu trong actionConfig
      // Không cần tạo các entity riêng biệt nữa

      // NOTE: Reminders cũng được lưu trong actionConfig trong version tối ưu

      this.logger.log(
        `Đã tạo sự kiện calendar: ${savedEvent.id} cho user ${userId}`,
      );
      return savedEvent;
    } catch (error) {
      this.logger.error(
        `Lỗi khi tạo sự kiện calendar: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        CALENDAR_ERROR_CODES.INTERNAL_ERROR,
        CALENDAR_ERROR_CODES.INTERNAL_ERROR.message,
      );
    }
  }

  /**
   * Cập nhật sự kiện calendar
   */
  async updateEvent(
    eventId: string,
    userId: number,
    updateEventDto: UpdateCalendarEventDto,
  ): Promise<CalendarEvent> {
    try {
      const event = await this.calendarEventRepository.findOne({
        where: { id: eventId, userId },
      });

      if (!event) {
        throw new AppException(
          CALENDAR_ERROR_CODES.EVENT_NOT_FOUND,
          CALENDAR_ERROR_CODES.EVENT_NOT_FOUND.message,
        );
      }

      // Kiểm tra xung đột thời gian nếu thay đổi thời gian
      if (updateEventDto.startTime || updateEventDto.endTime) {
        const startTime = updateEventDto.startTime
          ? new Date(updateEventDto.startTime)
          : event.startTime;
        const endTime = updateEventDto.endTime
          ? new Date(updateEventDto.endTime)
          : event.endTime;

        if (endTime) {
          const conflictingEvents =
            await this.calendarEventRepository.findConflictingEvents(
              userId,
              startTime,
              endTime,
              eventId,
            );

          if (conflictingEvents.length > 0) {
            throw new AppException(
              CALENDAR_ERROR_CODES.EVENT_CONFLICT,
              CALENDAR_ERROR_CODES.EVENT_CONFLICT.message,
            );
          }
        }
      }

      // Cập nhật sự kiện
      const updateData = {
        ...updateEventDto,
        startTime: updateEventDto.startTime
          ? new Date(updateEventDto.startTime)
          : undefined,
        endTime: updateEventDto.endTime
          ? new Date(updateEventDto.endTime)
          : undefined,
      };

      await this.calendarEventRepository.update(eventId, updateData);

      const updatedEvent = await this.calendarEventRepository.findOne({
        where: { id: eventId },
      });

      this.logger.log(`Đã cập nhật sự kiện calendar: ${eventId}`);
      return updatedEvent!;
    } catch (error) {
      this.logger.error(
        `Lỗi khi cập nhật sự kiện calendar: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        CALENDAR_ERROR_CODES.INTERNAL_ERROR,
        CALENDAR_ERROR_CODES.INTERNAL_ERROR.message,
      );
    }
  }

  /**
   * Xóa sự kiện calendar
   */
  async deleteEvent(eventId: string, userId: number): Promise<void> {
    try {
      const event = await this.calendarEventRepository.findOne({
        where: { id: eventId, userId },
      });

      if (!event) {
        throw new AppException(
          CALENDAR_ERROR_CODES.EVENT_NOT_FOUND,
          CALENDAR_ERROR_CODES.EVENT_NOT_FOUND.message,
        );
      }

      // Hủy job nếu có
      if (event.jobId) {
        try {
          // TODO: removeJob method không tồn tại, skip
          this.logger.log(`Should remove job: ${event.jobId}`);
        } catch (error) {
          this.logger.warn(
            `Không thể hủy job ${event.jobId}: ${error.message}`,
          );
        }
      }

      // Xóa sự kiện (execution history sẽ được xóa cascade)
      await this.calendarEventRepository.delete(eventId);

      this.logger.log(`Đã xóa sự kiện calendar: ${eventId}`);
    } catch (error) {
      this.logger.error(
        `Lỗi khi xóa sự kiện calendar: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        CALENDAR_ERROR_CODES.INTERNAL_ERROR,
        CALENDAR_ERROR_CODES.INTERNAL_ERROR.message,
      );
    }
  }

  /**
   * Lấy danh sách sự kiện
   */
  async getEvents(
    userId: number,
    queryDto: CalendarEventQueryDto,
  ): Promise<PaginatedResult<CalendarEvent>> {
    try {
      return await this.calendarEventRepository.findCalendarEvents(
        queryDto,
        userId,
      );
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy danh sách sự kiện: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        CALENDAR_ERROR_CODES.INTERNAL_ERROR,
        CALENDAR_ERROR_CODES.INTERNAL_ERROR.message,
      );
    }
  }

  /**
   * Lấy chi tiết sự kiện
   */
  async getEventById(eventId: string, userId: number): Promise<CalendarEvent> {
    try {
      const event = await this.calendarEventRepository.findOne({
        where: { id: eventId, userId },
        relations: ['executionHistory'],
      });

      if (!event) {
        throw new AppException(
          CALENDAR_ERROR_CODES.EVENT_NOT_FOUND,
          CALENDAR_ERROR_CODES.EVENT_NOT_FOUND.message,
        );
      }

      return event;
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy chi tiết sự kiện: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        CALENDAR_ERROR_CODES.INTERNAL_ERROR,
        CALENDAR_ERROR_CODES.INTERNAL_ERROR.message,
      );
    }
  }

  /**
   * Thực thi sự kiện calendar (task, reminder, report)
   */
  async executeEvent(eventId: string, userId: number): Promise<void> {
    try {
      const event = await this.calendarEventRepository.findOne({
        where: { id: eventId, userId },
      });

      if (!event) {
        throw new AppException(
          CALENDAR_ERROR_CODES.EVENT_NOT_FOUND,
          CALENDAR_ERROR_CODES.EVENT_NOT_FOUND.message,
        );
      }

      // Tạo job data dựa trên actionType
      const jobData = {
        eventId: event.id,
        userId: event.userId,
        actionType: event.actionType,
        actionConfig: event.actionConfig,
        title: event.title,
        description: event.description,
        metadata: event.metadata,
      };

      // Thêm job vào queue dựa trên loại action
      let jobId: string | undefined;
      switch (event.actionType) {
        case CalendarActionType.TASK:
          jobId = await this.queueService.addCalendarTaskJob(jobData);
          break;
        case CalendarActionType.REMINDER:
          jobId = await this.queueService.addCalendarReminderJob(jobData);
          break;
        case CalendarActionType.REPORT:
          jobId = await this.queueService.addCalendarReportJob(jobData);
          break;
        default:
          throw new Error(`Unsupported action type: ${event.actionType}`);
      }

      // Cập nhật trạng thái thực thi
      await this.calendarEventRepository.update(eventId, {
        executionStatus: ExecutionStatus.PENDING,
        jobId,
      });

      this.logger.log(
        `Đã lên lịch thực thi sự kiện: ${eventId} với job ${jobId}`,
      );
    } catch (error) {
      this.logger.error(
        `Lỗi khi thực thi sự kiện: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        CALENDAR_ERROR_CODES.INTERNAL_ERROR,
        CALENDAR_ERROR_CODES.INTERNAL_ERROR.message,
      );
    }
  }

  /**
   * Hủy thực thi sự kiện
   */
  async cancelExecution(eventId: string, userId: number): Promise<void> {
    try {
      const event = await this.calendarEventRepository.findOne({
        where: { id: eventId, userId },
      });

      if (!event) {
        throw new AppException(
          CALENDAR_ERROR_CODES.EVENT_NOT_FOUND,
          CALENDAR_ERROR_CODES.EVENT_NOT_FOUND.message,
        );
      }

      // Hủy job nếu có
      if (event.jobId) {
        // TODO: removeJob method không tồn tại, skip
        this.logger.log(`Should remove job: ${event.jobId}`);
      }

      // Cập nhật trạng thái
      await this.calendarEventRepository.update(eventId, {
        executionStatus: ExecutionStatus.CANCELLED,
        jobId: undefined,
      });

      this.logger.log(`Đã hủy thực thi sự kiện: ${eventId}`);
    } catch (error) {
      this.logger.error(
        `Lỗi khi hủy thực thi sự kiện: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        CALENDAR_ERROR_CODES.INTERNAL_ERROR,
        CALENDAR_ERROR_CODES.INTERNAL_ERROR.message,
      );
    }
  }
}

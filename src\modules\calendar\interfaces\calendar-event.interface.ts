import {
  CalendarActionType,
  CalendarEventStatus,
  CalendarPriority,
  ExecutionStatus,
} from '../entities';

/**
 * Interface cho calendar event - T<PERSON><PERSON>u hóa
 */
export interface ICalendarEvent {
  id: string;
  userId: number;
  actionType: CalendarActionType;
  title: string;
  description?: string;
  startTime: Date;
  endTime?: Date;
  timeZone: string;
  status: CalendarEventStatus;
  priority: CalendarPriority;
  actionConfig: any;
  executionStatus: ExecutionStatus;
  executionStartTime?: Date;
  executionEndTime?: Date;
  executionResult?: any;
  executionError?: string;
  jobId?: string;
  retryCount: number;
  nextRetryTime?: Date;
  isActive: boolean;
  metadata?: any;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Interface cho execution history
 */
export interface ICalendarExecutionHistory {
  id: string;
  eventId: string;
  executionTime: Date;
  status: ExecutionStatus;
  startTime?: Date;
  endTime?: Date;
  duration?: number;
  result?: any;
  error?: string;
  jobId?: string;
  retryAttempt: number;
  executionConfig?: any;
  metadata?: any;
  createdAt: Date;
}

import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  Request,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { AdminDashboardPageService } from '../services/admin-dashboard-page.service';
import {
  CreateDashboardPageDto,
  UpdateDashboardPageDto,
  QueryDashboardPageDto,
  DashboardPageResponseDto,
} from '../dto/dashboard-page.dto';
import { ApiResponseDto, PaginatedResult } from '@common/response';
import { JwtEmployeeGuard } from '@modules/auth/guards';
import { SWAGGER_API_TAGS } from '@/common/swagger';

@ApiTags(SWAGGER_API_TAGS.ADMIN_DASHBOARD_PAGE)
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtEmployeeGuard)
@Controller('admin/dashboard/pages')
export class AdminDashboardPageController {
  constructor(private readonly adminDashboardPageService: AdminDashboardPageService) {}

  /**
   * Tạo dashboard page mới cho admin
   */
  @Post()
  @ApiOperation({ summary: 'Tạo dashboard page mới cho admin' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Dashboard page đã được tạo thành công',
    type: DashboardPageResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Dữ liệu đầu vào không hợp lệ',
  })
  @ApiResponse({
    status: HttpStatus.CONFLICT,
    description: 'Slug đã tồn tại',
  })
  async create(
    @Body() dto: CreateDashboardPageDto,
    @Request() req: any,
  ): Promise<ApiResponseDto<DashboardPageResponseDto>> {
    const result = await this.adminDashboardPageService.create(
      dto,
      req.employee?.id,
    );

    return ApiResponseDto.created(
      result,
      'Dashboard page admin đã được tạo thành công',
    );
  }

  /**
   * Lấy danh sách dashboard pages của admin
   */
  @Get()
  @ApiOperation({ summary: 'Lấy danh sách dashboard pages của admin' })
  @ApiQuery({
    name: 'pageType',
    required: false,
    enum: [
      'ADMIN_CUSTOM',
      'SYSTEM_EMPLOYEE_TEMPLATE',
      'ADMIN_TEMPLATE',
    ],
    description: 'Lọc theo loại dashboard page',
    example: 'ADMIN_CUSTOM',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Danh sách dashboard pages',
    type: PaginatedResult<DashboardPageResponseDto>,
  })
  async findMany(
    @Query() query: QueryDashboardPageDto,
    @Request() req: any,
  ): Promise<ApiResponseDto<PaginatedResult<DashboardPageResponseDto>>> {
    const result = await this.adminDashboardPageService.findMany(
      query,
      req.employee?.id,
      req.employee?.roles,
    );

    const pageResult: PaginatedResult<DashboardPageResponseDto> = {
      items: result.items,
      meta: {
        totalItems: result.total,
        itemCount: result.items.length,
        itemsPerPage: query.limit || 20,
        totalPages: Math.ceil(result.total / (query.limit || 20)),
        currentPage: query.page || 1,
      },
    };

    return ApiResponseDto.paginated(
      pageResult,
      'Lấy danh sách dashboard pages admin thành công',
    );
  }

  /**
   * Lấy danh sách dashboard pages có thể truy cập cho admin (cho menu)
   */
  @Get('accessible')
  @ApiOperation({ summary: 'Lấy danh sách dashboard pages có thể truy cập cho admin' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Danh sách dashboard pages có thể truy cập',
    type: [DashboardPageResponseDto],
  })
  async findAccessiblePages(
    @Request() req: any,
  ): Promise<ApiResponseDto<DashboardPageResponseDto[]>> {
    const result = await this.adminDashboardPageService.findAccessiblePages(
      req.employee?.id,
      req.employee?.roles,
    );

    return ApiResponseDto.success(
      result,
      'Lấy danh sách dashboard pages có thể truy cập cho admin thành công',
    );
  }

  /**
   * Lấy dashboard page mặc định cho admin
   */
  @Get('default')
  @ApiOperation({ summary: 'Lấy dashboard page mặc định cho admin' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Dashboard page mặc định',
    type: DashboardPageResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Không tìm thấy dashboard page mặc định',
  })
  async findDefaultPage(
    @Request() req: any,
  ): Promise<ApiResponseDto<DashboardPageResponseDto | null>> {
    const result = await this.adminDashboardPageService.findDefaultPage(
      req.employee?.id,
      req.employee?.roles,
    );

    return ApiResponseDto.success(
      result,
      result
        ? 'Lấy dashboard page mặc định cho admin thành công'
        : 'Không có dashboard page mặc định',
    );
  }

  /**
   * Lấy dashboard page theo slug cho admin
   */
  @Get('slug/:slug')
  @ApiOperation({ summary: 'Lấy dashboard page theo slug cho admin' })
  @ApiParam({ name: 'slug', description: 'Slug của dashboard page' })
  @ApiQuery({
    name: 'includeWidgets',
    required: false,
    type: Boolean,
    description: 'Bao gồm widgets',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Dashboard page',
    type: DashboardPageResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Dashboard page không tồn tại hoặc không có quyền truy cập',
  })
  async findBySlug(
    @Param('slug') slug: string,
    @Query('includeWidgets') includeWidgets: boolean = true,
    @Request() req: any,
  ): Promise<ApiResponseDto<DashboardPageResponseDto>> {
    const result = await this.adminDashboardPageService.findBySlug(
      slug,
      req.employee?.id,
      req.employee?.roles,
      includeWidgets,
    );

    return ApiResponseDto.success(result, 'Lấy dashboard page admin thành công');
  }

  /**
   * Lấy dashboard page theo ID cho admin
   */
  @Get(':id')
  @ApiOperation({ summary: 'Lấy dashboard page theo ID cho admin' })
  @ApiParam({ name: 'id', description: 'ID của dashboard page' })
  @ApiQuery({
    name: 'includeWidgets',
    required: false,
    type: Boolean,
    description: 'Bao gồm widgets',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Dashboard page',
    type: DashboardPageResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Dashboard page không tồn tại hoặc không có quyền truy cập',
  })
  async findById(
    @Param('id') id: string,
    @Query('includeWidgets') includeWidgets: boolean = false,
    @Request() req: any,
  ): Promise<ApiResponseDto<DashboardPageResponseDto>> {
    const result = await this.adminDashboardPageService.findById(
      id,
      req.employee?.id,
      req.employee?.roles,
      includeWidgets,
    );

    return ApiResponseDto.success(result, 'Lấy dashboard page admin thành công');
  }

  /**
   * Cập nhật dashboard page cho admin
   */
  @Put(':id')
  @ApiOperation({ summary: 'Cập nhật dashboard page cho admin' })
  @ApiParam({ name: 'id', description: 'ID của dashboard page' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Dashboard page đã được cập nhật thành công',
    type: DashboardPageResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Dashboard page không tồn tại hoặc không có quyền truy cập',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Dữ liệu đầu vào không hợp lệ hoặc không có quyền chỉnh sửa',
  })
  @ApiResponse({
    status: HttpStatus.CONFLICT,
    description: 'Slug đã tồn tại',
  })
  async update(
    @Param('id') id: string,
    @Body() dto: UpdateDashboardPageDto,
    @Request() req: any,
  ): Promise<ApiResponseDto<DashboardPageResponseDto>> {
    const result = await this.adminDashboardPageService.update(
      id,
      dto,
      req.employee?.id,
      req.employee?.roles,
    );

    return ApiResponseDto.updated(
      result,
      'Dashboard page admin đã được cập nhật thành công',
    );
  }

  /**
   * Đặt dashboard page làm mặc định cho admin
   */
  @Put(':id/set-default')
  @ApiOperation({ summary: 'Đặt dashboard page làm mặc định cho admin' })
  @ApiParam({ name: 'id', description: 'ID của dashboard page' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Dashboard page đã được đặt làm mặc định',
    type: DashboardPageResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Dashboard page không tồn tại hoặc không có quyền truy cập',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Không có quyền đặt dashboard page này làm mặc định',
  })
  async setAsDefault(
    @Param('id') id: string,
    @Request() req: any,
  ): Promise<ApiResponseDto<DashboardPageResponseDto>> {
    const result = await this.adminDashboardPageService.setAsDefault(
      id,
      req.employee?.id,
      req.employee?.roles,
    );

    return ApiResponseDto.updated(
      result,
      'Dashboard page admin đã được đặt làm mặc định',
    );
  }

  /**
   * Xóa dashboard page cho admin
   */
  @Delete(':id')
  @ApiOperation({ summary: 'Xóa dashboard page cho admin' })
  @ApiParam({ name: 'id', description: 'ID của dashboard page' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Dashboard page đã được xóa thành công',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Dashboard page không tồn tại hoặc không có quyền truy cập',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Không có quyền xóa dashboard page này',
  })
  async delete(
    @Param('id') id: string,
    @Request() req: any,
  ): Promise<ApiResponseDto<null>> {
    await this.adminDashboardPageService.delete(
      id,
      req.employee?.id,
      req.employee?.roles,
    );

    return ApiResponseDto.deleted(
      null,
      'Dashboard page admin đã được xóa thành công',
    );
  }

  /**
   * Cập nhật tabs config của dashboard page cho admin
   */
  @Put(':id/tabs')
  @ApiOperation({ summary: 'Cập nhật tabs config của dashboard page cho admin' })
  @ApiParam({ name: 'id', description: 'ID của dashboard page' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Tabs config đã được cập nhật thành công',
    type: DashboardPageResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Dashboard page không tồn tại hoặc không có quyền truy cập',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description:
      'Dữ liệu tabs config không hợp lệ hoặc không có quyền chỉnh sửa',
  })
  async updateTabsConfig(
    @Param('id') id: string,
    @Body() tabsConfig: any,
    @Request() req: any,
  ): Promise<ApiResponseDto<DashboardPageResponseDto>> {
    const result = await this.adminDashboardPageService.update(
      id,
      { tabsConfig },
      req.employee?.id,
      req.employee?.roles,
    );

    return ApiResponseDto.updated(
      result,
      'Tabs config admin đã được cập nhật thành công',
    );
  }

  /**
   * Lấy tabs config của dashboard page cho admin
   */
  @Get(':id/tabs')
  @ApiOperation({ summary: 'Lấy tabs config của dashboard page cho admin' })
  @ApiParam({ name: 'id', description: 'ID của dashboard page' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Tabs config',
    type: Object,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Dashboard page không tồn tại hoặc không có quyền truy cập',
  })
  async getTabsConfig(
    @Param('id') id: string,
    @Request() req: any,
  ): Promise<ApiResponseDto<any>> {
    const result = await this.adminDashboardPageService.findById(
      id,
      req.employee?.id,
      req.employee?.roles,
      false,
    );

    const defaultTabsConfig = {
      currentTabId: '',
      tabs: [] as any[],
    };

    return ApiResponseDto.success(
      result.tabsConfig || defaultTabsConfig,
      'Lấy tabs config admin thành công',
    );
  }

  /**
   * Lấy dashboard analytics cho admin
   */
  @Get('analytics/overview')
  @ApiOperation({ summary: 'Lấy dashboard analytics overview cho admin' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Dashboard analytics overview',
    type: Object,
  })
  async getAnalyticsOverview(
    @Request() req: any,
  ): Promise<ApiResponseDto<any>> {
    const result = await this.adminDashboardPageService.getAnalyticsOverview(
      req.employee?.id,
      req.employee?.roles,
    );

    return ApiResponseDto.success(
      result,
      'Lấy dashboard analytics overview thành công',
    );
  }
}
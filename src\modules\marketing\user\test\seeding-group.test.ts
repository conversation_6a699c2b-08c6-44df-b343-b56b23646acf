import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { DataSource, Repository } from 'typeorm';
import { SeedingGroupService } from '../services/seeding-group.service';
import { SeedingGroupRepository, SeedingGroupAccountRepository } from '../repositories/seeding-group.repository';
import { SeedingGroup, SeedingGroupAccount, SeedingGroupStatus } from '../entities/seeding-group.entity';
import { CreateSeedingGroupDto } from '../dto/seeding-group/seeding-group.dto';
import { MARKETING_ERROR_CODES } from '../exceptions/marketing-error.codes';

describe('SeedingGroupService', () => {
  let service: SeedingGroupService;
  let seedingGroupRepository: SeedingGroupRepository;
  let seedingGroupAccountRepository: SeedingGroupAccountRepository;
  let mockSeedingGroupEntityRepository: Repository<SeedingGroup>;
  let mockSeedingGroupAccountEntityRepository: Repository<SeedingGroupAccount>;

  const mockDataSource = {
    createEntityManager: jest.fn().mockReturnValue({}),
  };

  const mockSeedingGroup: SeedingGroup = {
    id: 'test-id',
    userId: 1,
    name: 'Test Seeding Group',
    description: 'Test description',
    oaAccountId: 'oa-123',
    oaAgentId: 'agent-123',
    groupId: 'group-123',
    status: SeedingGroupStatus.DRAFT,
    startTime: '09:00',
    endTime: '18:00',
    endDate: '2024-12-31',
    intervalMinutes: 30,
    maxPerDay: 20,
    randomize: false,
    totalAccounts: 1,
    activeAccounts: 0,
    totalMessagesSent: 0,
    lastActivityAt: null,
    createdAt: Date.now(),
    updatedAt: Date.now(),
    user: null,
    accounts: [],
  };

  const mockCreateDto: CreateSeedingGroupDto = {
    name: 'Test Seeding Group',
    description: 'Test description',
    oaAccountId: 'oa-123',
    oaAgentId: 'agent-123',
    groupId: 'group-123',
    accounts: [
      {
        personalAccountId: 'personal-123',
        agentId: 'agent-456',
      },
    ],
    startTime: '09:00',
    endTime: '18:00',
    endDate: '2024-12-31',
    intervalMinutes: 30,
    maxPerDay: 20,
    randomize: false,
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        SeedingGroupService,
        SeedingGroupRepository,
        SeedingGroupAccountRepository,
        {
          provide: DataSource,
          useValue: mockDataSource,
        },
        {
          provide: getRepositoryToken(SeedingGroup),
          useValue: {
            findOne: jest.fn(),
            save: jest.fn(),
            update: jest.fn(),
            delete: jest.fn(),
            find: jest.fn(),
            createQueryBuilder: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(SeedingGroupAccount),
          useValue: {
            save: jest.fn(),
            find: jest.fn(),
            delete: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<SeedingGroupService>(SeedingGroupService);
    seedingGroupRepository = module.get<SeedingGroupRepository>(SeedingGroupRepository);
    seedingGroupAccountRepository = module.get<SeedingGroupAccountRepository>(SeedingGroupAccountRepository);
    mockSeedingGroupEntityRepository = module.get<Repository<SeedingGroup>>(getRepositoryToken(SeedingGroup));
    mockSeedingGroupAccountEntityRepository = module.get<Repository<SeedingGroupAccount>>(getRepositoryToken(SeedingGroupAccount));
  });

  describe('create', () => {
    it('should create a seeding group successfully', async () => {
      // Mock repository methods
      jest.spyOn(mockSeedingGroupEntityRepository, 'findOne').mockResolvedValue(null);
      jest.spyOn(mockSeedingGroupEntityRepository, 'save').mockResolvedValue(mockSeedingGroup);
      jest.spyOn(mockSeedingGroupAccountEntityRepository, 'save').mockResolvedValue([]);
      jest.spyOn(seedingGroupRepository, 'findByIdAndUserId').mockResolvedValue(mockSeedingGroup);

      const result = await service.create(1, mockCreateDto);

      expect(result).toEqual(mockSeedingGroup);
      expect(mockSeedingGroupEntityRepository.save).toHaveBeenCalled();
    });

    it('should throw error if name already exists', async () => {
      jest.spyOn(mockSeedingGroupEntityRepository, 'findOne').mockResolvedValue(mockSeedingGroup);

      await expect(service.create(1, mockCreateDto)).rejects.toThrow();
    });

    it('should throw error if groupId already in use', async () => {
      jest.spyOn(mockSeedingGroupEntityRepository, 'findOne')
        .mockResolvedValueOnce(null) // For name check
        .mockResolvedValueOnce(mockSeedingGroup); // For groupId check

      await expect(service.create(1, mockCreateDto)).rejects.toThrow();
    });

    it('should throw error if start time >= end time', async () => {
      const invalidDto = { ...mockCreateDto, startTime: '18:00', endTime: '09:00' };

      await expect(service.create(1, invalidDto)).rejects.toThrow();
    });

    it('should throw error if no accounts provided', async () => {
      const invalidDto = { ...mockCreateDto, accounts: [] };

      await expect(service.create(1, invalidDto)).rejects.toThrow();
    });
  });

  describe('findOne', () => {
    it('should return seeding group if found', async () => {
      jest.spyOn(seedingGroupRepository, 'findByIdAndUserId').mockResolvedValue(mockSeedingGroup);

      const result = await service.findOne('test-id', 1);

      expect(result).toEqual(mockSeedingGroup);
    });

    it('should throw error if seeding group not found', async () => {
      jest.spyOn(seedingGroupRepository, 'findByIdAndUserId').mockResolvedValue(null);

      await expect(service.findOne('test-id', 1)).rejects.toThrow();
    });
  });

  describe('start', () => {
    it('should start seeding group successfully', async () => {
      const draftGroup = { ...mockSeedingGroup, status: SeedingGroupStatus.DRAFT };
      const activeGroup = { ...mockSeedingGroup, status: SeedingGroupStatus.ACTIVE };

      jest.spyOn(seedingGroupRepository, 'findByIdAndUserId')
        .mockResolvedValueOnce(draftGroup)
        .mockResolvedValueOnce(activeGroup);
      jest.spyOn(mockSeedingGroupEntityRepository, 'update').mockResolvedValue(undefined);

      const result = await service.start('test-id', 1);

      expect(result.status).toBe(SeedingGroupStatus.ACTIVE);
      expect(mockSeedingGroupEntityRepository.update).toHaveBeenCalled();
    });

    it('should throw error if seeding group already active', async () => {
      const activeGroup = { ...mockSeedingGroup, status: SeedingGroupStatus.ACTIVE };
      jest.spyOn(seedingGroupRepository, 'findByIdAndUserId').mockResolvedValue(activeGroup);

      await expect(service.start('test-id', 1)).rejects.toThrow();
    });
  });

  describe('pause', () => {
    it('should pause active seeding group successfully', async () => {
      const activeGroup = { ...mockSeedingGroup, status: SeedingGroupStatus.ACTIVE };
      const pausedGroup = { ...mockSeedingGroup, status: SeedingGroupStatus.PAUSED };

      jest.spyOn(seedingGroupRepository, 'findByIdAndUserId')
        .mockResolvedValueOnce(activeGroup)
        .mockResolvedValueOnce(pausedGroup);
      jest.spyOn(mockSeedingGroupEntityRepository, 'update').mockResolvedValue(undefined);

      const result = await service.pause('test-id', 1);

      expect(result.status).toBe(SeedingGroupStatus.PAUSED);
    });

    it('should throw error if seeding group not active', async () => {
      const draftGroup = { ...mockSeedingGroup, status: SeedingGroupStatus.DRAFT };
      jest.spyOn(seedingGroupRepository, 'findByIdAndUserId').mockResolvedValue(draftGroup);

      await expect(service.pause('test-id', 1)).rejects.toThrow();
    });
  });

  describe('remove', () => {
    it('should delete draft seeding group successfully', async () => {
      const draftGroup = { ...mockSeedingGroup, status: SeedingGroupStatus.DRAFT };
      jest.spyOn(seedingGroupRepository, 'findByIdAndUserId').mockResolvedValue(draftGroup);
      jest.spyOn(seedingGroupAccountRepository, 'bulkDeleteBySeedingGroupId').mockResolvedValue(undefined);
      jest.spyOn(mockSeedingGroupEntityRepository, 'delete').mockResolvedValue(undefined);

      await service.remove('test-id', 1);

      expect(seedingGroupAccountRepository.bulkDeleteBySeedingGroupId).toHaveBeenCalledWith('test-id');
      expect(mockSeedingGroupEntityRepository.delete).toHaveBeenCalledWith({ id: 'test-id', userId: 1 });
    });

    it('should throw error if trying to delete active seeding group', async () => {
      const activeGroup = { ...mockSeedingGroup, status: SeedingGroupStatus.ACTIVE };
      jest.spyOn(seedingGroupRepository, 'findByIdAndUserId').mockResolvedValue(activeGroup);

      await expect(service.remove('test-id', 1)).rejects.toThrow();
    });
  });
});
